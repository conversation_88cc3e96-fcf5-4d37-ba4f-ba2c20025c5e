const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const mongoose = require('mongoose');
const { verifyToken } = require('../middlewares/authUserMiddleware');
const taskKanbanController = require('../controllers/taskKanbanController');
const taskAIController = require('../controllers/taskAIController');

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

// Récupérer toutes les tâches
router.get('/', async (req, res) => {
  try {
    const tasks = await Task.find()
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email')
      .populate('teamId', 'name');

    res.status(200).json(tasks);
  } catch (error) {
    console.error('Erreur lors de la récupération des tâches:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération des tâches', error: error.message });
  }
});

// Récupérer les tâches d'une équipe spécifique
router.get('/team/:teamId', async (req, res) => {
  try {
    const { teamId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(teamId)) {
      return res.status(400).json({ message: 'ID d\'équipe invalide' });
    }

    const tasks = await Task.find({ teamId })
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email');

    res.status(200).json(tasks);
  } catch (error) {
    console.error('Erreur lors de la récupération des tâches de l\'équipe:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération des tâches de l\'équipe', error: error.message });
  }
});

// Récupérer une tâche par son ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID de tâche invalide' });
    }

    const task = await Task.findById(id)
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email')
      .populate('teamId', 'name');

    if (!task) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    res.status(200).json(task);
  } catch (error) {
    console.error('Erreur lors de la récupération de la tâche:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération de la tâche', error: error.message });
  }
});

// Créer une nouvelle tâche
router.post('/', async (req, res) => {
  try {
    const { title, description, status, priority, dueDate, assignedTo, teamId, createdBy } = req.body;

    if (!title) {
      return res.status(400).json({ message: 'Le titre est requis' });
    }

    if (!teamId || !mongoose.Types.ObjectId.isValid(teamId)) {
      return res.status(400).json({ message: 'ID d\'équipe invalide ou manquant' });
    }

    const newTask = new Task({
      title,
      description,
      status: status || 'todo',
      priority: priority || 'medium',
      dueDate,
      assignedTo,
      teamId,
      createdBy
    });

    const savedTask = await newTask.save();

    // Populate the saved task with user and team details
    const populatedTask = await Task.findById(savedTask._id)
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email')
      .populate('teamId', 'name');

    res.status(201).json(populatedTask);
  } catch (error) {
    console.error('Erreur lors de la création de la tâche:', error);
    res.status(500).json({ message: 'Erreur lors de la création de la tâche', error: error.message });
  }
});

// Mettre à jour une tâche existante
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, status, priority, dueDate, assignedTo } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID de tâche invalide' });
    }

    const updatedTask = await Task.findByIdAndUpdate(
      id,
      { title, description, status, priority, dueDate, assignedTo },
      { new: true, runValidators: true }
    )
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email')
      .populate('teamId', 'name');

    if (!updatedTask) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    res.status(200).json(updatedTask);
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la tâche:', error);
    res.status(500).json({ message: 'Erreur lors de la mise à jour de la tâche', error: error.message });
  }
});

// Mettre à jour uniquement le statut d'une tâche
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID de tâche invalide' });
    }

    if (!status || !['todo', 'in-progress', 'done'].includes(status)) {
      return res.status(400).json({ message: 'Statut invalide' });
    }

    const updatedTask = await Task.findByIdAndUpdate(
      id,
      { status },
      { new: true, runValidators: true }
    )
      .populate('assignedTo', 'name firstName lastName email')
      .populate('createdBy', 'name firstName lastName email')
      .populate('teamId', 'name');

    if (!updatedTask) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    res.status(200).json(updatedTask);
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut de la tâche:', error);
    res.status(500).json({ message: 'Erreur lors de la mise à jour du statut de la tâche', error: error.message });
  }
});

// Supprimer une tâche
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID de tâche invalide' });
    }

    const deletedTask = await Task.findByIdAndDelete(id);

    if (!deletedTask) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    res.status(200).json({ message: 'Tâche supprimée avec succès', id });
  } catch (error) {
    console.error('Erreur lors de la suppression de la tâche:', error);
    res.status(500).json({ message: 'Erreur lors de la suppression de la tâche', error: error.message });
  }
});

// ==================== ROUTES KANBAN ====================

// Obtenir le tableau Kanban d'une équipe
router.get('/kanban/:teamId', taskKanbanController.getKanbanBoard);

// Créer une tâche dans une colonne spécifique
router.post('/kanban/:teamId', taskKanbanController.createTaskInColumn);

// Déplacer une tâche (drag & drop)
router.patch('/:taskId/move', taskKanbanController.moveTask);

// Archiver/désarchiver une tâche
router.patch('/:taskId/archive', taskKanbanController.toggleArchiveTask);

// Statistiques des tâches pour une équipe
router.get('/stats/:teamId', taskKanbanController.getTaskStatistics);

// ==================== ROUTES IA ====================

// Analyser une tâche avec l'IA
router.post('/:taskId/ai/analyze', taskAIController.analyzeTask);

// Générer des sous-tâches avec l'IA
router.post('/:taskId/ai/subtasks', taskAIController.generateSubtasks);

// Estimer l'effort avec l'IA
router.post('/:taskId/ai/estimate', taskAIController.estimateEffort);

// Générer une description avec l'IA
router.post('/:taskId/ai/description', taskAIController.generateDescription);

// Appliquer une suggestion IA
router.post('/:taskId/ai/suggestions/:suggestionIndex/apply', taskAIController.applySuggestion);

// ==================== ROUTES AVANCÉES ====================

// Ajouter un commentaire à une tâche
router.post('/:taskId/comments', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    await task.addComment(userId, content);
    await task.populate('comments.user', 'fullName email profileImage');

    res.json({
      message: 'Commentaire ajouté avec succès',
      task
    });
  } catch (error) {
    console.error('Erreur ajout commentaire:', error);
    res.status(500).json({ message: 'Erreur lors de l\'ajout du commentaire', error: error.message });
  }
});

// Ajouter/retirer un label
router.post('/:taskId/labels', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { name, color, action = 'add' } = req.body;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    if (action === 'add') {
      await task.addLabel(name, color);
    } else if (action === 'remove') {
      await task.removeLabel(name);
    }

    res.json({
      message: `Label ${action === 'add' ? 'ajouté' : 'retiré'} avec succès`,
      task
    });
  } catch (error) {
    console.error('Erreur gestion label:', error);
    res.status(500).json({ message: 'Erreur lors de la gestion du label', error: error.message });
  }
});

// Assigner/désassigner des utilisateurs
router.patch('/:taskId/assign', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { userIds } = req.body;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    await task.assignTo(userIds);
    await task.populate('assignedTo', 'fullName email profileImage');

    res.json({
      message: 'Assignation mise à jour avec succès',
      task
    });
  } catch (error) {
    console.error('Erreur assignation:', error);
    res.status(500).json({ message: 'Erreur lors de l\'assignation', error: error.message });
  }
});

// Obtenir les sous-tâches d'une tâche
router.get('/:taskId/subtasks', async (req, res) => {
  try {
    const { taskId } = req.params;

    const subtasks = await Task.find({ parentTask: taskId })
      .populate('assignedTo', 'fullName email profileImage')
      .populate('createdBy', 'fullName email profileImage')
      .sort({ position: 1, createdAt: -1 });

    res.json(subtasks);
  } catch (error) {
    console.error('Erreur récupération sous-tâches:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération des sous-tâches', error: error.message });
  }
});

// Mettre à jour les métriques de vue
router.post('/:taskId/view', async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Tâche non trouvée' });
    }

    task.metrics.viewCount += 1;
    task.metrics.lastViewed = new Date();
    await task.save();

    res.json({ message: 'Vue enregistrée' });
  } catch (error) {
    console.error('Erreur enregistrement vue:', error);
    res.status(500).json({ message: 'Erreur lors de l\'enregistrement de la vue', error: error.message });
  }
});

module.exports = router;
