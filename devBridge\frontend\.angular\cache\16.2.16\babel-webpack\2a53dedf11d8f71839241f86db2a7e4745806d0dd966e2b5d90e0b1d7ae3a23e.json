{"ast": null, "code": "function noop() {}\nconst defaultDispose = noop;\nconst _WeakRef = typeof WeakRef !== \"undefined\" ? WeakRef : function (value) {\n  return {\n    deref: () => value\n  };\n};\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry = typeof FinalizationRegistry !== \"undefined\" ? FinalizationRegistry : function () {\n  return {\n    register: noop,\n    unregister: noop\n  };\n};\nconst finalizationBatchSize = 10024;\nexport class WeakCache {\n  constructor(max = Infinity, dispose = defaultDispose) {\n    this.max = max;\n    this.dispose = dispose;\n    this.map = new _WeakMap();\n    this.newest = null;\n    this.oldest = null;\n    this.unfinalizedNodes = new Set();\n    this.finalizationScheduled = false;\n    this.size = 0;\n    this.finalize = () => {\n      const iterator = this.unfinalizedNodes.values();\n      for (let i = 0; i < finalizationBatchSize; i++) {\n        const node = iterator.next().value;\n        if (!node) break;\n        this.unfinalizedNodes.delete(node);\n        const key = node.key;\n        delete node.key;\n        node.keyRef = new _WeakRef(key);\n        this.registry.register(key, node, node);\n      }\n      if (this.unfinalizedNodes.size > 0) {\n        queueMicrotask(this.finalize);\n      } else {\n        this.finalizationScheduled = false;\n      }\n    };\n    this.registry = new _FinalizationRegistry(this.deleteNode.bind(this));\n  }\n  has(key) {\n    return this.map.has(key);\n  }\n  get(key) {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n  getNode(key) {\n    const node = this.map.get(key);\n    if (node && node !== this.newest) {\n      const {\n        older,\n        newer\n      } = node;\n      if (newer) {\n        newer.older = older;\n      }\n      if (older) {\n        older.newer = newer;\n      }\n      node.older = this.newest;\n      node.older.newer = node;\n      node.newer = null;\n      this.newest = node;\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n    return node;\n  }\n  set(key, value) {\n    let node = this.getNode(key);\n    if (node) {\n      return node.value = value;\n    }\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest\n    };\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n    this.newest = node;\n    this.oldest = this.oldest || node;\n    this.scheduleFinalization(node);\n    this.map.set(key, node);\n    this.size++;\n    return node.value;\n  }\n  clean() {\n    while (this.oldest && this.size > this.max) {\n      this.deleteNode(this.oldest);\n    }\n  }\n  deleteNode(node) {\n    if (node === this.newest) {\n      this.newest = node.older;\n    }\n    if (node === this.oldest) {\n      this.oldest = node.newer;\n    }\n    if (node.newer) {\n      node.newer.older = node.older;\n    }\n    if (node.older) {\n      node.older.newer = node.newer;\n    }\n    this.size--;\n    const key = node.key || node.keyRef && node.keyRef.deref();\n    this.dispose(node.value, key);\n    if (!node.keyRef) {\n      this.unfinalizedNodes.delete(node);\n    } else {\n      this.registry.unregister(node);\n    }\n    if (key) this.map.delete(key);\n  }\n  delete(key) {\n    const node = this.map.get(key);\n    if (node) {\n      this.deleteNode(node);\n      return true;\n    }\n    return false;\n  }\n  scheduleFinalization(node) {\n    this.unfinalizedNodes.add(node);\n    if (!this.finalizationScheduled) {\n      this.finalizationScheduled = true;\n      queueMicrotask(this.finalize);\n    }\n  }\n}", "map": {"version": 3, "names": ["noop", "defaultDispose", "_WeakRef", "WeakRef", "value", "deref", "_WeakMap", "WeakMap", "Map", "_FinalizationRegistry", "FinalizationRegistry", "register", "unregister", "finalizationBatchSize", "<PERSON>ak<PERSON><PERSON>", "constructor", "max", "Infinity", "dispose", "map", "newest", "oldest", "unfinalizedNodes", "Set", "finalizationScheduled", "size", "finalize", "iterator", "values", "i", "node", "next", "delete", "key", "keyRef", "registry", "queueMicrotask", "deleteNode", "bind", "has", "get", "getNode", "older", "newer", "set", "scheduleFinalization", "clean", "add"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@wry/caches/lib/weak.js"], "sourcesContent": ["function noop() { }\nconst defaultDispose = noop;\nconst _WeakRef = typeof WeakRef !== \"undefined\"\n    ? WeakRef\n    : function (value) {\n        return { deref: () => value };\n    };\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry = typeof FinalizationRegistry !== \"undefined\"\n    ? FinalizationRegistry\n    : function () {\n        return {\n            register: noop,\n            unregister: noop,\n        };\n    };\nconst finalizationBatchSize = 10024;\nexport class WeakCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new _WeakMap();\n        this.newest = null;\n        this.oldest = null;\n        this.unfinalizedNodes = new Set();\n        this.finalizationScheduled = false;\n        this.size = 0;\n        this.finalize = () => {\n            const iterator = this.unfinalizedNodes.values();\n            for (let i = 0; i < finalizationBatchSize; i++) {\n                const node = iterator.next().value;\n                if (!node)\n                    break;\n                this.unfinalizedNodes.delete(node);\n                const key = node.key;\n                delete node.key;\n                node.keyRef = new _WeakRef(key);\n                this.registry.register(key, node, node);\n            }\n            if (this.unfinalizedNodes.size > 0) {\n                queueMicrotask(this.finalize);\n            }\n            else {\n                this.finalizationScheduled = false;\n            }\n        };\n        this.registry = new _FinalizationRegistry(this.deleteNode.bind(this));\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return (node.value = value);\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest,\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.scheduleFinalization(node);\n        this.map.set(key, node);\n        this.size++;\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.size > this.max) {\n            this.deleteNode(this.oldest);\n        }\n    }\n    deleteNode(node) {\n        if (node === this.newest) {\n            this.newest = node.older;\n        }\n        if (node === this.oldest) {\n            this.oldest = node.newer;\n        }\n        if (node.newer) {\n            node.newer.older = node.older;\n        }\n        if (node.older) {\n            node.older.newer = node.newer;\n        }\n        this.size--;\n        const key = node.key || (node.keyRef && node.keyRef.deref());\n        this.dispose(node.value, key);\n        if (!node.keyRef) {\n            this.unfinalizedNodes.delete(node);\n        }\n        else {\n            this.registry.unregister(node);\n        }\n        if (key)\n            this.map.delete(key);\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            this.deleteNode(node);\n            return true;\n        }\n        return false;\n    }\n    scheduleFinalization(node) {\n        this.unfinalizedNodes.add(node);\n        if (!this.finalizationScheduled) {\n            this.finalizationScheduled = true;\n            queueMicrotask(this.finalize);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAE;AAClB,MAAMC,cAAc,GAAGD,IAAI;AAC3B,MAAME,QAAQ,GAAG,OAAOC,OAAO,KAAK,WAAW,GACzCA,OAAO,GACP,UAAUC,KAAK,EAAE;EACf,OAAO;IAAEC,KAAK,EAAEA,CAAA,KAAMD;EAAM,CAAC;AACjC,CAAC;AACL,MAAME,QAAQ,GAAG,OAAOC,OAAO,KAAK,WAAW,GAAGA,OAAO,GAAGC,GAAG;AAC/D,MAAMC,qBAAqB,GAAG,OAAOC,oBAAoB,KAAK,WAAW,GACnEA,oBAAoB,GACpB,YAAY;EACV,OAAO;IACHC,QAAQ,EAAEX,IAAI;IACdY,UAAU,EAAEZ;EAChB,CAAC;AACL,CAAC;AACL,MAAMa,qBAAqB,GAAG,KAAK;AACnC,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,GAAG,GAAGC,QAAQ,EAAEC,OAAO,GAAGjB,cAAc,EAAE;IAClD,IAAI,CAACe,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,GAAG,GAAG,IAAIb,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACc,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,QAAQ,GAAG,MAAM;MAClB,MAAMC,QAAQ,GAAG,IAAI,CAACL,gBAAgB,CAACM,MAAM,CAAC,CAAC;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,qBAAqB,EAAEgB,CAAC,EAAE,EAAE;QAC5C,MAAMC,IAAI,GAAGH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC3B,KAAK;QAClC,IAAI,CAAC0B,IAAI,EACL;QACJ,IAAI,CAACR,gBAAgB,CAACU,MAAM,CAACF,IAAI,CAAC;QAClC,MAAMG,GAAG,GAAGH,IAAI,CAACG,GAAG;QACpB,OAAOH,IAAI,CAACG,GAAG;QACfH,IAAI,CAACI,MAAM,GAAG,IAAIhC,QAAQ,CAAC+B,GAAG,CAAC;QAC/B,IAAI,CAACE,QAAQ,CAACxB,QAAQ,CAACsB,GAAG,EAAEH,IAAI,EAAEA,IAAI,CAAC;MAC3C;MACA,IAAI,IAAI,CAACR,gBAAgB,CAACG,IAAI,GAAG,CAAC,EAAE;QAChCW,cAAc,CAAC,IAAI,CAACV,QAAQ,CAAC;MACjC,CAAC,MACI;QACD,IAAI,CAACF,qBAAqB,GAAG,KAAK;MACtC;IACJ,CAAC;IACD,IAAI,CAACW,QAAQ,GAAG,IAAI1B,qBAAqB,CAAC,IAAI,CAAC4B,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzE;EACAC,GAAGA,CAACN,GAAG,EAAE;IACL,OAAO,IAAI,CAACd,GAAG,CAACoB,GAAG,CAACN,GAAG,CAAC;EAC5B;EACAO,GAAGA,CAACP,GAAG,EAAE;IACL,MAAMH,IAAI,GAAG,IAAI,CAACW,OAAO,CAACR,GAAG,CAAC;IAC9B,OAAOH,IAAI,IAAIA,IAAI,CAAC1B,KAAK;EAC7B;EACAqC,OAAOA,CAACR,GAAG,EAAE;IACT,MAAMH,IAAI,GAAG,IAAI,CAACX,GAAG,CAACqB,GAAG,CAACP,GAAG,CAAC;IAC9B,IAAIH,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACV,MAAM,EAAE;MAC9B,MAAM;QAAEsB,KAAK;QAAEC;MAAM,CAAC,GAAGb,IAAI;MAC7B,IAAIa,KAAK,EAAE;QACPA,KAAK,CAACD,KAAK,GAAGA,KAAK;MACvB;MACA,IAAIA,KAAK,EAAE;QACPA,KAAK,CAACC,KAAK,GAAGA,KAAK;MACvB;MACAb,IAAI,CAACY,KAAK,GAAG,IAAI,CAACtB,MAAM;MACxBU,IAAI,CAACY,KAAK,CAACC,KAAK,GAAGb,IAAI;MACvBA,IAAI,CAACa,KAAK,GAAG,IAAI;MACjB,IAAI,CAACvB,MAAM,GAAGU,IAAI;MAClB,IAAIA,IAAI,KAAK,IAAI,CAACT,MAAM,EAAE;QACtB,IAAI,CAACA,MAAM,GAAGsB,KAAK;MACvB;IACJ;IACA,OAAOb,IAAI;EACf;EACAc,GAAGA,CAACX,GAAG,EAAE7B,KAAK,EAAE;IACZ,IAAI0B,IAAI,GAAG,IAAI,CAACW,OAAO,CAACR,GAAG,CAAC;IAC5B,IAAIH,IAAI,EAAE;MACN,OAAQA,IAAI,CAAC1B,KAAK,GAAGA,KAAK;IAC9B;IACA0B,IAAI,GAAG;MACHG,GAAG;MACH7B,KAAK;MACLuC,KAAK,EAAE,IAAI;MACXD,KAAK,EAAE,IAAI,CAACtB;IAChB,CAAC;IACD,IAAI,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACuB,KAAK,GAAGb,IAAI;IAC5B;IACA,IAAI,CAACV,MAAM,GAAGU,IAAI;IAClB,IAAI,CAACT,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIS,IAAI;IACjC,IAAI,CAACe,oBAAoB,CAACf,IAAI,CAAC;IAC/B,IAAI,CAACX,GAAG,CAACyB,GAAG,CAACX,GAAG,EAAEH,IAAI,CAAC;IACvB,IAAI,CAACL,IAAI,EAAE;IACX,OAAOK,IAAI,CAAC1B,KAAK;EACrB;EACA0C,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACzB,MAAM,IAAI,IAAI,CAACI,IAAI,GAAG,IAAI,CAACT,GAAG,EAAE;MACxC,IAAI,CAACqB,UAAU,CAAC,IAAI,CAAChB,MAAM,CAAC;IAChC;EACJ;EACAgB,UAAUA,CAACP,IAAI,EAAE;IACb,IAAIA,IAAI,KAAK,IAAI,CAACV,MAAM,EAAE;MACtB,IAAI,CAACA,MAAM,GAAGU,IAAI,CAACY,KAAK;IAC5B;IACA,IAAIZ,IAAI,KAAK,IAAI,CAACT,MAAM,EAAE;MACtB,IAAI,CAACA,MAAM,GAAGS,IAAI,CAACa,KAAK;IAC5B;IACA,IAAIb,IAAI,CAACa,KAAK,EAAE;MACZb,IAAI,CAACa,KAAK,CAACD,KAAK,GAAGZ,IAAI,CAACY,KAAK;IACjC;IACA,IAAIZ,IAAI,CAACY,KAAK,EAAE;MACZZ,IAAI,CAACY,KAAK,CAACC,KAAK,GAAGb,IAAI,CAACa,KAAK;IACjC;IACA,IAAI,CAAClB,IAAI,EAAE;IACX,MAAMQ,GAAG,GAAGH,IAAI,CAACG,GAAG,IAAKH,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACI,MAAM,CAAC7B,KAAK,CAAC,CAAE;IAC5D,IAAI,CAACa,OAAO,CAACY,IAAI,CAAC1B,KAAK,EAAE6B,GAAG,CAAC;IAC7B,IAAI,CAACH,IAAI,CAACI,MAAM,EAAE;MACd,IAAI,CAACZ,gBAAgB,CAACU,MAAM,CAACF,IAAI,CAAC;IACtC,CAAC,MACI;MACD,IAAI,CAACK,QAAQ,CAACvB,UAAU,CAACkB,IAAI,CAAC;IAClC;IACA,IAAIG,GAAG,EACH,IAAI,CAACd,GAAG,CAACa,MAAM,CAACC,GAAG,CAAC;EAC5B;EACAD,MAAMA,CAACC,GAAG,EAAE;IACR,MAAMH,IAAI,GAAG,IAAI,CAACX,GAAG,CAACqB,GAAG,CAACP,GAAG,CAAC;IAC9B,IAAIH,IAAI,EAAE;MACN,IAAI,CAACO,UAAU,CAACP,IAAI,CAAC;MACrB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAe,oBAAoBA,CAACf,IAAI,EAAE;IACvB,IAAI,CAACR,gBAAgB,CAACyB,GAAG,CAACjB,IAAI,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACN,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACjCY,cAAc,CAAC,IAAI,CAACV,QAAQ,CAAC;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}