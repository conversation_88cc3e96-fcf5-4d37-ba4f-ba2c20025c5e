{"ast": null, "code": "import { inspect } from './inspect.mjs';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nexport const instanceOf = /* c8 ignore next 6 */\n// FIXME: https://github.com/graphql/graphql-js/issues/2317\nglobalThis.process && globalThis.process.env.NODE_ENV === 'production' ? function instanceOf(value, constructor) {\n  return value instanceof constructor;\n} : function instanceOf(value, constructor) {\n  if (value instanceof constructor) {\n    return true;\n  }\n  if (typeof value === 'object' && value !== null) {\n    var _value$constructor;\n\n    // Prefer Symbol.toStringTag since it is immune to minification.\n    const className = constructor.prototype[Symbol.toStringTag];\n    const valueClassName =\n    // We still need to support constructor's name to detect conflicts with older versions of this library.\n    Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n    ? value[Symbol.toStringTag] : (_value$constructor = value.constructor) === null || _value$constructor === void 0 ? void 0 : _value$constructor.name;\n    if (className === valueClassName) {\n      const stringifiedValue = inspect(value);\n      throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n    }\n  }\n  return false;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}