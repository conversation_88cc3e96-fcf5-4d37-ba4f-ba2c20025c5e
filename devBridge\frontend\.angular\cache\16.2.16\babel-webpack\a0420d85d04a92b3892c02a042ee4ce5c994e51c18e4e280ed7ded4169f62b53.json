{"ast": null, "code": "// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst {\n  forEach,\n  slice\n} = Array.prototype;\nconst {\n  hasOwnProperty\n} = Object.prototype;\nexport class Trie {\n  constructor(weakness = true, makeData = defaultMakeData) {\n    this.weakness = weakness;\n    this.makeData = makeData;\n  }\n  lookup() {\n    return this.lookupArray(arguments);\n  }\n  lookupArray(array) {\n    let node = this;\n    forEach.call(array, key => node = node.getChildTrie(key));\n    return hasOwnProperty.call(node, \"data\") ? node.data : node.data = this.makeData(slice.call(array));\n  }\n  peek() {\n    return this.peekArray(arguments);\n  }\n  peekArray(array) {\n    let node = this;\n    for (let i = 0, len = array.length; node && i < len; ++i) {\n      const map = node.mapFor(array[i], false);\n      node = map && map.get(array[i]);\n    }\n    return node && node.data;\n  }\n  remove() {\n    return this.removeArray(arguments);\n  }\n  removeArray(array) {\n    let data;\n    if (array.length) {\n      const head = array[0];\n      const map = this.mapFor(head, false);\n      const child = map && map.get(head);\n      if (child) {\n        data = child.removeArray(slice.call(array, 1));\n        if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n          map.delete(head);\n        }\n      }\n    } else {\n      data = this.data;\n      delete this.data;\n    }\n    return data;\n  }\n  getChildTrie(key) {\n    const map = this.mapFor(key, true);\n    let child = map.get(key);\n    if (!child) map.set(key, child = new Trie(this.weakness, this.makeData));\n    return child;\n  }\n  mapFor(key, create) {\n    return this.weakness && isObjRef(key) ? this.weak || (create ? this.weak = new WeakMap() : void 0) : this.strong || (create ? this.strong = new Map() : void 0);\n  }\n}\nfunction isObjRef(value) {\n  switch (typeof value) {\n    case \"object\":\n      if (value === null) break;\n    // Fall through to return true...\n    case \"function\":\n      return true;\n  }\n  return false;\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}