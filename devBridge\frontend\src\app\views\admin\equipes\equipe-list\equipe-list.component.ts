import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { EquipeService } from 'src/app/services/equipe.service';
import { NotificationService } from 'src/app/services/notification.service';
import { AuthuserService } from 'src/app/services/authuser.service';
import {
  Equipe,
  TeamSearchFilters,
  TeamListResponse
} from 'src/app/models/equipe.model';
import { finalize, debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-equipe-list',
  templateUrl: './equipe-list.component.html',
  styleUrls: ['./equipe-list.component.css']
})
export class EquipeListComponent implements OnInit {
  equipes: Equipe[] = [];
  loading = false;
  error: string | null = null;

  // Nouvelles propriétés pour les fonctionnalités avancées
  searchControl = new FormControl('');
  statusFilter = new FormControl('all');
  publicFilter = new FormControl('all');
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 0;
  totalItems = 0;

  // Options pour les filtres
  statusOptions = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'active', label: 'Actives' },
    { value: 'inactive', label: 'Inactives' },
    { value: 'archived', label: 'Archivées' }
  ];

  publicOptions = [
    { value: 'all', label: 'Toutes' },
    { value: 'true', label: 'Publiques' },
    { value: 'false', label: 'Privées' }
  ];

  // Colonnes affichées
  displayedColumns = ['name', 'admin', 'members', 'status', 'actions'];

  currentUser: any;

  constructor(
    private equipeService: EquipeService,
    private router: Router,
    private notificationService: NotificationService,
    private authService: AuthuserService
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.setupSearchSubscription();
    this.setupFilterSubscriptions();
    this.loadEquipes();
  }

  private setupSearchSubscription(): void {
    this.searchControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadEquipes();
    });
  }

  private setupFilterSubscriptions(): void {
    this.statusFilter.valueChanges.subscribe(() => {
      this.currentPage = 1;
      this.loadEquipes();
    });

    this.publicFilter.valueChanges.subscribe(() => {
      this.currentPage = 1;
      this.loadEquipes();
    });
  }

  loadEquipes(): void {
    this.loading = true;
    this.error = null;

    const filters: TeamSearchFilters = {
      page: this.currentPage,
      limit: this.itemsPerPage
    };

    // Ajouter les filtres si ils ne sont pas "all"
    const searchValue = this.searchControl.value?.trim();
    if (searchValue) {
      filters.search = searchValue;
    }

    const statusValue = this.statusFilter.value;
    if (statusValue && statusValue !== 'all') {
      filters.status = statusValue;
    }

    const publicValue = this.publicFilter.value;
    if (publicValue && publicValue !== 'all') {
      filters.isPublic = publicValue === 'true';
    }

    this.equipeService.getEquipesWithFilters(filters).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: (response: TeamListResponse) => {
        console.log('Équipes chargées:', response);
        this.equipes = response.teams;
        this.totalPages = response.pagination.total;
        this.totalItems = response.pagination.totalItems;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des équipes:', error);
        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';
        this.notificationService.showError('Erreur lors du chargement des équipes');

        // Fallback vers l'ancienne méthode
        this.loadEquipesLegacy();
      }
    });
  }

  private loadEquipesLegacy(): void {
    this.equipeService.getEquipes().pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: (data) => {
        console.log('Équipes chargées (legacy):', data);
        this.equipes = data;
        this.totalItems = data.length;
        this.totalPages = Math.ceil(data.length / this.itemsPerPage);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des équipes (legacy):', error);
        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';
        this.notificationService.showError('Erreur lors du chargement des équipes');
      }
    });
  }

  navigateToAddEquipe(): void {
    this.router.navigate(['/equipes/ajouter']);
  }

  navigateToEditEquipe(id: string): void {
    this.router.navigate(['/equipes/modifier', id]);
  }

  navigateToEquipeDetail(id: string): void {
    this.router.navigate(['/equipes/detail', id]);
  }

  deleteEquipe(id: string): void {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }

    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation
    const equipe = this.equipes.find(e => e._id === id);
    const equipeName = equipe?.name || 'cette équipe';

    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe "${equipeName}" ?`)) {
      this.loading = true;

      this.equipeService.deleteEquipe(id).pipe(
        finalize(() => this.loading = false)
      ).subscribe({
        next: () => {
          console.log('Équipe supprimée avec succès');
          this.notificationService.showSuccess(`L'équipe "${equipeName}" a été supprimée avec succès`);
          this.loadEquipes();
        },
        error: (error) => {
          console.error('Erreur lors de la suppression de l\'équipe:', error);
          this.error = 'Impossible de supprimer l\'équipe. Veuillez réessayer plus tard.';
          this.notificationService.showError(`Erreur lors de la suppression de l'équipe "${equipeName}"`);
        }
      });
    }
  }

  navigateToTasks(id: string): void {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }

    const equipe = this.equipes.find(e => e._id === id);
    const equipeName = equipe?.name || 'cette équipe';

    // Naviguer vers la page des tâches de l'équipe (route admin)
    this.router.navigate(['/admin/tasks', id]);
  }

  // Méthode pour obtenir le nombre de tâches d'une équipe (simulé)
  getTaskCount(equipeId: string): number {
    // TODO: Implémenter l'appel API réel pour obtenir le nombre de tâches
    // Pour l'instant, retourner un nombre aléatoire pour la démonstration
    const counts = [0, 5, 9, 15, 7, 11, 18, 3];
    const index = equipeId.length % counts.length;
    return counts[index];
  }

  // Méthode pour obtenir le statut des tâches d'une équipe
  getTaskStatus(equipeId: string): { completed: number; total: number; percentage: number } {
    const total = this.getTaskCount(equipeId);
    const completed = Math.floor(total * 0.7); // 70% des tâches sont complétées (admin a plus de contrôle)
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { completed, total, percentage };
  }

  // Nouvelles méthodes pour les fonctionnalités avancées

  /**
   * Gestion de la pagination
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadEquipes();
  }

  /**
   * Changer le nombre d'éléments par page
   */
  onItemsPerPageChange(itemsPerPage: number): void {
    this.itemsPerPage = itemsPerPage;
    this.currentPage = 1;
    this.loadEquipes();
  }

  /**
   * Réinitialiser tous les filtres
   */
  resetFilters(): void {
    this.searchControl.setValue('');
    this.statusFilter.setValue('all');
    this.publicFilter.setValue('all');
    this.currentPage = 1;
    this.loadEquipes();
  }

  /**
   * Archiver une équipe
   */
  archiveTeam(equipe: Equipe): void {
    if (!equipe._id) return;

    if (confirm(`Êtes-vous sûr de vouloir archiver l'équipe "${equipe.name}" ?`)) {
      this.loading = true;

      this.equipeService.archiveTeam(equipe._id).pipe(
        finalize(() => this.loading = false)
      ).subscribe({
        next: (response) => {
          this.notificationService.showSuccess(`L'équipe "${equipe.name}" a été archivée`);
          this.loadEquipes();
        },
        error: (error) => {
          console.error('Erreur lors de l\'archivage:', error);
          this.notificationService.showError('Erreur lors de l\'archivage de l\'équipe');
        }
      });
    }
  }

  /**
   * Activer une équipe
   */
  activateTeam(equipe: Equipe): void {
    if (!equipe._id) return;

    this.loading = true;

    this.equipeService.activateTeam(equipe._id).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: (response) => {
        this.notificationService.showSuccess(`L'équipe "${equipe.name}" a été activée`);
        this.loadEquipes();
      },
      error: (error) => {
        console.error('Erreur lors de l\'activation:', error);
        this.notificationService.showError('Erreur lors de l\'activation de l\'équipe');
      }
    });
  }

  /**
   * Vérifier si l'utilisateur peut modifier une équipe
   */
  canEditTeam(equipe: Equipe): boolean {
    if (!this.currentUser) return false;

    // Admin système peut tout modifier
    if (this.currentUser.role === 'admin') return true;

    // Admin de l'équipe peut modifier
    return this.equipeService.isTeamAdmin(equipe, this.currentUser.id);
  }

  /**
   * Vérifier si l'utilisateur peut supprimer une équipe
   */
  canDeleteTeam(equipe: Equipe): boolean {
    return this.canEditTeam(equipe);
  }

  /**
   * Obtenir le nom d'affichage d'un admin
   */
  getAdminDisplayName(admin: any): string {
    if (!admin) return 'Non assigné';

    if (typeof admin === 'string') return admin;

    return admin.fullName || admin.username || admin.email || 'Utilisateur';
  }

  /**
   * Obtenir le nombre de membres d'une équipe
   */
  getMemberCount(equipe: Equipe): number {
    return equipe.memberCount || equipe.members?.length || 0;
  }

  /**
   * Obtenir le badge de statut
   */
  getStatusBadgeClass(status?: string): string {
    switch (status) {
      case 'active': return 'badge-success';
      case 'inactive': return 'badge-warning';
      case 'archived': return 'badge-secondary';
      default: return 'badge-primary';
    }
  }

  /**
   * Obtenir le texte du statut
   */
  getStatusText(status?: string): string {
    switch (status) {
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      case 'archived': return 'Archivée';
      default: return 'Inconnue';
    }
  }

  /**
   * Vérifier si une équipe est pleine
   */
  isTeamFull(equipe: Equipe): boolean {
    return equipe.isFullTeam || false;
  }

  /**
   * Obtenir les slots disponibles
   */
  getAvailableSlots(equipe: Equipe): number {
    return equipe.availableSlots || 0;
  }
}

