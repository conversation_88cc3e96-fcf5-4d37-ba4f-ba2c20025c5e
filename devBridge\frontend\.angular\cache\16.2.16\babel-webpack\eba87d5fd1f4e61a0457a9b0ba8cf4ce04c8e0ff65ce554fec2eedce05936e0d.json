{"ast": null, "code": "import { isPromise } from './isPromise.mjs';\n\n/**\n * Similar to Array.prototype.reduce(), however the reducing callback may return\n * a Promise, in which case reduction will continue after each promise resolves.\n *\n * If the callback does not return a Promise, then this function will also not\n * return a Promise.\n */\nexport function promiseReduce(values, callbackFn, initialValue) {\n  let accumulator = initialValue;\n  for (const value of values) {\n    accumulator = isPromise(accumulator) ? accumulator.then(resolved => callbackFn(resolved, value)) : callbackFn(accumulator, value);\n  }\n  return accumulator;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}