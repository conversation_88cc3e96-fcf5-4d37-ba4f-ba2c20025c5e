{"ast": null, "code": "function noop() {}\nconst defaultDispose = noop;\nconst _WeakRef = typeof WeakRef !== \"undefined\" ? WeakRef : function (value) {\n  return {\n    deref: () => value\n  };\n};\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry = typeof FinalizationRegistry !== \"undefined\" ? FinalizationRegistry : function () {\n  return {\n    register: noop,\n    unregister: noop\n  };\n};\nconst finalizationBatchSize = 10024;\nexport class WeakCache {\n  constructor(max = Infinity, dispose = defaultDispose) {\n    this.max = max;\n    this.dispose = dispose;\n    this.map = new _WeakMap();\n    this.newest = null;\n    this.oldest = null;\n    this.unfinalizedNodes = new Set();\n    this.finalizationScheduled = false;\n    this.size = 0;\n    this.finalize = () => {\n      const iterator = this.unfinalizedNodes.values();\n      for (let i = 0; i < finalizationBatchSize; i++) {\n        const node = iterator.next().value;\n        if (!node) break;\n        this.unfinalizedNodes.delete(node);\n        const key = node.key;\n        delete node.key;\n        node.keyRef = new _WeakRef(key);\n        this.registry.register(key, node, node);\n      }\n      if (this.unfinalizedNodes.size > 0) {\n        queueMicrotask(this.finalize);\n      } else {\n        this.finalizationScheduled = false;\n      }\n    };\n    this.registry = new _FinalizationRegistry(this.deleteNode.bind(this));\n  }\n  has(key) {\n    return this.map.has(key);\n  }\n  get(key) {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n  getNode(key) {\n    const node = this.map.get(key);\n    if (node && node !== this.newest) {\n      const {\n        older,\n        newer\n      } = node;\n      if (newer) {\n        newer.older = older;\n      }\n      if (older) {\n        older.newer = newer;\n      }\n      node.older = this.newest;\n      node.older.newer = node;\n      node.newer = null;\n      this.newest = node;\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n    return node;\n  }\n  set(key, value) {\n    let node = this.getNode(key);\n    if (node) {\n      return node.value = value;\n    }\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest\n    };\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n    this.newest = node;\n    this.oldest = this.oldest || node;\n    this.scheduleFinalization(node);\n    this.map.set(key, node);\n    this.size++;\n    return node.value;\n  }\n  clean() {\n    while (this.oldest && this.size > this.max) {\n      this.deleteNode(this.oldest);\n    }\n  }\n  deleteNode(node) {\n    if (node === this.newest) {\n      this.newest = node.older;\n    }\n    if (node === this.oldest) {\n      this.oldest = node.newer;\n    }\n    if (node.newer) {\n      node.newer.older = node.older;\n    }\n    if (node.older) {\n      node.older.newer = node.newer;\n    }\n    this.size--;\n    const key = node.key || node.keyRef && node.keyRef.deref();\n    this.dispose(node.value, key);\n    if (!node.keyRef) {\n      this.unfinalizedNodes.delete(node);\n    } else {\n      this.registry.unregister(node);\n    }\n    if (key) this.map.delete(key);\n  }\n  delete(key) {\n    const node = this.map.get(key);\n    if (node) {\n      this.deleteNode(node);\n      return true;\n    }\n    return false;\n  }\n  scheduleFinalization(node) {\n    this.unfinalizedNodes.add(node);\n    if (!this.finalizationScheduled) {\n      this.finalizationScheduled = true;\n      queueMicrotask(this.finalize);\n    }\n  }\n}\n//# sourceMappingURL=weak.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}