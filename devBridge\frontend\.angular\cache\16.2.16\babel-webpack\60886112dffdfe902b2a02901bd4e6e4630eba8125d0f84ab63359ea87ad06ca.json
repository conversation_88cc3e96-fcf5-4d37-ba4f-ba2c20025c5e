{"ast": null, "code": "'use strict';\n\nconst defaultIsExtractableFile = require('./isExtractableFile.js');\n\n/**\n * Clones a value, recursively extracting\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob) and\n * [`ReactNativeFile`]{@link ReactNativeFile} instances with their\n * [object paths]{@link ObjectPath}, replacing them with `null`.\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/Filelist) instances\n * are treated as [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File)\n * instance arrays.\n * @kind function\n * @name extractFiles\n * @param {*} value Value (typically an object tree) to extract files from.\n * @param {ObjectPath} [path=''] Prefix for object paths for extracted files.\n * @param {ExtractableFileMatcher} [isExtractableFile=isExtractableFile] The function used to identify extractable files.\n * @returns {ExtractFilesResult} Result.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { extractFiles } from 'extract-files';\n * ```\n *\n * ```js\n * import extractFiles from 'extract-files/public/extractFiles.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { extractFiles } = require('extract-files');\n * ```\n *\n * ```js\n * const extractFiles = require('extract-files/public/extractFiles.js');\n * ```\n * @example <caption>Extract files from an object.</caption>\n * For the following:\n *\n * ```js\n * const file1 = new File(['1'], '1.txt', { type: 'text/plain' });\n * const file2 = new File(['2'], '2.txt', { type: 'text/plain' });\n * const value = {\n *   a: file1,\n *   b: [file1, file2],\n * };\n *\n * const { clone, files } = extractFiles(value, 'prefix');\n * ```\n *\n * `value` remains the same.\n *\n * `clone` is:\n *\n * ```json\n * {\n *   \"a\": null,\n *   \"b\": [null, null]\n * }\n * ```\n *\n * `files` is a [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map) instance containing:\n *\n * | Key     | Value                        |\n * | :------ | :--------------------------- |\n * | `file1` | `['prefix.a', 'prefix.b.0']` |\n * | `file2` | `['prefix.b.1']`             |\n */\nmodule.exports = function extractFiles(value, path = '', isExtractableFile = defaultIsExtractableFile) {\n  // Map of extracted files and their object paths within the input value.\n  const files = new Map();\n\n  // Map of arrays and objects recursed within the input value and their clones,\n  // for reusing clones of values that are referenced multiple times within the\n  // input value.\n  const clones = new Map();\n\n  /**\n   * Recursively clones the value, extracting files.\n   * @kind function\n   * @name extractFiles~recurse\n   * @param {*} value Value to extract files from.\n   * @param {ObjectPath} path Prefix for object paths for extracted files.\n   * @param {Set} recursed Recursed arrays and objects for avoiding infinite recursion of circular references within the input value.\n   * @returns {*} Clone of the value with files replaced with `null`.\n   * @ignore\n   */\n  function recurse(value, path, recursed) {\n    let clone = value;\n    if (isExtractableFile(value)) {\n      clone = null;\n      const filePaths = files.get(value);\n      filePaths ? filePaths.push(path) : files.set(value, [path]);\n    } else {\n      const isList = Array.isArray(value) || typeof FileList !== 'undefined' && value instanceof FileList;\n      const isObject = value && value.constructor === Object;\n      if (isList || isObject) {\n        const hasClone = clones.has(value);\n        if (hasClone) clone = clones.get(value);else {\n          clone = isList ? [] : {};\n          clones.set(value, clone);\n        }\n        if (!recursed.has(value)) {\n          const pathPrefix = path ? `${path}.` : '';\n          const recursedDeeper = new Set(recursed).add(value);\n          if (isList) {\n            let index = 0;\n            for (const item of value) {\n              const itemClone = recurse(item, pathPrefix + index++, recursedDeeper);\n              if (!hasClone) clone.push(itemClone);\n            }\n          } else for (const key in value) {\n            const propertyClone = recurse(value[key], pathPrefix + key, recursedDeeper);\n            if (!hasClone) clone[key] = propertyClone;\n          }\n        }\n      }\n    }\n    return clone;\n  }\n  return {\n    clone: recurse(value, path, new Set()),\n    files\n  };\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}