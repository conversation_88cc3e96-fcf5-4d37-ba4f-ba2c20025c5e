"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.usageReportingSignature = exports.calculateReferencedFieldsByType = void 0;
var calculateReferencedFieldsByType_1 = require("./calculateReferencedFieldsByType");
Object.defineProperty(exports, "calculateReferencedFieldsByType", { enumerable: true, get: function () { return calculateReferencedFieldsByType_1.calculateReferencedFieldsByType; } });
var signature_1 = require("./signature");
Object.defineProperty(exports, "usageReportingSignature", { enumerable: true, get: function () { return signature_1.usageReportingSignature; } });
//# sourceMappingURL=index.js.map