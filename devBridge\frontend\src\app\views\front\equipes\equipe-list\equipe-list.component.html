<!-- Interface Claire et Professionnelle pour la Gestion des Équipes -->
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
  <div class="container mx-auto px-4 py-8 max-w-7xl">

    <!-- En-tête Principal -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-8">
      <div class="px-8 py-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <!-- Titre et Description -->
          <div class="text-center lg:text-left">
            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              <i class="fas fa-users text-blue-600 mr-3"></i>
              Me<PERSON>
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">
              Organisez et gérez efficacement vos équipes de travail
            </p>
            <div class="flex items-center justify-center lg:justify-start mt-3 text-sm text-gray-500 dark:text-gray-400">
              <i class="fas fa-info-circle mr-2"></i>
              <span>{{ equipes?.length || 0 }} équipe(s) disponible(s)</span>
            </div>
          </div>

          <!-- Actions Principales -->
          <div class="flex flex-col sm:flex-row gap-3">
            <!-- Bouton Rafraîchir -->
            <button
              (click)="refreshEquipes()"
              class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
              [disabled]="loading"
              title="Actualiser la liste des équipes"
            >
              <i class="fas fa-sync-alt mr-2" [class.animate-spin]="loading"></i>
              Actualiser
            </button>

            <!-- Bouton Créer Équipe -->
            <button
              (click)="navigateToAddEquipe()"
              class="inline-flex items-center justify-center px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <i class="fas fa-plus mr-2"></i>
              Créer une Équipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- État de Chargement -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-16">
      <div class="relative">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <i class="fas fa-users text-blue-600 text-lg"></i>
        </div>
      </div>
      <p class="mt-6 text-lg font-medium text-gray-600 dark:text-gray-400">
        Chargement de vos équipes...
      </p>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-500">
        Veuillez patienter quelques instants
      </p>
    </div>

    <!-- État d'Erreur -->
    <div *ngIf="error && !loading" class="max-w-md mx-auto">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
        <div class="text-red-500 mb-4">
          <i class="fas fa-exclamation-triangle text-3xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
          Erreur de chargement
        </h3>
        <p class="text-sm text-red-700 dark:text-red-300 mb-4">
          {{ error }}
        </p>
        <button
          (click)="loadEquipes()"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          Réessayer
        </button>
      </div>
    </div>

    <!-- État Vide - Aucune Équipe -->
    <div *ngIf="!loading && !error && equipes.length === 0" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <div class="bg-gray-100 dark:bg-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-users text-4xl text-gray-400 dark:text-gray-500"></i>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3">
          Aucune équipe trouvée
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
          Vous n'avez pas encore créé d'équipe.<br>
          Commencez par créer votre première équipe pour organiser vos projets.
        </p>
        <button
          (click)="navigateToAddEquipe()"
          class="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <i class="fas fa-plus mr-2"></i>
          Créer ma première équipe
        </button>
      </div>
    </div>

    <!-- Liste des Équipes - Design Clair et Professionnel -->
    <div *ngIf="!loading && !error && equipes.length > 0">
      <!-- En-tête de la Liste -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            <i class="fas fa-list mr-2 text-blue-600"></i>
            Liste de vos équipes
          </h2>
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ equipes.length }} équipe(s) trouvée(s)
          </span>
        </div>
      </div>

      <!-- Grille des Équipes -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div
          *ngFor="let equipe of equipes; trackBy: trackByEquipeId"
          class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
        >
          <!-- En-tête de la Carte -->
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <i class="fas fa-users text-white text-xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-white truncate max-w-[200px]" [title]="equipe.name">
                    {{ equipe.name }}
                  </h3>
                  <p class="text-blue-100 text-sm">
                    Équipe de {{ equipe.members?.length || 0 }} membre(s)
                  </p>
                </div>
              </div>
              <!-- Badge de Statut -->
              <div class="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                Actif
              </div>
            </div>
          </div>

          <!-- Contenu de la Carte -->
          <div class="p-6">
            <!-- Statistiques -->
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {{ equipe.members?.length || 0 }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Membres
                </div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                  {{ getTaskCount(equipe._id || '') }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Tâches
                </div>
              </div>
            </div>

            <!-- Description -->
            <div *ngIf="equipe.description" class="mb-6">
              <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3" [title]="equipe.description">
                {{ equipe.description }}
              </p>
            </div>

            <!-- Barre de Progression -->
            <div class="mb-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Progression</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ getTaskStatus(equipe._id || '').percentage }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-500"
                  [style.width.%]="getTaskStatus(equipe._id || '').percentage">
                </div>
              </div>
            </div>

            <!-- Actions - Les 3 Boutons Demandés -->
            <div class="space-y-3">
              <!-- Bouton Tasks (Principal) -->
              <button
                (click)="equipe._id && navigateToTasks(equipe._id)"
                class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center group shadow-lg hover:shadow-xl"
                title="Gérer les tâches de l'équipe"
              >
                <i class="fas fa-tasks mr-2 group-hover:scale-110 transition-transform"></i>
                <span>Gérer les Tâches</span>
                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
              </button>

              <!-- Boutons Secondaires -->
              <div class="grid grid-cols-2 gap-3">
                <!-- Bouton Details -->
                <button
                  (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
                  class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-3 rounded-lg transition-colors flex items-center justify-center"
                  title="Voir les détails de l'équipe"
                >
                  <i class="fas fa-eye mr-2"></i>
                  <span>Details</span>
                </button>

                <!-- Bouton Update -->
                <button
                  (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                  class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-3 rounded-lg transition-colors flex items-center justify-center"
                  title="Modifier l'équipe"
                >
                  <i class="fas fa-edit mr-2"></i>
                  <span>Update</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
