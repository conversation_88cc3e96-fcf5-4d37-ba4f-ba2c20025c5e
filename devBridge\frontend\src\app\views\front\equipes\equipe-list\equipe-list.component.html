<!-- Design épuré et moderne pour la liste des équipes -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Background subtil -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 dark:from-gray-800/20 dark:to-gray-900/20"></div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header épuré -->
    <div class="mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <!-- Titre et description -->
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Me<PERSON>
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              <PERSON><PERSON><PERSON> et organisez vos équipes de travail
            </p>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-3">
            <!-- Bouton Rafraîchir -->
            <button
              (click)="refreshEquipes()"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              title="Rafraîchir la liste"
              [disabled]="loading"
            >
              <i class="fas fa-sync-alt mr-2" [class.animate-spin]="loading"></i>
              <span class="hidden sm:inline">Rafraîchir</span>
            </button>

            <!-- Bouton Nouvelle Équipe -->
            <button
              (click)="navigateToAddEquipe()"
              class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors shadow-sm"
            >
              <i class="fas fa-plus mr-2"></i>
              Nouvelle Équipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-16">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p class="mt-4 text-gray-600 dark:text-gray-400">
        Chargement des équipes...
      </p>
    </div>

    <!-- Error Alert -->
    <div *ngIf="error" class="mb-6">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-start">
          <div class="text-red-500 mr-3">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="flex-1">
            <h3 class="font-medium text-red-800 dark:text-red-200 mb-1">
              Erreur de chargement
            </h3>
            <p class="text-sm text-red-700 dark:text-red-300">{{ error }}</p>
            <button
              (click)="loadEquipes()"
              class="mt-3 inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-800/30 rounded-md hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
            >
              <i class="fas fa-sync-alt mr-1.5"></i> Réessayer
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Teams -->
    <div *ngIf="!loading && !error && equipes.length === 0" class="text-center py-16">
      <div class="w-20 h-20 mx-auto mb-6 text-gray-400 dark:text-gray-500">
        <i class="fas fa-users text-5xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        Aucune équipe trouvée
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        Commencez par créer une nouvelle équipe pour organiser vos projets
      </p>
      <button
        (click)="navigateToAddEquipe()"
        class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors shadow-sm"
      >
        <i class="fas fa-plus-circle mr-2"></i>
        Créer une équipe
      </button>
    </div>

    <!-- Teams Grid -->
    <div *ngIf="!loading && equipes.length > 0" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        *ngFor="let equipe of equipes"
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 hover:-translate-y-1"
      >
        <!-- Header de la carte -->
        <div class="p-6 border-b border-gray-100 dark:border-gray-700">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {{ equipe.name }}
              </h3>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <i class="fas fa-users mr-2"></i>
                <span>{{ equipe.members?.length || 0 }} membre(s)</span>
              </div>
            </div>

            <!-- Avatar de l'équipe -->
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
            </div>
          </div>

          <!-- Description -->
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-4 line-clamp-2">
            {{
              equipe.description && equipe.description.length > 100
                ? (equipe.description | slice : 0 : 100) + "..."
                : equipe.description || "Aucune description disponible"
            }}
          </p>
        </div>

        <!-- Actions -->
        <div class="p-6 space-y-4">
          <!-- Statistiques des tâches -->
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <i class="fas fa-tasks mr-2"></i>
              <span>{{ getTaskCount(equipe._id || '') }} tâches</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span class="text-gray-600 dark:text-gray-400">{{ getTaskStatus(equipe._id || '').percentage }}% complété</span>
            </div>
          </div>

          <!-- Barre de progression -->
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-green-500 h-2 rounded-full transition-all duration-500"
              [style.width.%]="getTaskStatus(equipe._id || '').percentage">
            </div>
          </div>

          <!-- Bouton principal - Gérer les tâches -->
          <button
            (click)="equipe._id && navigateToTasks(equipe._id)"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center group"
          >
            <i class="fas fa-tasks mr-2 group-hover:scale-110 transition-transform"></i>
            Gérer les Tâches
            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
          </button>

          <!-- Actions secondaires -->
          <div class="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
            <!-- Bouton Détails -->
            <button
              (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
              class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-sm font-medium transition-colors flex items-center"
            >
              <i class="fas fa-eye mr-2"></i>
              Voir détails
            </button>

            <!-- Actions rapides -->
            <div class="flex items-center space-x-2">
              <button
                (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all"
                title="Modifier l'équipe"
              >
                <i class="fas fa-edit"></i>
              </button>

              <button
                (click)="equipe._id && deleteEquipe(equipe._id)"
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all"
                title="Supprimer l'équipe"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
