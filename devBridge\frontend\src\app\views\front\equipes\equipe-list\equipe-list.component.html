<!-- Interface Simple pour Gestion d'Équipes -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
  <div class="max-w-6xl mx-auto">

    <!-- Header Simple -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gestion des Équipes</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1"><PERSON><PERSON>ez et gérez vos équipes de travail</p>
        </div>

        <!-- Bouton Créer Équipe -->
        <button
          (click)="navigateToAddEquipe()"
          class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
        >
          <i class="fas fa-plus mr-2"></i>
          <PERSON><PERSON><PERSON>
        </button>
      </div>
    </div>

    <!-- États de chargement -->
    <div *ngIf="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-4 text-gray-600 dark:text-gray-400">Chargement des équipes...</p>
    </div>

    <!-- Aucune équipe -->
    <div *ngIf="!loading && !error && equipes.length === 0" class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <i class="fas fa-users text-4xl"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Aucune équipe</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">Créez votre première équipe pour commencer</p>
      <button
        (click)="navigateToAddEquipe()"
        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
      >
        <i class="fas fa-plus mr-2"></i>
        Créer une équipe
      </button>
    </div>

    <!-- Liste des Équipes -->
    <div *ngIf="!loading && equipes.length > 0" class="space-y-4">
      <div
        *ngFor="let equipe of equipes"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center justify-between">
          <!-- Informations de l'équipe -->
          <div class="flex items-center space-x-4">
            <!-- Avatar -->
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <i class="fas fa-users text-blue-600 dark:text-blue-400 text-lg"></i>
            </div>

            <!-- Détails -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ equipe.name }}
              </h3>
              <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>
                  <i class="fas fa-users mr-1"></i>
                  {{ equipe.members?.length || 0 }} membres
                </span>
                <span>
                  <i class="fas fa-tasks mr-1"></i>
                  {{ getTaskCount(equipe._id || '') }} tâches
                </span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center space-x-3">
            <!-- 1. Bouton Gérer les Tâches (Principal) -->
            <button
              (click)="equipe._id && navigateToTasks(equipe._id)"
              class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
              title="Gérer les tâches de l'équipe"
            >
              <i class="fas fa-tasks mr-2"></i>
              Gérer Tâches
            </button>

            <!-- 2. Bouton Détails -->
            <button
              (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
              class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
              title="Voir les détails de l'équipe"
            >
              <i class="fas fa-eye mr-2"></i>
              Détails
            </button>

            <!-- 3. Bouton Modifier -->
            <button
              (click)="equipe._id && navigateToEditEquipe(equipe._id)"
              class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
              title="Modifier l'équipe"
            >
              <i class="fas fa-edit mr-2"></i>
              Modifier
            </button>

            <!-- 4. Bouton Supprimer -->
            <button
              (click)="equipe._id && deleteEquipe(equipe._id)"
              class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
              title="Supprimer l'équipe"
            >
              <i class="fas fa-trash mr-2"></i>
              Supprimer
            </button>
          </div>
        </div>

        <!-- Description (si disponible) -->
        <div *ngIf="equipe.description" class="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ equipe.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
