{"ast": null, "code": "import { __assign } from \"tslib\";\nexport function createOperation(starting, operation) {\n  var context = __assign({}, starting);\n  var setContext = function (next) {\n    if (typeof next === \"function\") {\n      context = __assign(__assign({}, context), next(context));\n    } else {\n      context = __assign(__assign({}, context), next);\n    }\n  };\n  var getContext = function () {\n    return __assign({}, context);\n  };\n  Object.defineProperty(operation, \"setContext\", {\n    enumerable: false,\n    value: setContext\n  });\n  Object.defineProperty(operation, \"getContext\", {\n    enumerable: false,\n    value: getContext\n  });\n  return operation;\n}\n//# sourceMappingURL=createOperation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}