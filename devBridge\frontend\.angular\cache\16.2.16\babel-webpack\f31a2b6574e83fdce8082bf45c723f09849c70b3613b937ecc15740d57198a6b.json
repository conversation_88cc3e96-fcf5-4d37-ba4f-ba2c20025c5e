{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Lone Schema definition\n *\n * A GraphQL document is only valid if it contains only one schema definition.\n */\nexport function LoneSchemaDefinitionRule(context) {\n  var _ref, _ref2, _oldSchema$astNode;\n  const oldSchema = context.getSchema();\n  const alreadyDefined = (_ref = (_ref2 = (_oldSchema$astNode = oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.astNode) !== null && _oldSchema$astNode !== void 0 ? _oldSchema$astNode : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getQueryType()) !== null && _ref2 !== void 0 ? _ref2 : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getMutationType()) !== null && _ref !== void 0 ? _ref : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getSubscriptionType();\n  let schemaDefinitionsCount = 0;\n  return {\n    SchemaDefinition(node) {\n      if (alreadyDefined) {\n        context.reportError(new GraphQLError('Cannot define a new schema within a schema extension.', {\n          nodes: node\n        }));\n        return;\n      }\n      if (schemaDefinitionsCount > 0) {\n        context.reportError(new GraphQLError('Must provide only one schema definition.', {\n          nodes: node\n        }));\n      }\n      ++schemaDefinitionsCount;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}