# @smithy/util-body-length-node

[![NPM version](https://img.shields.io/npm/v/@smithy/util-body-length-node/latest.svg)](https://www.npmjs.com/package/@smithy/util-body-length-node)
[![NPM downloads](https://img.shields.io/npm/dm/@smithy/util-body-length-node.svg)](https://www.npmjs.com/package/@smithy/util-body-length-node)

Determines the length of a request body in node.js

> An internal package

## Usage

You probably shouldn't, at least directly.
