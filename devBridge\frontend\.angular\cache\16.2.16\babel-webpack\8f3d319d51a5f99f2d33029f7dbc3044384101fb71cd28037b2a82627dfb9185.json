{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMonthOfInterval} function options.\n */\n\n/**\n * The {@link eachMonthOfInterval} function result type. It resolves the proper data type.\n */\n\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of months from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\nexport function eachMonthOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setDate(1);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setMonth(date.getMonth() + step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMonthOfInterval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}