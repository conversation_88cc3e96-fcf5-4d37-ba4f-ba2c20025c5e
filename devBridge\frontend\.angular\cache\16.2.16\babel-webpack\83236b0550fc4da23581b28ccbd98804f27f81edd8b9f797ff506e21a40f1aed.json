{"ast": null, "code": "/**\n * Produce the GraphQL query recommended for a full schema introspection.\n * Accepts optional IntrospectionOptions.\n */\nexport function getIntrospectionQuery(options) {\n  const optionsWithDefault = {\n    descriptions: true,\n    specifiedByUrl: false,\n    directiveIsRepeatable: false,\n    schemaDescription: false,\n    inputValueDeprecation: false,\n    ...options\n  };\n  const descriptions = optionsWithDefault.descriptions ? 'description' : '';\n  const specifiedByUrl = optionsWithDefault.specifiedByUrl ? 'specifiedByURL' : '';\n  const directiveIsRepeatable = optionsWithDefault.directiveIsRepeatable ? 'isRepeatable' : '';\n  const schemaDescription = optionsWithDefault.schemaDescription ? descriptions : '';\n  function inputDeprecation(str) {\n    return optionsWithDefault.inputValueDeprecation ? str : '';\n  }\n  return `\n    query IntrospectionQuery {\n      __schema {\n        ${schemaDescription}\n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${descriptions}\n          ${directiveIsRepeatable}\n          locations\n          args${inputDeprecation('(includeDeprecated: true)')} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${descriptions}\n      ${specifiedByUrl}\n      fields(includeDeprecated: true) {\n        name\n        ${descriptions}\n        args${inputDeprecation('(includeDeprecated: true)')} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${inputDeprecation('(includeDeprecated: true)')} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${descriptions}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${descriptions}\n      type { ...TypeRef }\n      defaultValue\n      ${inputDeprecation('isDeprecated')}\n      ${inputDeprecation('deprecationReason')}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n}", "map": {"version": 3, "names": ["getIntrospectionQuery", "options", "optionsWithDefault", "descriptions", "specifiedByUrl", "directiveIsRepeatable", "schemaDescription", "inputValueDeprecation", "inputDeprecation", "str"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/getIntrospectionQuery.mjs"], "sourcesContent": ["/**\n * Produce the GraphQL query recommended for a full schema introspection.\n * Accepts optional IntrospectionOptions.\n */\nexport function getIntrospectionQuery(options) {\n  const optionsWithDefault = {\n    descriptions: true,\n    specifiedByUrl: false,\n    directiveIsRepeatable: false,\n    schemaDescription: false,\n    inputValueDeprecation: false,\n    ...options,\n  };\n  const descriptions = optionsWithDefault.descriptions ? 'description' : '';\n  const specifiedByUrl = optionsWithDefault.specifiedByUrl\n    ? 'specifiedByURL'\n    : '';\n  const directiveIsRepeatable = optionsWithDefault.directiveIsRepeatable\n    ? 'isRepeatable'\n    : '';\n  const schemaDescription = optionsWithDefault.schemaDescription\n    ? descriptions\n    : '';\n\n  function inputDeprecation(str) {\n    return optionsWithDefault.inputValueDeprecation ? str : '';\n  }\n\n  return `\n    query IntrospectionQuery {\n      __schema {\n        ${schemaDescription}\n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${descriptions}\n          ${directiveIsRepeatable}\n          locations\n          args${inputDeprecation('(includeDeprecated: true)')} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${descriptions}\n      ${specifiedByUrl}\n      fields(includeDeprecated: true) {\n        name\n        ${descriptions}\n        args${inputDeprecation('(includeDeprecated: true)')} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${inputDeprecation('(includeDeprecated: true)')} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${descriptions}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${descriptions}\n      type { ...TypeRef }\n      defaultValue\n      ${inputDeprecation('isDeprecated')}\n      ${inputDeprecation('deprecationReason')}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,MAAMC,kBAAkB,GAAG;IACzBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,KAAK;IACrBC,qBAAqB,EAAE,KAAK;IAC5BC,iBAAiB,EAAE,KAAK;IACxBC,qBAAqB,EAAE,KAAK;IAC5B,GAAGN;EACL,CAAC;EACD,MAAME,YAAY,GAAGD,kBAAkB,CAACC,YAAY,GAAG,aAAa,GAAG,EAAE;EACzE,MAAMC,cAAc,GAAGF,kBAAkB,CAACE,cAAc,GACpD,gBAAgB,GAChB,EAAE;EACN,MAAMC,qBAAqB,GAAGH,kBAAkB,CAACG,qBAAqB,GAClE,cAAc,GACd,EAAE;EACN,MAAMC,iBAAiB,GAAGJ,kBAAkB,CAACI,iBAAiB,GAC1DH,YAAY,GACZ,EAAE;EAEN,SAASK,gBAAgBA,CAACC,GAAG,EAAE;IAC7B,OAAOP,kBAAkB,CAACK,qBAAqB,GAAGE,GAAG,GAAG,EAAE;EAC5D;EAEA,OAAQ;AACV;AACA;AACA,UAAUH,iBAAkB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYH,YAAa;AACzB,YAAYE,qBAAsB;AAClC;AACA,gBAAgBG,gBAAgB,CAAC,2BAA2B,CAAE;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQL,YAAa;AACrB,QAAQC,cAAe;AACvB;AACA;AACA,UAAUD,YAAa;AACvB,cAAcK,gBAAgB,CAAC,2BAA2B,CAAE;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBA,gBAAgB,CAAC,2BAA2B,CAAE;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUL,YAAa;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQA,YAAa;AACrB;AACA;AACA,QAAQK,gBAAgB,CAAC,cAAc,CAAE;AACzC,QAAQA,gBAAgB,CAAC,mBAAmB,CAAE;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}