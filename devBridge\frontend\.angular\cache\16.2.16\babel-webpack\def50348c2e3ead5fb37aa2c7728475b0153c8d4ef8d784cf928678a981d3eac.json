{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfYear} function options.\n */\n\n/**\n * @name lastDayOfYear\n * @category Year Helpers\n * @summary Return the last day of a year for the given date.\n *\n * @description\n * Return the last day of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a year\n *\n * @example\n * // The last day of a year for 2 September 2014 11:55:00:\n * const result = lastDayOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Dec 31 2014 00:00:00\n */\nexport function lastDayOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  const year = date_.getFullYear();\n  date_.setFullYear(year + 1, 0, 0);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}