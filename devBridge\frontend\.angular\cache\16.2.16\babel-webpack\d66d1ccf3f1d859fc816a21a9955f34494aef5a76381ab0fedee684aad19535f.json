{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\nimport { isSameMinute } from \"./isSameMinute.js\";\n\n/**\n * @name isThisMinute\n * @category Minute Helpers\n * @summary Is the given date in the same minute as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same minute as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this minute\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:00 in this minute?\n * const result = isThisMinute(new Date(2014, 8, 25, 18, 30))\n * //=> true\n */\n\nexport function isThisMinute(date) {\n  return isSameMinute(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisMinute;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}