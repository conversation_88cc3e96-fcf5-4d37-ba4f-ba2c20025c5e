<div class="ai-assistant-container">
  <!-- Header -->
  <div class="ai-header">
    <div class="ai-title">
      <mat-icon>psychology</mat-icon>
      <h3>Assistant IA</h3>
    </div>
    <div class="ai-status" [class.enabled]="isAIEnabled()">
      <mat-icon>{{ isAIEnabled() ? 'smart_toy' : 'smart_toy_outlined' }}</mat-icon>
      <span>{{ isAIEnabled() ? 'Activé' : 'Désactivé' }}</span>
    </div>
  </div>

  <!-- Informations sur la tâche -->
  <div class="task-info" *ngIf="task">
    <h4>{{ task.title }}</h4>
    <div class="task-meta">
      <span class="meta-item">
        <mat-icon>flag</mat-icon>
        {{ task.priority }}
      </span>
      <span class="meta-item">
        <mat-icon>category</mat-icon>
        {{ task.category || 'Non définie' }}
      </span>
      <span class="meta-item" *ngIf="task.estimatedHours">
        <mat-icon>schedule</mat-icon>
        {{ task.estimatedHours }}h
      </span>
    </div>
  </div>

  <!-- Actions IA -->
  <div class="ai-actions">
    <h4>Actions disponibles</h4>
    
    <div class="action-grid">
      <!-- Analyser la tâche -->
      <button mat-raised-button 
              color="primary"
              [disabled]="loading.analyze"
              (click)="analyzeTask()">
        <mat-icon>analytics</mat-icon>
        <span>Analyser</span>
        <mat-spinner *ngIf="loading.analyze" diameter="20"></mat-spinner>
      </button>

      <!-- Générer des sous-tâches -->
      <button mat-raised-button 
              color="accent"
              [disabled]="loading.subtasks"
              (click)="generateSubtasks()">
        <mat-icon>list</mat-icon>
        <span>Sous-tâches</span>
        <mat-spinner *ngIf="loading.subtasks" diameter="20"></mat-spinner>
      </button>

      <!-- Estimer l'effort -->
      <button mat-raised-button 
              [disabled]="loading.estimate"
              (click)="estimateEffort()">
        <mat-icon>timer</mat-icon>
        <span>Estimer</span>
        <mat-spinner *ngIf="loading.estimate" diameter="20"></mat-spinner>
      </button>

      <!-- Générer description -->
      <button mat-raised-button 
              [disabled]="loading.description"
              (click)="generateDescription()">
        <mat-icon>description</mat-icon>
        <span>Description</span>
        <mat-spinner *ngIf="loading.description" diameter="20"></mat-spinner>
      </button>
    </div>
  </div>

  <!-- Suggestions IA -->
  <div class="ai-suggestions" *ngIf="suggestions.length > 0">
    <div class="suggestions-header">
      <h4>Suggestions IA</h4>
      <button mat-icon-button (click)="toggleSuggestions()">
        <mat-icon>{{ showSuggestions ? 'expand_less' : 'expand_more' }}</mat-icon>
      </button>
    </div>

    <div class="suggestions-list" *ngIf="showSuggestions">
      <div class="suggestion-item" 
           *ngFor="let suggestion of suggestions; let i = index"
           [class.applied]="suggestion.applied">
        
        <div class="suggestion-header">
          <div class="suggestion-icon">
            <mat-icon [style.color]="getConfidenceColor(suggestion.confidence)">
              {{ getSuggestionIcon(suggestion.type) }}
            </mat-icon>
          </div>
          
          <div class="suggestion-content">
            <h5>{{ suggestion.title }}</h5>
            <p>{{ suggestion.description }}</p>
          </div>
          
          <div class="suggestion-actions">
            <div class="confidence-badge" 
                 [style.background-color]="getConfidenceColor(suggestion.confidence)">
              {{ (suggestion.confidence * 100).toFixed(0) }}%
            </div>
            
            <button mat-icon-button 
                    *ngIf="canApplySuggestion(suggestion)"
                    [disabled]="loading.applySuggestion"
                    (click)="applySuggestion(i)"
                    matTooltip="Appliquer la suggestion">
              <mat-icon>check</mat-icon>
            </button>
          </div>
        </div>

        <div class="suggestion-details" *ngIf="suggestion.details">
          <mat-expansion-panel>
            <mat-expansion-panel-header>
              <mat-panel-title>Détails</mat-panel-title>
            </mat-expansion-panel-header>
            <div class="details-content">
              <p>{{ suggestion.details }}</p>
              <div class="suggestion-metadata">
                <span class="meta-item">
                  <strong>Type:</strong> {{ suggestion.type }}
                </span>
                <span class="meta-item">
                  <strong>Confiance:</strong> {{ getConfidenceText(suggestion.confidence) }}
                </span>
                <span class="meta-item" *ngIf="suggestion.impact">
                  <strong>Impact:</strong> {{ suggestion.impact }}
                </span>
              </div>
            </div>
          </mat-expansion-panel>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques IA -->
  <div class="ai-stats" *ngIf="isAIEnabled()">
    <h4>Statistiques IA</h4>
    
    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-label">Dernière analyse</span>
        <span class="stat-value">{{ getLastAnalysisTime() }}</span>
      </div>
      
      <div class="stat-item">
        <span class="stat-label">Suggestions</span>
        <span class="stat-value">{{ suggestions.length }}</span>
      </div>
      
      <div class="stat-item">
        <span class="stat-label">Non appliquées</span>
        <span class="stat-value">{{ getUnappliedSuggestionsCount() }}</span>
      </div>
      
      <div class="stat-item">
        <span class="stat-label">Confiance moyenne</span>
        <span class="stat-value">{{ (getAverageConfidence() * 100).toFixed(0) }}%</span>
      </div>
    </div>

    <!-- Recommandation de nouvelle analyse -->
    <div class="reanalysis-recommendation" *ngIf="shouldReanalyze()">
      <mat-icon color="warn">warning</mat-icon>
      <span>Une nouvelle analyse est recommandée</span>
      <button mat-button color="primary" (click)="analyzeTask()">
        Analyser maintenant
      </button>
    </div>
  </div>

  <!-- État vide -->
  <div class="empty-state" *ngIf="!isAIEnabled()">
    <mat-icon>smart_toy_outlined</mat-icon>
    <h4>Assistant IA désactivé</h4>
    <p>L'assistant IA n'est pas activé pour cette tâche.</p>
  </div>

  <div class="empty-state" *ngIf="isAIEnabled() && suggestions.length === 0">
    <mat-icon>lightbulb_outline</mat-icon>
    <h4>Aucune suggestion</h4>
    <p>Lancez une analyse pour obtenir des suggestions IA.</p>
    <button mat-raised-button color="primary" (click)="analyzeTask()">
      <mat-icon>analytics</mat-icon>
      Analyser la tâche
    </button>
  </div>
</div>
