{"ast": null, "code": "import { invariant } from '../../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType, isInputObjectType } from '../../../type/definition.mjs';\n\n/**\n * No deprecated\n *\n * A GraphQL document is only valid if all selected fields and all used enum values have not been\n * deprecated.\n *\n * Note: This rule is optional and is not part of the Validation section of the GraphQL\n * Specification. The main purpose of this rule is detection of deprecated usages and not\n * necessarily to forbid their use when querying a service.\n */\nexport function NoDeprecatedCustomRule(context) {\n  return {\n    Field(node) {\n      const fieldDef = context.getFieldDef();\n      const deprecationReason = fieldDef === null || fieldDef === void 0 ? void 0 : fieldDef.deprecationReason;\n      if (fieldDef && deprecationReason != null) {\n        const parentType = context.getParentType();\n        parentType != null || invariant(false);\n        context.reportError(new GraphQLError(`The field ${parentType.name}.${fieldDef.name} is deprecated. ${deprecationReason}`, {\n          nodes: node\n        }));\n      }\n    },\n    Argument(node) {\n      const argDef = context.getArgument();\n      const deprecationReason = argDef === null || argDef === void 0 ? void 0 : argDef.deprecationReason;\n      if (argDef && deprecationReason != null) {\n        const directiveDef = context.getDirective();\n        if (directiveDef != null) {\n          context.reportError(new GraphQLError(`Directive \"@${directiveDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        } else {\n          const parentType = context.getParentType();\n          const fieldDef = context.getFieldDef();\n          parentType != null && fieldDef != null || invariant(false);\n          context.reportError(new GraphQLError(`Field \"${parentType.name}.${fieldDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    ObjectField(node) {\n      const inputObjectDef = getNamedType(context.getParentInputType());\n      if (isInputObjectType(inputObjectDef)) {\n        const inputFieldDef = inputObjectDef.getFields()[node.name.value];\n        const deprecationReason = inputFieldDef === null || inputFieldDef === void 0 ? void 0 : inputFieldDef.deprecationReason;\n        if (deprecationReason != null) {\n          context.reportError(new GraphQLError(`The input field ${inputObjectDef.name}.${inputFieldDef.name} is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    EnumValue(node) {\n      const enumValueDef = context.getEnumValue();\n      const deprecationReason = enumValueDef === null || enumValueDef === void 0 ? void 0 : enumValueDef.deprecationReason;\n      if (enumValueDef && deprecationReason != null) {\n        const enumTypeDef = getNamedType(context.getInputType());\n        enumTypeDef != null || invariant(false);\n        context.reportError(new GraphQLError(`The enum value \"${enumTypeDef.name}.${enumValueDef.name}\" is deprecated. ${deprecationReason}`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["invariant", "GraphQLError", "getNamedType", "isInputObjectType", "NoDeprecatedCustomRule", "context", "Field", "node", "fieldDef", "getFieldDef", "deprecationReason", "parentType", "getParentType", "reportError", "name", "nodes", "Argument", "argDef", "getArgument", "directiveDef", "getDirective", "ObjectField", "inputObjectDef", "getParentInputType", "inputFieldDef", "getFields", "value", "EnumValue", "enumValueDef", "getEnumValue", "enumTypeDef", "getInputType"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/custom/NoDeprecatedCustomRule.mjs"], "sourcesContent": ["import { invariant } from '../../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType, isInputObjectType } from '../../../type/definition.mjs';\n\n/**\n * No deprecated\n *\n * A GraphQL document is only valid if all selected fields and all used enum values have not been\n * deprecated.\n *\n * Note: This rule is optional and is not part of the Validation section of the GraphQL\n * Specification. The main purpose of this rule is detection of deprecated usages and not\n * necessarily to forbid their use when querying a service.\n */\nexport function NoDeprecatedCustomRule(context) {\n  return {\n    Field(node) {\n      const fieldDef = context.getFieldDef();\n      const deprecationReason =\n        fieldDef === null || fieldDef === void 0\n          ? void 0\n          : fieldDef.deprecationReason;\n\n      if (fieldDef && deprecationReason != null) {\n        const parentType = context.getParentType();\n        parentType != null || invariant(false);\n        context.reportError(\n          new GraphQLError(\n            `The field ${parentType.name}.${fieldDef.name} is deprecated. ${deprecationReason}`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    Argument(node) {\n      const argDef = context.getArgument();\n      const deprecationReason =\n        argDef === null || argDef === void 0\n          ? void 0\n          : argDef.deprecationReason;\n\n      if (argDef && deprecationReason != null) {\n        const directiveDef = context.getDirective();\n\n        if (directiveDef != null) {\n          context.reportError(\n            new GraphQLError(\n              `Directive \"@${directiveDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        } else {\n          const parentType = context.getParentType();\n          const fieldDef = context.getFieldDef();\n          (parentType != null && fieldDef != null) || invariant(false);\n          context.reportError(\n            new GraphQLError(\n              `Field \"${parentType.name}.${fieldDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n    },\n\n    ObjectField(node) {\n      const inputObjectDef = getNamedType(context.getParentInputType());\n\n      if (isInputObjectType(inputObjectDef)) {\n        const inputFieldDef = inputObjectDef.getFields()[node.name.value];\n        const deprecationReason =\n          inputFieldDef === null || inputFieldDef === void 0\n            ? void 0\n            : inputFieldDef.deprecationReason;\n\n        if (deprecationReason != null) {\n          context.reportError(\n            new GraphQLError(\n              `The input field ${inputObjectDef.name}.${inputFieldDef.name} is deprecated. ${deprecationReason}`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n    },\n\n    EnumValue(node) {\n      const enumValueDef = context.getEnumValue();\n      const deprecationReason =\n        enumValueDef === null || enumValueDef === void 0\n          ? void 0\n          : enumValueDef.deprecationReason;\n\n      if (enumValueDef && deprecationReason != null) {\n        const enumTypeDef = getNamedType(context.getInputType());\n        enumTypeDef != null || invariant(false);\n        context.reportError(\n          new GraphQLError(\n            `The enum value \"${enumTypeDef.name}.${enumValueDef.name}\" is deprecated. ${deprecationReason}`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,8BAA8B;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EAC9C,OAAO;IACLC,KAAKA,CAACC,IAAI,EAAE;MACV,MAAMC,QAAQ,GAAGH,OAAO,CAACI,WAAW,CAAC,CAAC;MACtC,MAAMC,iBAAiB,GACrBF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GACpC,KAAK,CAAC,GACNA,QAAQ,CAACE,iBAAiB;MAEhC,IAAIF,QAAQ,IAAIE,iBAAiB,IAAI,IAAI,EAAE;QACzC,MAAMC,UAAU,GAAGN,OAAO,CAACO,aAAa,CAAC,CAAC;QAC1CD,UAAU,IAAI,IAAI,IAAIX,SAAS,CAAC,KAAK,CAAC;QACtCK,OAAO,CAACQ,WAAW,CACjB,IAAIZ,YAAY,CACb,aAAYU,UAAU,CAACG,IAAK,IAAGN,QAAQ,CAACM,IAAK,mBAAkBJ,iBAAkB,EAAC,EACnF;UACEK,KAAK,EAAER;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDS,QAAQA,CAACT,IAAI,EAAE;MACb,MAAMU,MAAM,GAAGZ,OAAO,CAACa,WAAW,CAAC,CAAC;MACpC,MAAMR,iBAAiB,GACrBO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAChC,KAAK,CAAC,GACNA,MAAM,CAACP,iBAAiB;MAE9B,IAAIO,MAAM,IAAIP,iBAAiB,IAAI,IAAI,EAAE;QACvC,MAAMS,YAAY,GAAGd,OAAO,CAACe,YAAY,CAAC,CAAC;QAE3C,IAAID,YAAY,IAAI,IAAI,EAAE;UACxBd,OAAO,CAACQ,WAAW,CACjB,IAAIZ,YAAY,CACb,eAAckB,YAAY,CAACL,IAAK,eAAcG,MAAM,CAACH,IAAK,oBAAmBJ,iBAAkB,EAAC,EACjG;YACEK,KAAK,EAAER;UACT,CACF,CACF,CAAC;QACH,CAAC,MAAM;UACL,MAAMI,UAAU,GAAGN,OAAO,CAACO,aAAa,CAAC,CAAC;UAC1C,MAAMJ,QAAQ,GAAGH,OAAO,CAACI,WAAW,CAAC,CAAC;UACrCE,UAAU,IAAI,IAAI,IAAIH,QAAQ,IAAI,IAAI,IAAKR,SAAS,CAAC,KAAK,CAAC;UAC5DK,OAAO,CAACQ,WAAW,CACjB,IAAIZ,YAAY,CACb,UAASU,UAAU,CAACG,IAAK,IAAGN,QAAQ,CAACM,IAAK,eAAcG,MAAM,CAACH,IAAK,oBAAmBJ,iBAAkB,EAAC,EAC3G;YACEK,KAAK,EAAER;UACT,CACF,CACF,CAAC;QACH;MACF;IACF,CAAC;IAEDc,WAAWA,CAACd,IAAI,EAAE;MAChB,MAAMe,cAAc,GAAGpB,YAAY,CAACG,OAAO,CAACkB,kBAAkB,CAAC,CAAC,CAAC;MAEjE,IAAIpB,iBAAiB,CAACmB,cAAc,CAAC,EAAE;QACrC,MAAME,aAAa,GAAGF,cAAc,CAACG,SAAS,CAAC,CAAC,CAAClB,IAAI,CAACO,IAAI,CAACY,KAAK,CAAC;QACjE,MAAMhB,iBAAiB,GACrBc,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAC9C,KAAK,CAAC,GACNA,aAAa,CAACd,iBAAiB;QAErC,IAAIA,iBAAiB,IAAI,IAAI,EAAE;UAC7BL,OAAO,CAACQ,WAAW,CACjB,IAAIZ,YAAY,CACb,mBAAkBqB,cAAc,CAACR,IAAK,IAAGU,aAAa,CAACV,IAAK,mBAAkBJ,iBAAkB,EAAC,EAClG;YACEK,KAAK,EAAER;UACT,CACF,CACF,CAAC;QACH;MACF;IACF,CAAC;IAEDoB,SAASA,CAACpB,IAAI,EAAE;MACd,MAAMqB,YAAY,GAAGvB,OAAO,CAACwB,YAAY,CAAC,CAAC;MAC3C,MAAMnB,iBAAiB,GACrBkB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAC5C,KAAK,CAAC,GACNA,YAAY,CAAClB,iBAAiB;MAEpC,IAAIkB,YAAY,IAAIlB,iBAAiB,IAAI,IAAI,EAAE;QAC7C,MAAMoB,WAAW,GAAG5B,YAAY,CAACG,OAAO,CAAC0B,YAAY,CAAC,CAAC,CAAC;QACxDD,WAAW,IAAI,IAAI,IAAI9B,SAAS,CAAC,KAAK,CAAC;QACvCK,OAAO,CAACQ,WAAW,CACjB,IAAIZ,YAAY,CACb,mBAAkB6B,WAAW,CAAChB,IAAK,IAAGc,YAAY,CAACd,IAAK,oBAAmBJ,iBAAkB,EAAC,EAC/F;UACEK,KAAK,EAAER;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}