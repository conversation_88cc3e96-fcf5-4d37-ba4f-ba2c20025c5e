{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { dayPeriodEnumToHours } from \"../utils.js\";\nexport class AMPMMidnightParser extends Parser {\n  priority = 80;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n      case \"bbb\":\n        return match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"bbbbb\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"bbbb\":\n      default:\n        return match.dayPeriod(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n    }\n  }\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"a\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}