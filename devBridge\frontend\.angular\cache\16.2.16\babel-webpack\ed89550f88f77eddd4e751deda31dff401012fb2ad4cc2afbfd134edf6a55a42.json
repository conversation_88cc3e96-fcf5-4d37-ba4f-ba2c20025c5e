{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n  var iterator = {\n    next: function () {\n      return reader.read();\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}\n//# sourceMappingURL=reader.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}