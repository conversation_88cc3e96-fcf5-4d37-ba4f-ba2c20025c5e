const { Team } = require('../models/Team');
const User = require('../models/User');
const JoinRequest = require('../models/JoinRequest');
const TeamHistory = require('../models/TeamHistory');
const TeamFavorite = require('../models/TeamFavorite');
const { logger } = require('../utils/logger');

// ==================== DEMANDES D'ADHÉSION ====================

// Envoyer une demande d'adhésion
exports.sendJoinRequest = async (req, res) => {
  try {
    const { message, requestedRole = 'member' } = req.body;
    const teamId = req.params.id;
    const userId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.allowJoinRequests) {
      return res.status(403).json({ 
        message: "Cette équipe n'accepte pas les demandes d'adhésion" 
      });
    }

    if (team.isMember(userId)) {
      return res.status(400).json({ 
        message: "Vous êtes déjà membre de cette équipe" 
      });
    }

    if (team.isFullTeam) {
      return res.status(400).json({ 
        message: "L'équipe a atteint sa capacité maximale" 
      });
    }

    // Vérifier s'il y a déjà une demande en attente
    const existingRequest = await JoinRequest.hasExistingRequest(teamId, userId);
    if (existingRequest) {
      return res.status(400).json({ 
        message: "Vous avez déjà une demande en attente pour cette équipe" 
      });
    }

    // Créer la demande
    const joinRequest = new JoinRequest({
      team: teamId,
      user: userId,
      message: message || '',
      requestedRole: requestedRole,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'web'
      }
    });

    await joinRequest.save();
    await joinRequest.populate([
      { path: 'team', select: 'name description' },
      { path: 'user', select: 'fullName email profileImage' }
    ]);

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId, 
      userId, 
      'join_request_sent', 
      `${req.user.fullName} a demandé à rejoindre l'équipe`,
      { 
        affectedUser: userId,
        newData: { requestedRole, message },
        category: 'request'
      }
    );

    logger.info(`Demande d'adhésion envoyée pour l'équipe ${team.name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Demande d'adhésion envoyée avec succès",
      joinRequest
    });
  } catch (error) {
    logger.error('Erreur envoi demande adhésion:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'envoi de la demande", 
      error: error.message 
    });
  }
};

// Obtenir les demandes d'adhésion d'une équipe
exports.getJoinRequests = async (req, res) => {
  try {
    const teamId = req.params.id;
    const { status = 'pending' } = req.query;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.hasModeratorRights(req.user.id)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour voir les demandes" 
      });
    }

    const requests = await JoinRequest.find({
      team: teamId,
      ...(status !== 'all' && { status })
    })
    .populate('user', 'fullName email profileImage role')
    .sort({ createdAt: -1 });

    res.json({
      requests,
      count: requests.length
    });
  } catch (error) {
    logger.error('Erreur récupération demandes:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des demandes", 
      error: error.message 
    });
  }
};

// Approuver une demande d'adhésion
exports.approveJoinRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { message = '', assignedRole } = req.body;
    const adminId = req.user.id;

    const joinRequest = await JoinRequest.findById(requestId)
      .populate('team')
      .populate('user');

    if (!joinRequest) {
      return res.status(404).json({ message: "Demande non trouvée" });
    }

    const team = joinRequest.team;

    // Vérifier les permissions
    if (!team.hasModeratorRights(adminId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour approuver cette demande" 
      });
    }

    if (joinRequest.status !== 'pending') {
      return res.status(400).json({ 
        message: "Cette demande a déjà été traitée" 
      });
    }

    if (team.isFullTeam) {
      return res.status(400).json({ 
        message: "L'équipe a atteint sa capacité maximale" 
      });
    }

    // Approuver la demande
    await joinRequest.approve(adminId, message);

    // Ajouter l'utilisateur à l'équipe
    const roleToAssign = assignedRole || joinRequest.requestedRole;
    const userId = joinRequest.user._id;

    if (roleToAssign === 'moderator') {
      team.addMember(userId);
      team.addModerator(userId);
    } else if (roleToAssign === 'co-admin') {
      team.addMember(userId);
      team.addCoAdmin(userId);
    } else {
      team.addMember(userId);
    }

    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      team._id, 
      adminId, 
      'join_request_approved', 
      `Demande de ${joinRequest.user.fullName} approuvée`,
      { 
        affectedUser: userId,
        newData: { role: roleToAssign },
        category: 'request'
      }
    );

    logger.info(`Demande d'adhésion approuvée pour l'équipe ${team.name}: ${joinRequest.user.fullName}`);

    res.json({
      message: "Demande approuvée avec succès",
      joinRequest,
      assignedRole: roleToAssign
    });
  } catch (error) {
    logger.error('Erreur approbation demande:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'approbation de la demande", 
      error: error.message 
    });
  }
};

// Rejeter une demande d'adhésion
exports.rejectJoinRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { message = '' } = req.body;
    const adminId = req.user.id;

    const joinRequest = await JoinRequest.findById(requestId)
      .populate('team')
      .populate('user');

    if (!joinRequest) {
      return res.status(404).json({ message: "Demande non trouvée" });
    }

    const team = joinRequest.team;

    // Vérifier les permissions
    if (!team.hasModeratorRights(adminId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour rejeter cette demande" 
      });
    }

    if (joinRequest.status !== 'pending') {
      return res.status(400).json({ 
        message: "Cette demande a déjà été traitée" 
      });
    }

    // Rejeter la demande
    await joinRequest.reject(adminId, message);

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      team._id, 
      adminId, 
      'join_request_rejected', 
      `Demande de ${joinRequest.user.fullName} rejetée`,
      { 
        affectedUser: joinRequest.user._id,
        newData: { reason: message },
        category: 'request'
      }
    );

    logger.info(`Demande d'adhésion rejetée pour l'équipe ${team.name}: ${joinRequest.user.fullName}`);

    res.json({
      message: "Demande rejetée",
      joinRequest
    });
  } catch (error) {
    logger.error('Erreur rejet demande:', error);
    res.status(500).json({ 
      message: "Erreur lors du rejet de la demande", 
      error: error.message 
    });
  }
};

// Annuler une demande d'adhésion
exports.cancelJoinRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const userId = req.user.id;

    const joinRequest = await JoinRequest.findById(requestId);
    if (!joinRequest) {
      return res.status(404).json({ message: "Demande non trouvée" });
    }

    // Vérifier que c'est bien l'utilisateur qui a fait la demande
    if (joinRequest.user.toString() !== userId) {
      return res.status(403).json({ 
        message: "Vous ne pouvez annuler que vos propres demandes" 
      });
    }

    if (joinRequest.status !== 'pending') {
      return res.status(400).json({ 
        message: "Cette demande ne peut plus être annulée" 
      });
    }

    await joinRequest.cancel();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      joinRequest.team, 
      userId, 
      'join_request_cancelled', 
      `${req.user.fullName} a annulé sa demande d'adhésion`,
      { 
        affectedUser: userId,
        category: 'request'
      }
    );

    logger.info(`Demande d'adhésion annulée par ${req.user.fullName}`);

    res.json({
      message: "Demande annulée avec succès"
    });
  } catch (error) {
    logger.error('Erreur annulation demande:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'annulation de la demande", 
      error: error.message 
    });
  }
};

// ==================== FAVORIS ====================

// Ajouter aux favoris
exports.addToFavorites = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { category, notes, tags, priority } = req.body;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier si déjà en favoris
    const existingFavorite = await TeamFavorite.isFavorite(userId, teamId);
    if (existingFavorite) {
      return res.status(400).json({ 
        message: "Cette équipe est déjà dans vos favoris" 
      });
    }

    const favorite = await TeamFavorite.addToFavorites(userId, teamId, {
      category,
      notes,
      tags,
      priority,
      addedFrom: 'team_page'
    });

    logger.info(`Équipe ${team.name} ajoutée aux favoris par ${req.user.fullName}`);

    res.status(201).json({
      message: "Équipe ajoutée aux favoris",
      favorite
    });
  } catch (error) {
    logger.error('Erreur ajout favoris:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'ajout aux favoris", 
      error: error.message 
    });
  }
};

// Retirer des favoris
exports.removeFromFavorites = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;

    const removed = await TeamFavorite.removeFromFavorites(userId, teamId);
    if (!removed) {
      return res.status(404).json({ 
        message: "Cette équipe n'est pas dans vos favoris" 
      });
    }

    logger.info(`Équipe retirée des favoris par ${req.user.fullName}`);

    res.json({
      message: "Équipe retirée des favoris"
    });
  } catch (error) {
    logger.error('Erreur suppression favoris:', error);
    res.status(500).json({ 
      message: "Erreur lors de la suppression des favoris", 
      error: error.message 
    });
  }
};

// Obtenir les favoris de l'utilisateur
exports.getUserFavorites = async (req, res) => {
  try {
    const userId = req.user.id;
    const { category, tags, sortBy, limit } = req.query;

    const favorites = await TeamFavorite.getUserFavorites(userId, {
      category,
      tags: tags ? tags.split(',') : undefined,
      sortBy,
      limit: limit ? parseInt(limit) : undefined
    });

    res.json({
      favorites,
      count: favorites.length
    });
  } catch (error) {
    logger.error('Erreur récupération favoris:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des favoris", 
      error: error.message 
    });
  }
};

module.exports = {
  sendJoinRequest: exports.sendJoinRequest,
  getJoinRequests: exports.getJoinRequests,
  approveJoinRequest: exports.approveJoinRequest,
  rejectJoinRequest: exports.rejectJoinRequest,
  cancelJoinRequest: exports.cancelJoinRequest,
  addToFavorites: exports.addToFavorites,
  removeFromFavorites: exports.removeFromFavorites,
  getUserFavorites: exports.getUserFavorites
};
