{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nexport class Source {\n  constructor(body, name = 'GraphQL request', locationOffset = {\n    line: 1,\n    column: 1\n  }) {\n    typeof body === 'string' || devAssert(false, `Body must be a string. Received: ${inspect(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 || devAssert(false, 'line in locationOffset is 1-indexed and must be positive.');\n    this.locationOffset.column > 0 || devAssert(false, 'column in locationOffset is 1-indexed and must be positive.');\n  }\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nexport function isSource(source) {\n  return instanceOf(source, Source);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}