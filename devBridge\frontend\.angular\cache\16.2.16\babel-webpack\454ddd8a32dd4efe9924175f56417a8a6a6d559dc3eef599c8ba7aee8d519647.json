{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { doTypesOverlap } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Possible fragment spread\n *\n * A fragment spread is only valid if the type condition could ever possibly\n * be true: if there is a non-empty intersection of the possible parent types,\n * and possible types which pass the type condition.\n */\nexport function PossibleFragmentSpreadsRule(context) {\n  return {\n    InlineFragment(node) {\n      const fragType = context.getType();\n      const parentType = context.getParentType();\n      if (isCompositeType(fragType) && isCompositeType(parentType) && !doTypesOverlap(context.getSchema(), fragType, parentType)) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(new GraphQLError(`Fragment cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`, {\n          nodes: node\n        }));\n      }\n    },\n    FragmentSpread(node) {\n      const fragName = node.name.value;\n      const fragType = getFragmentType(context, fragName);\n      const parentType = context.getParentType();\n      if (fragType && parentType && !doTypesOverlap(context.getSchema(), fragType, parentType)) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(new GraphQLError(`Fragment \"${fragName}\" cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nfunction getFragmentType(context, name) {\n  const frag = context.getFragment(name);\n  if (frag) {\n    const type = typeFromAST(context.getSchema(), frag.typeCondition);\n    if (isCompositeType(type)) {\n      return type;\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}