{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isTypeDefinitionNode, isTypeExtensionNode } from '../../language/predicates.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Unique directive names per location\n *\n * A GraphQL document is only valid if all non-repeatable directives at\n * a given location are uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Unique-Per-Location\n */\nexport function UniqueDirectivesPerLocationRule(context) {\n  const uniqueDirectiveMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    uniqueDirectiveMap[directive.name] = !directive.isRepeatable;\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      uniqueDirectiveMap[def.name.value] = !def.repeatable;\n    }\n  }\n  const schemaDirectives = Object.create(null);\n  const typeDirectivesMap = Object.create(null);\n  return {\n    // Many different AST nodes may contain directives. Rather than listing\n    // them all, just listen for entering any node, and check to see if it\n    // defines any directives.\n    enter(node) {\n      if (!('directives' in node) || !node.directives) {\n        return;\n      }\n      let seenDirectives;\n      if (node.kind === Kind.SCHEMA_DEFINITION || node.kind === Kind.SCHEMA_EXTENSION) {\n        seenDirectives = schemaDirectives;\n      } else if (isTypeDefinitionNode(node) || isTypeExtensionNode(node)) {\n        const typeName = node.name.value;\n        seenDirectives = typeDirectivesMap[typeName];\n        if (seenDirectives === undefined) {\n          typeDirectivesMap[typeName] = seenDirectives = Object.create(null);\n        }\n      } else {\n        seenDirectives = Object.create(null);\n      }\n      for (const directive of node.directives) {\n        const directiveName = directive.name.value;\n        if (uniqueDirectiveMap[directiveName]) {\n          if (seenDirectives[directiveName]) {\n            context.reportError(new GraphQLError(`The directive \"@${directiveName}\" can only be used once at this location.`, {\n              nodes: [seenDirectives[directiveName], directive]\n            }));\n          } else {\n            seenDirectives[directiveName] = directive;\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "Kind", "isTypeDefinitionNode", "isTypeExtensionNode", "specifiedDirectives", "UniqueDirectivesPerLocationRule", "context", "uniqueDirectiveMap", "Object", "create", "schema", "getSchema", "definedDirectives", "getDirectives", "directive", "name", "isRepeatable", "astDefinitions", "getDocument", "definitions", "def", "kind", "DIRECTIVE_DEFINITION", "value", "repeatable", "schemaDirectives", "typeDirectivesMap", "enter", "node", "directives", "seenDirectives", "SCHEMA_DEFINITION", "SCHEMA_EXTENSION", "typeName", "undefined", "directiveName", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueDirectivesPerLocationRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport {\n  isTypeDefinitionNode,\n  isTypeExtensionNode,\n} from '../../language/predicates.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Unique directive names per location\n *\n * A GraphQL document is only valid if all non-repeatable directives at\n * a given location are uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Unique-Per-Location\n */\nexport function UniqueDirectivesPerLocationRule(context) {\n  const uniqueDirectiveMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema\n    ? schema.getDirectives()\n    : specifiedDirectives;\n\n  for (const directive of definedDirectives) {\n    uniqueDirectiveMap[directive.name] = !directive.isRepeatable;\n  }\n\n  const astDefinitions = context.getDocument().definitions;\n\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      uniqueDirectiveMap[def.name.value] = !def.repeatable;\n    }\n  }\n\n  const schemaDirectives = Object.create(null);\n  const typeDirectivesMap = Object.create(null);\n  return {\n    // Many different AST nodes may contain directives. Rather than listing\n    // them all, just listen for entering any node, and check to see if it\n    // defines any directives.\n    enter(node) {\n      if (!('directives' in node) || !node.directives) {\n        return;\n      }\n\n      let seenDirectives;\n\n      if (\n        node.kind === Kind.SCHEMA_DEFINITION ||\n        node.kind === Kind.SCHEMA_EXTENSION\n      ) {\n        seenDirectives = schemaDirectives;\n      } else if (isTypeDefinitionNode(node) || isTypeExtensionNode(node)) {\n        const typeName = node.name.value;\n        seenDirectives = typeDirectivesMap[typeName];\n\n        if (seenDirectives === undefined) {\n          typeDirectivesMap[typeName] = seenDirectives = Object.create(null);\n        }\n      } else {\n        seenDirectives = Object.create(null);\n      }\n\n      for (const directive of node.directives) {\n        const directiveName = directive.name.value;\n\n        if (uniqueDirectiveMap[directiveName]) {\n          if (seenDirectives[directiveName]) {\n            context.reportError(\n              new GraphQLError(\n                `The directive \"@${directiveName}\" can only be used once at this location.`,\n                {\n                  nodes: [seenDirectives[directiveName], directive],\n                },\n              ),\n            );\n          } else {\n            seenDirectives[directiveName] = directive;\n          }\n        }\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SACEC,oBAAoB,EACpBC,mBAAmB,QACd,+BAA+B;AACtC,SAASC,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAACC,OAAO,EAAE;EACvD,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9C,MAAMC,MAAM,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,MAAM,GAC5BA,MAAM,CAACG,aAAa,CAAC,CAAC,GACtBT,mBAAmB;EAEvB,KAAK,MAAMU,SAAS,IAAIF,iBAAiB,EAAE;IACzCL,kBAAkB,CAACO,SAAS,CAACC,IAAI,CAAC,GAAG,CAACD,SAAS,CAACE,YAAY;EAC9D;EAEA,MAAMC,cAAc,GAAGX,OAAO,CAACY,WAAW,CAAC,CAAC,CAACC,WAAW;EAExD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;IAChC,IAAIG,GAAG,CAACC,IAAI,KAAKpB,IAAI,CAACqB,oBAAoB,EAAE;MAC1Cf,kBAAkB,CAACa,GAAG,CAACL,IAAI,CAACQ,KAAK,CAAC,GAAG,CAACH,GAAG,CAACI,UAAU;IACtD;EACF;EAEA,MAAMC,gBAAgB,GAAGjB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMiB,iBAAiB,GAAGlB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7C,OAAO;IACL;IACA;IACA;IACAkB,KAAKA,CAACC,IAAI,EAAE;MACV,IAAI,EAAE,YAAY,IAAIA,IAAI,CAAC,IAAI,CAACA,IAAI,CAACC,UAAU,EAAE;QAC/C;MACF;MAEA,IAAIC,cAAc;MAElB,IACEF,IAAI,CAACP,IAAI,KAAKpB,IAAI,CAAC8B,iBAAiB,IACpCH,IAAI,CAACP,IAAI,KAAKpB,IAAI,CAAC+B,gBAAgB,EACnC;QACAF,cAAc,GAAGL,gBAAgB;MACnC,CAAC,MAAM,IAAIvB,oBAAoB,CAAC0B,IAAI,CAAC,IAAIzB,mBAAmB,CAACyB,IAAI,CAAC,EAAE;QAClE,MAAMK,QAAQ,GAAGL,IAAI,CAACb,IAAI,CAACQ,KAAK;QAChCO,cAAc,GAAGJ,iBAAiB,CAACO,QAAQ,CAAC;QAE5C,IAAIH,cAAc,KAAKI,SAAS,EAAE;UAChCR,iBAAiB,CAACO,QAAQ,CAAC,GAAGH,cAAc,GAAGtB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACpE;MACF,CAAC,MAAM;QACLqB,cAAc,GAAGtB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACtC;MAEA,KAAK,MAAMK,SAAS,IAAIc,IAAI,CAACC,UAAU,EAAE;QACvC,MAAMM,aAAa,GAAGrB,SAAS,CAACC,IAAI,CAACQ,KAAK;QAE1C,IAAIhB,kBAAkB,CAAC4B,aAAa,CAAC,EAAE;UACrC,IAAIL,cAAc,CAACK,aAAa,CAAC,EAAE;YACjC7B,OAAO,CAAC8B,WAAW,CACjB,IAAIpC,YAAY,CACb,mBAAkBmC,aAAc,2CAA0C,EAC3E;cACEE,KAAK,EAAE,CAACP,cAAc,CAACK,aAAa,CAAC,EAAErB,SAAS;YAClD,CACF,CACF,CAAC;UACH,CAAC,MAAM;YACLgB,cAAc,CAACK,aAAa,CAAC,GAAGrB,SAAS;UAC3C;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}