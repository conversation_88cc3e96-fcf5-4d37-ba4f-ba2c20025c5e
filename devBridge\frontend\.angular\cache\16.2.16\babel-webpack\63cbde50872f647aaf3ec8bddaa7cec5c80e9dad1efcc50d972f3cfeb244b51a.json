{"ast": null, "code": "import { isPromise } from './isPromise.mjs';\n\n/**\n * Similar to Array.prototype.reduce(), however the reducing callback may return\n * a Promise, in which case reduction will continue after each promise resolves.\n *\n * If the callback does not return a Promise, then this function will also not\n * return a Promise.\n */\nexport function promiseReduce(values, callbackFn, initialValue) {\n  let accumulator = initialValue;\n  for (const value of values) {\n    accumulator = isPromise(accumulator) ? accumulator.then(resolved => callbackFn(resolved, value)) : callbackFn(accumulator, value);\n  }\n  return accumulator;\n}", "map": {"version": 3, "names": ["isPromise", "promiseReduce", "values", "callbackFn", "initialValue", "accumulator", "value", "then", "resolved"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/jsutils/promiseReduce.mjs"], "sourcesContent": ["import { isPromise } from './isPromise.mjs';\n\n/**\n * Similar to Array.prototype.reduce(), however the reducing callback may return\n * a Promise, in which case reduction will continue after each promise resolves.\n *\n * If the callback does not return a Promise, then this function will also not\n * return a Promise.\n */\nexport function promiseReduce(values, callbackFn, initialValue) {\n  let accumulator = initialValue;\n\n  for (const value of values) {\n    accumulator = isPromise(accumulator)\n      ? accumulator.then((resolved) => callbackFn(resolved, value))\n      : callbackFn(accumulator, value);\n  }\n\n  return accumulator;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC9D,IAAIC,WAAW,GAAGD,YAAY;EAE9B,KAAK,MAAME,KAAK,IAAIJ,MAAM,EAAE;IAC1BG,WAAW,GAAGL,SAAS,CAACK,WAAW,CAAC,GAChCA,WAAW,CAACE,IAAI,CAAEC,QAAQ,IAAKL,UAAU,CAACK,QAAQ,EAAEF,KAAK,CAAC,CAAC,GAC3DH,UAAU,CAACE,WAAW,EAAEC,KAAK,CAAC;EACpC;EAEA,OAAOD,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}