{"name": "@apollo/utils.fetcher", "version": "2.0.1", "description": "Minimal web-style fetch TypeScript typings", "main": "", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/fetcher/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}}