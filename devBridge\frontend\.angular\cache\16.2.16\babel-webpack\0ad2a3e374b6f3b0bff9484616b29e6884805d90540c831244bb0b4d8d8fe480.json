{"ast": null, "code": "import { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique input field names\n *\n * A GraphQL input object value is only valid if all supplied fields are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Input-Object-Field-Uniqueness\n */\nexport function UniqueInputFieldNamesRule(context) {\n  const knownNameStack = [];\n  let knownNames = Object.create(null);\n  return {\n    ObjectValue: {\n      enter() {\n        knownNameStack.push(knownNames);\n        knownNames = Object.create(null);\n      },\n      leave() {\n        const prevKnownNames = knownNameStack.pop();\n        prevKnownNames || invariant(false);\n        knownNames = prevKnownNames;\n      }\n    },\n    ObjectField(node) {\n      const fieldName = node.name.value;\n      if (knownNames[fieldName]) {\n        context.reportError(new GraphQLError(`There can be only one input field named \"${fieldName}\".`, {\n          nodes: [knownNames[fieldName], node.name]\n        }));\n      } else {\n        knownNames[fieldName] = node.name;\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}