{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { closestIndexTo } from \"./closestIndexTo.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link closestTo} function options.\n */\n\n/**\n * The {@link closestTo} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateToCompare - Date to compare argument type.\n * @typeParam DatesType - Dates array argument type.\n * @typeParam Options - Options type.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates, options) {\n  const [dateToCompare_, ...dates_] = normalizeDates(options?.in, dateToCompare, ...dates);\n  const index = closestIndexTo(dateToCompare_, dates_);\n  if (typeof index === \"number\" && isNaN(index)) return constructFrom(dateToCompare_, NaN);\n  if (index !== undefined) return dates_[index];\n}\n\n// Fallback for modularized imports:\nexport default closestTo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}