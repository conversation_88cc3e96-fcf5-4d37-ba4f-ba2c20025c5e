{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { addDays } from \"./addDays.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { isValid } from \"./isValid.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link differenceInBusinessDays} function options.\n */\n\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that aren't in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of business days\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\nexport function differenceInBusinessDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  if (!isValid(laterDate_) || !isValid(earlierDate_)) return NaN;\n  const diff = differenceInCalendarDays(laterDate_, earlierDate_);\n  const sign = diff < 0 ? -1 : 1;\n  const weeks = Math.trunc(diff / 7);\n  let result = weeks * 5;\n  let movingDate = addDays(earlierDate_, weeks * 7);\n\n  // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n  while (!isSameDay(laterDate_, movingDate)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(movingDate, options) ? 0 : sign;\n    movingDate = addDays(movingDate, sign);\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInBusinessDays;", "map": {"version": 3, "names": ["normalizeDates", "addDays", "differenceInCalendarDays", "isSameDay", "<PERSON><PERSON><PERSON><PERSON>", "isWeekend", "differenceInBusinessDays", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "NaN", "diff", "sign", "weeks", "Math", "trunc", "result", "movingDate"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/differenceInBusinessDays.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { addDays } from \"./addDays.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { isValid } from \"./isValid.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link differenceInBusinessDays} function options.\n */\n\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that aren't in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of business days\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\nexport function differenceInBusinessDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  if (!isValid(laterDate_) || !isValid(earlierDate_)) return NaN;\n\n  const diff = differenceInCalendarDays(laterDate_, earlierDate_);\n  const sign = diff < 0 ? -1 : 1;\n  const weeks = Math.trunc(diff / 7);\n\n  let result = weeks * 5;\n  let movingDate = addDays(earlierDate_, weeks * 7);\n\n  // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n  while (!isSameDay(laterDate_, movingDate)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(movingDate, options) ? 0 : sign;\n    movingDate = addDays(movingDate, sign);\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInBusinessDays;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACxE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGX,cAAc,CAC/CS,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,IAAI,CAACJ,OAAO,CAACM,UAAU,CAAC,IAAI,CAACN,OAAO,CAACO,YAAY,CAAC,EAAE,OAAOE,GAAG;EAE9D,MAAMC,IAAI,GAAGZ,wBAAwB,CAACQ,UAAU,EAAEC,YAAY,CAAC;EAC/D,MAAMI,IAAI,GAAGD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC9B,MAAME,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,CAAC,CAAC;EAElC,IAAIK,MAAM,GAAGH,KAAK,GAAG,CAAC;EACtB,IAAII,UAAU,GAAGnB,OAAO,CAACU,YAAY,EAAEK,KAAK,GAAG,CAAC,CAAC;;EAEjD;EACA,OAAO,CAACb,SAAS,CAACO,UAAU,EAAEU,UAAU,CAAC,EAAE;IACzC;IACAD,MAAM,IAAId,SAAS,CAACe,UAAU,EAAEX,OAAO,CAAC,GAAG,CAAC,GAAGM,IAAI;IACnDK,UAAU,GAAGnB,OAAO,CAACmB,UAAU,EAAEL,IAAI,CAAC;EACxC;;EAEA;EACA,OAAOI,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAeb,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}