{"ast": null, "code": "/**\n * Memoizes the provided three-argument function.\n */\nexport function memoize3(fn) {\n  let cache0;\n  return function memoized(a1, a2, a3) {\n    if (cache0 === undefined) {\n      cache0 = new WeakMap();\n    }\n    let cache1 = cache0.get(a1);\n    if (cache1 === undefined) {\n      cache1 = new WeakMap();\n      cache0.set(a1, cache1);\n    }\n    let cache2 = cache1.get(a2);\n    if (cache2 === undefined) {\n      cache2 = new WeakMap();\n      cache1.set(a2, cache2);\n    }\n    let fnResult = cache2.get(a3);\n    if (fnResult === undefined) {\n      fnResult = fn(a1, a2, a3);\n      cache2.set(a3, fnResult);\n    }\n    return fnResult;\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}