{"ast": null, "code": "import { parentEntrySlot } from \"./context.js\";\nimport { hasOwnProperty, maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nconst EntryMethods = {\n  setDirty: true,\n  dispose: true,\n  forget: true // Fully remove parent Entry from LRU cache and computation graph\n};\n\nexport function dep(options) {\n  const depsByKey = new Map();\n  const subscribe = options && options.subscribe;\n  function depend(key) {\n    const parent = parentEntrySlot.getValue();\n    if (parent) {\n      let dep = depsByKey.get(key);\n      if (!dep) {\n        depsByKey.set(key, dep = new Set());\n      }\n      parent.dependOn(dep);\n      if (typeof subscribe === \"function\") {\n        maybeUnsubscribe(dep);\n        dep.unsubscribe = subscribe(key);\n      }\n    }\n  }\n  depend.dirty = function dirty(key, entryMethodName) {\n    const dep = depsByKey.get(key);\n    if (dep) {\n      const m = entryMethodName && hasOwnProperty.call(EntryMethods, entryMethodName) ? entryMethodName : \"setDirty\";\n      // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n      // because modifying a Set while iterating over it can cause elements in\n      // the Set to be removed from the Set before they've been iterated over.\n      arrayFromSet(dep).forEach(entry => entry[m]());\n      depsByKey.delete(key);\n      maybeUnsubscribe(dep);\n    }\n  };\n  return depend;\n}\n//# sourceMappingURL=dep.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}