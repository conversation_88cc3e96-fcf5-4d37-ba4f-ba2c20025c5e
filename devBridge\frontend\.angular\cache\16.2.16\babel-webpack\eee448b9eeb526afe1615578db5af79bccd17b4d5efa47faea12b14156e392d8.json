{"ast": null, "code": "import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in hours\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport function millisecondsToHours(milliseconds) {\n  const hours = milliseconds / millisecondsInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToHours;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}