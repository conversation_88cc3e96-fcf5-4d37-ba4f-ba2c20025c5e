{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { parseValue } from '../language/parser.mjs';\nimport { assertInterfaceType, assertNullableType, assertObjectType, GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isInputType, isOutputType } from '../type/definition.mjs';\nimport { GraphQLDirective } from '../type/directives.mjs';\nimport { introspectionTypes, TypeKind } from '../type/introspection.mjs';\nimport { specifiedScalarTypes } from '../type/scalars.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\nimport { valueFromAST } from './valueFromAST.mjs';\n/**\n * Build a GraphQLSchema for use by client tools.\n *\n * Given the result of a client running the introspection query, creates and\n * returns a GraphQLSchema instance which can be then used with all graphql-js\n * tools, but cannot be used to execute a query, as introspection does not\n * represent the \"resolver\", \"parse\" or \"serialize\" functions or any other\n * server-internal mechanisms.\n *\n * This function expects a complete introspection result. Don't forget to check\n * the \"errors\" field of a server response before calling this function.\n */\n\nexport function buildClientSchema(introspection, options) {\n  isObjectLike(introspection) && isObjectLike(introspection.__schema) || devAssert(false, `Invalid or incomplete introspection result. Ensure that you are passing \"data\" property of introspection response and no \"errors\" was returned alongside: ${inspect(introspection)}.`); // Get the schema from the introspection result.\n\n  const schemaIntrospection = introspection.__schema; // Iterate through all types, getting the type definition for each.\n\n  const typeMap = keyValMap(schemaIntrospection.types, typeIntrospection => typeIntrospection.name, typeIntrospection => buildType(typeIntrospection)); // Include standard types only if they are used.\n\n  for (const stdType of [...specifiedScalarTypes, ...introspectionTypes]) {\n    if (typeMap[stdType.name]) {\n      typeMap[stdType.name] = stdType;\n    }\n  } // Get the root Query, Mutation, and Subscription types.\n\n  const queryType = schemaIntrospection.queryType ? getObjectType(schemaIntrospection.queryType) : null;\n  const mutationType = schemaIntrospection.mutationType ? getObjectType(schemaIntrospection.mutationType) : null;\n  const subscriptionType = schemaIntrospection.subscriptionType ? getObjectType(schemaIntrospection.subscriptionType) : null; // Get the directives supported by Introspection, assuming empty-set if\n  // directives were not queried for.\n\n  const directives = schemaIntrospection.directives ? schemaIntrospection.directives.map(buildDirective) : []; // Then produce and return a Schema with these types.\n\n  return new GraphQLSchema({\n    description: schemaIntrospection.description,\n    query: queryType,\n    mutation: mutationType,\n    subscription: subscriptionType,\n    types: Object.values(typeMap),\n    directives,\n    assumeValid: options === null || options === void 0 ? void 0 : options.assumeValid\n  }); // Given a type reference in introspection, return the GraphQLType instance.\n  // preferring cached instances before building new instances.\n\n  function getType(typeRef) {\n    if (typeRef.kind === TypeKind.LIST) {\n      const itemRef = typeRef.ofType;\n      if (!itemRef) {\n        throw new Error('Decorated type deeper than introspection query.');\n      }\n      return new GraphQLList(getType(itemRef));\n    }\n    if (typeRef.kind === TypeKind.NON_NULL) {\n      const nullableRef = typeRef.ofType;\n      if (!nullableRef) {\n        throw new Error('Decorated type deeper than introspection query.');\n      }\n      const nullableType = getType(nullableRef);\n      return new GraphQLNonNull(assertNullableType(nullableType));\n    }\n    return getNamedType(typeRef);\n  }\n  function getNamedType(typeRef) {\n    const typeName = typeRef.name;\n    if (!typeName) {\n      throw new Error(`Unknown type reference: ${inspect(typeRef)}.`);\n    }\n    const type = typeMap[typeName];\n    if (!type) {\n      throw new Error(`Invalid or incomplete schema, unknown type: ${typeName}. Ensure that a full introspection query is used in order to build a client schema.`);\n    }\n    return type;\n  }\n  function getObjectType(typeRef) {\n    return assertObjectType(getNamedType(typeRef));\n  }\n  function getInterfaceType(typeRef) {\n    return assertInterfaceType(getNamedType(typeRef));\n  } // Given a type's introspection result, construct the correct\n  // GraphQLType instance.\n\n  function buildType(type) {\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (type != null && type.name != null && type.kind != null) {\n      // FIXME: Properly type IntrospectionType, it's a breaking change so fix in v17\n      // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n      switch (type.kind) {\n        case TypeKind.SCALAR:\n          return buildScalarDef(type);\n        case TypeKind.OBJECT:\n          return buildObjectDef(type);\n        case TypeKind.INTERFACE:\n          return buildInterfaceDef(type);\n        case TypeKind.UNION:\n          return buildUnionDef(type);\n        case TypeKind.ENUM:\n          return buildEnumDef(type);\n        case TypeKind.INPUT_OBJECT:\n          return buildInputObjectDef(type);\n      }\n    }\n    const typeStr = inspect(type);\n    throw new Error(`Invalid or incomplete introspection result. Ensure that a full introspection query is used in order to build a client schema: ${typeStr}.`);\n  }\n  function buildScalarDef(scalarIntrospection) {\n    return new GraphQLScalarType({\n      name: scalarIntrospection.name,\n      description: scalarIntrospection.description,\n      specifiedByURL: scalarIntrospection.specifiedByURL\n    });\n  }\n  function buildImplementationsList(implementingIntrospection) {\n    // TODO: Temporary workaround until GraphQL ecosystem will fully support\n    // 'interfaces' on interface types.\n    if (implementingIntrospection.interfaces === null && implementingIntrospection.kind === TypeKind.INTERFACE) {\n      return [];\n    }\n    if (!implementingIntrospection.interfaces) {\n      const implementingIntrospectionStr = inspect(implementingIntrospection);\n      throw new Error(`Introspection result missing interfaces: ${implementingIntrospectionStr}.`);\n    }\n    return implementingIntrospection.interfaces.map(getInterfaceType);\n  }\n  function buildObjectDef(objectIntrospection) {\n    return new GraphQLObjectType({\n      name: objectIntrospection.name,\n      description: objectIntrospection.description,\n      interfaces: () => buildImplementationsList(objectIntrospection),\n      fields: () => buildFieldDefMap(objectIntrospection)\n    });\n  }\n  function buildInterfaceDef(interfaceIntrospection) {\n    return new GraphQLInterfaceType({\n      name: interfaceIntrospection.name,\n      description: interfaceIntrospection.description,\n      interfaces: () => buildImplementationsList(interfaceIntrospection),\n      fields: () => buildFieldDefMap(interfaceIntrospection)\n    });\n  }\n  function buildUnionDef(unionIntrospection) {\n    if (!unionIntrospection.possibleTypes) {\n      const unionIntrospectionStr = inspect(unionIntrospection);\n      throw new Error(`Introspection result missing possibleTypes: ${unionIntrospectionStr}.`);\n    }\n    return new GraphQLUnionType({\n      name: unionIntrospection.name,\n      description: unionIntrospection.description,\n      types: () => unionIntrospection.possibleTypes.map(getObjectType)\n    });\n  }\n  function buildEnumDef(enumIntrospection) {\n    if (!enumIntrospection.enumValues) {\n      const enumIntrospectionStr = inspect(enumIntrospection);\n      throw new Error(`Introspection result missing enumValues: ${enumIntrospectionStr}.`);\n    }\n    return new GraphQLEnumType({\n      name: enumIntrospection.name,\n      description: enumIntrospection.description,\n      values: keyValMap(enumIntrospection.enumValues, valueIntrospection => valueIntrospection.name, valueIntrospection => ({\n        description: valueIntrospection.description,\n        deprecationReason: valueIntrospection.deprecationReason\n      }))\n    });\n  }\n  function buildInputObjectDef(inputObjectIntrospection) {\n    if (!inputObjectIntrospection.inputFields) {\n      const inputObjectIntrospectionStr = inspect(inputObjectIntrospection);\n      throw new Error(`Introspection result missing inputFields: ${inputObjectIntrospectionStr}.`);\n    }\n    return new GraphQLInputObjectType({\n      name: inputObjectIntrospection.name,\n      description: inputObjectIntrospection.description,\n      fields: () => buildInputValueDefMap(inputObjectIntrospection.inputFields)\n    });\n  }\n  function buildFieldDefMap(typeIntrospection) {\n    if (!typeIntrospection.fields) {\n      throw new Error(`Introspection result missing fields: ${inspect(typeIntrospection)}.`);\n    }\n    return keyValMap(typeIntrospection.fields, fieldIntrospection => fieldIntrospection.name, buildField);\n  }\n  function buildField(fieldIntrospection) {\n    const type = getType(fieldIntrospection.type);\n    if (!isOutputType(type)) {\n      const typeStr = inspect(type);\n      throw new Error(`Introspection must provide output type for fields, but received: ${typeStr}.`);\n    }\n    if (!fieldIntrospection.args) {\n      const fieldIntrospectionStr = inspect(fieldIntrospection);\n      throw new Error(`Introspection result missing field args: ${fieldIntrospectionStr}.`);\n    }\n    return {\n      description: fieldIntrospection.description,\n      deprecationReason: fieldIntrospection.deprecationReason,\n      type,\n      args: buildInputValueDefMap(fieldIntrospection.args)\n    };\n  }\n  function buildInputValueDefMap(inputValueIntrospections) {\n    return keyValMap(inputValueIntrospections, inputValue => inputValue.name, buildInputValue);\n  }\n  function buildInputValue(inputValueIntrospection) {\n    const type = getType(inputValueIntrospection.type);\n    if (!isInputType(type)) {\n      const typeStr = inspect(type);\n      throw new Error(`Introspection must provide input type for arguments, but received: ${typeStr}.`);\n    }\n    const defaultValue = inputValueIntrospection.defaultValue != null ? valueFromAST(parseValue(inputValueIntrospection.defaultValue), type) : undefined;\n    return {\n      description: inputValueIntrospection.description,\n      type,\n      defaultValue,\n      deprecationReason: inputValueIntrospection.deprecationReason\n    };\n  }\n  function buildDirective(directiveIntrospection) {\n    if (!directiveIntrospection.args) {\n      const directiveIntrospectionStr = inspect(directiveIntrospection);\n      throw new Error(`Introspection result missing directive args: ${directiveIntrospectionStr}.`);\n    }\n    if (!directiveIntrospection.locations) {\n      const directiveIntrospectionStr = inspect(directiveIntrospection);\n      throw new Error(`Introspection result missing directive locations: ${directiveIntrospectionStr}.`);\n    }\n    return new GraphQLDirective({\n      name: directiveIntrospection.name,\n      description: directiveIntrospection.description,\n      isRepeatable: directiveIntrospection.isRepeatable,\n      locations: directiveIntrospection.locations.slice(),\n      args: buildInputValueDefMap(directiveIntrospection.args)\n    });\n  }\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "isObjectLike", "keyValMap", "parseValue", "assertInterfaceType", "assertNullableType", "assertObjectType", "GraphQLEnumType", "GraphQLInputObjectType", "GraphQLInterfaceType", "GraphQLList", "GraphQLNonNull", "GraphQLObjectType", "GraphQLScalarType", "GraphQLUnionType", "isInputType", "isOutputType", "GraphQLDirective", "introspectionTypes", "TypeKind", "specifiedScalarTypes", "GraphQLSchema", "valueFromAST", "buildClientSchema", "introspection", "options", "__schema", "schemaIntrospection", "typeMap", "types", "typeIntrospection", "name", "buildType", "stdType", "queryType", "getObjectType", "mutationType", "subscriptionType", "directives", "map", "buildDirective", "description", "query", "mutation", "subscription", "Object", "values", "<PERSON><PERSON><PERSON><PERSON>", "getType", "typeRef", "kind", "LIST", "itemRef", "ofType", "Error", "NON_NULL", "nullableRef", "nullableType", "getNamedType", "typeName", "type", "getInterfaceType", "SCALAR", "buildScalarDef", "OBJECT", "buildObjectDef", "INTERFACE", "buildInterfaceDef", "UNION", "buildUnionDef", "ENUM", "buildEnumDef", "INPUT_OBJECT", "buildInputObjectDef", "typeStr", "scalarIntrospection", "specifiedByURL", "buildImplementationsList", "implementingIntrospection", "interfaces", "implementingIntrospectionStr", "objectIntrospection", "fields", "buildFieldDefMap", "interfaceIntrospection", "unionIntrospection", "possibleTypes", "unionIntrospectionStr", "enumIntrospection", "enum<PERSON><PERSON><PERSON>", "enumIntrospectionStr", "valueIntrospection", "deprecationReason", "inputObjectIntrospection", "inputFields", "inputObjectIntrospectionStr", "buildInputValueDefMap", "fieldIntrospection", "buildField", "args", "fieldIntrospectionStr", "inputValueIntrospections", "inputValue", "buildInputValue", "inputValueIntrospection", "defaultValue", "undefined", "directiveIntrospection", "directiveIntrospectionStr", "locations", "isRepeatable", "slice"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/buildClientSchema.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { parseValue } from '../language/parser.mjs';\nimport {\n  assertInterfaceType,\n  assertNullableType,\n  assertObjectType,\n  GraphQLEnumType,\n  GraphQLInputObjectType,\n  GraphQLInterfaceType,\n  GraphQLList,\n  GraphQLNonNull,\n  GraphQLObjectType,\n  GraphQLScalarType,\n  GraphQLUnionType,\n  isInputType,\n  isOutputType,\n} from '../type/definition.mjs';\nimport { GraphQLDirective } from '../type/directives.mjs';\nimport { introspectionTypes, TypeKind } from '../type/introspection.mjs';\nimport { specifiedScalarTypes } from '../type/scalars.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\nimport { valueFromAST } from './valueFromAST.mjs';\n/**\n * Build a GraphQLSchema for use by client tools.\n *\n * Given the result of a client running the introspection query, creates and\n * returns a GraphQLSchema instance which can be then used with all graphql-js\n * tools, but cannot be used to execute a query, as introspection does not\n * represent the \"resolver\", \"parse\" or \"serialize\" functions or any other\n * server-internal mechanisms.\n *\n * This function expects a complete introspection result. Don't forget to check\n * the \"errors\" field of a server response before calling this function.\n */\n\nexport function buildClientSchema(introspection, options) {\n  (isObjectLike(introspection) && isObjectLike(introspection.__schema)) ||\n    devAssert(\n      false,\n      `Invalid or incomplete introspection result. Ensure that you are passing \"data\" property of introspection response and no \"errors\" was returned alongside: ${inspect(\n        introspection,\n      )}.`,\n    ); // Get the schema from the introspection result.\n\n  const schemaIntrospection = introspection.__schema; // Iterate through all types, getting the type definition for each.\n\n  const typeMap = keyValMap(\n    schemaIntrospection.types,\n    (typeIntrospection) => typeIntrospection.name,\n    (typeIntrospection) => buildType(typeIntrospection),\n  ); // Include standard types only if they are used.\n\n  for (const stdType of [...specifiedScalarTypes, ...introspectionTypes]) {\n    if (typeMap[stdType.name]) {\n      typeMap[stdType.name] = stdType;\n    }\n  } // Get the root Query, Mutation, and Subscription types.\n\n  const queryType = schemaIntrospection.queryType\n    ? getObjectType(schemaIntrospection.queryType)\n    : null;\n  const mutationType = schemaIntrospection.mutationType\n    ? getObjectType(schemaIntrospection.mutationType)\n    : null;\n  const subscriptionType = schemaIntrospection.subscriptionType\n    ? getObjectType(schemaIntrospection.subscriptionType)\n    : null; // Get the directives supported by Introspection, assuming empty-set if\n  // directives were not queried for.\n\n  const directives = schemaIntrospection.directives\n    ? schemaIntrospection.directives.map(buildDirective)\n    : []; // Then produce and return a Schema with these types.\n\n  return new GraphQLSchema({\n    description: schemaIntrospection.description,\n    query: queryType,\n    mutation: mutationType,\n    subscription: subscriptionType,\n    types: Object.values(typeMap),\n    directives,\n    assumeValid:\n      options === null || options === void 0 ? void 0 : options.assumeValid,\n  }); // Given a type reference in introspection, return the GraphQLType instance.\n  // preferring cached instances before building new instances.\n\n  function getType(typeRef) {\n    if (typeRef.kind === TypeKind.LIST) {\n      const itemRef = typeRef.ofType;\n\n      if (!itemRef) {\n        throw new Error('Decorated type deeper than introspection query.');\n      }\n\n      return new GraphQLList(getType(itemRef));\n    }\n\n    if (typeRef.kind === TypeKind.NON_NULL) {\n      const nullableRef = typeRef.ofType;\n\n      if (!nullableRef) {\n        throw new Error('Decorated type deeper than introspection query.');\n      }\n\n      const nullableType = getType(nullableRef);\n      return new GraphQLNonNull(assertNullableType(nullableType));\n    }\n\n    return getNamedType(typeRef);\n  }\n\n  function getNamedType(typeRef) {\n    const typeName = typeRef.name;\n\n    if (!typeName) {\n      throw new Error(`Unknown type reference: ${inspect(typeRef)}.`);\n    }\n\n    const type = typeMap[typeName];\n\n    if (!type) {\n      throw new Error(\n        `Invalid or incomplete schema, unknown type: ${typeName}. Ensure that a full introspection query is used in order to build a client schema.`,\n      );\n    }\n\n    return type;\n  }\n\n  function getObjectType(typeRef) {\n    return assertObjectType(getNamedType(typeRef));\n  }\n\n  function getInterfaceType(typeRef) {\n    return assertInterfaceType(getNamedType(typeRef));\n  } // Given a type's introspection result, construct the correct\n  // GraphQLType instance.\n\n  function buildType(type) {\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (type != null && type.name != null && type.kind != null) {\n      // FIXME: Properly type IntrospectionType, it's a breaking change so fix in v17\n      // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n      switch (type.kind) {\n        case TypeKind.SCALAR:\n          return buildScalarDef(type);\n\n        case TypeKind.OBJECT:\n          return buildObjectDef(type);\n\n        case TypeKind.INTERFACE:\n          return buildInterfaceDef(type);\n\n        case TypeKind.UNION:\n          return buildUnionDef(type);\n\n        case TypeKind.ENUM:\n          return buildEnumDef(type);\n\n        case TypeKind.INPUT_OBJECT:\n          return buildInputObjectDef(type);\n      }\n    }\n\n    const typeStr = inspect(type);\n    throw new Error(\n      `Invalid or incomplete introspection result. Ensure that a full introspection query is used in order to build a client schema: ${typeStr}.`,\n    );\n  }\n\n  function buildScalarDef(scalarIntrospection) {\n    return new GraphQLScalarType({\n      name: scalarIntrospection.name,\n      description: scalarIntrospection.description,\n      specifiedByURL: scalarIntrospection.specifiedByURL,\n    });\n  }\n\n  function buildImplementationsList(implementingIntrospection) {\n    // TODO: Temporary workaround until GraphQL ecosystem will fully support\n    // 'interfaces' on interface types.\n    if (\n      implementingIntrospection.interfaces === null &&\n      implementingIntrospection.kind === TypeKind.INTERFACE\n    ) {\n      return [];\n    }\n\n    if (!implementingIntrospection.interfaces) {\n      const implementingIntrospectionStr = inspect(implementingIntrospection);\n      throw new Error(\n        `Introspection result missing interfaces: ${implementingIntrospectionStr}.`,\n      );\n    }\n\n    return implementingIntrospection.interfaces.map(getInterfaceType);\n  }\n\n  function buildObjectDef(objectIntrospection) {\n    return new GraphQLObjectType({\n      name: objectIntrospection.name,\n      description: objectIntrospection.description,\n      interfaces: () => buildImplementationsList(objectIntrospection),\n      fields: () => buildFieldDefMap(objectIntrospection),\n    });\n  }\n\n  function buildInterfaceDef(interfaceIntrospection) {\n    return new GraphQLInterfaceType({\n      name: interfaceIntrospection.name,\n      description: interfaceIntrospection.description,\n      interfaces: () => buildImplementationsList(interfaceIntrospection),\n      fields: () => buildFieldDefMap(interfaceIntrospection),\n    });\n  }\n\n  function buildUnionDef(unionIntrospection) {\n    if (!unionIntrospection.possibleTypes) {\n      const unionIntrospectionStr = inspect(unionIntrospection);\n      throw new Error(\n        `Introspection result missing possibleTypes: ${unionIntrospectionStr}.`,\n      );\n    }\n\n    return new GraphQLUnionType({\n      name: unionIntrospection.name,\n      description: unionIntrospection.description,\n      types: () => unionIntrospection.possibleTypes.map(getObjectType),\n    });\n  }\n\n  function buildEnumDef(enumIntrospection) {\n    if (!enumIntrospection.enumValues) {\n      const enumIntrospectionStr = inspect(enumIntrospection);\n      throw new Error(\n        `Introspection result missing enumValues: ${enumIntrospectionStr}.`,\n      );\n    }\n\n    return new GraphQLEnumType({\n      name: enumIntrospection.name,\n      description: enumIntrospection.description,\n      values: keyValMap(\n        enumIntrospection.enumValues,\n        (valueIntrospection) => valueIntrospection.name,\n        (valueIntrospection) => ({\n          description: valueIntrospection.description,\n          deprecationReason: valueIntrospection.deprecationReason,\n        }),\n      ),\n    });\n  }\n\n  function buildInputObjectDef(inputObjectIntrospection) {\n    if (!inputObjectIntrospection.inputFields) {\n      const inputObjectIntrospectionStr = inspect(inputObjectIntrospection);\n      throw new Error(\n        `Introspection result missing inputFields: ${inputObjectIntrospectionStr}.`,\n      );\n    }\n\n    return new GraphQLInputObjectType({\n      name: inputObjectIntrospection.name,\n      description: inputObjectIntrospection.description,\n      fields: () => buildInputValueDefMap(inputObjectIntrospection.inputFields),\n    });\n  }\n\n  function buildFieldDefMap(typeIntrospection) {\n    if (!typeIntrospection.fields) {\n      throw new Error(\n        `Introspection result missing fields: ${inspect(typeIntrospection)}.`,\n      );\n    }\n\n    return keyValMap(\n      typeIntrospection.fields,\n      (fieldIntrospection) => fieldIntrospection.name,\n      buildField,\n    );\n  }\n\n  function buildField(fieldIntrospection) {\n    const type = getType(fieldIntrospection.type);\n\n    if (!isOutputType(type)) {\n      const typeStr = inspect(type);\n      throw new Error(\n        `Introspection must provide output type for fields, but received: ${typeStr}.`,\n      );\n    }\n\n    if (!fieldIntrospection.args) {\n      const fieldIntrospectionStr = inspect(fieldIntrospection);\n      throw new Error(\n        `Introspection result missing field args: ${fieldIntrospectionStr}.`,\n      );\n    }\n\n    return {\n      description: fieldIntrospection.description,\n      deprecationReason: fieldIntrospection.deprecationReason,\n      type,\n      args: buildInputValueDefMap(fieldIntrospection.args),\n    };\n  }\n\n  function buildInputValueDefMap(inputValueIntrospections) {\n    return keyValMap(\n      inputValueIntrospections,\n      (inputValue) => inputValue.name,\n      buildInputValue,\n    );\n  }\n\n  function buildInputValue(inputValueIntrospection) {\n    const type = getType(inputValueIntrospection.type);\n\n    if (!isInputType(type)) {\n      const typeStr = inspect(type);\n      throw new Error(\n        `Introspection must provide input type for arguments, but received: ${typeStr}.`,\n      );\n    }\n\n    const defaultValue =\n      inputValueIntrospection.defaultValue != null\n        ? valueFromAST(parseValue(inputValueIntrospection.defaultValue), type)\n        : undefined;\n    return {\n      description: inputValueIntrospection.description,\n      type,\n      defaultValue,\n      deprecationReason: inputValueIntrospection.deprecationReason,\n    };\n  }\n\n  function buildDirective(directiveIntrospection) {\n    if (!directiveIntrospection.args) {\n      const directiveIntrospectionStr = inspect(directiveIntrospection);\n      throw new Error(\n        `Introspection result missing directive args: ${directiveIntrospectionStr}.`,\n      );\n    }\n\n    if (!directiveIntrospection.locations) {\n      const directiveIntrospectionStr = inspect(directiveIntrospection);\n      throw new Error(\n        `Introspection result missing directive locations: ${directiveIntrospectionStr}.`,\n      );\n    }\n\n    return new GraphQLDirective({\n      name: directiveIntrospection.name,\n      description: directiveIntrospection.description,\n      isRepeatable: directiveIntrospection.isRepeatable,\n      locations: directiveIntrospection.locations.slice(),\n      args: buildInputValueDefMap(directiveIntrospection.args),\n    });\n  }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,EAChBC,eAAe,EACfC,sBAAsB,EACtBC,oBAAoB,EACpBC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB,EAChBC,WAAW,EACXC,YAAY,QACP,wBAAwB;AAC/B,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,QAAQ,QAAQ,2BAA2B;AACxE,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EACvDxB,YAAY,CAACuB,aAAa,CAAC,IAAIvB,YAAY,CAACuB,aAAa,CAACE,QAAQ,CAAC,IAClE3B,SAAS,CACP,KAAK,EACJ,6JAA4JC,OAAO,CAClKwB,aACF,CAAE,GACJ,CAAC,CAAC,CAAC;;EAEL,MAAMG,mBAAmB,GAAGH,aAAa,CAACE,QAAQ,CAAC,CAAC;;EAEpD,MAAME,OAAO,GAAG1B,SAAS,CACvByB,mBAAmB,CAACE,KAAK,EACxBC,iBAAiB,IAAKA,iBAAiB,CAACC,IAAI,EAC5CD,iBAAiB,IAAKE,SAAS,CAACF,iBAAiB,CACpD,CAAC,CAAC,CAAC;;EAEH,KAAK,MAAMG,OAAO,IAAI,CAAC,GAAGb,oBAAoB,EAAE,GAAGF,kBAAkB,CAAC,EAAE;IACtE,IAAIU,OAAO,CAACK,OAAO,CAACF,IAAI,CAAC,EAAE;MACzBH,OAAO,CAACK,OAAO,CAACF,IAAI,CAAC,GAAGE,OAAO;IACjC;EACF,CAAC,CAAC;;EAEF,MAAMC,SAAS,GAAGP,mBAAmB,CAACO,SAAS,GAC3CC,aAAa,CAACR,mBAAmB,CAACO,SAAS,CAAC,GAC5C,IAAI;EACR,MAAME,YAAY,GAAGT,mBAAmB,CAACS,YAAY,GACjDD,aAAa,CAACR,mBAAmB,CAACS,YAAY,CAAC,GAC/C,IAAI;EACR,MAAMC,gBAAgB,GAAGV,mBAAmB,CAACU,gBAAgB,GACzDF,aAAa,CAACR,mBAAmB,CAACU,gBAAgB,CAAC,GACnD,IAAI,CAAC,CAAC;EACV;;EAEA,MAAMC,UAAU,GAAGX,mBAAmB,CAACW,UAAU,GAC7CX,mBAAmB,CAACW,UAAU,CAACC,GAAG,CAACC,cAAc,CAAC,GAClD,EAAE,CAAC,CAAC;;EAER,OAAO,IAAInB,aAAa,CAAC;IACvBoB,WAAW,EAAEd,mBAAmB,CAACc,WAAW;IAC5CC,KAAK,EAAER,SAAS;IAChBS,QAAQ,EAAEP,YAAY;IACtBQ,YAAY,EAAEP,gBAAgB;IAC9BR,KAAK,EAAEgB,MAAM,CAACC,MAAM,CAAClB,OAAO,CAAC;IAC7BU,UAAU;IACVS,WAAW,EACTtB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsB;EAC9D,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEA,SAASC,OAAOA,CAACC,OAAO,EAAE;IACxB,IAAIA,OAAO,CAACC,IAAI,KAAK/B,QAAQ,CAACgC,IAAI,EAAE;MAClC,MAAMC,OAAO,GAAGH,OAAO,CAACI,MAAM;MAE9B,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIE,KAAK,CAAC,iDAAiD,CAAC;MACpE;MAEA,OAAO,IAAI5C,WAAW,CAACsC,OAAO,CAACI,OAAO,CAAC,CAAC;IAC1C;IAEA,IAAIH,OAAO,CAACC,IAAI,KAAK/B,QAAQ,CAACoC,QAAQ,EAAE;MACtC,MAAMC,WAAW,GAAGP,OAAO,CAACI,MAAM;MAElC,IAAI,CAACG,WAAW,EAAE;QAChB,MAAM,IAAIF,KAAK,CAAC,iDAAiD,CAAC;MACpE;MAEA,MAAMG,YAAY,GAAGT,OAAO,CAACQ,WAAW,CAAC;MACzC,OAAO,IAAI7C,cAAc,CAACN,kBAAkB,CAACoD,YAAY,CAAC,CAAC;IAC7D;IAEA,OAAOC,YAAY,CAACT,OAAO,CAAC;EAC9B;EAEA,SAASS,YAAYA,CAACT,OAAO,EAAE;IAC7B,MAAMU,QAAQ,GAAGV,OAAO,CAAClB,IAAI;IAE7B,IAAI,CAAC4B,QAAQ,EAAE;MACb,MAAM,IAAIL,KAAK,CAAE,2BAA0BtD,OAAO,CAACiD,OAAO,CAAE,GAAE,CAAC;IACjE;IAEA,MAAMW,IAAI,GAAGhC,OAAO,CAAC+B,QAAQ,CAAC;IAE9B,IAAI,CAACC,IAAI,EAAE;MACT,MAAM,IAAIN,KAAK,CACZ,+CAA8CK,QAAS,qFAC1D,CAAC;IACH;IAEA,OAAOC,IAAI;EACb;EAEA,SAASzB,aAAaA,CAACc,OAAO,EAAE;IAC9B,OAAO3C,gBAAgB,CAACoD,YAAY,CAACT,OAAO,CAAC,CAAC;EAChD;EAEA,SAASY,gBAAgBA,CAACZ,OAAO,EAAE;IACjC,OAAO7C,mBAAmB,CAACsD,YAAY,CAACT,OAAO,CAAC,CAAC;EACnD,CAAC,CAAC;EACF;;EAEA,SAASjB,SAASA,CAAC4B,IAAI,EAAE;IACvB;IACA,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7B,IAAI,IAAI,IAAI,IAAI6B,IAAI,CAACV,IAAI,IAAI,IAAI,EAAE;MAC1D;MACA;MACA,QAAQU,IAAI,CAACV,IAAI;QACf,KAAK/B,QAAQ,CAAC2C,MAAM;UAClB,OAAOC,cAAc,CAACH,IAAI,CAAC;QAE7B,KAAKzC,QAAQ,CAAC6C,MAAM;UAClB,OAAOC,cAAc,CAACL,IAAI,CAAC;QAE7B,KAAKzC,QAAQ,CAAC+C,SAAS;UACrB,OAAOC,iBAAiB,CAACP,IAAI,CAAC;QAEhC,KAAKzC,QAAQ,CAACiD,KAAK;UACjB,OAAOC,aAAa,CAACT,IAAI,CAAC;QAE5B,KAAKzC,QAAQ,CAACmD,IAAI;UAChB,OAAOC,YAAY,CAACX,IAAI,CAAC;QAE3B,KAAKzC,QAAQ,CAACqD,YAAY;UACxB,OAAOC,mBAAmB,CAACb,IAAI,CAAC;MACpC;IACF;IAEA,MAAMc,OAAO,GAAG1E,OAAO,CAAC4D,IAAI,CAAC;IAC7B,MAAM,IAAIN,KAAK,CACZ,iIAAgIoB,OAAQ,GAC3I,CAAC;EACH;EAEA,SAASX,cAAcA,CAACY,mBAAmB,EAAE;IAC3C,OAAO,IAAI9D,iBAAiB,CAAC;MAC3BkB,IAAI,EAAE4C,mBAAmB,CAAC5C,IAAI;MAC9BU,WAAW,EAAEkC,mBAAmB,CAAClC,WAAW;MAC5CmC,cAAc,EAAED,mBAAmB,CAACC;IACtC,CAAC,CAAC;EACJ;EAEA,SAASC,wBAAwBA,CAACC,yBAAyB,EAAE;IAC3D;IACA;IACA,IACEA,yBAAyB,CAACC,UAAU,KAAK,IAAI,IAC7CD,yBAAyB,CAAC5B,IAAI,KAAK/B,QAAQ,CAAC+C,SAAS,EACrD;MACA,OAAO,EAAE;IACX;IAEA,IAAI,CAACY,yBAAyB,CAACC,UAAU,EAAE;MACzC,MAAMC,4BAA4B,GAAGhF,OAAO,CAAC8E,yBAAyB,CAAC;MACvE,MAAM,IAAIxB,KAAK,CACZ,4CAA2C0B,4BAA6B,GAC3E,CAAC;IACH;IAEA,OAAOF,yBAAyB,CAACC,UAAU,CAACxC,GAAG,CAACsB,gBAAgB,CAAC;EACnE;EAEA,SAASI,cAAcA,CAACgB,mBAAmB,EAAE;IAC3C,OAAO,IAAIrE,iBAAiB,CAAC;MAC3BmB,IAAI,EAAEkD,mBAAmB,CAAClD,IAAI;MAC9BU,WAAW,EAAEwC,mBAAmB,CAACxC,WAAW;MAC5CsC,UAAU,EAAEA,CAAA,KAAMF,wBAAwB,CAACI,mBAAmB,CAAC;MAC/DC,MAAM,EAAEA,CAAA,KAAMC,gBAAgB,CAACF,mBAAmB;IACpD,CAAC,CAAC;EACJ;EAEA,SAASd,iBAAiBA,CAACiB,sBAAsB,EAAE;IACjD,OAAO,IAAI3E,oBAAoB,CAAC;MAC9BsB,IAAI,EAAEqD,sBAAsB,CAACrD,IAAI;MACjCU,WAAW,EAAE2C,sBAAsB,CAAC3C,WAAW;MAC/CsC,UAAU,EAAEA,CAAA,KAAMF,wBAAwB,CAACO,sBAAsB,CAAC;MAClEF,MAAM,EAAEA,CAAA,KAAMC,gBAAgB,CAACC,sBAAsB;IACvD,CAAC,CAAC;EACJ;EAEA,SAASf,aAAaA,CAACgB,kBAAkB,EAAE;IACzC,IAAI,CAACA,kBAAkB,CAACC,aAAa,EAAE;MACrC,MAAMC,qBAAqB,GAAGvF,OAAO,CAACqF,kBAAkB,CAAC;MACzD,MAAM,IAAI/B,KAAK,CACZ,+CAA8CiC,qBAAsB,GACvE,CAAC;IACH;IAEA,OAAO,IAAIzE,gBAAgB,CAAC;MAC1BiB,IAAI,EAAEsD,kBAAkB,CAACtD,IAAI;MAC7BU,WAAW,EAAE4C,kBAAkB,CAAC5C,WAAW;MAC3CZ,KAAK,EAAEA,CAAA,KAAMwD,kBAAkB,CAACC,aAAa,CAAC/C,GAAG,CAACJ,aAAa;IACjE,CAAC,CAAC;EACJ;EAEA,SAASoC,YAAYA,CAACiB,iBAAiB,EAAE;IACvC,IAAI,CAACA,iBAAiB,CAACC,UAAU,EAAE;MACjC,MAAMC,oBAAoB,GAAG1F,OAAO,CAACwF,iBAAiB,CAAC;MACvD,MAAM,IAAIlC,KAAK,CACZ,4CAA2CoC,oBAAqB,GACnE,CAAC;IACH;IAEA,OAAO,IAAInF,eAAe,CAAC;MACzBwB,IAAI,EAAEyD,iBAAiB,CAACzD,IAAI;MAC5BU,WAAW,EAAE+C,iBAAiB,CAAC/C,WAAW;MAC1CK,MAAM,EAAE5C,SAAS,CACfsF,iBAAiB,CAACC,UAAU,EAC3BE,kBAAkB,IAAKA,kBAAkB,CAAC5D,IAAI,EAC9C4D,kBAAkB,KAAM;QACvBlD,WAAW,EAAEkD,kBAAkB,CAAClD,WAAW;QAC3CmD,iBAAiB,EAAED,kBAAkB,CAACC;MACxC,CAAC,CACH;IACF,CAAC,CAAC;EACJ;EAEA,SAASnB,mBAAmBA,CAACoB,wBAAwB,EAAE;IACrD,IAAI,CAACA,wBAAwB,CAACC,WAAW,EAAE;MACzC,MAAMC,2BAA2B,GAAG/F,OAAO,CAAC6F,wBAAwB,CAAC;MACrE,MAAM,IAAIvC,KAAK,CACZ,6CAA4CyC,2BAA4B,GAC3E,CAAC;IACH;IAEA,OAAO,IAAIvF,sBAAsB,CAAC;MAChCuB,IAAI,EAAE8D,wBAAwB,CAAC9D,IAAI;MACnCU,WAAW,EAAEoD,wBAAwB,CAACpD,WAAW;MACjDyC,MAAM,EAAEA,CAAA,KAAMc,qBAAqB,CAACH,wBAAwB,CAACC,WAAW;IAC1E,CAAC,CAAC;EACJ;EAEA,SAASX,gBAAgBA,CAACrD,iBAAiB,EAAE;IAC3C,IAAI,CAACA,iBAAiB,CAACoD,MAAM,EAAE;MAC7B,MAAM,IAAI5B,KAAK,CACZ,wCAAuCtD,OAAO,CAAC8B,iBAAiB,CAAE,GACrE,CAAC;IACH;IAEA,OAAO5B,SAAS,CACd4B,iBAAiB,CAACoD,MAAM,EACvBe,kBAAkB,IAAKA,kBAAkB,CAAClE,IAAI,EAC/CmE,UACF,CAAC;EACH;EAEA,SAASA,UAAUA,CAACD,kBAAkB,EAAE;IACtC,MAAMrC,IAAI,GAAGZ,OAAO,CAACiD,kBAAkB,CAACrC,IAAI,CAAC;IAE7C,IAAI,CAAC5C,YAAY,CAAC4C,IAAI,CAAC,EAAE;MACvB,MAAMc,OAAO,GAAG1E,OAAO,CAAC4D,IAAI,CAAC;MAC7B,MAAM,IAAIN,KAAK,CACZ,oEAAmEoB,OAAQ,GAC9E,CAAC;IACH;IAEA,IAAI,CAACuB,kBAAkB,CAACE,IAAI,EAAE;MAC5B,MAAMC,qBAAqB,GAAGpG,OAAO,CAACiG,kBAAkB,CAAC;MACzD,MAAM,IAAI3C,KAAK,CACZ,4CAA2C8C,qBAAsB,GACpE,CAAC;IACH;IAEA,OAAO;MACL3D,WAAW,EAAEwD,kBAAkB,CAACxD,WAAW;MAC3CmD,iBAAiB,EAAEK,kBAAkB,CAACL,iBAAiB;MACvDhC,IAAI;MACJuC,IAAI,EAAEH,qBAAqB,CAACC,kBAAkB,CAACE,IAAI;IACrD,CAAC;EACH;EAEA,SAASH,qBAAqBA,CAACK,wBAAwB,EAAE;IACvD,OAAOnG,SAAS,CACdmG,wBAAwB,EACvBC,UAAU,IAAKA,UAAU,CAACvE,IAAI,EAC/BwE,eACF,CAAC;EACH;EAEA,SAASA,eAAeA,CAACC,uBAAuB,EAAE;IAChD,MAAM5C,IAAI,GAAGZ,OAAO,CAACwD,uBAAuB,CAAC5C,IAAI,CAAC;IAElD,IAAI,CAAC7C,WAAW,CAAC6C,IAAI,CAAC,EAAE;MACtB,MAAMc,OAAO,GAAG1E,OAAO,CAAC4D,IAAI,CAAC;MAC7B,MAAM,IAAIN,KAAK,CACZ,sEAAqEoB,OAAQ,GAChF,CAAC;IACH;IAEA,MAAM+B,YAAY,GAChBD,uBAAuB,CAACC,YAAY,IAAI,IAAI,GACxCnF,YAAY,CAACnB,UAAU,CAACqG,uBAAuB,CAACC,YAAY,CAAC,EAAE7C,IAAI,CAAC,GACpE8C,SAAS;IACf,OAAO;MACLjE,WAAW,EAAE+D,uBAAuB,CAAC/D,WAAW;MAChDmB,IAAI;MACJ6C,YAAY;MACZb,iBAAiB,EAAEY,uBAAuB,CAACZ;IAC7C,CAAC;EACH;EAEA,SAASpD,cAAcA,CAACmE,sBAAsB,EAAE;IAC9C,IAAI,CAACA,sBAAsB,CAACR,IAAI,EAAE;MAChC,MAAMS,yBAAyB,GAAG5G,OAAO,CAAC2G,sBAAsB,CAAC;MACjE,MAAM,IAAIrD,KAAK,CACZ,gDAA+CsD,yBAA0B,GAC5E,CAAC;IACH;IAEA,IAAI,CAACD,sBAAsB,CAACE,SAAS,EAAE;MACrC,MAAMD,yBAAyB,GAAG5G,OAAO,CAAC2G,sBAAsB,CAAC;MACjE,MAAM,IAAIrD,KAAK,CACZ,qDAAoDsD,yBAA0B,GACjF,CAAC;IACH;IAEA,OAAO,IAAI3F,gBAAgB,CAAC;MAC1Bc,IAAI,EAAE4E,sBAAsB,CAAC5E,IAAI;MACjCU,WAAW,EAAEkE,sBAAsB,CAAClE,WAAW;MAC/CqE,YAAY,EAAEH,sBAAsB,CAACG,YAAY;MACjDD,SAAS,EAAEF,sBAAsB,CAACE,SAAS,CAACE,KAAK,CAAC,CAAC;MACnDZ,IAAI,EAAEH,qBAAqB,CAACW,sBAAsB,CAACR,IAAI;IACzD,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}