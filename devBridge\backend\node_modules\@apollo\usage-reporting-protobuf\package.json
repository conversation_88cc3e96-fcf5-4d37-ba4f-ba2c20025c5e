{"name": "@apollo/usage-reporting-protobuf", "version": "4.1.1", "description": "Protobuf format for Apollo usage reporting", "type": "module", "exports": {".": {"types": {"require": "./generated/cjs/protobuf.d.ts", "default": "./generated/esm/protobuf.d.ts"}, "import": "./generated/esm/protobuf.js", "require": "./generated/cjs/protobuf.js"}}, "main": "generated/cjs/protobuf.js", "module": "generated/esm/protobuf.js", "types": "generated/esm/protobuf.d.ts", "scripts": {"generate": "rm -rf generated && mkdir -p generated/{esm,cjs} && npm run pbjs-cjs && npm run pbjs-esm && npm run pbts", "pbjs-cjs": "apollo-pbjs --target static-module --out generated/cjs/protobuf.cjs --wrap commonjs --force-number --no-from-object src/reports.proto", "pbjs-esm": "apollo-pbjs --target static-module --out generated/esm/protobuf.mjs --es6 --force-number --no-from-object src/reports.proto", "pbts-cjs": "mv generated/cjs/protobuf.{c,}js && apollo-pbts -o generated/cjs/protobuf.d.ts generated/cjs/protobuf.js", "pbts-esm": "mv generated/esm/protobuf.{m,}js && apollo-pbts -o generated/esm/protobuf.d.ts generated/esm/protobuf.js", "pbts": "npm run pbts-cjs && npm run pbts-esm", "update-proto": "curl -sSfo src/reports.proto https://usage-reporting.api.apollographql.com/proto/reports.proto"}, "repository": {"type": "git", "url": "https://github.com/apollographql/apollo-server", "directory": "packages/usage-reporting-protobuf"}, "keywords": ["GraphQL", "Apollo", "Server", "Javascript"], "author": "Apollo <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/apollographql/apollo-server/issues"}, "homepage": "https://github.com/apollographql/apollo-server#readme", "//": "Don't caret this, we want to be explicit about the version of our fork of protobufjs and update it intentionally if we need to.", "dependencies": {"@apollo/protobufjs": "1.2.7"}, "volta": {"extends": "../../package.json"}}