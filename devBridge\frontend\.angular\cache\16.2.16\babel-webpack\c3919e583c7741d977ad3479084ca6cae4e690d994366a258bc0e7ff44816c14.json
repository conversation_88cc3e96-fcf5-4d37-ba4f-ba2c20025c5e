{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { isNameContinue, isNameStart } from '../language/characterClasses.mjs';\n/**\n * Upholds the spec rules about naming.\n */\n\nexport function assertName(name) {\n  name != null || devAssert(false, 'Must provide name.');\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n  if (name.length === 0) {\n    throw new GraphQLError('Expected name to be a non-empty string.');\n  }\n  for (let i = 1; i < name.length; ++i) {\n    if (!isNameContinue(name.charCodeAt(i))) {\n      throw new GraphQLError(`Names must only contain [_a-zA-Z0-9] but \"${name}\" does not.`);\n    }\n  }\n  if (!isNameStart(name.charCodeAt(0))) {\n    throw new GraphQLError(`Names must start with [_a-zA-Z] but \"${name}\" does not.`);\n  }\n  return name;\n}\n/**\n * Upholds the spec rules about naming enum values.\n *\n * @internal\n */\n\nexport function assertEnumValueName(name) {\n  if (name === 'true' || name === 'false' || name === 'null') {\n    throw new GraphQLError(`Enum values cannot be named: ${name}`);\n  }\n  return assertName(name);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}