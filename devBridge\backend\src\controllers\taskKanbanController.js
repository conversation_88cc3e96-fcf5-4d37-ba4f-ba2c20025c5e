const Task = require('../models/Task');
const { Team } = require('../models/Team');
const TeamHistory = require('../models/TeamHistory');
const { logger } = require('../utils/logger');

// ==================== KANBAN BOARD ====================

// Obtenir le tableau Kanban d'une équipe
exports.getKanbanBoard = async (req, res) => {
  try {
    const { teamId } = req.params;
    const { assignedTo, labels, category, includeArchived = false } = req.query;
    const userId = req.user.id;

    // Vérifier l'accès à l'équipe
    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.isMember(userId) && !team.isPublic) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès aux tâches de cette équipe" 
      });
    }

    // Construire les filtres
    const filters = {
      includeArchived: includeArchived === 'true',
      assignedTo: assignedTo ? assignedTo.split(',') : undefined,
      labels: labels ? labels.split(',') : undefined,
      category
    };

    // Récupérer les tâches organisées par statut
    const tasks = await Task.getKanbanBoard(teamId, filters);

    // Organiser par colonnes Kanban
    const kanbanBoard = {
      backlog: [],
      todo: [],
      'in-progress': [],
      review: [],
      testing: [],
      done: [],
      archived: []
    };

    tasks.forEach(task => {
      if (kanbanBoard[task.status]) {
        kanbanBoard[task.status].push(task);
      }
    });

    // Statistiques du board
    const stats = {
      total: tasks.length,
      byStatus: Object.keys(kanbanBoard).reduce((acc, status) => {
        acc[status] = kanbanBoard[status].length;
        return acc;
      }, {}),
      overdue: tasks.filter(task => task.isOverdue).length,
      blocked: tasks.filter(task => task.isBlocked).length
    };

    res.json({
      teamId,
      teamName: team.name,
      kanbanBoard,
      stats,
      filters
    });
  } catch (error) {
    logger.error('Erreur récupération Kanban:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération du tableau Kanban", 
      error: error.message 
    });
  }
};

// Déplacer une tâche dans le Kanban (drag & drop)
exports.moveTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { newStatus, newPosition, oldStatus } = req.body;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour modifier cette tâche" 
      });
    }

    // Mettre à jour le statut et la position
    if (newStatus && newStatus !== task.status) {
      await task.changeStatus(newStatus, userId);
    }

    if (newPosition !== undefined) {
      await task.updatePosition(newPosition);
    }

    // Réorganiser les positions des autres tâches si nécessaire
    if (newStatus !== oldStatus) {
      await reorderTasksInColumn(task.teamId, newStatus, taskId, newPosition);
      if (oldStatus) {
        await reorderTasksInColumn(task.teamId, oldStatus);
      }
    }

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      'task_moved',
      `Tâche "${task.title}" déplacée de ${oldStatus || 'unknown'} vers ${newStatus}`,
      {
        affectedUser: userId,
        previousData: { status: oldStatus, position: task.position },
        newData: { status: newStatus, position: newPosition },
        category: 'task'
      }
    );

    await task.populate([
      { path: 'assignedTo', select: 'fullName email profileImage' },
      { path: 'createdBy', select: 'fullName email profileImage' }
    ]);

    logger.info(`Tâche ${task.title} déplacée par ${req.user.fullName}`);

    res.json({
      message: "Tâche déplacée avec succès",
      task
    });
  } catch (error) {
    logger.error('Erreur déplacement tâche:', error);
    res.status(500).json({ 
      message: "Erreur lors du déplacement de la tâche", 
      error: error.message 
    });
  }
};

// Réorganiser les tâches dans une colonne
async function reorderTasksInColumn(teamId, status, excludeTaskId = null, insertPosition = null) {
  const tasks = await Task.find({ 
    teamId, 
    status, 
    isArchived: false,
    ...(excludeTaskId && { _id: { $ne: excludeTaskId } })
  }).sort({ position: 1 });

  // Réassigner les positions
  for (let i = 0; i < tasks.length; i++) {
    const newPosition = insertPosition !== null && i >= insertPosition ? i + 1 : i;
    if (tasks[i].position !== newPosition) {
      tasks[i].position = newPosition;
      await tasks[i].save();
    }
  }
}

// Créer une tâche avec position Kanban
exports.createTaskInColumn = async (req, res) => {
  try {
    const { teamId } = req.params;
    const { status = 'todo', position } = req.body;
    const userId = req.user.id;

    // Vérifier l'accès à l'équipe
    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour créer des tâches dans cette équipe" 
      });
    }

    // Déterminer la position si non spécifiée
    let taskPosition = position;
    if (taskPosition === undefined) {
      const lastTask = await Task.findOne({ 
        teamId, 
        status, 
        isArchived: false 
      }).sort({ position: -1 });
      taskPosition = lastTask ? lastTask.position + 1 : 0;
    }

    // Créer la tâche
    const taskData = {
      ...req.body,
      teamId,
      status,
      position: taskPosition,
      createdBy: userId,
      assignedTo: req.body.assignedTo || [userId]
    };

    const task = new Task(taskData);
    await task.save();

    // Réorganiser les autres tâches si nécessaire
    if (position !== undefined) {
      await reorderTasksInColumn(teamId, status, task._id, position);
    }

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      userId,
      'task_created',
      `Tâche "${task.title}" créée`,
      {
        affectedUser: userId,
        newData: { taskId: task._id, status, position: taskPosition },
        category: 'task'
      }
    );

    await task.populate([
      { path: 'assignedTo', select: 'fullName email profileImage' },
      { path: 'createdBy', select: 'fullName email profileImage' }
    ]);

    logger.info(`Tâche ${task.title} créée dans l'équipe ${team.name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Tâche créée avec succès",
      task
    });
  } catch (error) {
    logger.error('Erreur création tâche Kanban:', error);
    res.status(500).json({ 
      message: "Erreur lors de la création de la tâche", 
      error: error.message 
    });
  }
};

// Archiver/désarchiver une tâche
exports.toggleArchiveTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.hasModeratorRights(userId) && !task.assignedTo.includes(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour archiver cette tâche" 
      });
    }

    // Basculer l'état d'archivage
    if (task.isArchived) {
      await task.unarchive();
    } else {
      await task.archive(userId);
    }

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      task.isArchived ? 'task_archived' : 'task_unarchived',
      `Tâche "${task.title}" ${task.isArchived ? 'archivée' : 'désarchivée'}`,
      {
        affectedUser: userId,
        newData: { isArchived: task.isArchived },
        category: 'task'
      }
    );

    logger.info(`Tâche ${task.title} ${task.isArchived ? 'archivée' : 'désarchivée'} par ${req.user.fullName}`);

    res.json({
      message: `Tâche ${task.isArchived ? 'archivée' : 'désarchivée'} avec succès`,
      task
    });
  } catch (error) {
    logger.error('Erreur archivage tâche:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'archivage de la tâche", 
      error: error.message 
    });
  }
};

// Obtenir les statistiques des tâches pour une équipe
exports.getTaskStatistics = async (req, res) => {
  try {
    const { teamId } = req.params;
    const { period = 30 } = req.query;
    const userId = req.user.id;

    // Vérifier l'accès à l'équipe
    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès aux statistiques de cette équipe" 
      });
    }

    // Récupérer les statistiques
    const stats = await Task.getTaskStatistics(teamId, parseInt(period));

    // Statistiques supplémentaires
    const totalTasks = await Task.countDocuments({ teamId, isArchived: false });
    const overdueTasks = await Task.countDocuments({ 
      teamId, 
      isArchived: false,
      dueDate: { $lt: new Date() },
      status: { $ne: 'done' }
    });

    res.json({
      teamId,
      teamName: team.name,
      period: parseInt(period),
      statistics: {
        byStatus: stats,
        total: totalTasks,
        overdue: overdueTasks
      }
    });
  } catch (error) {
    logger.error('Erreur statistiques tâches:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des statistiques", 
      error: error.message 
    });
  }
};

module.exports = {
  getKanbanBoard: exports.getKanbanBoard,
  moveTask: exports.moveTask,
  createTaskInColumn: exports.createTaskInColumn,
  toggleArchiveTask: exports.toggleArchiveTask,
  getTaskStatistics: exports.getTaskStatistics
};
