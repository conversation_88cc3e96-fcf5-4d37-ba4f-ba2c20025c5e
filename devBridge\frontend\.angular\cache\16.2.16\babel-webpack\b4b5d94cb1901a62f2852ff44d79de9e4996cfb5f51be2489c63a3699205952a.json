{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setHours} function options.\n */\n\n/**\n * @name setHours\n * @category Hour Helpers\n * @summary Set the hours to the given date.\n *\n * @description\n * Set the hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param hours - The hours of the new date\n * @param options - An object with options\n *\n * @returns The new date with the hours set\n *\n * @example\n * // Set 4 hours to 1 September 2014 11:30:00:\n * const result = setHours(new Date(2014, 8, 1, 11, 30), 4)\n * //=> Mon Sep 01 2014 04:30:00\n */\nexport function setHours(date, hours, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(hours);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setHours;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}