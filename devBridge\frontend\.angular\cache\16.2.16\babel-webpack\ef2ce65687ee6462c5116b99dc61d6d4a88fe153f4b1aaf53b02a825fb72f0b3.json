{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function nodeStreamIterator(stream) {\n  var cleanup = null;\n  var error = null;\n  var done = false;\n  var data = [];\n  var waiting = [];\n  function onData(chunk) {\n    if (error) return;\n    if (waiting.length) {\n      var shiftedArr = waiting.shift();\n      if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n        return shiftedArr[0]({\n          value: chunk,\n          done: false\n        });\n      }\n    }\n    data.push(chunk);\n  }\n  function onError(err) {\n    error = err;\n    var all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[1](err);\n    });\n    !cleanup || cleanup();\n  }\n  function onEnd() {\n    done = true;\n    var all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[0]({\n        value: undefined,\n        done: true\n      });\n    });\n    !cleanup || cleanup();\n  }\n  cleanup = function () {\n    cleanup = null;\n    stream.removeListener(\"data\", onData);\n    stream.removeListener(\"error\", onError);\n    stream.removeListener(\"end\", onEnd);\n    stream.removeListener(\"finish\", onEnd);\n    stream.removeListener(\"close\", onEnd);\n  };\n  stream.on(\"data\", onData);\n  stream.on(\"error\", onError);\n  stream.on(\"end\", onEnd);\n  stream.on(\"finish\", onEnd);\n  stream.on(\"close\", onEnd);\n  function getNext() {\n    return new Promise(function (resolve, reject) {\n      if (error) return reject(error);\n      if (data.length) return resolve({\n        value: data.shift(),\n        done: false\n      });\n      if (done) return resolve({\n        value: undefined,\n        done: true\n      });\n      waiting.push([resolve, reject]);\n    });\n  }\n  var iterator = {\n    next: function () {\n      return getNext();\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}\n//# sourceMappingURL=nodeStream.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}