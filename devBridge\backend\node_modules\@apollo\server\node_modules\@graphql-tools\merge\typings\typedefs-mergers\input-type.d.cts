import { Config } from './merge-typedefs.cjs';
import { InputObjectTypeDefinitionNode, InputObjectTypeExtensionNode, DirectiveDefinitionNode } from 'graphql';
export declare function mergeInputType(node: InputObjectTypeDefinitionNode | InputObjectTypeExtensionNode, existingNode: InputObjectTypeDefinitionNode | InputObjectTypeExtensionNode, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): InputObjectTypeDefinitionNode | InputObjectTypeExtensionNode;
