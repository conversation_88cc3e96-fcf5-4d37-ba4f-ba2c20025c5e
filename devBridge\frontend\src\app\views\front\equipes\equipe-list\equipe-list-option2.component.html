<!-- Option 2: Design en Grille Compacte avec Bouton Tasks Proéminent -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 dark:from-gray-800/20 dark:to-gray-900/20"></div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header épuré -->
    <div class="mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Mes Équipes
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              <PERSON><PERSON><PERSON> et organisez vos équipes de travail
            </p>
          </div>
          <div class="flex items-center gap-3">
            <button
              (click)="refreshEquipes()"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              [disabled]="loading"
            >
              <i class="fas fa-sync-alt mr-2" [class.animate-spin]="loading"></i>
              <span class="hidden sm:inline">Rafraîchir</span>
            </button>
            <button
              (click)="navigateToAddEquipe()"
              class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors shadow-sm"
            >
              <i class="fas fa-plus mr-2"></i>
              Nouvelle Équipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading, Error, Empty states... -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-16">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p class="mt-4 text-gray-600 dark:text-gray-400">Chargement des équipes...</p>
    </div>

    <!-- Teams Grid - Option 2: Cartes Compactes -->
    <div *ngIf="!loading && equipes.length > 0" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        *ngFor="let equipe of equipes"
        class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 overflow-hidden group"
      >
        <!-- Header avec gradient -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 text-white">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <i class="fas fa-users text-lg"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg">{{ equipe.name }}</h3>
                <p class="text-blue-100 text-sm">{{ equipe.members?.length || 0 }} membres</p>
              </div>
            </div>
            <!-- Badge de tâches -->
            <div class="bg-white/20 rounded-full px-3 py-1 text-sm font-medium">
              {{ getTaskCount(equipe._id || '') }} tâches
            </div>
          </div>
        </div>

        <!-- Contenu -->
        <div class="p-4">
          <!-- Description -->
          <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
            {{
              equipe.description && equipe.description.length > 100
                ? (equipe.description | slice : 0 : 100) + "..."
                : equipe.description || "Aucune description disponible"
            }}
          </p>

          <!-- Statistiques -->
          <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
            <span>Progression</span>
            <span class="font-medium">{{ getTaskStatus(equipe._id || '').percentage }}% complété</span>
          </div>

          <!-- Barre de progression -->
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
            <div
              class="bg-green-500 h-2 rounded-full transition-all duration-500"
              [style.width.%]="getTaskStatus(equipe._id || '').percentage">
            </div>
          </div>

          <!-- Bouton Tasks TRÈS VISIBLE -->
          <button
            (click)="equipe._id && navigateToTasks(equipe._id)"
            class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl flex items-center justify-center group/tasks mb-3"
          >
            <i class="fas fa-tasks mr-2 text-lg group-hover/tasks:scale-110 transition-transform"></i>
            <span class="text-lg">GÉRER TASKS</span>
            <i class="fas fa-arrow-right ml-2 group-hover/tasks:translate-x-1 transition-transform"></i>
          </button>

          <!-- Actions secondaires -->
          <div class="flex items-center justify-between">
            <button
              (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
              class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
            >
              <i class="fas fa-eye mr-1"></i>
              Voir détails
            </button>

            <div class="flex items-center space-x-2">
              <button
                (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all"
                title="Modifier"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button
                (click)="equipe._id && deleteEquipe(equipe._id)"
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all"
                title="Supprimer"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
