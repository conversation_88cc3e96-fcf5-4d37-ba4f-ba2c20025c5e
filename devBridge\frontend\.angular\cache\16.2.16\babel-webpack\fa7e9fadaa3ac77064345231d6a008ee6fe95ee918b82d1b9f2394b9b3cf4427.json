{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused variables\n *\n * A GraphQL operation is only valid if all variables defined by an operation\n * are used, either directly or within a spread fragment.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variables-Used\n */\nexport function NoUnusedVariablesRule(context) {\n  let variableDefs = [];\n  return {\n    OperationDefinition: {\n      enter() {\n        variableDefs = [];\n      },\n      leave(operation) {\n        const variableNameUsed = Object.create(null);\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node\n        } of usages) {\n          variableNameUsed[node.name.value] = true;\n        }\n        for (const variableDef of variableDefs) {\n          const variableName = variableDef.variable.name.value;\n          if (variableNameUsed[variableName] !== true) {\n            context.reportError(new GraphQLError(operation.name ? `Variable \"$${variableName}\" is never used in operation \"${operation.name.value}\".` : `Variable \"$${variableName}\" is never used.`, {\n              nodes: variableDef\n            }));\n          }\n        }\n      }\n    },\n    VariableDefinition(def) {\n      variableDefs.push(def);\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}