import { GraphQLInputType, ArgumentNode, VariableDefinitionNode } from 'graphql';
export declare function updateArgument(argumentNodes: Record<string, ArgumentNode>, variableDefinitionsMap: Record<string, VariableDefinitionNode>, variableValues: Record<string, any>, argName: string, varName: string, type: GraphQLInputType, value: any): void;
export declare function createVariableNameGenerator(variableDefinitionMap: Record<string, VariableDefinitionNode>): (argName: string) => string;
