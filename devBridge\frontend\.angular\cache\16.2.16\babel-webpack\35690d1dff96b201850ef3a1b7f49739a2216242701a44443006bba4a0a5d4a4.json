{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No undefined variables\n *\n * A GraphQL operation is only valid if all variables encountered, both directly\n * and via fragment spreads, are defined by that operation.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Uses-Defined\n */\nexport function NoUndefinedVariablesRule(context) {\n  let variableNameDefined = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        variableNameDefined = Object.create(null);\n      },\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node\n        } of usages) {\n          const varName = node.name.value;\n          if (variableNameDefined[varName] !== true) {\n            context.reportError(new GraphQLError(operation.name ? `Variable \"$${varName}\" is not defined by operation \"${operation.name.value}\".` : `Variable \"$${varName}\" is not defined.`, {\n              nodes: [node, operation]\n            }));\n          }\n        }\n      }\n    },\n    VariableDefinition(node) {\n      variableNameDefined[node.variable.name.value] = true;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}