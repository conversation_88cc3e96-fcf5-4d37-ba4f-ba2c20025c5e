{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/task.service\";\nimport * as i3 from \"../../../services/equipe.service\";\nimport * as i4 from \"../../../services/authuser.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../components/kanban-board/kanban-board.component\";\nimport * as i9 from \"../../../components/task-ai-assistant/task-ai-assistant.component\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/menu\";\nimport * as i15 from \"@angular/material/tooltip\";\nfunction TasksComponent_div_15_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"span\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4, \"En retard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stats_r9 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stats_r9.overdue);\n  }\n}\nfunction TasksComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵtext(5, \"Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"span\", 25);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 26);\n    i0.ɵɵtext(10, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"span\", 25);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 26);\n    i0.ɵɵtext(15, \"En cours\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 24)(17, \"span\", 25);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 26);\n    i0.ɵɵtext(20, \"Termin\\u00E9\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, TasksComponent_div_15_div_21_Template, 5, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stats_r9 = ctx.ngIf;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stats_r9.total);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.todo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.inProgress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.done);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", stats_r9.overdue > 0);\n  }\n}\nfunction TasksComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"app-kanban-board\", 30);\n    i0.ɵɵlistener(\"taskSelected\", function TasksComponent_div_43_Template_app_kanban_board_taskSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onTaskSelected($event));\n    })(\"taskCreated\", function TasksComponent_div_43_Template_app_kanban_board_taskCreated_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onTaskCreated($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"teamId\", ctx_r2.teamId)(\"filters\", ctx_r2.kanbanFilters);\n  }\n}\nfunction TasksComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"mat-card\", 32)(2, \"mat-card-content\")(3, \"div\", 33)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"list\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Vue Liste\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Cette vue sera bient\\u00F4t disponible\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TasksComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 32)(2, \"mat-card-content\")(3, \"div\", 33)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Vue Calendrier\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Cette vue sera bient\\u00F4t disponible\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TasksComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"mat-spinner\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des t\\u00E2ches...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-icon\", 38);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Erreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_47_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.refresh());\n    });\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.error);\n  }\n}\nfunction TasksComponent_div_48_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.selectedTask.description, \" \");\n  }\n}\nfunction TasksComponent_div_48_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"Cat\\u00E9gorie:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.selectedTask.category);\n  }\n}\nfunction TasksComponent_div_48_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"\\u00C9ch\\u00E9ance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r19.selectedTask.dueDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction TasksComponent_div_48_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"Estimation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r20.selectedTask.estimatedHours, \"h\");\n  }\n}\nfunction TasksComponent_div_48_div_26_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r24 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", label_r24.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", label_r24.name, \" \");\n  }\n}\nfunction TasksComponent_div_48_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Labels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵtemplate(4, TasksComponent_div_48_div_26_span_4_Template, 2, 3, \"span\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.selectedTask.labels);\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 68);\n  }\n  if (rf & 2) {\n    const assignee_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", assignee_r26.profileImage, i0.ɵɵsanitizeUrl)(\"alt\", assignee_r26.fullName || assignee_r26);\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (assignee_r26.fullName || assignee_r26).charAt(0).toUpperCase(), \" \");\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtemplate(2, TasksComponent_div_48_div_27_div_4_img_2_Template, 1, 2, \"img\", 65);\n    i0.ɵɵtemplate(3, TasksComponent_div_48_div_27_div_4_span_3_Template, 2, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 67);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const assignee_r26 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", assignee_r26.profileImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !assignee_r26.profileImage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(assignee_r26.fullName || assignee_r26);\n  }\n}\nfunction TasksComponent_div_48_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Assign\\u00E9 \\u00E0:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61);\n    i0.ɵɵtemplate(4, TasksComponent_div_48_div_27_div_4_Template, 6, 3, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.selectedTask.assignedTo);\n  }\n}\nfunction TasksComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"h3\");\n    i0.ɵɵtext(3, \"D\\u00E9tails de la t\\u00E2che\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_48_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.closeTaskDetails());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"div\", 44)(9, \"h4\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TasksComponent_div_48_p_11_Template, 2, 1, \"p\", 45);\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"div\", 47)(14, \"span\", 48);\n    i0.ɵɵtext(15, \"Statut:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 47)(19, \"span\", 48);\n    i0.ɵɵtext(20, \"Priorit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, TasksComponent_div_48_div_23_Template, 5, 1, \"div\", 49);\n    i0.ɵɵtemplate(24, TasksComponent_div_48_div_24_Template, 6, 4, \"div\", 49);\n    i0.ɵɵtemplate(25, TasksComponent_div_48_div_25_Template, 5, 1, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, TasksComponent_div_48_div_26_Template, 5, 1, \"div\", 50);\n    i0.ɵɵtemplate(27, TasksComponent_div_48_div_27_Template, 5, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 52)(29, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_48_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.openAIAssistant());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Assistant IA \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r7.showTaskDetails);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate1(\"value status-\", ctx_r7.selectedTask.status, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"value priority-\", ctx_r7.selectedTask.priority, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.priority);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.category);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.dueDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.estimatedHours);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.labels && ctx_r7.selectedTask.labels.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.assignedTo && ctx_r7.selectedTask.assignedTo.length > 0);\n  }\n}\nfunction TasksComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 41)(2, \"h3\");\n    i0.ɵɵtext(3, \"Assistant IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_49_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.closeAIAssistant());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"app-task-ai-assistant\", 70);\n    i0.ɵɵlistener(\"taskUpdated\", function TasksComponent_div_49_Template_app_task_ai_assistant_taskUpdated_8_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onTaskUpdated($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r8.showAIAssistant);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"task\", ctx_r8.selectedTask);\n  }\n}\nexport class TasksComponent {\n  constructor(route, router, taskService, equipeService, authService, snackBar, dialog) {\n    this.route = route;\n    this.router = router;\n    this.taskService = taskService;\n    this.equipeService = equipeService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.destroy$ = new Subject();\n    this.team = null;\n    // Vue actuelle\n    this.currentView = 'kanban';\n    // Données Kanban\n    this.kanbanData = null;\n    this.kanbanFilters = {};\n    // Statistiques\n    this.taskStats = null;\n    // États\n    this.loading = false;\n    this.error = null;\n    // Tâche sélectionnée\n    this.selectedTask = null;\n    this.showTaskDetails = false;\n    this.showAIAssistant = false;\n    this.currentUser = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.teamId = params['teamId'];\n      if (this.teamId) {\n        this.loadTeamData();\n        this.loadKanbanBoard();\n        this.loadTaskStatistics();\n      } else {\n        this.error = 'ID d\\'équipe manquant';\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  // Charger les données de l'équipe\n  loadTeamData() {\n    this.equipeService.getEquipeById(this.teamId).subscribe({\n      next: team => {\n        this.team = team;\n        // Vérifier les permissions\n        if (!this.hasTeamAccess()) {\n          this.error = 'Vous n\\'avez pas accès aux tâches de cette équipe';\n          return;\n        }\n      },\n      error: error => {\n        console.error('Erreur chargement équipe:', error);\n        this.error = 'Équipe non trouvée';\n      }\n    });\n  }\n  // Charger le tableau Kanban\n  loadKanbanBoard() {\n    this.loading = true;\n    this.error = null;\n    this.taskService.getKanbanBoard(this.teamId, this.kanbanFilters).subscribe({\n      next: data => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Erreur chargement Kanban:', error);\n        this.error = 'Erreur lors du chargement des tâches';\n        this.loading = false;\n        this.snackBar.open('Erreur lors du chargement des tâches', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Charger les statistiques\n  loadTaskStatistics() {\n    this.taskService.getTaskStatistics(this.teamId).subscribe({\n      next: stats => {\n        this.taskStats = stats;\n      },\n      error: error => {\n        console.error('Erreur chargement statistiques:', error);\n      }\n    });\n  }\n  // Vérifier l'accès à l'équipe\n  hasTeamAccess() {\n    if (!this.team || !this.currentUser) return false;\n    // Admin système a accès à tout\n    if (this.currentUser.role === 'admin') return true;\n    // Vérifier si l'utilisateur est membre de l'équipe\n    return this.equipeService.isTeamMember(this.team, this.currentUser.id);\n  }\n  // Changer de vue\n  switchView(view) {\n    this.currentView = view;\n    if (view === 'kanban') {\n      this.loadKanbanBoard();\n    }\n    // Ajouter d'autres vues plus tard\n  }\n  // Gérer la sélection d'une tâche\n  onTaskSelected(task) {\n    this.selectedTask = task;\n    this.showTaskDetails = true;\n  }\n  // Gérer la création d'une tâche\n  onTaskCreated(task) {\n    this.snackBar.open('Tâche créée avec succès', 'Fermer', {\n      duration: 2000\n    });\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n  // Gérer la mise à jour d'une tâche\n  onTaskUpdated(task) {\n    this.selectedTask = task;\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n  // Appliquer des filtres\n  applyFilters(filters) {\n    this.kanbanFilters = {\n      ...filters\n    };\n    this.loadKanbanBoard();\n  }\n  // Réinitialiser les filtres\n  resetFilters() {\n    this.kanbanFilters = {};\n    this.loadKanbanBoard();\n  }\n  // Actualiser les données\n  refresh() {\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n  // Ouvrir l'assistant IA\n  openAIAssistant() {\n    if (this.selectedTask) {\n      this.showAIAssistant = true;\n    } else {\n      this.snackBar.open('Veuillez sélectionner une tâche', 'Fermer', {\n        duration: 2000\n      });\n    }\n  }\n  // Fermer les panneaux\n  closeTaskDetails() {\n    this.showTaskDetails = false;\n    this.selectedTask = null;\n  }\n  closeAIAssistant() {\n    this.showAIAssistant = false;\n  }\n  // Naviguer vers l'équipe\n  navigateToTeam() {\n    this.router.navigate(['/equipes/detail', this.teamId]);\n  }\n  // Obtenir le nom de l'équipe\n  getTeamName() {\n    return this.team?.name || 'Équipe';\n  }\n  // Vérifier si l'utilisateur peut créer des tâches\n  canCreateTasks() {\n    if (!this.team || !this.currentUser) return false;\n    // Admin système peut tout faire\n    if (this.currentUser.role === 'admin') return true;\n    // Membres de l'équipe peuvent créer des tâches\n    return this.hasTeamAccess();\n  }\n  // Vérifier si l'utilisateur peut modifier des tâches\n  canEditTasks() {\n    return this.canCreateTasks();\n  }\n  // Obtenir les statistiques rapides\n  getQuickStats() {\n    if (!this.kanbanData) return null;\n    return {\n      total: this.kanbanData.stats.total,\n      todo: this.kanbanData.stats.byStatus['todo'] || 0,\n      inProgress: this.kanbanData.stats.byStatus['in-progress'] || 0,\n      done: this.kanbanData.stats.byStatus['done'] || 0,\n      overdue: this.kanbanData.stats.overdue,\n      blocked: this.kanbanData.stats.blocked\n    };\n  }\n  // Obtenir la couleur du statut de l'équipe\n  getTeamStatusColor() {\n    if (!this.team) return '#6b7280';\n    switch (this.team.status) {\n      case 'active':\n        return '#10b981';\n      case 'inactive':\n        return '#f59e0b';\n      case 'archived':\n        return '#6b7280';\n      default:\n        return '#6b7280';\n    }\n  }\n  static {\n    this.ɵfac = function TasksComponent_Factory(t) {\n      return new (t || TasksComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TaskService), i0.ɵɵdirectiveInject(i3.EquipeService), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TasksComponent,\n      selectors: [[\"app-tasks\"]],\n      decls: 51,\n      vars: 22,\n      consts: [[1, \"tasks-container\"], [1, \"tasks-header\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Retour \\u00E0 l'\\u00E9quipe\", 3, \"click\"], [1, \"team-info\"], [1, \"team-name\"], [1, \"team-status\"], [1, \"header-actions\"], [\"class\", \"quick-stats\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Actualiser\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Assistant IA\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Changer de vue\", 3, \"matMenuTriggerFor\"], [\"viewMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"tasks-content\"], [\"class\", \"kanban-view\", 4, \"ngIf\"], [\"class\", \"list-view\", 4, \"ngIf\"], [\"class\", \"calendar-view\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"task-details-panel\", 3, \"open\", 4, \"ngIf\"], [\"class\", \"ai-assistant-panel\", 3, \"open\", 4, \"ngIf\"], [1, \"panels-overlay\", 3, \"click\"], [1, \"quick-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"stat-value\", \"overdue\"], [1, \"kanban-view\"], [3, \"teamId\", \"filters\", \"taskSelected\", \"taskCreated\"], [1, \"list-view\"], [1, \"coming-soon\"], [1, \"coming-soon-content\"], [1, \"calendar-view\"], [1, \"loading-overlay\"], [\"diameter\", \"40\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"task-details-panel\"], [1, \"panel-header\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"panel-content\"], [1, \"task-info\"], [\"class\", \"task-description\", 4, \"ngIf\"], [1, \"task-meta\"], [1, \"meta-item\"], [1, \"label\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"class\", \"task-labels\", 4, \"ngIf\"], [\"class\", \"task-assignees\", 4, \"ngIf\"], [1, \"task-actions\"], [1, \"task-description\"], [1, \"value\"], [1, \"task-labels\"], [1, \"label-title\"], [1, \"labels-list\"], [\"class\", \"task-label\", 3, \"background-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-label\"], [1, \"task-assignees\"], [1, \"assignees-list\"], [\"class\", \"assignee\", 4, \"ngFor\", \"ngForOf\"], [1, \"assignee\"], [1, \"assignee-avatar\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"assignee-name\"], [3, \"src\", \"alt\"], [1, \"ai-assistant-panel\"], [3, \"task\", \"taskUpdated\"]],\n      template: function TasksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_3_listener() {\n            return ctx.navigateToTeam();\n          });\n          i0.ɵɵelementStart(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 7);\n          i0.ɵɵtemplate(15, TasksComponent_div_15_Template, 22, 5, \"div\", 8);\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_16_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_19_listener() {\n            return ctx.openAIAssistant();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"psychology\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 11)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"view_module\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"mat-menu\", null, 12)(27, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_27_listener() {\n            return ctx.switchView(\"kanban\");\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"view_kanban\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"Kanban\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_32_listener() {\n            return ctx.switchView(\"list\");\n          });\n          i0.ɵɵelementStart(33, \"mat-icon\");\n          i0.ɵɵtext(34, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36, \"Liste\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_37_listener() {\n            return ctx.switchView(\"calendar\");\n          });\n          i0.ɵɵelementStart(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"span\");\n          i0.ɵɵtext(41, \"Calendrier\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"div\", 14);\n          i0.ɵɵtemplate(43, TasksComponent_div_43_Template, 2, 2, \"div\", 15);\n          i0.ɵɵtemplate(44, TasksComponent_div_44_Template, 10, 0, \"div\", 16);\n          i0.ɵɵtemplate(45, TasksComponent_div_45_Template, 10, 0, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, TasksComponent_div_46_Template, 4, 0, \"div\", 18);\n          i0.ɵɵtemplate(47, TasksComponent_div_47_Template, 9, 1, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, TasksComponent_div_48_Template, 33, 17, \"div\", 20);\n          i0.ɵɵtemplate(49, TasksComponent_div_49_Template, 9, 3, \"div\", 21);\n          i0.ɵɵelementStart(50, \"div\", 22);\n          i0.ɵɵlistener(\"click\", function TasksComponent_Template_div_click_50_listener() {\n            ctx.closeTaskDetails();\n            return ctx.closeAIAssistant();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(26);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getTeamName());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", ctx.getTeamStatusColor());\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate((ctx.team == null ? null : ctx.team.status) || \"active\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.getQuickStats());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedTask);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.currentView === \"kanban\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.currentView === \"list\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.currentView === \"calendar\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"kanban\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"list\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"calendar\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTask);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTask && ctx.showAIAssistant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.showTaskDetails || ctx.showAIAssistant);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.KanbanBoardComponent, i9.TaskAiAssistantComponent, i10.MatButton, i10.MatIconButton, i11.MatCard, i11.MatCardContent, i12.MatIcon, i13.MatProgressSpinner, i14.MatMenu, i14.MatMenuItem, i14.MatMenuTrigger, i15.MatTooltip, i7.DatePipe],\n      styles: [\".tasks-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: #f8fafc;\\n  position: relative;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e2e8f0;\\n  padding: 1rem 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  z-index: 10;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  margin-top: 0.25rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  width: 0.75rem;\\n  height: 0.75rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  padding: 0 1rem;\\n  border-left: 1px solid #e2e8f0;\\n  border-right: 1px solid #e2e8f0;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value.overdue[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  margin-top: 0.125rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .kanban-view[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%] {\\n  height: 100%;\\n  padding: 1.5rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #64748b;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  z-index: 100;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  margin: 0;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #64748b;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  margin-bottom: 1rem;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #1e293b;\\n}\\n.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.task-details-panel[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -400px;\\n  width: 400px;\\n  height: 100vh;\\n  background: white;\\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\\n  transition: right 0.3s ease;\\n  z-index: 1000;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.task-details-panel.open[_ngcontent-%COMP%], .ai-assistant-panel.open[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e2e8f0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8fafc;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem;\\n}\\n\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n  color: #64748b;\\n  line-height: 1.6;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #64748b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-todo[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-in-progress[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-review[_ngcontent-%COMP%] {\\n  color: #8b5cf6;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-testing[_ngcontent-%COMP%] {\\n  color: #06b6d4;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-done[_ngcontent-%COMP%] {\\n  color: #10b981;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-lowest[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-low[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-medium[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-high[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-highest[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-critical[_ngcontent-%COMP%] {\\n  color: #991b1b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .label-title[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .label-title[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  color: #64748b;\\n  margin-bottom: 0.5rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: white;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #64748b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-name[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1e293b;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e2e8f0;\\n}\\n.task-details-panel[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.ai-assistant-panel[_ngcontent-%COMP%] {\\n  width: 500px;\\n  right: -500px;\\n}\\n\\n.panels-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.3);\\n  z-index: 999;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.panels-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.mat-menu-item.active[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #3b82f6;\\n}\\n.mat-menu-item.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n}\\n\\n@media (max-width: 768px) {\\n  .tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n  .tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n    padding: 0;\\n    border: none;\\n  }\\n  .task-details-panel[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%] {\\n    width: 100vw;\\n    right: -100vw;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stats_r9", "overdue", "ɵɵtemplate", "TasksComponent_div_15_div_21_Template", "total", "todo", "inProgress", "done", "ɵɵproperty", "ɵɵlistener", "TasksComponent_div_43_Template_app_kanban_board_taskSelected_1_listener", "$event", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "onTaskSelected", "TasksComponent_div_43_Template_app_kanban_board_taskCreated_1_listener", "ctx_r14", "onTaskCreated", "ctx_r2", "teamId", "kanbanFilters", "ɵɵelement", "TasksComponent_div_47_Template_button_click_7_listener", "_r16", "ctx_r15", "refresh", "ctx_r6", "error", "ɵɵtextInterpolate1", "ctx_r17", "selectedTask", "description", "ctx_r18", "category", "ɵɵpipeBind2", "ctx_r19", "dueDate", "ctx_r20", "estimatedHours", "ɵɵstyleProp", "label_r24", "color", "name", "TasksComponent_div_48_div_26_span_4_Template", "ctx_r21", "labels", "assignee_r26", "profileImage", "ɵɵsanitizeUrl", "fullName", "char<PERSON>t", "toUpperCase", "TasksComponent_div_48_div_27_div_4_img_2_Template", "TasksComponent_div_48_div_27_div_4_span_3_Template", "TasksComponent_div_48_div_27_div_4_Template", "ctx_r22", "assignedTo", "TasksComponent_div_48_Template_button_click_4_listener", "_r32", "ctx_r31", "closeTaskDetails", "TasksComponent_div_48_p_11_Template", "TasksComponent_div_48_div_23_Template", "TasksComponent_div_48_div_24_Template", "TasksComponent_div_48_div_25_Template", "TasksComponent_div_48_div_26_Template", "TasksComponent_div_48_div_27_Template", "TasksComponent_div_48_Template_button_click_29_listener", "ctx_r33", "openAIAssistant", "ɵɵclassProp", "ctx_r7", "showTaskDetails", "title", "ɵɵclassMapInterpolate1", "status", "priority", "length", "TasksComponent_div_49_Template_button_click_4_listener", "_r35", "ctx_r34", "closeAIAssistant", "TasksComponent_div_49_Template_app_task_ai_assistant_taskUpdated_8_listener", "ctx_r36", "onTaskUpdated", "ctx_r8", "showAIAssistant", "TasksComponent", "constructor", "route", "router", "taskService", "equipeService", "authService", "snackBar", "dialog", "destroy$", "team", "current<PERSON>iew", "kanbanData", "taskStats", "loading", "currentUser", "getCurrentUser", "ngOnInit", "params", "pipe", "subscribe", "loadTeamData", "loadKanbanBoard", "loadTaskStatistics", "ngOnDestroy", "next", "complete", "getEquipeById", "hasTeamAccess", "console", "getKanbanBoard", "data", "open", "duration", "getTaskStatistics", "stats", "role", "isTeamMember", "id", "switchView", "view", "task", "applyFilters", "filters", "resetFilters", "navigateToTeam", "navigate", "getTeamName", "canCreateTasks", "canEditTasks", "getQuickStats", "byStatus", "blocked", "getTeamStatusColor", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "TaskService", "i3", "EquipeService", "i4", "AuthuserService", "i5", "MatSnackBar", "i6", "MatDialog", "selectors", "decls", "vars", "consts", "template", "TasksComponent_Template", "rf", "ctx", "TasksComponent_Template_button_click_3_listener", "TasksComponent_div_15_Template", "TasksComponent_Template_button_click_16_listener", "TasksComponent_Template_button_click_19_listener", "TasksComponent_Template_button_click_27_listener", "TasksComponent_Template_button_click_32_listener", "TasksComponent_Template_button_click_37_listener", "TasksComponent_div_43_Template", "TasksComponent_div_44_Template", "TasksComponent_div_45_Template", "TasksComponent_div_46_Template", "TasksComponent_div_47_Template", "TasksComponent_div_48_Template", "TasksComponent_div_49_Template", "TasksComponent_Template_div_click_50_listener", "_r1"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\admin\\tasks\\tasks.component.ts", "C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\admin\\tasks\\tasks.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { TaskService } from '../../../services/task.service';\nimport { EquipeService } from '../../../services/equipe.service';\nimport { AuthuserService } from '../../../services/authuser.service';\nimport { \n  Task, \n  KanbanBoard, \n  KanbanFilters,\n  TaskStatistics \n} from '../../../models/task.model';\nimport { Equipe } from '../../../models/equipe.model';\n\n@Component({\n  selector: 'app-tasks',\n  templateUrl: './tasks.component.html',\n  styleUrls: ['./tasks.component.scss']\n})\nexport class TasksComponent implements OnInit, On<PERSON><PERSON>roy {\n  private destroy$ = new Subject<void>();\n  \n  teamId!: string;\n  team: Equipe | null = null;\n  currentUser: any;\n  \n  // Vue actuelle\n  currentView: 'kanban' | 'list' | 'calendar' = 'kanban';\n  \n  // Données Kanban\n  kanbanData: KanbanBoard | null = null;\n  kanbanFilters: KanbanFilters = {};\n  \n  // Statistiques\n  taskStats: TaskStatistics | null = null;\n  \n  // États\n  loading = false;\n  error: string | null = null;\n  \n  // Tâche sélectionnée\n  selectedTask: Task | null = null;\n  showTaskDetails = false;\n  showAIAssistant = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private taskService: TaskService,\n    private equipeService: EquipeService,\n    private authService: AuthuserService,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog\n  ) {\n    this.currentUser = this.authService.getCurrentUser();\n  }\n\n  ngOnInit(): void {\n    this.route.params.pipe(\n      takeUntil(this.destroy$)\n    ).subscribe(params => {\n      this.teamId = params['teamId'];\n      if (this.teamId) {\n        this.loadTeamData();\n        this.loadKanbanBoard();\n        this.loadTaskStatistics();\n      } else {\n        this.error = 'ID d\\'équipe manquant';\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  // Charger les données de l'équipe\n  loadTeamData(): void {\n    this.equipeService.getEquipeById(this.teamId).subscribe({\n      next: (team) => {\n        this.team = team;\n        // Vérifier les permissions\n        if (!this.hasTeamAccess()) {\n          this.error = 'Vous n\\'avez pas accès aux tâches de cette équipe';\n          return;\n        }\n      },\n      error: (error) => {\n        console.error('Erreur chargement équipe:', error);\n        this.error = 'Équipe non trouvée';\n      }\n    });\n  }\n\n  // Charger le tableau Kanban\n  loadKanbanBoard(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.taskService.getKanbanBoard(this.teamId, this.kanbanFilters).subscribe({\n      next: (data) => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Erreur chargement Kanban:', error);\n        this.error = 'Erreur lors du chargement des tâches';\n        this.loading = false;\n        this.snackBar.open('Erreur lors du chargement des tâches', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Charger les statistiques\n  loadTaskStatistics(): void {\n    this.taskService.getTaskStatistics(this.teamId).subscribe({\n      next: (stats) => {\n        this.taskStats = stats;\n      },\n      error: (error) => {\n        console.error('Erreur chargement statistiques:', error);\n      }\n    });\n  }\n\n  // Vérifier l'accès à l'équipe\n  hasTeamAccess(): boolean {\n    if (!this.team || !this.currentUser) return false;\n    \n    // Admin système a accès à tout\n    if (this.currentUser.role === 'admin') return true;\n    \n    // Vérifier si l'utilisateur est membre de l'équipe\n    return this.equipeService.isTeamMember(this.team, this.currentUser.id);\n  }\n\n  // Changer de vue\n  switchView(view: 'kanban' | 'list' | 'calendar'): void {\n    this.currentView = view;\n    \n    if (view === 'kanban') {\n      this.loadKanbanBoard();\n    }\n    // Ajouter d'autres vues plus tard\n  }\n\n  // Gérer la sélection d'une tâche\n  onTaskSelected(task: Task): void {\n    this.selectedTask = task;\n    this.showTaskDetails = true;\n  }\n\n  // Gérer la création d'une tâche\n  onTaskCreated(task: Task): void {\n    this.snackBar.open('Tâche créée avec succès', 'Fermer', { duration: 2000 });\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n\n  // Gérer la mise à jour d'une tâche\n  onTaskUpdated(task: Task): void {\n    this.selectedTask = task;\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n\n  // Appliquer des filtres\n  applyFilters(filters: KanbanFilters): void {\n    this.kanbanFilters = { ...filters };\n    this.loadKanbanBoard();\n  }\n\n  // Réinitialiser les filtres\n  resetFilters(): void {\n    this.kanbanFilters = {};\n    this.loadKanbanBoard();\n  }\n\n  // Actualiser les données\n  refresh(): void {\n    this.loadKanbanBoard();\n    this.loadTaskStatistics();\n  }\n\n  // Ouvrir l'assistant IA\n  openAIAssistant(): void {\n    if (this.selectedTask) {\n      this.showAIAssistant = true;\n    } else {\n      this.snackBar.open('Veuillez sélectionner une tâche', 'Fermer', { duration: 2000 });\n    }\n  }\n\n  // Fermer les panneaux\n  closeTaskDetails(): void {\n    this.showTaskDetails = false;\n    this.selectedTask = null;\n  }\n\n  closeAIAssistant(): void {\n    this.showAIAssistant = false;\n  }\n\n  // Naviguer vers l'équipe\n  navigateToTeam(): void {\n    this.router.navigate(['/equipes/detail', this.teamId]);\n  }\n\n  // Obtenir le nom de l'équipe\n  getTeamName(): string {\n    return this.team?.name || 'Équipe';\n  }\n\n  // Vérifier si l'utilisateur peut créer des tâches\n  canCreateTasks(): boolean {\n    if (!this.team || !this.currentUser) return false;\n    \n    // Admin système peut tout faire\n    if (this.currentUser.role === 'admin') return true;\n    \n    // Membres de l'équipe peuvent créer des tâches\n    return this.hasTeamAccess();\n  }\n\n  // Vérifier si l'utilisateur peut modifier des tâches\n  canEditTasks(): boolean {\n    return this.canCreateTasks();\n  }\n\n  // Obtenir les statistiques rapides\n  getQuickStats(): any {\n    if (!this.kanbanData) return null;\n    \n    return {\n      total: this.kanbanData.stats.total,\n      todo: this.kanbanData.stats.byStatus['todo'] || 0,\n      inProgress: this.kanbanData.stats.byStatus['in-progress'] || 0,\n      done: this.kanbanData.stats.byStatus['done'] || 0,\n      overdue: this.kanbanData.stats.overdue,\n      blocked: this.kanbanData.stats.blocked\n    };\n  }\n\n  // Obtenir la couleur du statut de l'équipe\n  getTeamStatusColor(): string {\n    if (!this.team) return '#6b7280';\n    \n    switch (this.team.status) {\n      case 'active': return '#10b981';\n      case 'inactive': return '#f59e0b';\n      case 'archived': return '#6b7280';\n      default: return '#6b7280';\n    }\n  }\n}\n", "<div class=\"tasks-container\">\n  <!-- Header -->\n  <div class=\"tasks-header\">\n    <div class=\"header-left\">\n      <button mat-icon-button (click)=\"navigateToTeam()\" matTooltip=\"Retour à l'équipe\">\n        <mat-icon>arrow_back</mat-icon>\n      </button>\n      \n      <div class=\"team-info\">\n        <h1 class=\"team-name\">{{ getTeamName() }}</h1>\n        <div class=\"team-status\" [style.color]=\"getTeamStatusColor()\">\n          <mat-icon>circle</mat-icon>\n          <span>{{ team?.status || 'active' }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"header-actions\">\n      <!-- Statistiques rapides -->\n      <div class=\"quick-stats\" *ngIf=\"getQuickStats() as stats\">\n        <div class=\"stat-item\">\n          <span class=\"stat-value\">{{ stats.total }}</span>\n          <span class=\"stat-label\">Total</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-value\">{{ stats.todo }}</span>\n          <span class=\"stat-label\">À faire</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-value\">{{ stats.inProgress }}</span>\n          <span class=\"stat-label\">En cours</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-value\">{{ stats.done }}</span>\n          <span class=\"stat-label\">Terminé</span>\n        </div>\n        <div class=\"stat-item\" *ngIf=\"stats.overdue > 0\">\n          <span class=\"stat-value overdue\">{{ stats.overdue }}</span>\n          <span class=\"stat-label\">En retard</span>\n        </div>\n      </div>\n\n      <!-- Actions -->\n      <button mat-icon-button (click)=\"refresh()\" matTooltip=\"Actualiser\">\n        <mat-icon>refresh</mat-icon>\n      </button>\n      \n      <button mat-icon-button \n              (click)=\"openAIAssistant()\" \n              [disabled]=\"!selectedTask\"\n              matTooltip=\"Assistant IA\">\n        <mat-icon>psychology</mat-icon>\n      </button>\n\n      <button mat-icon-button [matMenuTriggerFor]=\"viewMenu\" matTooltip=\"Changer de vue\">\n        <mat-icon>view_module</mat-icon>\n      </button>\n      \n      <mat-menu #viewMenu=\"matMenu\">\n        <button mat-menu-item (click)=\"switchView('kanban')\" [class.active]=\"currentView === 'kanban'\">\n          <mat-icon>view_kanban</mat-icon>\n          <span>Kanban</span>\n        </button>\n        <button mat-menu-item (click)=\"switchView('list')\" [class.active]=\"currentView === 'list'\">\n          <mat-icon>list</mat-icon>\n          <span>Liste</span>\n        </button>\n        <button mat-menu-item (click)=\"switchView('calendar')\" [class.active]=\"currentView === 'calendar'\">\n          <mat-icon>calendar_today</mat-icon>\n          <span>Calendrier</span>\n        </button>\n      </mat-menu>\n    </div>\n  </div>\n\n  <!-- Contenu principal -->\n  <div class=\"tasks-content\">\n    <!-- Vue Kanban -->\n    <div class=\"kanban-view\" *ngIf=\"currentView === 'kanban'\">\n      <app-kanban-board \n        [teamId]=\"teamId\"\n        [filters]=\"kanbanFilters\"\n        (taskSelected)=\"onTaskSelected($event)\"\n        (taskCreated)=\"onTaskCreated($event)\">\n      </app-kanban-board>\n    </div>\n\n    <!-- Vue Liste (à implémenter) -->\n    <div class=\"list-view\" *ngIf=\"currentView === 'list'\">\n      <mat-card class=\"coming-soon\">\n        <mat-card-content>\n          <div class=\"coming-soon-content\">\n            <mat-icon>list</mat-icon>\n            <h3>Vue Liste</h3>\n            <p>Cette vue sera bientôt disponible</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Vue Calendrier (à implémenter) -->\n    <div class=\"calendar-view\" *ngIf=\"currentView === 'calendar'\">\n      <mat-card class=\"coming-soon\">\n        <mat-card-content>\n          <div class=\"coming-soon-content\">\n            <mat-icon>calendar_today</mat-icon>\n            <h3>Vue Calendrier</h3>\n            <p>Cette vue sera bientôt disponible</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Loading state -->\n  <div class=\"loading-overlay\" *ngIf=\"loading\">\n    <mat-spinner diameter=\"40\"></mat-spinner>\n    <p>Chargement des tâches...</p>\n  </div>\n\n  <!-- Error state -->\n  <div class=\"error-container\" *ngIf=\"error && !loading\">\n    <mat-icon color=\"warn\">error</mat-icon>\n    <h3>Erreur</h3>\n    <p>{{ error }}</p>\n    <button mat-raised-button color=\"primary\" (click)=\"refresh()\">\n      Réessayer\n    </button>\n  </div>\n</div>\n\n<!-- Panneau latéral pour les détails de tâche -->\n<div class=\"task-details-panel\" [class.open]=\"showTaskDetails\" *ngIf=\"selectedTask\">\n  <div class=\"panel-header\">\n    <h3>Détails de la tâche</h3>\n    <button mat-icon-button (click)=\"closeTaskDetails()\">\n      <mat-icon>close</mat-icon>\n    </button>\n  </div>\n  \n  <div class=\"panel-content\">\n    <div class=\"task-info\">\n      <h4>{{ selectedTask.title }}</h4>\n      <p class=\"task-description\" *ngIf=\"selectedTask.description\">\n        {{ selectedTask.description }}\n      </p>\n      \n      <div class=\"task-meta\">\n        <div class=\"meta-item\">\n          <span class=\"label\">Statut:</span>\n          <span class=\"value status-{{ selectedTask.status }}\">{{ selectedTask.status }}</span>\n        </div>\n        \n        <div class=\"meta-item\">\n          <span class=\"label\">Priorité:</span>\n          <span class=\"value priority-{{ selectedTask.priority }}\">{{ selectedTask.priority }}</span>\n        </div>\n        \n        <div class=\"meta-item\" *ngIf=\"selectedTask.category\">\n          <span class=\"label\">Catégorie:</span>\n          <span class=\"value\">{{ selectedTask.category }}</span>\n        </div>\n        \n        <div class=\"meta-item\" *ngIf=\"selectedTask.dueDate\">\n          <span class=\"label\">Échéance:</span>\n          <span class=\"value\">{{ selectedTask.dueDate | date:'dd/MM/yyyy' }}</span>\n        </div>\n        \n        <div class=\"meta-item\" *ngIf=\"selectedTask.estimatedHours\">\n          <span class=\"label\">Estimation:</span>\n          <span class=\"value\">{{ selectedTask.estimatedHours }}h</span>\n        </div>\n      </div>\n      \n      <!-- Labels -->\n      <div class=\"task-labels\" *ngIf=\"selectedTask.labels && selectedTask.labels.length > 0\">\n        <span class=\"label-title\">Labels:</span>\n        <div class=\"labels-list\">\n          <span class=\"task-label\" \n                *ngFor=\"let label of selectedTask.labels\"\n                [style.background-color]=\"label.color\">\n            {{ label.name }}\n          </span>\n        </div>\n      </div>\n      \n      <!-- Assignés -->\n      <div class=\"task-assignees\" *ngIf=\"selectedTask.assignedTo && selectedTask.assignedTo.length > 0\">\n        <span class=\"label-title\">Assigné à:</span>\n        <div class=\"assignees-list\">\n          <div class=\"assignee\" *ngFor=\"let assignee of selectedTask.assignedTo\">\n            <div class=\"assignee-avatar\">\n              <img *ngIf=\"assignee.profileImage\" \n                   [src]=\"assignee.profileImage\" \n                   [alt]=\"assignee.fullName || assignee\">\n              <span *ngIf=\"!assignee.profileImage\">\n                {{ (assignee.fullName || assignee).charAt(0).toUpperCase() }}\n              </span>\n            </div>\n            <span class=\"assignee-name\">{{ assignee.fullName || assignee }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Actions -->\n    <div class=\"task-actions\">\n      <button mat-raised-button color=\"primary\" (click)=\"openAIAssistant()\">\n        <mat-icon>psychology</mat-icon>\n        Assistant IA\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Panneau Assistant IA -->\n<div class=\"ai-assistant-panel\" [class.open]=\"showAIAssistant\" *ngIf=\"selectedTask && showAIAssistant\">\n  <div class=\"panel-header\">\n    <h3>Assistant IA</h3>\n    <button mat-icon-button (click)=\"closeAIAssistant()\">\n      <mat-icon>close</mat-icon>\n    </button>\n  </div>\n  \n  <div class=\"panel-content\">\n    <app-task-ai-assistant \n      [task]=\"selectedTask\"\n      (taskUpdated)=\"onTaskUpdated($event)\">\n    </app-task-ai-assistant>\n  </div>\n</div>\n\n<!-- Overlay pour les panneaux -->\n<div class=\"panels-overlay\" \n     [class.active]=\"showTaskDetails || showAIAssistant\"\n     (click)=\"closeTaskDetails(); closeAIAssistant()\">\n</div>\n"], "mappings": "AAIA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;IC+BlCC,EAAA,CAAAC,cAAA,cAAiD;IACdD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADRH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAC,OAAA,CAAmB;;;;;IAlBxDP,EAAA,CAAAC,cAAA,cAA0D;IAE7BD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvCH,EAAA,CAAAC,cAAA,cAAuB;IACID,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1CH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAQ,UAAA,KAAAC,qCAAA,kBAGM;IACRT,EAAA,CAAAG,YAAA,EAAM;;;;IAnBuBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAI,KAAA,CAAiB;IAIjBV,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAK,IAAA,CAAgB;IAIhBX,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAM,UAAA,CAAsB;IAItBZ,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAO,IAAA,CAAgB;IAGnBb,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAc,UAAA,SAAAR,QAAA,CAAAC,OAAA,KAAuB;;;;;;IA0CnDP,EAAA,CAAAC,cAAA,cAA0D;IAItDD,EAAA,CAAAe,UAAA,0BAAAC,wEAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAgBrB,EAAA,CAAAsB,WAAA,CAAAF,OAAA,CAAAG,cAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC,yBAAAO,uEAAAP,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAzB,EAAA,CAAAqB,aAAA;MAAA,OACxBrB,EAAA,CAAAsB,WAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAT,MAAA,CAAqB;IAAA,EADG;IAEzCjB,EAAA,CAAAG,YAAA,EAAmB;;;;IAJjBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAc,UAAA,WAAAa,MAAA,CAAAC,MAAA,CAAiB,YAAAD,MAAA,CAAAE,aAAA;;;;;IAQrB7B,EAAA,CAAAC,cAAA,cAAsD;IAIpCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOhDH,EAAA,CAAAC,cAAA,cAA8D;IAI5CD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQlDH,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAA8B,SAAA,sBAAyC;IACzC9B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,oCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIjCH,EAAA,CAAAC,cAAA,cAAuD;IAC9BD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAA8D;IAApBD,EAAA,CAAAe,UAAA,mBAAAgB,uDAAA;MAAA/B,EAAA,CAAAkB,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAW,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAC3DlC,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHNH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAA8B,MAAA,CAAAC,KAAA,CAAW;;;;;IAmBZpC,EAAA,CAAAC,cAAA,YAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAqC,kBAAA,MAAAC,OAAA,CAAAC,YAAA,CAAAC,WAAA,MACF;;;;;IAaExC,EAAA,CAAAC,cAAA,cAAqD;IAC/BD,EAAA,CAAAE,MAAA,sBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAF,YAAA,CAAAG,QAAA,CAA2B;;;;;IAGjD1C,EAAA,CAAAC,cAAA,cAAoD;IAC9BD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArDH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2C,WAAA,OAAAC,OAAA,CAAAL,YAAA,CAAAM,OAAA,gBAA8C;;;;;IAGpE7C,EAAA,CAAAC,cAAA,cAA2D;IACrCD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqC,kBAAA,KAAAS,OAAA,CAAAP,YAAA,CAAAQ,cAAA,MAAkC;;;;;IAQtD/C,EAAA,CAAAC,cAAA,eAE6C;IAC3CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFDH,EAAA,CAAAgD,WAAA,qBAAAC,SAAA,CAAAC,KAAA,CAAsC;IAC1ClD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAqC,kBAAA,MAAAY,SAAA,CAAAE,IAAA,MACF;;;;;IAPJnD,EAAA,CAAAC,cAAA,cAAuF;IAC3DD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAQ,UAAA,IAAA4C,4CAAA,mBAIO;IACTpD,EAAA,CAAAG,YAAA,EAAM;;;;IAJoBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAc,UAAA,YAAAuC,OAAA,CAAAd,YAAA,CAAAe,MAAA,CAAsB;;;;;IAa1CtD,EAAA,CAAA8B,SAAA,cAE2C;;;;IADtC9B,EAAA,CAAAc,UAAA,QAAAyC,YAAA,CAAAC,YAAA,EAAAxD,EAAA,CAAAyD,aAAA,CAA6B,QAAAF,YAAA,CAAAG,QAAA,IAAAH,YAAA;;;;;IAElCvD,EAAA,CAAAC,cAAA,WAAqC;IACnCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAqC,kBAAA,OAAAkB,YAAA,CAAAG,QAAA,IAAAH,YAAA,EAAAI,MAAA,IAAAC,WAAA,QACF;;;;;IAPJ5D,EAAA,CAAAC,cAAA,cAAuE;IAEnED,EAAA,CAAAQ,UAAA,IAAAqD,iDAAA,kBAE2C;IAC3C7D,EAAA,CAAAQ,UAAA,IAAAsD,kDAAA,mBAEO;IACT9D,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAP9DH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAc,UAAA,SAAAyC,YAAA,CAAAC,YAAA,CAA2B;IAG1BxD,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAc,UAAA,UAAAyC,YAAA,CAAAC,YAAA,CAA4B;IAITxD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAkD,YAAA,CAAAG,QAAA,IAAAH,YAAA,CAAmC;;;;;IAZrEvD,EAAA,CAAAC,cAAA,cAAkG;IACtED,EAAA,CAAAE,MAAA,2BAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAQ,UAAA,IAAAuD,2CAAA,kBAUM;IACR/D,EAAA,CAAAG,YAAA,EAAM;;;;IAXuCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,YAAAkD,OAAA,CAAAzB,YAAA,CAAA0B,UAAA,CAA0B;;;;;;IA1D/EjE,EAAA,CAAAC,cAAA,cAAoF;IAE5ED,EAAA,CAAAE,MAAA,oCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,iBAAqD;IAA7BD,EAAA,CAAAe,UAAA,mBAAAmD,uDAAA;MAAAlE,EAAA,CAAAkB,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAA8C,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAClDrE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI9BH,EAAA,CAAAC,cAAA,cAA2B;IAEnBD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAQ,UAAA,KAAA8D,mCAAA,gBAEI;IAEJtE,EAAA,CAAAC,cAAA,eAAuB;IAECD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,YAAqD;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGvFH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,sBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,YAAyD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7FH,EAAA,CAAAQ,UAAA,KAAA+D,qCAAA,kBAGM;IAENvE,EAAA,CAAAQ,UAAA,KAAAgE,qCAAA,kBAGM;IAENxE,EAAA,CAAAQ,UAAA,KAAAiE,qCAAA,kBAGM;IACRzE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAQ,UAAA,KAAAkE,qCAAA,kBASM;IAGN1E,EAAA,CAAAQ,UAAA,KAAAmE,qCAAA,kBAeM;IACR3E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IACkBD,EAAA,CAAAe,UAAA,mBAAA6D,wDAAA;MAAA5E,EAAA,CAAAkB,aAAA,CAAAiD,IAAA;MAAA,MAAAU,OAAA,GAAA7E,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAuD,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACnE9E,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA9EiBH,EAAA,CAAA+E,WAAA,SAAAC,MAAA,CAAAC,eAAA,CAA8B;IAUpDjF,EAAA,CAAAI,SAAA,IAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAA2E,MAAA,CAAAzC,YAAA,CAAA2C,KAAA,CAAwB;IACClF,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAAC,WAAA,CAA8B;IAOjDxC,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAmF,sBAAA,kBAAAH,MAAA,CAAAzC,YAAA,CAAA6C,MAAA,KAA8C;IAACpF,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAA2E,MAAA,CAAAzC,YAAA,CAAA6C,MAAA,CAAyB;IAKxEpF,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAmF,sBAAA,oBAAAH,MAAA,CAAAzC,YAAA,CAAA8C,QAAA,KAAkD;IAACrF,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAA2E,MAAA,CAAAzC,YAAA,CAAA8C,QAAA,CAA2B;IAG9DrF,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAAG,QAAA,CAA2B;IAK3B1C,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAAM,OAAA,CAA0B;IAK1B7C,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAAQ,cAAA,CAAiC;IAOjC/C,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAAe,MAAA,IAAA0B,MAAA,CAAAzC,YAAA,CAAAe,MAAA,CAAAgC,MAAA,KAA2D;IAYxDtF,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAc,UAAA,SAAAkE,MAAA,CAAAzC,YAAA,CAAA0B,UAAA,IAAAe,MAAA,CAAAzC,YAAA,CAAA0B,UAAA,CAAAqB,MAAA,KAAmE;;;;;;IA6BtGtF,EAAA,CAAAC,cAAA,cAAuG;IAE/FD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,iBAAqD;IAA7BD,EAAA,CAAAe,UAAA,mBAAAwE,uDAAA;MAAAvF,EAAA,CAAAkB,aAAA,CAAAsE,IAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAmE,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAClD1F,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI9BH,EAAA,CAAAC,cAAA,cAA2B;IAGvBD,EAAA,CAAAe,UAAA,yBAAA4E,4EAAA1E,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsE,IAAA;MAAA,MAAAI,OAAA,GAAA5F,EAAA,CAAAqB,aAAA;MAAA,OAAerB,EAAA,CAAAsB,WAAA,CAAAsE,OAAA,CAAAC,aAAA,CAAA5E,MAAA,CAAqB;IAAA,EAAC;IACvCjB,EAAA,CAAAG,YAAA,EAAwB;;;;IAZIH,EAAA,CAAA+E,WAAA,SAAAe,MAAA,CAAAC,eAAA,CAA8B;IAUxD/F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAc,UAAA,SAAAgF,MAAA,CAAAvD,YAAA,CAAqB;;;AD3M3B,OAAM,MAAOyD,cAAc;EA0BzBC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,aAA4B,EAC5BC,WAA4B,EAC5BC,QAAqB,EACrBC,MAAiB;IANjB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAhCR,KAAAC,QAAQ,GAAG,IAAI3G,OAAO,EAAQ;IAGtC,KAAA4G,IAAI,GAAkB,IAAI;IAG1B;IACA,KAAAC,WAAW,GAAmC,QAAQ;IAEtD;IACA,KAAAC,UAAU,GAAuB,IAAI;IACrC,KAAA/E,aAAa,GAAkB,EAAE;IAEjC;IACA,KAAAgF,SAAS,GAA0B,IAAI;IAEvC;IACA,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA1E,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAG,YAAY,GAAgB,IAAI;IAChC,KAAA0C,eAAe,GAAG,KAAK;IACvB,KAAAc,eAAe,GAAG,KAAK;IAWrB,IAAI,CAACgB,WAAW,GAAG,IAAI,CAACT,WAAW,CAACU,cAAc,EAAE;EACtD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACf,KAAK,CAACgB,MAAM,CAACC,IAAI,CACpBpH,SAAS,CAAC,IAAI,CAAC0G,QAAQ,CAAC,CACzB,CAACW,SAAS,CAACF,MAAM,IAAG;MACnB,IAAI,CAACtF,MAAM,GAAGsF,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,IAAI,CAACtF,MAAM,EAAE;QACf,IAAI,CAACyF,YAAY,EAAE;QACnB,IAAI,CAACC,eAAe,EAAE;QACtB,IAAI,CAACC,kBAAkB,EAAE;OAC1B,MAAM;QACL,IAAI,CAACnF,KAAK,GAAG,uBAAuB;;IAExC,CAAC,CAAC;EACJ;EAEAoF,WAAWA,CAAA;IACT,IAAI,CAACf,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACiB,QAAQ,EAAE;EAC1B;EAEA;EACAL,YAAYA,CAAA;IACV,IAAI,CAAChB,aAAa,CAACsB,aAAa,CAAC,IAAI,CAAC/F,MAAM,CAAC,CAACwF,SAAS,CAAC;MACtDK,IAAI,EAAGf,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB;QACA,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAE,EAAE;UACzB,IAAI,CAACxF,KAAK,GAAG,mDAAmD;UAChE;;MAEJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfyF,OAAO,CAACzF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACA,KAAK,GAAG,oBAAoB;MACnC;KACD,CAAC;EACJ;EAEA;EACAkF,eAAeA,CAAA;IACb,IAAI,CAACR,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC1E,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACgE,WAAW,CAAC0B,cAAc,CAAC,IAAI,CAAClG,MAAM,EAAE,IAAI,CAACC,aAAa,CAAC,CAACuF,SAAS,CAAC;MACzEK,IAAI,EAAGM,IAAI,IAAI;QACb,IAAI,CAACnB,UAAU,GAAGmB,IAAI;QACtB,IAAI,CAACjB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD1E,KAAK,EAAGA,KAAK,IAAI;QACfyF,OAAO,CAACzF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACA,KAAK,GAAG,sCAAsC;QACnD,IAAI,CAAC0E,OAAO,GAAG,KAAK;QACpB,IAAI,CAACP,QAAQ,CAACyB,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC1F;KACD,CAAC;EACJ;EAEA;EACAV,kBAAkBA,CAAA;IAChB,IAAI,CAACnB,WAAW,CAAC8B,iBAAiB,CAAC,IAAI,CAACtG,MAAM,CAAC,CAACwF,SAAS,CAAC;MACxDK,IAAI,EAAGU,KAAK,IAAI;QACd,IAAI,CAACtB,SAAS,GAAGsB,KAAK;MACxB,CAAC;MACD/F,KAAK,EAAGA,KAAK,IAAI;QACfyF,OAAO,CAACzF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC;EACJ;EAEA;EACAwF,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAClB,IAAI,IAAI,CAAC,IAAI,CAACK,WAAW,EAAE,OAAO,KAAK;IAEjD;IACA,IAAI,IAAI,CAACA,WAAW,CAACqB,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAElD;IACA,OAAO,IAAI,CAAC/B,aAAa,CAACgC,YAAY,CAAC,IAAI,CAAC3B,IAAI,EAAE,IAAI,CAACK,WAAW,CAACuB,EAAE,CAAC;EACxE;EAEA;EACAC,UAAUA,CAACC,IAAoC;IAC7C,IAAI,CAAC7B,WAAW,GAAG6B,IAAI;IAEvB,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAI,CAAClB,eAAe,EAAE;;IAExB;EACF;EAEA;EACA/F,cAAcA,CAACkH,IAAU;IACvB,IAAI,CAAClG,YAAY,GAAGkG,IAAI;IACxB,IAAI,CAACxD,eAAe,GAAG,IAAI;EAC7B;EAEA;EACAvD,aAAaA,CAAC+G,IAAU;IACtB,IAAI,CAAClC,QAAQ,CAACyB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAC3E,IAAI,CAACX,eAAe,EAAE;IACtB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACA1B,aAAaA,CAAC4C,IAAU;IACtB,IAAI,CAAClG,YAAY,GAAGkG,IAAI;IACxB,IAAI,CAACnB,eAAe,EAAE;IACtB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAmB,YAAYA,CAACC,OAAsB;IACjC,IAAI,CAAC9G,aAAa,GAAG;MAAE,GAAG8G;IAAO,CAAE;IACnC,IAAI,CAACrB,eAAe,EAAE;EACxB;EAEA;EACAsB,YAAYA,CAAA;IACV,IAAI,CAAC/G,aAAa,GAAG,EAAE;IACvB,IAAI,CAACyF,eAAe,EAAE;EACxB;EAEA;EACApF,OAAOA,CAAA;IACL,IAAI,CAACoF,eAAe,EAAE;IACtB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;EACAzC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACvC,YAAY,EAAE;MACrB,IAAI,CAACwD,eAAe,GAAG,IAAI;KAC5B,MAAM;MACL,IAAI,CAACQ,QAAQ,CAACyB,IAAI,CAAC,iCAAiC,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;;EAEvF;EAEA;EACA5D,gBAAgBA,CAAA;IACd,IAAI,CAACY,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC1C,YAAY,GAAG,IAAI;EAC1B;EAEAmD,gBAAgBA,CAAA;IACd,IAAI,CAACK,eAAe,GAAG,KAAK;EAC9B;EAEA;EACA8C,cAAcA,CAAA;IACZ,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAClH,MAAM,CAAC,CAAC;EACxD;EAEA;EACAmH,WAAWA,CAAA;IACT,OAAO,IAAI,CAACrC,IAAI,EAAEvD,IAAI,IAAI,QAAQ;EACpC;EAEA;EACA6F,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACtC,IAAI,IAAI,CAAC,IAAI,CAACK,WAAW,EAAE,OAAO,KAAK;IAEjD;IACA,IAAI,IAAI,CAACA,WAAW,CAACqB,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAElD;IACA,OAAO,IAAI,CAACR,aAAa,EAAE;EAC7B;EAEA;EACAqB,YAAYA,CAAA;IACV,OAAO,IAAI,CAACD,cAAc,EAAE;EAC9B;EAEA;EACAE,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACtC,UAAU,EAAE,OAAO,IAAI;IAEjC,OAAO;MACLlG,KAAK,EAAE,IAAI,CAACkG,UAAU,CAACuB,KAAK,CAACzH,KAAK;MAClCC,IAAI,EAAE,IAAI,CAACiG,UAAU,CAACuB,KAAK,CAACgB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;MACjDvI,UAAU,EAAE,IAAI,CAACgG,UAAU,CAACuB,KAAK,CAACgB,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;MAC9DtI,IAAI,EAAE,IAAI,CAAC+F,UAAU,CAACuB,KAAK,CAACgB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;MACjD5I,OAAO,EAAE,IAAI,CAACqG,UAAU,CAACuB,KAAK,CAAC5H,OAAO;MACtC6I,OAAO,EAAE,IAAI,CAACxC,UAAU,CAACuB,KAAK,CAACiB;KAChC;EACH;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC3C,IAAI,EAAE,OAAO,SAAS;IAEhC,QAAQ,IAAI,CAACA,IAAI,CAACtB,MAAM;MACtB,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;;EAE7B;;;uBA3OWY,cAAc,EAAAhG,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzJ,EAAA,CAAAsJ,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA7J,EAAA,CAAAsJ,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAA/J,EAAA,CAAAsJ,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAjK,EAAA,CAAAsJ,iBAAA,CAAAY,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAdnE,cAAc;MAAAoE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB3B1K,EAAA,CAAAC,cAAA,aAA6B;UAICD,EAAA,CAAAe,UAAA,mBAAA6J,gDAAA;YAAA,OAASD,GAAA,CAAA9B,cAAA,EAAgB;UAAA,EAAC;UAChD7I,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGjCH,EAAA,CAAAC,cAAA,aAAuB;UACCD,EAAA,CAAAE,MAAA,GAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9CH,EAAA,CAAAC,cAAA,aAA8D;UAClDD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKjDH,EAAA,CAAAC,cAAA,cAA4B;UAE1BD,EAAA,CAAAQ,UAAA,KAAAqK,8BAAA,kBAqBM;UAGN7K,EAAA,CAAAC,cAAA,iBAAoE;UAA5CD,EAAA,CAAAe,UAAA,mBAAA+J,iDAAA;YAAA,OAASH,GAAA,CAAAzI,OAAA,EAAS;UAAA,EAAC;UACzClC,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAG9BH,EAAA,CAAAC,cAAA,kBAGkC;UAF1BD,EAAA,CAAAe,UAAA,mBAAAgK,iDAAA;YAAA,OAASJ,GAAA,CAAA7F,eAAA,EAAiB;UAAA,EAAC;UAGjC9E,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGjCH,EAAA,CAAAC,cAAA,kBAAmF;UACvED,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGlCH,EAAA,CAAAC,cAAA,0BAA8B;UACND,EAAA,CAAAe,UAAA,mBAAAiK,iDAAA;YAAA,OAASL,GAAA,CAAApC,UAAA,CAAW,QAAQ,CAAC;UAAA,EAAC;UAClDvI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErBH,EAAA,CAAAC,cAAA,kBAA2F;UAArED,EAAA,CAAAe,UAAA,mBAAAkK,iDAAA;YAAA,OAASN,GAAA,CAAApC,UAAA,CAAW,MAAM,CAAC;UAAA,EAAC;UAChDvI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEpBH,EAAA,CAAAC,cAAA,kBAAmG;UAA7ED,EAAA,CAAAe,UAAA,mBAAAmK,iDAAA;YAAA,OAASP,GAAA,CAAApC,UAAA,CAAW,UAAU,CAAC;UAAA,EAAC;UACpDvI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAO/BH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAQ,UAAA,KAAA2K,8BAAA,kBAOM;UAGNnL,EAAA,CAAAQ,UAAA,KAAA4K,8BAAA,mBAUM;UAGNpL,EAAA,CAAAQ,UAAA,KAAA6K,8BAAA,mBAUM;UACRrL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAQ,UAAA,KAAA8K,8BAAA,kBAGM;UAGNtL,EAAA,CAAAQ,UAAA,KAAA+K,8BAAA,kBAOM;UACRvL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAQ,UAAA,KAAAgL,8BAAA,oBAiFM;UAGNxL,EAAA,CAAAQ,UAAA,KAAAiL,8BAAA,kBAcM;UAGNzL,EAAA,CAAAC,cAAA,eAEsD;UAAjDD,EAAA,CAAAe,UAAA,mBAAA2K,8CAAA;YAASf,GAAA,CAAAtG,gBAAA,EAAkB;YAAA,OAAEsG,GAAA,CAAAjF,gBAAA,EAAkB;UAAA,EAAC;UACrD1F,EAAA,CAAAG,YAAA,EAAM;;;;UAnOwBH,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAK,iBAAA,CAAAsK,GAAA,CAAA5B,WAAA,GAAmB;UAChB/I,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAgD,WAAA,UAAA2H,GAAA,CAAAtB,kBAAA,GAAoC;UAErDrJ,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAK,iBAAA,EAAAsK,GAAA,CAAAjE,IAAA,kBAAAiE,GAAA,CAAAjE,IAAA,CAAAtB,MAAA,cAA8B;UAOdpF,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAAzB,aAAA,GAAsB;UA8BxClJ,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAc,UAAA,cAAA6J,GAAA,CAAApI,YAAA,CAA0B;UAKVvC,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAc,UAAA,sBAAA6K,GAAA,CAA8B;UAKC3L,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAA+E,WAAA,WAAA4F,GAAA,CAAAhE,WAAA,cAAyC;UAI3C3G,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAA+E,WAAA,WAAA4F,GAAA,CAAAhE,WAAA,YAAuC;UAInC3G,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAA+E,WAAA,WAAA4F,GAAA,CAAAhE,WAAA,gBAA2C;UAW5E3G,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAAhE,WAAA,cAA8B;UAUhC3G,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAAhE,WAAA,YAA4B;UAaxB3G,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAAhE,WAAA,gBAAgC;UAchC3G,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAA7D,OAAA,CAAa;UAMb9G,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAAvI,KAAA,KAAAuI,GAAA,CAAA7D,OAAA,CAAuB;UAWS9G,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAApI,YAAA,CAAkB;UAoFlBvC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAc,UAAA,SAAA6J,GAAA,CAAApI,YAAA,IAAAoI,GAAA,CAAA5E,eAAA,CAAqC;UAkBhG/F,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAA+E,WAAA,WAAA4F,GAAA,CAAA1F,eAAA,IAAA0F,GAAA,CAAA5E,eAAA,CAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}