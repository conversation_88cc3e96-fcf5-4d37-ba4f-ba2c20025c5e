{"ast": null, "code": "/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\nexport { DirectiveLocation };\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */", "map": {"version": 3, "names": ["DirectiveLocation"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/language/directiveLocation.mjs"], "sourcesContent": ["/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\n\nexport { DirectiveLocation };\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,iBAAiB;AAErB,CAAC,UAAUA,iBAAiB,EAAE;EAC5BA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC1CA,iBAAiB,CAAC,cAAc,CAAC,GAAG,cAAc;EAClDA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAChEA,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACxDA,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACxDA,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAChEA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACtCA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACtCA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACtCA,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC1DA,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAChEA,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC5CA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;EAClCA,iBAAiB,CAAC,YAAY,CAAC,GAAG,YAAY;EAC9CA,iBAAiB,CAAC,cAAc,CAAC,GAAG,cAAc;EAClDA,iBAAiB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;AACxE,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjD,SAASA,iBAAiB;AAC1B;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}