{"name": "@apollo/utils.stripsensitiveliterals", "version": "2.0.1", "description": "Remove literals from an AST which might contain PII (strings and numbers, and optionally lists and objects)", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/stripSensitiveLiterals/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}