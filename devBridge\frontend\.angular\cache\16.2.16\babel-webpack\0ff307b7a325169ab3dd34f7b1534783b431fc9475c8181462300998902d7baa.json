{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISODay} function options.\n */\n\n/**\n * @name getISODay\n * @category Weekday Helpers\n * @summary Get the day of the ISO week of the given date.\n *\n * @description\n * Get the day of the ISO week of the given date,\n * which is 7 for Sunday, 1 for Monday etc.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The day of ISO week\n *\n * @example\n * // Which day of the ISO week is 26 February 2012?\n * const result = getISODay(new Date(2012, 1, 26))\n * //=> 7\n */\nexport function getISODay(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 ? 7 : day;\n}\n\n// Fallback for modularized imports:\nexport default getISODay;", "map": {"version": 3, "names": ["toDate", "getISODay", "date", "options", "day", "in", "getDay"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/getISODay.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISODay} function options.\n */\n\n/**\n * @name getISODay\n * @category Weekday Helpers\n * @summary Get the day of the ISO week of the given date.\n *\n * @description\n * Get the day of the ISO week of the given date,\n * which is 7 for Sunday, 1 for Monday etc.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The day of ISO week\n *\n * @example\n * // Which day of the ISO week is 26 February 2012?\n * const result = getISODay(new Date(2012, 1, 26))\n * //=> 7\n */\nexport function getISODay(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 ? 7 : day;\n}\n\n// Fallback for modularized imports:\nexport default getISODay;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,GAAG,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC;EAC9C,OAAOF,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,GAAG;AAC5B;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}