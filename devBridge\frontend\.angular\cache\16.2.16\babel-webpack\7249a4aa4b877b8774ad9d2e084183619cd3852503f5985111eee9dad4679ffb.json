{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isSunday} function options.\n */\n\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is Sunday\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\nexport function isSunday(date, options) {\n  return toDate(date, options?.in).getDay() === 0;\n}\n\n// Fallback for modularized imports:\nexport default isSunday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}