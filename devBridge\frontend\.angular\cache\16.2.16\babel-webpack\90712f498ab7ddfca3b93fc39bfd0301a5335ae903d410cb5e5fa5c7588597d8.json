{"ast": null, "code": "import { minutesInHour } from \"./constants.js\";\n\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in hours\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\nexport function minutesToHours(minutes) {\n  const hours = minutes / minutesInHour;\n  return Math.trunc(hours);\n}\n\n// Fallback for modularized imports:\nexport default minutesToHours;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}