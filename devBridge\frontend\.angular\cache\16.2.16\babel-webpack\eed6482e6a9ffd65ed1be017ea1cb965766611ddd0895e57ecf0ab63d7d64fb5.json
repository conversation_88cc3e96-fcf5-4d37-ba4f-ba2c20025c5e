{"ast": null, "code": "// Produce the GraphQL query recommended for a full schema introspection.\nexport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n// Gets the target Operation from a Document.\nexport { getOperationAST } from './getOperationAST.mjs'; // Gets the Type for the target Operation AST.\n\nexport { getOperationRootType } from './getOperationRootType.mjs'; // Convert a GraphQLSchema to an IntrospectionQuery.\n\nexport { introspectionFromSchema } from './introspectionFromSchema.mjs'; // Build a GraphQLSchema from an introspection result.\n\nexport { buildClientSchema } from './buildClientSchema.mjs'; // Build a GraphQLSchema from GraphQL Schema language.\n\nexport { buildASTSchema, buildSchema } from './buildASTSchema.mjs';\n// Extends an existing GraphQLSchema from a parsed GraphQL Schema language AST.\nexport { extendSchema } from './extendSchema.mjs'; // Sort a GraphQLSchema.\n\nexport { lexicographicSortSchema } from './lexicographicSortSchema.mjs'; // Print a GraphQLSchema to GraphQL Schema language.\n\nexport { printSchema, printType, printIntrospectionSchema } from './printSchema.mjs'; // Create a GraphQLType from a GraphQL language AST.\n\nexport { typeFromAST } from './typeFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST with a type.\n\nexport { valueFromAST } from './valueFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST without a type.\n\nexport { valueFromASTUntyped } from './valueFromASTUntyped.mjs'; // Create a GraphQL language AST from a JavaScript value.\n\nexport { astFromValue } from './astFromValue.mjs'; // A helper to use within recursive-descent visitors which need to be aware of the GraphQL type system.\n\nexport { TypeInfo, visitWithTypeInfo } from './TypeInfo.mjs'; // Coerces a JavaScript value to a GraphQL type, or produces errors.\n\nexport { coerceInputValue } from './coerceInputValue.mjs'; // Concatenates multiple AST together.\n\nexport { concatAST } from './concatAST.mjs'; // Separates an AST into an AST per Operation.\n\nexport { separateOperations } from './separateOperations.mjs'; // Strips characters that are not significant to the validity or execution of a GraphQL document.\n\nexport { stripIgnoredCharacters } from './stripIgnoredCharacters.mjs'; // Comparators for types\n\nexport { isEqualType, isTypeSubTypeOf, doTypesOverlap } from './typeComparators.mjs'; // Asserts that a string is a valid GraphQL name\n\nexport { assertValidName, isValidNameError } from './assertValidName.mjs'; // Compares two GraphQLSchemas and detects breaking changes.\n\nexport { BreakingChangeType, DangerousChangeType, findBreakingChanges, findDangerousChanges } from './findBreakingChanges.mjs';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}