{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\nexport default function asyncIterator(source) {\n  var _a;\n  var iterator = source[Symbol.asyncIterator]();\n  return _a = {\n    next: function () {\n      return iterator.next();\n    }\n  }, _a[Symbol.asyncIterator] = function () {\n    return this;\n  }, _a;\n}", "map": {"version": 3, "names": ["asyncIterator", "source", "_a", "iterator", "Symbol", "next"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@apollo/client/link/http/iterators/async.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\nexport default function asyncIterator(source) {\n    var _a;\n    var iterator = source[Symbol.asyncIterator]();\n    return _a = {\n            next: function () {\n                return iterator.next();\n            }\n        },\n        _a[Symbol.asyncIterator] = function () {\n            return this;\n        },\n        _a;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,aAAaA,CAACC,MAAM,EAAE;EAC1C,IAAIC,EAAE;EACN,IAAIC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACJ,aAAa,CAAC,CAAC,CAAC;EAC7C,OAAOE,EAAE,GAAG;IACJG,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,OAAOF,QAAQ,CAACE,IAAI,CAAC,CAAC;IAC1B;EACJ,CAAC,EACDH,EAAE,CAACE,MAAM,CAACJ,aAAa,CAAC,GAAG,YAAY;IACnC,OAAO,IAAI;EACf,CAAC,EACDE,EAAE;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}