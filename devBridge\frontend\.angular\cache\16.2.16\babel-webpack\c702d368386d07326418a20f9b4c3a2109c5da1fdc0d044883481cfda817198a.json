{"ast": null, "code": "/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nexport function isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nexport function isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nexport function isLetter(code) {\n  return code >= 0x0061 && code <= 0x007a ||\n  // A-Z\n  code >= 0x0041 && code <= 0x005a // a-z\n  ;\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nexport function isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nexport function isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}", "map": {"version": 3, "names": ["isWhiteSpace", "code", "isDigit", "isLetter", "isNameStart", "isNameContinue"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/language/characterClasses.mjs"], "sourcesContent": ["/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nexport function isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nexport function isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nexport function isLetter(code) {\n  return (\n    (code >= 0x0061 && code <= 0x007a) || // A-Z\n    (code >= 0x0041 && code <= 0x005a) // a-z\n  );\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nexport function isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nexport function isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,OAAOA,CAACD,IAAI,EAAE;EAC5B,OAAOA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,QAAQA,CAACF,IAAI,EAAE;EAC7B,OACGA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;EAAK;EACrCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAO,CAAC;EAAA;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASG,WAAWA,CAACH,IAAI,EAAE;EAChC,OAAOE,QAAQ,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,MAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,cAAcA,CAACJ,IAAI,EAAE;EACnC,OAAOE,QAAQ,CAACF,IAAI,CAAC,IAAIC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI,KAAK,MAAM;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}