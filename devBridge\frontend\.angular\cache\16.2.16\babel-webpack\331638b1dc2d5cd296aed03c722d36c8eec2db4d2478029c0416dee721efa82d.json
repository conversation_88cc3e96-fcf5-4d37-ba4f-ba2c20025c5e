{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\n\n/**\n * An instance of this class is passed as the \"this\" context to all validators,\n * allowing access to commonly useful contextual information from within a\n * validation rule.\n */\nexport class ASTValidationContext {\n  constructor(ast, onError) {\n    this._ast = ast;\n    this._fragments = undefined;\n    this._fragmentSpreads = new Map();\n    this._recursivelyReferencedFragments = new Map();\n    this._onError = onError;\n  }\n  get [Symbol.toStringTag]() {\n    return 'ASTValidationContext';\n  }\n  reportError(error) {\n    this._onError(error);\n  }\n  getDocument() {\n    return this._ast;\n  }\n  getFragment(name) {\n    let fragments;\n    if (this._fragments) {\n      fragments = this._fragments;\n    } else {\n      fragments = Object.create(null);\n      for (const defNode of this.getDocument().definitions) {\n        if (defNode.kind === Kind.FRAGMENT_DEFINITION) {\n          fragments[defNode.name.value] = defNode;\n        }\n      }\n      this._fragments = fragments;\n    }\n    return fragments[name];\n  }\n  getFragmentSpreads(node) {\n    let spreads = this._fragmentSpreads.get(node);\n    if (!spreads) {\n      spreads = [];\n      const setsToVisit = [node];\n      let set;\n      while (set = setsToVisit.pop()) {\n        for (const selection of set.selections) {\n          if (selection.kind === Kind.FRAGMENT_SPREAD) {\n            spreads.push(selection);\n          } else if (selection.selectionSet) {\n            setsToVisit.push(selection.selectionSet);\n          }\n        }\n      }\n      this._fragmentSpreads.set(node, spreads);\n    }\n    return spreads;\n  }\n  getRecursivelyReferencedFragments(operation) {\n    let fragments = this._recursivelyReferencedFragments.get(operation);\n    if (!fragments) {\n      fragments = [];\n      const collectedNames = Object.create(null);\n      const nodesToVisit = [operation.selectionSet];\n      let node;\n      while (node = nodesToVisit.pop()) {\n        for (const spread of this.getFragmentSpreads(node)) {\n          const fragName = spread.name.value;\n          if (collectedNames[fragName] !== true) {\n            collectedNames[fragName] = true;\n            const fragment = this.getFragment(fragName);\n            if (fragment) {\n              fragments.push(fragment);\n              nodesToVisit.push(fragment.selectionSet);\n            }\n          }\n        }\n      }\n      this._recursivelyReferencedFragments.set(operation, fragments);\n    }\n    return fragments;\n  }\n}\nexport class SDLValidationContext extends ASTValidationContext {\n  constructor(ast, schema, onError) {\n    super(ast, onError);\n    this._schema = schema;\n  }\n  get [Symbol.toStringTag]() {\n    return 'SDLValidationContext';\n  }\n  getSchema() {\n    return this._schema;\n  }\n}\nexport class ValidationContext extends ASTValidationContext {\n  constructor(schema, ast, typeInfo, onError) {\n    super(ast, onError);\n    this._schema = schema;\n    this._typeInfo = typeInfo;\n    this._variableUsages = new Map();\n    this._recursiveVariableUsages = new Map();\n  }\n  get [Symbol.toStringTag]() {\n    return 'ValidationContext';\n  }\n  getSchema() {\n    return this._schema;\n  }\n  getVariableUsages(node) {\n    let usages = this._variableUsages.get(node);\n    if (!usages) {\n      const newUsages = [];\n      const typeInfo = new TypeInfo(this._schema);\n      visit(node, visitWithTypeInfo(typeInfo, {\n        VariableDefinition: () => false,\n        Variable(variable) {\n          newUsages.push({\n            node: variable,\n            type: typeInfo.getInputType(),\n            defaultValue: typeInfo.getDefaultValue()\n          });\n        }\n      }));\n      usages = newUsages;\n      this._variableUsages.set(node, usages);\n    }\n    return usages;\n  }\n  getRecursiveVariableUsages(operation) {\n    let usages = this._recursiveVariableUsages.get(operation);\n    if (!usages) {\n      usages = this.getVariableUsages(operation);\n      for (const frag of this.getRecursivelyReferencedFragments(operation)) {\n        usages = usages.concat(this.getVariableUsages(frag));\n      }\n      this._recursiveVariableUsages.set(operation, usages);\n    }\n    return usages;\n  }\n  getType() {\n    return this._typeInfo.getType();\n  }\n  getParentType() {\n    return this._typeInfo.getParentType();\n  }\n  getInputType() {\n    return this._typeInfo.getInputType();\n  }\n  getParentInputType() {\n    return this._typeInfo.getParentInputType();\n  }\n  getFieldDef() {\n    return this._typeInfo.getFieldDef();\n  }\n  getDirective() {\n    return this._typeInfo.getDirective();\n  }\n  getArgument() {\n    return this._typeInfo.getArgument();\n  }\n  getEnumValue() {\n    return this._typeInfo.getEnumValue();\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}