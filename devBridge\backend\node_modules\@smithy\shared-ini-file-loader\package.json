{"name": "@smithy/shared-ini-file-loader", "version": "4.0.2", "dependencies": {"@smithy/types": "^4.2.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/node": "^18.11.9", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typedoc": "0.23.23"}, "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types && yarn build:types:downlevel'", "build:cjs": "node ../../scripts/inline shared-ini-file-loader", "build:es": "yarn g:tsc -p tsconfig.es.json", "build:types": "yarn g:tsc -p tsconfig.types.json", "build:types:downlevel": "rimraf dist-types/ts3.4 && downlevel-dts dist-types dist-types/ts3.4", "stage-release": "rimraf ./.release && yarn pack && mkdir ./.release && tar zxvf ./package.tgz --directory ./.release && rm ./package.tgz", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo || exit 0", "lint": "eslint -c ../../.eslintrc.js \"src/**/*.ts\"", "format": "prettier --config ../../prettier.config.js --ignore-path ../../.prettierignore --write \"**/*.{ts,md,json}\"", "extract:docs": "api-extractor run --local", "test": "yarn g:vitest run", "test:watch": "yarn g:vitest watch"}, "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "browser": {"./dist-es/getSSOTokenFromFile": false, "./dist-es/slurpFile": false}, "react-native": {"./dist-cjs/getSSOTokenFromFile": false, "./dist-cjs/slurpFile": false, "./dist-es/getSSOTokenFromFile": false, "./dist-es/slurpFile": false}, "engines": {"node": ">=18.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "homepage": "https://github.com/smithy-lang/smithy-typescript/tree/main/packages/shared-ini-file-loader", "repository": {"type": "git", "url": "https://github.com/smithy-lang/smithy-typescript.git", "directory": "packages/shared-ini-file-loader"}, "typedoc": {"entryPoint": "src/index.ts"}, "publishConfig": {"directory": ".release/package"}}