{"ast": null, "code": "import { didYouMean } from '../jsutils/didYouMean.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { printPathArray } from '../jsutils/printPathArray.mjs';\nimport { suggestionList } from '../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\n\n/**\n * Coerces a JavaScript value given a GraphQL Input Type.\n */\nexport function coerceInputValue(inputValue, type, onError = defaultOnError) {\n  return coerceInputValueImpl(inputValue, type, onError, undefined);\n}\nfunction defaultOnError(path, invalidValue, error) {\n  let errorPrefix = 'Invalid value ' + inspect(invalidValue);\n  if (path.length > 0) {\n    errorPrefix += ` at \"value${printPathArray(path)}\"`;\n  }\n  error.message = errorPrefix + ': ' + error.message;\n  throw error;\n}\nfunction coerceInputValueImpl(inputValue, type, onError, path) {\n  if (isNonNullType(type)) {\n    if (inputValue != null) {\n      return coerceInputValueImpl(inputValue, type.ofType, onError, path);\n    }\n    onError(pathToArray(path), inputValue, new GraphQLError(`Expected non-nullable type \"${inspect(type)}\" not to be null.`));\n    return;\n  }\n  if (inputValue == null) {\n    // Explicitly return the value null.\n    return null;\n  }\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (isIterableObject(inputValue)) {\n      return Array.from(inputValue, (itemValue, index) => {\n        const itemPath = addPath(path, index, undefined);\n        return coerceInputValueImpl(itemValue, itemType, onError, itemPath);\n      });\n    } // Lists accept a non-list value as a list of one.\n\n    return [coerceInputValueImpl(inputValue, itemType, onError, path)];\n  }\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(inputValue)) {\n      onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\" to be an object.`));\n      return;\n    }\n    const coercedValue = {};\n    const fieldDefs = type.getFields();\n    for (const field of Object.values(fieldDefs)) {\n      const fieldValue = inputValue[field.name];\n      if (fieldValue === undefined) {\n        if (field.defaultValue !== undefined) {\n          coercedValue[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          const typeStr = inspect(field.type);\n          onError(pathToArray(path), inputValue, new GraphQLError(`Field \"${field.name}\" of required type \"${typeStr}\" was not provided.`));\n        }\n        continue;\n      }\n      coercedValue[field.name] = coerceInputValueImpl(fieldValue, field.type, onError, addPath(path, field.name, type.name));\n    } // Ensure every provided field is defined.\n\n    for (const fieldName of Object.keys(inputValue)) {\n      if (!fieldDefs[fieldName]) {\n        const suggestions = suggestionList(fieldName, Object.keys(type.getFields()));\n        onError(pathToArray(path), inputValue, new GraphQLError(`Field \"${fieldName}\" is not defined by type \"${type.name}\".` + didYouMean(suggestions)));\n      }\n    }\n    return coercedValue;\n  }\n  if (isLeafType(type)) {\n    let parseResult; // Scalars and Enums determine if a input value is valid via parseValue(),\n    // which can throw to indicate failure. If it throws, maintain a reference\n    // to the original error.\n\n    try {\n      parseResult = type.parseValue(inputValue);\n    } catch (error) {\n      if (error instanceof GraphQLError) {\n        onError(pathToArray(path), inputValue, error);\n      } else {\n        onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\". ` + error.message, {\n          originalError: error\n        }));\n      }\n      return;\n    }\n    if (parseResult === undefined) {\n      onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\".`));\n    }\n    return parseResult;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "inspect", "invariant", "isIterableObject", "isObjectLike", "addPath", "pathToArray", "printPathArray", "suggestionList", "GraphQLError", "isInputObjectType", "isLeafType", "isListType", "isNonNullType", "coerceInputValue", "inputValue", "type", "onError", "defaultOnError", "coerceInputValueImpl", "undefined", "path", "invalidV<PERSON>ue", "error", "errorPrefix", "length", "message", "ofType", "itemType", "Array", "from", "itemValue", "index", "itemPath", "name", "coerced<PERSON><PERSON><PERSON>", "fieldDefs", "getFields", "field", "Object", "values", "fieldValue", "defaultValue", "typeStr", "fieldName", "keys", "suggestions", "parseResult", "parseValue", "originalError"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/coerceInputValue.mjs"], "sourcesContent": ["import { didYouMean } from '../jsutils/didYouMean.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { printPathArray } from '../jsutils/printPathArray.mjs';\nimport { suggestionList } from '../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport {\n  isInputObjectType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n} from '../type/definition.mjs';\n\n/**\n * Coerces a JavaScript value given a GraphQL Input Type.\n */\nexport function coerceInputValue(inputValue, type, onError = defaultOnError) {\n  return coerceInputValueImpl(inputValue, type, onError, undefined);\n}\n\nfunction defaultOnError(path, invalidValue, error) {\n  let errorPrefix = 'Invalid value ' + inspect(invalidValue);\n\n  if (path.length > 0) {\n    errorPrefix += ` at \"value${printPathArray(path)}\"`;\n  }\n\n  error.message = errorPrefix + ': ' + error.message;\n  throw error;\n}\n\nfunction coerceInputValueImpl(inputValue, type, onError, path) {\n  if (isNonNullType(type)) {\n    if (inputValue != null) {\n      return coerceInputValueImpl(inputValue, type.ofType, onError, path);\n    }\n\n    onError(\n      pathToArray(path),\n      inputValue,\n      new GraphQLError(\n        `Expected non-nullable type \"${inspect(type)}\" not to be null.`,\n      ),\n    );\n    return;\n  }\n\n  if (inputValue == null) {\n    // Explicitly return the value null.\n    return null;\n  }\n\n  if (isListType(type)) {\n    const itemType = type.ofType;\n\n    if (isIterableObject(inputValue)) {\n      return Array.from(inputValue, (itemValue, index) => {\n        const itemPath = addPath(path, index, undefined);\n        return coerceInputValueImpl(itemValue, itemType, onError, itemPath);\n      });\n    } // Lists accept a non-list value as a list of one.\n\n    return [coerceInputValueImpl(inputValue, itemType, onError, path)];\n  }\n\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(inputValue)) {\n      onError(\n        pathToArray(path),\n        inputValue,\n        new GraphQLError(`Expected type \"${type.name}\" to be an object.`),\n      );\n      return;\n    }\n\n    const coercedValue = {};\n    const fieldDefs = type.getFields();\n\n    for (const field of Object.values(fieldDefs)) {\n      const fieldValue = inputValue[field.name];\n\n      if (fieldValue === undefined) {\n        if (field.defaultValue !== undefined) {\n          coercedValue[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          const typeStr = inspect(field.type);\n          onError(\n            pathToArray(path),\n            inputValue,\n            new GraphQLError(\n              `Field \"${field.name}\" of required type \"${typeStr}\" was not provided.`,\n            ),\n          );\n        }\n\n        continue;\n      }\n\n      coercedValue[field.name] = coerceInputValueImpl(\n        fieldValue,\n        field.type,\n        onError,\n        addPath(path, field.name, type.name),\n      );\n    } // Ensure every provided field is defined.\n\n    for (const fieldName of Object.keys(inputValue)) {\n      if (!fieldDefs[fieldName]) {\n        const suggestions = suggestionList(\n          fieldName,\n          Object.keys(type.getFields()),\n        );\n        onError(\n          pathToArray(path),\n          inputValue,\n          new GraphQLError(\n            `Field \"${fieldName}\" is not defined by type \"${type.name}\".` +\n              didYouMean(suggestions),\n          ),\n        );\n      }\n    }\n\n    return coercedValue;\n  }\n\n  if (isLeafType(type)) {\n    let parseResult; // Scalars and Enums determine if a input value is valid via parseValue(),\n    // which can throw to indicate failure. If it throws, maintain a reference\n    // to the original error.\n\n    try {\n      parseResult = type.parseValue(inputValue);\n    } catch (error) {\n      if (error instanceof GraphQLError) {\n        onError(pathToArray(path), inputValue, error);\n      } else {\n        onError(\n          pathToArray(path),\n          inputValue,\n          new GraphQLError(`Expected type \"${type.name}\". ` + error.message, {\n            originalError: error,\n          }),\n        );\n      }\n\n      return;\n    }\n\n    if (parseResult === undefined) {\n      onError(\n        pathToArray(path),\n        inputValue,\n        new GraphQLError(`Expected type \"${type.name}\".`),\n      );\n    }\n\n    return parseResult;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,2BAA2B;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SACEC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,aAAa,QACR,wBAAwB;;AAE/B;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,IAAI,EAAEC,OAAO,GAAGC,cAAc,EAAE;EAC3E,OAAOC,oBAAoB,CAACJ,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEG,SAAS,CAAC;AACnE;AAEA,SAASF,cAAcA,CAACG,IAAI,EAAEC,YAAY,EAAEC,KAAK,EAAE;EACjD,IAAIC,WAAW,GAAG,gBAAgB,GAAGvB,OAAO,CAACqB,YAAY,CAAC;EAE1D,IAAID,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;IACnBD,WAAW,IAAK,aAAYjB,cAAc,CAACc,IAAI,CAAE,GAAE;EACrD;EAEAE,KAAK,CAACG,OAAO,GAAGF,WAAW,GAAG,IAAI,GAAGD,KAAK,CAACG,OAAO;EAClD,MAAMH,KAAK;AACb;AAEA,SAASJ,oBAAoBA,CAACJ,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEI,IAAI,EAAE;EAC7D,IAAIR,aAAa,CAACG,IAAI,CAAC,EAAE;IACvB,IAAID,UAAU,IAAI,IAAI,EAAE;MACtB,OAAOI,oBAAoB,CAACJ,UAAU,EAAEC,IAAI,CAACW,MAAM,EAAEV,OAAO,EAAEI,IAAI,CAAC;IACrE;IAEAJ,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CACb,+BAA8BR,OAAO,CAACe,IAAI,CAAE,mBAC/C,CACF,CAAC;IACD;EACF;EAEA,IAAID,UAAU,IAAI,IAAI,EAAE;IACtB;IACA,OAAO,IAAI;EACb;EAEA,IAAIH,UAAU,CAACI,IAAI,CAAC,EAAE;IACpB,MAAMY,QAAQ,GAAGZ,IAAI,CAACW,MAAM;IAE5B,IAAIxB,gBAAgB,CAACY,UAAU,CAAC,EAAE;MAChC,OAAOc,KAAK,CAACC,IAAI,CAACf,UAAU,EAAE,CAACgB,SAAS,EAAEC,KAAK,KAAK;QAClD,MAAMC,QAAQ,GAAG5B,OAAO,CAACgB,IAAI,EAAEW,KAAK,EAAEZ,SAAS,CAAC;QAChD,OAAOD,oBAAoB,CAACY,SAAS,EAAEH,QAAQ,EAAEX,OAAO,EAAEgB,QAAQ,CAAC;MACrE,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF,OAAO,CAACd,oBAAoB,CAACJ,UAAU,EAAEa,QAAQ,EAAEX,OAAO,EAAEI,IAAI,CAAC,CAAC;EACpE;EAEA,IAAIX,iBAAiB,CAACM,IAAI,CAAC,EAAE;IAC3B,IAAI,CAACZ,YAAY,CAACW,UAAU,CAAC,EAAE;MAC7BE,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CAAE,kBAAiBO,IAAI,CAACkB,IAAK,oBAAmB,CAClE,CAAC;MACD;IACF;IAEA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvB,MAAMC,SAAS,GAAGpB,IAAI,CAACqB,SAAS,CAAC,CAAC;IAElC,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACC,MAAM,CAACJ,SAAS,CAAC,EAAE;MAC5C,MAAMK,UAAU,GAAG1B,UAAU,CAACuB,KAAK,CAACJ,IAAI,CAAC;MAEzC,IAAIO,UAAU,KAAKrB,SAAS,EAAE;QAC5B,IAAIkB,KAAK,CAACI,YAAY,KAAKtB,SAAS,EAAE;UACpCe,YAAY,CAACG,KAAK,CAACJ,IAAI,CAAC,GAAGI,KAAK,CAACI,YAAY;QAC/C,CAAC,MAAM,IAAI7B,aAAa,CAACyB,KAAK,CAACtB,IAAI,CAAC,EAAE;UACpC,MAAM2B,OAAO,GAAG1C,OAAO,CAACqC,KAAK,CAACtB,IAAI,CAAC;UACnCC,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CACb,UAAS6B,KAAK,CAACJ,IAAK,uBAAsBS,OAAQ,qBACrD,CACF,CAAC;QACH;QAEA;MACF;MAEAR,YAAY,CAACG,KAAK,CAACJ,IAAI,CAAC,GAAGf,oBAAoB,CAC7CsB,UAAU,EACVH,KAAK,CAACtB,IAAI,EACVC,OAAO,EACPZ,OAAO,CAACgB,IAAI,EAAEiB,KAAK,CAACJ,IAAI,EAAElB,IAAI,CAACkB,IAAI,CACrC,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAMU,SAAS,IAAIL,MAAM,CAACM,IAAI,CAAC9B,UAAU,CAAC,EAAE;MAC/C,IAAI,CAACqB,SAAS,CAACQ,SAAS,CAAC,EAAE;QACzB,MAAME,WAAW,GAAGtC,cAAc,CAChCoC,SAAS,EACTL,MAAM,CAACM,IAAI,CAAC7B,IAAI,CAACqB,SAAS,CAAC,CAAC,CAC9B,CAAC;QACDpB,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CACb,UAASmC,SAAU,6BAA4B5B,IAAI,CAACkB,IAAK,IAAG,GAC3DlC,UAAU,CAAC8C,WAAW,CAC1B,CACF,CAAC;MACH;IACF;IAEA,OAAOX,YAAY;EACrB;EAEA,IAAIxB,UAAU,CAACK,IAAI,CAAC,EAAE;IACpB,IAAI+B,WAAW,CAAC,CAAC;IACjB;IACA;;IAEA,IAAI;MACFA,WAAW,GAAG/B,IAAI,CAACgC,UAAU,CAACjC,UAAU,CAAC;IAC3C,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYd,YAAY,EAAE;QACjCQ,OAAO,CAACX,WAAW,CAACe,IAAI,CAAC,EAAEN,UAAU,EAAEQ,KAAK,CAAC;MAC/C,CAAC,MAAM;QACLN,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CAAE,kBAAiBO,IAAI,CAACkB,IAAK,KAAI,GAAGX,KAAK,CAACG,OAAO,EAAE;UACjEuB,aAAa,EAAE1B;QACjB,CAAC,CACH,CAAC;MACH;MAEA;IACF;IAEA,IAAIwB,WAAW,KAAK3B,SAAS,EAAE;MAC7BH,OAAO,CACLX,WAAW,CAACe,IAAI,CAAC,EACjBN,UAAU,EACV,IAAIN,YAAY,CAAE,kBAAiBO,IAAI,CAACkB,IAAK,IAAG,CAClD,CAAC;IACH;IAEA,OAAOa,WAAW;EACpB;EACA;EACA;;EAEA,KAAK,IAAI7C,SAAS,CAAC,KAAK,EAAE,yBAAyB,GAAGD,OAAO,CAACe,IAAI,CAAC,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}