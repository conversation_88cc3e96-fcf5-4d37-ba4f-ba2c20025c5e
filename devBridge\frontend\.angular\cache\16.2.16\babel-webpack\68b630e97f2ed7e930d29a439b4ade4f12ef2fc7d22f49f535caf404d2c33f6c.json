{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known argument names\n *\n * A GraphQL field is only valid if all supplied arguments are defined by\n * that field.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n * See https://spec.graphql.org/draft/#sec-Directives-Are-In-Valid-Locations\n */\nexport function KnownArgumentNamesRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...KnownArgumentNamesOnDirectivesRule(context),\n    Argument(argNode) {\n      const argDef = context.getArgument();\n      const fieldDef = context.getFieldDef();\n      const parentType = context.getParentType();\n      if (!argDef && fieldDef && parentType) {\n        const argName = argNode.name.value;\n        const knownArgsNames = fieldDef.args.map(arg => arg.name);\n        const suggestions = suggestionList(argName, knownArgsNames);\n        context.reportError(new GraphQLError(`Unknown argument \"${argName}\" on field \"${parentType.name}.${fieldDef.name}\".` + didYouMean(suggestions), {\n          nodes: argNode\n        }));\n      }\n    }\n  };\n}\n/**\n * @internal\n */\n\nexport function KnownArgumentNamesOnDirectivesRule(context) {\n  const directiveArgs = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    directiveArgs[directive.name] = directive.args.map(arg => arg.name);\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argsNodes = (_def$arguments = def.arguments) !== null && _def$arguments !== void 0 ? _def$arguments : [];\n      directiveArgs[def.name.value] = argsNodes.map(arg => arg.name.value);\n    }\n  }\n  return {\n    Directive(directiveNode) {\n      const directiveName = directiveNode.name.value;\n      const knownArgs = directiveArgs[directiveName];\n      if (directiveNode.arguments && knownArgs) {\n        for (const argNode of directiveNode.arguments) {\n          const argName = argNode.name.value;\n          if (!knownArgs.includes(argName)) {\n            const suggestions = suggestionList(argName, knownArgs);\n            context.reportError(new GraphQLError(`Unknown argument \"${argName}\" on directive \"@${directiveName}\".` + didYouMean(suggestions), {\n              nodes: argNode\n            }));\n          }\n        }\n      }\n      return false;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}