{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\n/**\n * Provided a collection of ASTs, presumably each from different files,\n * concatenate the ASTs together into batched AST, useful for validating many\n * GraphQL source files which together represent one conceptual application.\n */\n\nexport function concatAST(documents) {\n  const definitions = [];\n  for (const doc of documents) {\n    definitions.push(...doc.definitions);\n  }\n  return {\n    kind: Kind.DOCUMENT,\n    definitions\n  };\n}", "map": {"version": 3, "names": ["Kind", "concatAST", "documents", "definitions", "doc", "push", "kind", "DOCUMENT"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/concatAST.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\n/**\n * Provided a collection of ASTs, presumably each from different files,\n * concatenate the ASTs together into batched AST, useful for validating many\n * GraphQL source files which together represent one conceptual application.\n */\n\nexport function concatAST(documents) {\n  const definitions = [];\n\n  for (const doc of documents) {\n    definitions.push(...doc.definitions);\n  }\n\n  return {\n    kind: Kind.DOCUMENT,\n    definitions,\n  };\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,SAASA,CAACC,SAAS,EAAE;EACnC,MAAMC,WAAW,GAAG,EAAE;EAEtB,KAAK,MAAMC,GAAG,IAAIF,SAAS,EAAE;IAC3BC,WAAW,CAACE,IAAI,CAAC,GAAGD,GAAG,CAACD,WAAW,CAAC;EACtC;EAEA,OAAO;IACLG,IAAI,EAAEN,IAAI,CAACO,QAAQ;IACnBJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}