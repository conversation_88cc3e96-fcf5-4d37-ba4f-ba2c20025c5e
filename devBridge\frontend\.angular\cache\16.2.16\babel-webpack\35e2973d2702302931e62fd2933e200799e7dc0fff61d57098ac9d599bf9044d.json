{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/task.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@angular/material/expansion\";\nfunction TaskAiAssistantComponent_div_12_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.task.estimatedHours, \"h \");\n  }\n}\nfunction TaskAiAssistantComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"span\", 16)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 16)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TaskAiAssistantComponent_div_12_span_12_Template, 4, 1, \"span\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.task.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.task.priority, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.task.category || \"Non d\\u00E9finie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.task.estimatedHours);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const i_r13 = i0.ɵɵnextContext().index;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.applySuggestion(i_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.loading.applySuggestion);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_div_14_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"strong\");\n    i0.ɵɵtext(2, \"Impact:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r12.impact, \" \");\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-expansion-panel\")(2, \"mat-expansion-panel-header\")(3, \"mat-panel-title\");\n    i0.ɵɵtext(4, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 35)(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"span\", 16)(10, \"strong\");\n    i0.ɵɵtext(11, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 16)(14, \"strong\");\n    i0.ɵɵtext(15, \"Confiance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TaskAiAssistantComponent_div_41_div_7_div_1_div_14_span_17_Template, 4, 1, \"span\", 17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(suggestion_r12.details);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r12.type, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getConfidenceText(suggestion_r12.confidence), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", suggestion_r12.impact);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 28)(6, \"h5\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template, 3, 1, \"button\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, TaskAiAssistantComponent_div_41_div_7_div_1_div_14_Template, 18, 4, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"applied\", suggestion_r12.applied);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", ctx_r11.getConfidenceColor(suggestion_r12.confidence));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getSuggestionIcon(suggestion_r12.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(suggestion_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r11.getConfidenceColor(suggestion_r12.confidence));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (suggestion_r12.confidence * 100).toFixed(0), \"% \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canApplySuggestion(suggestion_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", suggestion_r12.details);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, TaskAiAssistantComponent_div_41_div_7_div_1_Template, 15, 12, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.suggestions);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h4\");\n    i0.ɵɵtext(3, \"Suggestions IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_41_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.toggleSuggestions());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, TaskAiAssistantComponent_div_41_div_7_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.showSuggestions ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showSuggestions);\n  }\n}\nfunction TaskAiAssistantComponent_div_42_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"mat-icon\", 44);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Une nouvelle analyse est recommand\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_42_div_24_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.analyzeTask());\n    });\n    i0.ɵɵtext(6, \" Analyser maintenant \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskAiAssistantComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"h4\");\n    i0.ɵɵtext(2, \"Statistiques IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"div\", 39)(5, \"span\", 40);\n    i0.ɵɵtext(6, \"Derni\\u00E8re analyse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 41);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39)(10, \"span\", 40);\n    i0.ɵɵtext(11, \"Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 41);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"span\", 40);\n    i0.ɵɵtext(16, \"Non appliqu\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 41);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 39)(20, \"span\", 40);\n    i0.ɵɵtext(21, \"Confiance moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 41);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, TaskAiAssistantComponent_div_42_div_24_Template, 7, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.getLastAnalysisTime());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.suggestions.length);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.getUnappliedSuggestionsCount());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r6.getAverageConfidence() * 100).toFixed(0), \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.shouldReanalyze());\n  }\n}\nfunction TaskAiAssistantComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"smart_toy_outlined\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Assistant IA d\\u00E9sactiv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"L'assistant IA n'est pas activ\\u00E9 pour cette t\\u00E2che.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskAiAssistantComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Aucune suggestion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Lancez une analyse pour obtenir des suggestions IA.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_44_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.analyzeTask());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Analyser la t\\u00E2che \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TaskAiAssistantComponent {\n  constructor(taskService, snackBar) {\n    this.taskService = taskService;\n    this.snackBar = snackBar;\n    this.taskUpdated = new EventEmitter();\n    this.subtasksGenerated = new EventEmitter();\n    this.loading = {\n      analyze: false,\n      subtasks: false,\n      estimate: false,\n      description: false,\n      applySuggestion: false\n    };\n    this.suggestions = [];\n    this.showSuggestions = true;\n  }\n  ngOnInit() {\n    this.loadSuggestions();\n  }\n  // Charger les suggestions existantes\n  loadSuggestions() {\n    if (this.task.aiSuggestions?.suggestions) {\n      this.suggestions = this.task.aiSuggestions.suggestions;\n    }\n  }\n  // Analyser la tâche avec l'IA\n  analyzeTask() {\n    if (!this.task._id) return;\n    this.loading.analyze = true;\n    this.taskService.analyzeTaskWithAI(this.task._id).subscribe({\n      next: response => {\n        this.suggestions = response.suggestions;\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.analyze = false;\n        this.snackBar.open(`Analyse terminée - ${response.suggestions.length} suggestion(s) trouvée(s)`, 'Fermer', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        console.error('Erreur analyse IA:', error);\n        this.loading.analyze = false;\n        this.snackBar.open('Erreur lors de l\\'analyse IA', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Générer des sous-tâches avec l'IA\n  generateSubtasks() {\n    if (!this.task._id) return;\n    this.loading.subtasks = true;\n    this.taskService.generateSubtasksWithAI(this.task._id).subscribe({\n      next: response => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.subtasksGenerated.emit(response.subtasks);\n        this.loading.subtasks = false;\n        this.snackBar.open(`${response.subtasks.length} sous-tâche(s) générée(s) avec succès`, 'Fermer', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        console.error('Erreur génération sous-tâches:', error);\n        this.loading.subtasks = false;\n        this.snackBar.open('Erreur lors de la génération des sous-tâches', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Estimer l'effort avec l'IA\n  estimateEffort() {\n    if (!this.task._id) return;\n    this.loading.estimate = true;\n    this.taskService.estimateEffortWithAI(this.task._id).subscribe({\n      next: response => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.estimate = false;\n        this.snackBar.open(`Effort estimé: ${response.estimatedHours}h`, 'Fermer', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        console.error('Erreur estimation effort:', error);\n        this.loading.estimate = false;\n        this.snackBar.open('Erreur lors de l\\'estimation d\\'effort', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Générer une description avec l'IA\n  generateDescription() {\n    if (!this.task._id) return;\n    this.loading.description = true;\n    this.taskService.generateDescriptionWithAI(this.task._id).subscribe({\n      next: response => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.description = false;\n        this.snackBar.open('Description générée avec succès', 'Fermer', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        console.error('Erreur génération description:', error);\n        this.loading.description = false;\n        this.snackBar.open('Erreur lors de la génération de description', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Appliquer une suggestion IA\n  applySuggestion(suggestionIndex) {\n    if (!this.task._id) return;\n    this.loading.applySuggestion = true;\n    this.taskService.applySuggestion(this.task._id, suggestionIndex).subscribe({\n      next: response => {\n        this.task = response.task;\n        this.suggestions[suggestionIndex] = response.suggestion;\n        this.taskUpdated.emit(this.task);\n        this.loading.applySuggestion = false;\n        this.snackBar.open('Suggestion appliquée avec succès', 'Fermer', {\n          duration: 3000\n        });\n      },\n      error: error => {\n        console.error('Erreur application suggestion:', error);\n        this.loading.applySuggestion = false;\n        this.snackBar.open('Erreur lors de l\\'application de la suggestion', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Obtenir l'icône pour un type de suggestion\n  getSuggestionIcon(type) {\n    const icons = {\n      'optimization': 'tune',\n      'assignment': 'person_add',\n      'deadline': 'schedule',\n      'priority': 'flag',\n      'breakdown': 'list'\n    };\n    return icons[type] || 'lightbulb';\n  }\n  // Obtenir la couleur pour un niveau de confiance\n  getConfidenceColor(confidence) {\n    if (confidence >= 0.8) return '#10b981'; // Vert\n    if (confidence >= 0.6) return '#f59e0b'; // Orange\n    return '#ef4444'; // Rouge\n  }\n  // Obtenir le texte pour un niveau de confiance\n  getConfidenceText(confidence) {\n    if (confidence >= 0.8) return 'Haute confiance';\n    if (confidence >= 0.6) return 'Confiance moyenne';\n    return 'Faible confiance';\n  }\n  // Vérifier si une suggestion peut être appliquée automatiquement\n  canApplySuggestion(suggestion) {\n    return !suggestion.applied && ['priority', 'deadline'].includes(suggestion.type);\n  }\n  // Basculer l'affichage des suggestions\n  toggleSuggestions() {\n    this.showSuggestions = !this.showSuggestions;\n  }\n  // Vérifier si l'IA est activée pour cette tâche\n  isAIEnabled() {\n    return this.task.aiSuggestions?.enabled !== false;\n  }\n  // Vérifier si la tâche a été générée automatiquement\n  isAutoGenerated(field) {\n    return this.task.aiSuggestions?.autoGenerated?.[field] || false;\n  }\n  // Obtenir le temps depuis la dernière analyse\n  getLastAnalysisTime() {\n    if (!this.task.aiSuggestions?.lastAnalysis) return 'Jamais';\n    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n    const now = new Date();\n    const diffMs = now.getTime() - lastAnalysis.getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n    if (diffDays > 0) return `Il y a ${diffDays} jour(s)`;\n    if (diffHours > 0) return `Il y a ${diffHours} heure(s)`;\n    return 'Récemment';\n  }\n  // Vérifier si une nouvelle analyse est recommandée\n  shouldReanalyze() {\n    if (!this.task.aiSuggestions?.lastAnalysis) return true;\n    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n    const now = new Date();\n    const diffHours = (now.getTime() - lastAnalysis.getTime()) / (1000 * 60 * 60);\n    return diffHours > 24; // Recommander une nouvelle analyse après 24h\n  }\n  // Obtenir le nombre de suggestions non appliquées\n  getUnappliedSuggestionsCount() {\n    return this.suggestions.filter(s => !s.applied).length;\n  }\n  // Obtenir le score de confiance moyen\n  getAverageConfidence() {\n    if (this.suggestions.length === 0) return 0;\n    const total = this.suggestions.reduce((sum, s) => sum + s.confidence, 0);\n    return total / this.suggestions.length;\n  }\n  static {\n    this.ɵfac = function TaskAiAssistantComponent_Factory(t) {\n      return new (t || TaskAiAssistantComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskAiAssistantComponent,\n      selectors: [[\"app-task-ai-assistant\"]],\n      inputs: {\n        task: \"task\"\n      },\n      outputs: {\n        taskUpdated: \"taskUpdated\",\n        subtasksGenerated: \"subtasksGenerated\"\n      },\n      decls: 45,\n      vars: 17,\n      consts: [[1, \"ai-assistant-container\"], [1, \"ai-header\"], [1, \"ai-title\"], [1, \"ai-status\"], [\"class\", \"task-info\", 4, \"ngIf\"], [1, \"ai-actions\"], [1, \"action-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", 3, \"disabled\", \"click\"], [\"class\", \"ai-suggestions\", 4, \"ngIf\"], [\"class\", \"ai-stats\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"task-info\"], [1, \"task-meta\"], [1, \"meta-item\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"ai-suggestions\"], [1, \"suggestions-header\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"class\", \"suggestions-list\", 4, \"ngIf\"], [1, \"suggestions-list\"], [\"class\", \"suggestion-item\", 3, \"applied\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [1, \"suggestion-header\"], [1, \"suggestion-icon\"], [1, \"suggestion-content\"], [1, \"suggestion-actions\"], [1, \"confidence-badge\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Appliquer la suggestion\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"suggestion-details\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Appliquer la suggestion\", 3, \"disabled\", \"click\"], [1, \"suggestion-details\"], [1, \"details-content\"], [1, \"suggestion-metadata\"], [1, \"ai-stats\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"reanalysis-recommendation\", 4, \"ngIf\"], [1, \"reanalysis-recommendation\"], [\"color\", \"warn\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"empty-state\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function TaskAiAssistantComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h3\");\n          i0.ɵɵtext(6, \"Assistant IA\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, TaskAiAssistantComponent_div_12_Template, 13, 4, \"div\", 4);\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"h4\");\n          i0.ɵɵtext(15, \"Actions disponibles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 6)(17, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_17_listener() {\n            return ctx.analyzeTask();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"Analyser\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, TaskAiAssistantComponent_mat_spinner_22_Template, 1, 0, \"mat-spinner\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_23_listener() {\n            return ctx.generateSubtasks();\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Sous-t\\u00E2ches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, TaskAiAssistantComponent_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_29_listener() {\n            return ctx.estimateEffort();\n          });\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"timer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\");\n          i0.ɵɵtext(33, \"Estimer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, TaskAiAssistantComponent_mat_spinner_34_Template, 1, 0, \"mat-spinner\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_35_listener() {\n            return ctx.generateDescription();\n          });\n          i0.ɵɵelementStart(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"span\");\n          i0.ɵɵtext(39, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, TaskAiAssistantComponent_mat_spinner_40_Template, 1, 0, \"mat-spinner\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(41, TaskAiAssistantComponent_div_41_Template, 8, 2, \"div\", 11);\n          i0.ɵɵtemplate(42, TaskAiAssistantComponent_div_42_Template, 25, 5, \"div\", 12);\n          i0.ɵɵtemplate(43, TaskAiAssistantComponent_div_43_Template, 7, 0, \"div\", 13);\n          i0.ɵɵtemplate(44, TaskAiAssistantComponent_div_44_Template, 11, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"enabled\", ctx.isAIEnabled());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isAIEnabled() ? \"smart_toy\" : \"smart_toy_outlined\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isAIEnabled() ? \"Activ\\u00E9\" : \"D\\u00E9sactiv\\u00E9\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.task);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading.analyze);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading.analyze);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading.subtasks);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading.subtasks);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading.estimate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading.estimate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading.description);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading.description);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.suggestions.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAIEnabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAIEnabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAIEnabled() && ctx.suggestions.length === 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.MatButton, i4.MatIconButton, i5.MatIcon, i6.MatProgressSpinner, i7.MatTooltip, i8.MatExpansionPanel, i8.MatExpansionPanelHeader, i8.MatExpansionPanelTitle],\n      styles: [\".ai-assistant-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #8b5cf6;\\n  font-size: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.875rem;\\n  background: #f1f5f9;\\n  color: #64748b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status.enabled[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #16a34a;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status.enabled[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #16a34a;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  padding: 1rem;\\n  background: #f8fafc;\\n  border-radius: 8px;\\n  border-left: 4px solid #8b5cf6;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  color: #64748b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 0.75rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem;\\n  min-height: 80px;\\n  position: relative;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item.applied[_ngcontent-%COMP%] {\\n  background: #f0fdf4;\\n  border-color: #16a34a;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item.applied[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #f8fafc;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #64748b;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #64748b;\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1e293b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #1e293b;\\n  font-weight: 600;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background: #f8fafc;\\n  border-radius: 6px;\\n  text-align: center;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  margin-bottom: 0.25rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  background: #fef3c7;\\n  border: 1px solid #f59e0b;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #92400e;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  text-align: center;\\n  color: #64748b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #1e293b;\\n}\\n.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n  font-size: 0.875rem;\\n}\\n\\n@media (max-width: 480px) {\\n  .ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .ai-assistant-container[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n    gap: 0.75rem !important;\\n  }\\n  .ai-assistant-container[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r9", "task", "estimatedHours", "ɵɵtemplate", "TaskAiAssistantComponent_div_12_span_12_Template", "ɵɵtextInterpolate", "ctx_r0", "title", "priority", "category", "ɵɵproperty", "ɵɵelement", "ɵɵlistener", "TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template_button_click_0_listener", "ɵɵrestoreView", "_r18", "i_r13", "ɵɵnextContext", "index", "ctx_r16", "ɵɵresetView", "applySuggestion", "ctx_r14", "loading", "suggestion_r12", "impact", "TaskAiAssistantComponent_div_41_div_7_div_1_div_14_span_17_Template", "details", "type", "ctx_r15", "getConfidenceText", "confidence", "TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template", "TaskAiAssistantComponent_div_41_div_7_div_1_div_14_Template", "ɵɵclassProp", "applied", "ɵɵstyleProp", "ctx_r11", "getConfidenceColor", "getSuggestionIcon", "description", "toFixed", "canApplySuggestion", "TaskAiAssistantComponent_div_41_div_7_div_1_Template", "ctx_r10", "suggestions", "TaskAiAssistantComponent_div_41_Template_button_click_4_listener", "_r23", "ctx_r22", "toggleSuggestions", "TaskAiAssistantComponent_div_41_div_7_Template", "ctx_r5", "showSuggestions", "TaskAiAssistantComponent_div_42_div_24_Template_button_click_5_listener", "_r26", "ctx_r25", "analyzeTask", "TaskAiAssistantComponent_div_42_div_24_Template", "ctx_r6", "getLastAnalysisTime", "length", "getUnappliedSuggestionsCount", "getAverageConfidence", "shouldReanalyze", "TaskAiAssistantComponent_div_44_Template_button_click_7_listener", "_r28", "ctx_r27", "TaskAiAssistantComponent", "constructor", "taskService", "snackBar", "taskUpdated", "subtasksGenerated", "analyze", "subtasks", "estimate", "ngOnInit", "loadSuggestions", "aiSuggestions", "_id", "analyzeTaskWithAI", "subscribe", "next", "response", "emit", "open", "duration", "error", "console", "generateSubtasks", "generateSubtasksWithAI", "estimateEffort", "estimateEffortWithAI", "generateDescription", "generateDescriptionWithAI", "suggestionIndex", "suggestion", "icons", "includes", "isAIEnabled", "enabled", "isAutoGenerated", "field", "autoGenerated", "lastAnalysis", "Date", "now", "diffMs", "getTime", "diffHours", "Math", "floor", "diffDays", "filter", "s", "total", "reduce", "sum", "ɵɵdirectiveInject", "i1", "TaskService", "i2", "MatSnackBar", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "TaskAiAssistantComponent_Template", "rf", "ctx", "TaskAiAssistantComponent_div_12_Template", "TaskAiAssistantComponent_Template_button_click_17_listener", "TaskAiAssistantComponent_mat_spinner_22_Template", "TaskAiAssistantComponent_Template_button_click_23_listener", "TaskAiAssistantComponent_mat_spinner_28_Template", "TaskAiAssistantComponent_Template_button_click_29_listener", "TaskAiAssistantComponent_mat_spinner_34_Template", "TaskAiAssistantComponent_Template_button_click_35_listener", "TaskAiAssistantComponent_mat_spinner_40_Template", "TaskAiAssistantComponent_div_41_Template", "TaskAiAssistantComponent_div_42_Template", "TaskAiAssistantComponent_div_43_Template", "TaskAiAssistantComponent_div_44_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\components\\task-ai-assistant\\task-ai-assistant.component.ts", "C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\components\\task-ai-assistant\\task-ai-assistant.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { TaskService } from '../../services/task.service';\nimport { \n  Task, \n  AISuggestion,\n  AIAnalysisResponse,\n  GenerateSubtasksResponse,\n  EstimateEffortResponse,\n  GenerateDescriptionResponse\n} from '../../models/task.model';\n\n@Component({\n  selector: 'app-task-ai-assistant',\n  templateUrl: './task-ai-assistant.component.html',\n  styleUrls: ['./task-ai-assistant.component.scss']\n})\nexport class TaskAiAssistantComponent implements OnInit {\n  @Input() task!: Task;\n  @Output() taskUpdated = new EventEmitter<Task>();\n  @Output() subtasksGenerated = new EventEmitter<Task[]>();\n\n  loading = {\n    analyze: false,\n    subtasks: false,\n    estimate: false,\n    description: false,\n    applySuggestion: false\n  };\n\n  suggestions: AISuggestion[] = [];\n  showSuggestions = true;\n\n  constructor(\n    private taskService: TaskService,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadSuggestions();\n  }\n\n  // Charger les suggestions existantes\n  loadSuggestions(): void {\n    if (this.task.aiSuggestions?.suggestions) {\n      this.suggestions = this.task.aiSuggestions.suggestions;\n    }\n  }\n\n  // Analyser la tâche avec l'IA\n  analyzeTask(): void {\n    if (!this.task._id) return;\n\n    this.loading.analyze = true;\n\n    this.taskService.analyzeTaskWithAI(this.task._id).subscribe({\n      next: (response: AIAnalysisResponse) => {\n        this.suggestions = response.suggestions;\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.analyze = false;\n        \n        this.snackBar.open(\n          `Analyse terminée - ${response.suggestions.length} suggestion(s) trouvée(s)`, \n          'Fermer', \n          { duration: 3000 }\n        );\n      },\n      error: (error) => {\n        console.error('Erreur analyse IA:', error);\n        this.loading.analyze = false;\n        this.snackBar.open('Erreur lors de l\\'analyse IA', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Générer des sous-tâches avec l'IA\n  generateSubtasks(): void {\n    if (!this.task._id) return;\n\n    this.loading.subtasks = true;\n\n    this.taskService.generateSubtasksWithAI(this.task._id).subscribe({\n      next: (response: GenerateSubtasksResponse) => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.subtasksGenerated.emit(response.subtasks);\n        this.loading.subtasks = false;\n        \n        this.snackBar.open(\n          `${response.subtasks.length} sous-tâche(s) générée(s) avec succès`, \n          'Fermer', \n          { duration: 3000 }\n        );\n      },\n      error: (error) => {\n        console.error('Erreur génération sous-tâches:', error);\n        this.loading.subtasks = false;\n        this.snackBar.open('Erreur lors de la génération des sous-tâches', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Estimer l'effort avec l'IA\n  estimateEffort(): void {\n    if (!this.task._id) return;\n\n    this.loading.estimate = true;\n\n    this.taskService.estimateEffortWithAI(this.task._id).subscribe({\n      next: (response: EstimateEffortResponse) => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.estimate = false;\n        \n        this.snackBar.open(\n          `Effort estimé: ${response.estimatedHours}h`, \n          'Fermer', \n          { duration: 3000 }\n        );\n      },\n      error: (error) => {\n        console.error('Erreur estimation effort:', error);\n        this.loading.estimate = false;\n        this.snackBar.open('Erreur lors de l\\'estimation d\\'effort', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Générer une description avec l'IA\n  generateDescription(): void {\n    if (!this.task._id) return;\n\n    this.loading.description = true;\n\n    this.taskService.generateDescriptionWithAI(this.task._id).subscribe({\n      next: (response: GenerateDescriptionResponse) => {\n        this.task = response.task;\n        this.taskUpdated.emit(this.task);\n        this.loading.description = false;\n        \n        this.snackBar.open('Description générée avec succès', 'Fermer', { duration: 3000 });\n      },\n      error: (error) => {\n        console.error('Erreur génération description:', error);\n        this.loading.description = false;\n        this.snackBar.open('Erreur lors de la génération de description', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Appliquer une suggestion IA\n  applySuggestion(suggestionIndex: number): void {\n    if (!this.task._id) return;\n\n    this.loading.applySuggestion = true;\n\n    this.taskService.applySuggestion(this.task._id, suggestionIndex).subscribe({\n      next: (response) => {\n        this.task = response.task;\n        this.suggestions[suggestionIndex] = response.suggestion;\n        this.taskUpdated.emit(this.task);\n        this.loading.applySuggestion = false;\n        \n        this.snackBar.open('Suggestion appliquée avec succès', 'Fermer', { duration: 3000 });\n      },\n      error: (error) => {\n        console.error('Erreur application suggestion:', error);\n        this.loading.applySuggestion = false;\n        this.snackBar.open('Erreur lors de l\\'application de la suggestion', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Obtenir l'icône pour un type de suggestion\n  getSuggestionIcon(type: string): string {\n    const icons = {\n      'optimization': 'tune',\n      'assignment': 'person_add',\n      'deadline': 'schedule',\n      'priority': 'flag',\n      'breakdown': 'list'\n    };\n    return icons[type as keyof typeof icons] || 'lightbulb';\n  }\n\n  // Obtenir la couleur pour un niveau de confiance\n  getConfidenceColor(confidence: number): string {\n    if (confidence >= 0.8) return '#10b981'; // Vert\n    if (confidence >= 0.6) return '#f59e0b'; // Orange\n    return '#ef4444'; // Rouge\n  }\n\n  // Obtenir le texte pour un niveau de confiance\n  getConfidenceText(confidence: number): string {\n    if (confidence >= 0.8) return 'Haute confiance';\n    if (confidence >= 0.6) return 'Confiance moyenne';\n    return 'Faible confiance';\n  }\n\n  // Vérifier si une suggestion peut être appliquée automatiquement\n  canApplySuggestion(suggestion: AISuggestion): boolean {\n    return !suggestion.applied && ['priority', 'deadline'].includes(suggestion.type);\n  }\n\n  // Basculer l'affichage des suggestions\n  toggleSuggestions(): void {\n    this.showSuggestions = !this.showSuggestions;\n  }\n\n  // Vérifier si l'IA est activée pour cette tâche\n  isAIEnabled(): boolean {\n    return this.task.aiSuggestions?.enabled !== false;\n  }\n\n  // Vérifier si la tâche a été générée automatiquement\n  isAutoGenerated(field: string): boolean {\n    return this.task.aiSuggestions?.autoGenerated?.[field as keyof typeof this.task.aiSuggestions.autoGenerated] || false;\n  }\n\n  // Obtenir le temps depuis la dernière analyse\n  getLastAnalysisTime(): string {\n    if (!this.task.aiSuggestions?.lastAnalysis) return 'Jamais';\n    \n    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n    const now = new Date();\n    const diffMs = now.getTime() - lastAnalysis.getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n    \n    if (diffDays > 0) return `Il y a ${diffDays} jour(s)`;\n    if (diffHours > 0) return `Il y a ${diffHours} heure(s)`;\n    return 'Récemment';\n  }\n\n  // Vérifier si une nouvelle analyse est recommandée\n  shouldReanalyze(): boolean {\n    if (!this.task.aiSuggestions?.lastAnalysis) return true;\n    \n    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n    const now = new Date();\n    const diffHours = (now.getTime() - lastAnalysis.getTime()) / (1000 * 60 * 60);\n    \n    return diffHours > 24; // Recommander une nouvelle analyse après 24h\n  }\n\n  // Obtenir le nombre de suggestions non appliquées\n  getUnappliedSuggestionsCount(): number {\n    return this.suggestions.filter(s => !s.applied).length;\n  }\n\n  // Obtenir le score de confiance moyen\n  getAverageConfidence(): number {\n    if (this.suggestions.length === 0) return 0;\n    const total = this.suggestions.reduce((sum, s) => sum + s.confidence, 0);\n    return total / this.suggestions.length;\n  }\n}\n", "<div class=\"ai-assistant-container\">\n  <!-- Header -->\n  <div class=\"ai-header\">\n    <div class=\"ai-title\">\n      <mat-icon>psychology</mat-icon>\n      <h3>Assistant IA</h3>\n    </div>\n    <div class=\"ai-status\" [class.enabled]=\"isAIEnabled()\">\n      <mat-icon>{{ isAIEnabled() ? 'smart_toy' : 'smart_toy_outlined' }}</mat-icon>\n      <span>{{ isAIEnabled() ? 'Activé' : 'Désactivé' }}</span>\n    </div>\n  </div>\n\n  <!-- Informations sur la tâche -->\n  <div class=\"task-info\" *ngIf=\"task\">\n    <h4>{{ task.title }}</h4>\n    <div class=\"task-meta\">\n      <span class=\"meta-item\">\n        <mat-icon>flag</mat-icon>\n        {{ task.priority }}\n      </span>\n      <span class=\"meta-item\">\n        <mat-icon>category</mat-icon>\n        {{ task.category || 'Non définie' }}\n      </span>\n      <span class=\"meta-item\" *ngIf=\"task.estimatedHours\">\n        <mat-icon>schedule</mat-icon>\n        {{ task.estimatedHours }}h\n      </span>\n    </div>\n  </div>\n\n  <!-- Actions IA -->\n  <div class=\"ai-actions\">\n    <h4>Actions disponibles</h4>\n    \n    <div class=\"action-grid\">\n      <!-- Analyser la tâche -->\n      <button mat-raised-button \n              color=\"primary\"\n              [disabled]=\"loading.analyze\"\n              (click)=\"analyzeTask()\">\n        <mat-icon>analytics</mat-icon>\n        <span>Analyser</span>\n        <mat-spinner *ngIf=\"loading.analyze\" diameter=\"20\"></mat-spinner>\n      </button>\n\n      <!-- Générer des sous-tâches -->\n      <button mat-raised-button \n              color=\"accent\"\n              [disabled]=\"loading.subtasks\"\n              (click)=\"generateSubtasks()\">\n        <mat-icon>list</mat-icon>\n        <span>Sous-tâches</span>\n        <mat-spinner *ngIf=\"loading.subtasks\" diameter=\"20\"></mat-spinner>\n      </button>\n\n      <!-- Estimer l'effort -->\n      <button mat-raised-button \n              [disabled]=\"loading.estimate\"\n              (click)=\"estimateEffort()\">\n        <mat-icon>timer</mat-icon>\n        <span>Estimer</span>\n        <mat-spinner *ngIf=\"loading.estimate\" diameter=\"20\"></mat-spinner>\n      </button>\n\n      <!-- Générer description -->\n      <button mat-raised-button \n              [disabled]=\"loading.description\"\n              (click)=\"generateDescription()\">\n        <mat-icon>description</mat-icon>\n        <span>Description</span>\n        <mat-spinner *ngIf=\"loading.description\" diameter=\"20\"></mat-spinner>\n      </button>\n    </div>\n  </div>\n\n  <!-- Suggestions IA -->\n  <div class=\"ai-suggestions\" *ngIf=\"suggestions.length > 0\">\n    <div class=\"suggestions-header\">\n      <h4>Suggestions IA</h4>\n      <button mat-icon-button (click)=\"toggleSuggestions()\">\n        <mat-icon>{{ showSuggestions ? 'expand_less' : 'expand_more' }}</mat-icon>\n      </button>\n    </div>\n\n    <div class=\"suggestions-list\" *ngIf=\"showSuggestions\">\n      <div class=\"suggestion-item\" \n           *ngFor=\"let suggestion of suggestions; let i = index\"\n           [class.applied]=\"suggestion.applied\">\n        \n        <div class=\"suggestion-header\">\n          <div class=\"suggestion-icon\">\n            <mat-icon [style.color]=\"getConfidenceColor(suggestion.confidence)\">\n              {{ getSuggestionIcon(suggestion.type) }}\n            </mat-icon>\n          </div>\n          \n          <div class=\"suggestion-content\">\n            <h5>{{ suggestion.title }}</h5>\n            <p>{{ suggestion.description }}</p>\n          </div>\n          \n          <div class=\"suggestion-actions\">\n            <div class=\"confidence-badge\" \n                 [style.background-color]=\"getConfidenceColor(suggestion.confidence)\">\n              {{ (suggestion.confidence * 100).toFixed(0) }}%\n            </div>\n            \n            <button mat-icon-button \n                    *ngIf=\"canApplySuggestion(suggestion)\"\n                    [disabled]=\"loading.applySuggestion\"\n                    (click)=\"applySuggestion(i)\"\n                    matTooltip=\"Appliquer la suggestion\">\n              <mat-icon>check</mat-icon>\n            </button>\n          </div>\n        </div>\n\n        <div class=\"suggestion-details\" *ngIf=\"suggestion.details\">\n          <mat-expansion-panel>\n            <mat-expansion-panel-header>\n              <mat-panel-title>Détails</mat-panel-title>\n            </mat-expansion-panel-header>\n            <div class=\"details-content\">\n              <p>{{ suggestion.details }}</p>\n              <div class=\"suggestion-metadata\">\n                <span class=\"meta-item\">\n                  <strong>Type:</strong> {{ suggestion.type }}\n                </span>\n                <span class=\"meta-item\">\n                  <strong>Confiance:</strong> {{ getConfidenceText(suggestion.confidence) }}\n                </span>\n                <span class=\"meta-item\" *ngIf=\"suggestion.impact\">\n                  <strong>Impact:</strong> {{ suggestion.impact }}\n                </span>\n              </div>\n            </div>\n          </mat-expansion-panel>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Statistiques IA -->\n  <div class=\"ai-stats\" *ngIf=\"isAIEnabled()\">\n    <h4>Statistiques IA</h4>\n    \n    <div class=\"stats-grid\">\n      <div class=\"stat-item\">\n        <span class=\"stat-label\">Dernière analyse</span>\n        <span class=\"stat-value\">{{ getLastAnalysisTime() }}</span>\n      </div>\n      \n      <div class=\"stat-item\">\n        <span class=\"stat-label\">Suggestions</span>\n        <span class=\"stat-value\">{{ suggestions.length }}</span>\n      </div>\n      \n      <div class=\"stat-item\">\n        <span class=\"stat-label\">Non appliquées</span>\n        <span class=\"stat-value\">{{ getUnappliedSuggestionsCount() }}</span>\n      </div>\n      \n      <div class=\"stat-item\">\n        <span class=\"stat-label\">Confiance moyenne</span>\n        <span class=\"stat-value\">{{ (getAverageConfidence() * 100).toFixed(0) }}%</span>\n      </div>\n    </div>\n\n    <!-- Recommandation de nouvelle analyse -->\n    <div class=\"reanalysis-recommendation\" *ngIf=\"shouldReanalyze()\">\n      <mat-icon color=\"warn\">warning</mat-icon>\n      <span>Une nouvelle analyse est recommandée</span>\n      <button mat-button color=\"primary\" (click)=\"analyzeTask()\">\n        Analyser maintenant\n      </button>\n    </div>\n  </div>\n\n  <!-- État vide -->\n  <div class=\"empty-state\" *ngIf=\"!isAIEnabled()\">\n    <mat-icon>smart_toy_outlined</mat-icon>\n    <h4>Assistant IA désactivé</h4>\n    <p>L'assistant IA n'est pas activé pour cette tâche.</p>\n  </div>\n\n  <div class=\"empty-state\" *ngIf=\"isAIEnabled() && suggestions.length === 0\">\n    <mat-icon>lightbulb_outline</mat-icon>\n    <h4>Aucune suggestion</h4>\n    <p>Lancez une analyse pour obtenir des suggestions IA.</p>\n    <button mat-raised-button color=\"primary\" (click)=\"analyzeTask()\">\n      <mat-icon>analytics</mat-icon>\n      Analyser la tâche\n    </button>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2CA,YAAY,QAAQ,eAAe;;;;;;;;;;;;ICyBxEC,EAAA,CAAAC,cAAA,eAAoD;IACxCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,IAAA,CAAAC,cAAA,OACF;;;;;IAdJR,EAAA,CAAAC,cAAA,cAAoC;IAC9BD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,cAAuB;IAETD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAwB;IACZD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAS,UAAA,KAAAC,gDAAA,mBAGO;IACTV,EAAA,CAAAG,YAAA,EAAM;;;;IAdFH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAL,IAAA,CAAAM,KAAA,CAAgB;IAIhBb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAO,MAAA,CAAAL,IAAA,CAAAO,QAAA,MACF;IAGEd,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAO,MAAA,CAAAL,IAAA,CAAAQ,QAAA,4BACF;IACyBf,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAgB,UAAA,SAAAJ,MAAA,CAAAL,IAAA,CAAAC,cAAA,CAAyB;;;;;IAmBhDR,EAAA,CAAAiB,SAAA,sBAAiE;;;;;IAUjEjB,EAAA,CAAAiB,SAAA,sBAAkE;;;;;IASlEjB,EAAA,CAAAiB,SAAA,sBAAkE;;;;;IASlEjB,EAAA,CAAAiB,SAAA,sBAAqE;;;;;;IAqCjEjB,EAAA,CAAAC,cAAA,iBAI6C;IADrCD,EAAA,CAAAkB,UAAA,mBAAAC,uFAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAAtB,EAAA,CAAAuB,aAAA,GAAAC,KAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAA0B,WAAA,CAAAD,OAAA,CAAAE,eAAA,CAAAL,KAAA,CAAkB;IAAA,EAAC;IAElCtB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAHpBH,EAAA,CAAAgB,UAAA,aAAAY,OAAA,CAAAC,OAAA,CAAAF,eAAA,CAAoC;;;;;IAsBxC3B,EAAA,CAAAC,cAAA,eAAkD;IACxCD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC3B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADoBH,EAAA,CAAAI,SAAA,GAC3B;IAD2BJ,EAAA,CAAAK,kBAAA,MAAAyB,cAAA,CAAAC,MAAA,MAC3B;;;;;IAhBR/B,EAAA,CAAAC,cAAA,cAA2D;IAGpCD,EAAA,CAAAE,MAAA,mBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAE5CH,EAAA,CAAAC,cAAA,cAA6B;IACxBD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/BH,EAAA,CAAAC,cAAA,cAAiC;IAErBD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAwB;IACdD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAS,UAAA,KAAAuB,mEAAA,mBAEO;IACThC,EAAA,CAAAG,YAAA,EAAM;;;;;IAXHH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAW,iBAAA,CAAAmB,cAAA,CAAAG,OAAA,CAAwB;IAGAjC,EAAA,CAAAI,SAAA,GACzB;IADyBJ,EAAA,CAAAK,kBAAA,MAAAyB,cAAA,CAAAI,IAAA,MACzB;IAE8BlC,EAAA,CAAAI,SAAA,GAC9B;IAD8BJ,EAAA,CAAAK,kBAAA,MAAA8B,OAAA,CAAAC,iBAAA,CAAAN,cAAA,CAAAO,UAAA,OAC9B;IACyBrC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAgB,UAAA,SAAAc,cAAA,CAAAC,MAAA,CAAuB;;;;;IA9C1D/B,EAAA,CAAAC,cAAA,cAE0C;IAKlCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAGbH,EAAA,CAAAC,cAAA,cAAgC;IAC1BD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGrCH,EAAA,CAAAC,cAAA,eAAgC;IAG5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAS,UAAA,KAAA6B,8DAAA,qBAMS;IACXtC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAS,UAAA,KAAA8B,2DAAA,mBAoBM;IACRvC,EAAA,CAAAG,YAAA,EAAM;;;;;IAnDDH,EAAA,CAAAwC,WAAA,YAAAV,cAAA,CAAAW,OAAA,CAAoC;IAIzBzC,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA0C,WAAA,UAAAC,OAAA,CAAAC,kBAAA,CAAAd,cAAA,CAAAO,UAAA,EAAyD;IACjErC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsC,OAAA,CAAAE,iBAAA,CAAAf,cAAA,CAAAI,IAAA,OACF;IAIIlC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,iBAAA,CAAAmB,cAAA,CAAAjB,KAAA,CAAsB;IACvBb,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAW,iBAAA,CAAAmB,cAAA,CAAAgB,WAAA,CAA4B;IAK1B9C,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAA0C,WAAA,qBAAAC,OAAA,CAAAC,kBAAA,CAAAd,cAAA,CAAAO,UAAA,EAAoE;IACvErC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAyB,cAAA,CAAAO,UAAA,QAAAU,OAAA,UACF;IAGS/C,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAgB,UAAA,SAAA2B,OAAA,CAAAK,kBAAA,CAAAlB,cAAA,EAAoC;IAShB9B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAgB,UAAA,SAAAc,cAAA,CAAAG,OAAA,CAAwB;;;;;IAjC7DjC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAS,UAAA,IAAAwC,oDAAA,oBAqDM;IACRjD,EAAA,CAAAG,YAAA,EAAM;;;;IArDwBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAgB,UAAA,YAAAkC,OAAA,CAAAC,WAAA,CAAgB;;;;;;IAVhDnD,EAAA,CAAAC,cAAA,cAA2D;IAEnDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,iBAAsD;IAA9BD,EAAA,CAAAkB,UAAA,mBAAAkC,iEAAA;MAAApD,EAAA,CAAAoB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAA0B,WAAA,CAAA4B,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnDvD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI9EH,EAAA,CAAAS,UAAA,IAAA+C,8CAAA,kBAuDM;IACRxD,EAAA,CAAAG,YAAA,EAAM;;;;IA5DUH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAW,iBAAA,CAAA8C,MAAA,CAAAC,eAAA,iCAAqD;IAIpC1D,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAgB,UAAA,SAAAyC,MAAA,CAAAC,eAAA,CAAqB;;;;;;IAqFpD1D,EAAA,CAAAC,cAAA,cAAiE;IACxCD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,gDAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAA2D;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAyC,wEAAA;MAAA3D,EAAA,CAAAoB,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAA0B,WAAA,CAAAmC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACxD9D,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA/BbH,EAAA,CAAAC,cAAA,cAA4C;IACtCD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAExBH,EAAA,CAAAC,cAAA,cAAwB;IAEKD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7DH,EAAA,CAAAC,cAAA,cAAuB;IACID,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG1DH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,2BAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGtEH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpFH,EAAA,CAAAS,UAAA,KAAAsD,+CAAA,kBAMM;IACR/D,EAAA,CAAAG,YAAA,EAAM;;;;IA3ByBH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAW,iBAAA,CAAAqD,MAAA,CAAAC,mBAAA,GAA2B;IAK3BjE,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAW,iBAAA,CAAAqD,MAAA,CAAAb,WAAA,CAAAe,MAAA,CAAwB;IAKxBlE,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAW,iBAAA,CAAAqD,MAAA,CAAAG,4BAAA,GAAoC;IAKpCnE,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,kBAAA,MAAA2D,MAAA,CAAAI,oBAAA,UAAArB,OAAA,SAAgD;IAKrC/C,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAgB,UAAA,SAAAgD,MAAA,CAAAK,eAAA,GAAuB;;;;;IAUjErE,EAAA,CAAAC,cAAA,cAAgD;IACpCD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kEAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAG1DH,EAAA,CAAAC,cAAA,cAA2E;IAC/DD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1DH,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAoD,iEAAA;MAAAtE,EAAA,CAAAoB,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAA0B,WAAA,CAAA8C,OAAA,CAAAV,WAAA,EAAa;IAAA,EAAC;IAC/D9D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADjLb,OAAM,MAAOsE,wBAAwB;EAgBnCC,YACUC,WAAwB,EACxBC,QAAqB;IADrB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAhBR,KAAAC,WAAW,GAAG,IAAI9E,YAAY,EAAQ;IACtC,KAAA+E,iBAAiB,GAAG,IAAI/E,YAAY,EAAU;IAExD,KAAA8B,OAAO,GAAG;MACRkD,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfnC,WAAW,EAAE,KAAK;MAClBnB,eAAe,EAAE;KAClB;IAED,KAAAwB,WAAW,GAAmB,EAAE;IAChC,KAAAO,eAAe,GAAG,IAAI;EAKnB;EAEHwB,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAA,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5E,IAAI,CAAC6E,aAAa,EAAEjC,WAAW,EAAE;MACxC,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC5C,IAAI,CAAC6E,aAAa,CAACjC,WAAW;;EAE1D;EAEA;EACAW,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACvD,IAAI,CAAC8E,GAAG,EAAE;IAEpB,IAAI,CAACxD,OAAO,CAACkD,OAAO,GAAG,IAAI;IAE3B,IAAI,CAACJ,WAAW,CAACW,iBAAiB,CAAC,IAAI,CAAC/E,IAAI,CAAC8E,GAAG,CAAC,CAACE,SAAS,CAAC;MAC1DC,IAAI,EAAGC,QAA4B,IAAI;QACrC,IAAI,CAACtC,WAAW,GAAGsC,QAAQ,CAACtC,WAAW;QACvC,IAAI,CAAC5C,IAAI,GAAGkF,QAAQ,CAAClF,IAAI;QACzB,IAAI,CAACsE,WAAW,CAACa,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC;QAChC,IAAI,CAACsB,OAAO,CAACkD,OAAO,GAAG,KAAK;QAE5B,IAAI,CAACH,QAAQ,CAACe,IAAI,CAChB,sBAAsBF,QAAQ,CAACtC,WAAW,CAACe,MAAM,2BAA2B,EAC5E,QAAQ,EACR;UAAE0B,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,IAAI,CAAChE,OAAO,CAACkD,OAAO,GAAG,KAAK;QAC5B,IAAI,CAACH,QAAQ,CAACe,IAAI,CAAC,8BAA8B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAClF;KACD,CAAC;EACJ;EAEA;EACAG,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACxF,IAAI,CAAC8E,GAAG,EAAE;IAEpB,IAAI,CAACxD,OAAO,CAACmD,QAAQ,GAAG,IAAI;IAE5B,IAAI,CAACL,WAAW,CAACqB,sBAAsB,CAAC,IAAI,CAACzF,IAAI,CAAC8E,GAAG,CAAC,CAACE,SAAS,CAAC;MAC/DC,IAAI,EAAGC,QAAkC,IAAI;QAC3C,IAAI,CAAClF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI;QACzB,IAAI,CAACsE,WAAW,CAACa,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC;QAChC,IAAI,CAACuE,iBAAiB,CAACY,IAAI,CAACD,QAAQ,CAACT,QAAQ,CAAC;QAC9C,IAAI,CAACnD,OAAO,CAACmD,QAAQ,GAAG,KAAK;QAE7B,IAAI,CAACJ,QAAQ,CAACe,IAAI,CAChB,GAAGF,QAAQ,CAACT,QAAQ,CAACd,MAAM,uCAAuC,EAClE,QAAQ,EACR;UAAE0B,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAChE,OAAO,CAACmD,QAAQ,GAAG,KAAK;QAC7B,IAAI,CAACJ,QAAQ,CAACe,IAAI,CAAC,8CAA8C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAClG;KACD,CAAC;EACJ;EAEA;EACAK,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC1F,IAAI,CAAC8E,GAAG,EAAE;IAEpB,IAAI,CAACxD,OAAO,CAACoD,QAAQ,GAAG,IAAI;IAE5B,IAAI,CAACN,WAAW,CAACuB,oBAAoB,CAAC,IAAI,CAAC3F,IAAI,CAAC8E,GAAG,CAAC,CAACE,SAAS,CAAC;MAC7DC,IAAI,EAAGC,QAAgC,IAAI;QACzC,IAAI,CAAClF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI;QACzB,IAAI,CAACsE,WAAW,CAACa,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC;QAChC,IAAI,CAACsB,OAAO,CAACoD,QAAQ,GAAG,KAAK;QAE7B,IAAI,CAACL,QAAQ,CAACe,IAAI,CAChB,kBAAkBF,QAAQ,CAACjF,cAAc,GAAG,EAC5C,QAAQ,EACR;UAAEoF,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAChE,OAAO,CAACoD,QAAQ,GAAG,KAAK;QAC7B,IAAI,CAACL,QAAQ,CAACe,IAAI,CAAC,wCAAwC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC5F;KACD,CAAC;EACJ;EAEA;EACAO,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC5F,IAAI,CAAC8E,GAAG,EAAE;IAEpB,IAAI,CAACxD,OAAO,CAACiB,WAAW,GAAG,IAAI;IAE/B,IAAI,CAAC6B,WAAW,CAACyB,yBAAyB,CAAC,IAAI,CAAC7F,IAAI,CAAC8E,GAAG,CAAC,CAACE,SAAS,CAAC;MAClEC,IAAI,EAAGC,QAAqC,IAAI;QAC9C,IAAI,CAAClF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI;QACzB,IAAI,CAACsE,WAAW,CAACa,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC;QAChC,IAAI,CAACsB,OAAO,CAACiB,WAAW,GAAG,KAAK;QAEhC,IAAI,CAAC8B,QAAQ,CAACe,IAAI,CAAC,iCAAiC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACrF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAChE,OAAO,CAACiB,WAAW,GAAG,KAAK;QAChC,IAAI,CAAC8B,QAAQ,CAACe,IAAI,CAAC,6CAA6C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACjG;KACD,CAAC;EACJ;EAEA;EACAjE,eAAeA,CAAC0E,eAAuB;IACrC,IAAI,CAAC,IAAI,CAAC9F,IAAI,CAAC8E,GAAG,EAAE;IAEpB,IAAI,CAACxD,OAAO,CAACF,eAAe,GAAG,IAAI;IAEnC,IAAI,CAACgD,WAAW,CAAChD,eAAe,CAAC,IAAI,CAACpB,IAAI,CAAC8E,GAAG,EAAEgB,eAAe,CAAC,CAACd,SAAS,CAAC;MACzEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI;QACzB,IAAI,CAAC4C,WAAW,CAACkD,eAAe,CAAC,GAAGZ,QAAQ,CAACa,UAAU;QACvD,IAAI,CAACzB,WAAW,CAACa,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC;QAChC,IAAI,CAACsB,OAAO,CAACF,eAAe,GAAG,KAAK;QAEpC,IAAI,CAACiD,QAAQ,CAACe,IAAI,CAAC,kCAAkC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACtF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAChE,OAAO,CAACF,eAAe,GAAG,KAAK;QACpC,IAAI,CAACiD,QAAQ,CAACe,IAAI,CAAC,gDAAgD,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACpG;KACD,CAAC;EACJ;EAEA;EACA/C,iBAAiBA,CAACX,IAAY;IAC5B,MAAMqE,KAAK,GAAG;MACZ,cAAc,EAAE,MAAM;MACtB,YAAY,EAAE,YAAY;MAC1B,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,MAAM;MAClB,WAAW,EAAE;KACd;IACD,OAAOA,KAAK,CAACrE,IAA0B,CAAC,IAAI,WAAW;EACzD;EAEA;EACAU,kBAAkBA,CAACP,UAAkB;IACnC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACzC,OAAO,SAAS,CAAC,CAAC;EACpB;EAEA;EACAD,iBAAiBA,CAACC,UAAkB;IAClC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,iBAAiB;IAC/C,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,mBAAmB;IACjD,OAAO,kBAAkB;EAC3B;EAEA;EACAW,kBAAkBA,CAACsD,UAAwB;IACzC,OAAO,CAACA,UAAU,CAAC7D,OAAO,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC+D,QAAQ,CAACF,UAAU,CAACpE,IAAI,CAAC;EAClF;EAEA;EACAqB,iBAAiBA,CAAA;IACf,IAAI,CAACG,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;EACA+C,WAAWA,CAAA;IACT,OAAO,IAAI,CAAClG,IAAI,CAAC6E,aAAa,EAAEsB,OAAO,KAAK,KAAK;EACnD;EAEA;EACAC,eAAeA,CAACC,KAAa;IAC3B,OAAO,IAAI,CAACrG,IAAI,CAAC6E,aAAa,EAAEyB,aAAa,GAAGD,KAA2D,CAAC,IAAI,KAAK;EACvH;EAEA;EACA3C,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC1D,IAAI,CAAC6E,aAAa,EAAE0B,YAAY,EAAE,OAAO,QAAQ;IAE3D,MAAMA,YAAY,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACxG,IAAI,CAAC6E,aAAa,CAAC0B,YAAY,CAAC;IACnE,MAAME,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,MAAM,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGJ,YAAY,CAACI,OAAO,EAAE;IACrD,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMK,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;IAE3C,IAAIG,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAUA,QAAQ,UAAU;IACrD,IAAIH,SAAS,GAAG,CAAC,EAAE,OAAO,UAAUA,SAAS,WAAW;IACxD,OAAO,WAAW;EACpB;EAEA;EACA9C,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC9D,IAAI,CAAC6E,aAAa,EAAE0B,YAAY,EAAE,OAAO,IAAI;IAEvD,MAAMA,YAAY,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACxG,IAAI,CAAC6E,aAAa,CAAC0B,YAAY,CAAC;IACnE,MAAME,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMI,SAAS,GAAG,CAACH,GAAG,CAACE,OAAO,EAAE,GAAGJ,YAAY,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAE7E,OAAOC,SAAS,GAAG,EAAE,CAAC,CAAC;EACzB;EAEA;EACAhD,4BAA4BA,CAAA;IAC1B,OAAO,IAAI,CAAChB,WAAW,CAACoE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC/E,OAAO,CAAC,CAACyB,MAAM;EACxD;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACjB,WAAW,CAACe,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC3C,MAAMuD,KAAK,GAAG,IAAI,CAACtE,WAAW,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACnF,UAAU,EAAE,CAAC,CAAC;IACxE,OAAOoF,KAAK,GAAG,IAAI,CAACtE,WAAW,CAACe,MAAM;EACxC;;;uBA/OWO,wBAAwB,EAAAzE,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAxBvD,wBAAwB;MAAAwD,SAAA;MAAAC,MAAA;QAAA3H,IAAA;MAAA;MAAA4H,OAAA;QAAAtD,WAAA;QAAAC,iBAAA;MAAA;MAAAsD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrCzI,EAAA,CAAAC,cAAA,aAAoC;UAIpBD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEvBH,EAAA,CAAAC,cAAA,aAAuD;UAC3CD,EAAA,CAAAE,MAAA,GAAwD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7EH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK7DH,EAAA,CAAAS,UAAA,KAAAkI,wCAAA,kBAgBM;UAGN3I,EAAA,CAAAC,cAAA,cAAwB;UAClBD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,cAAyB;UAKfD,EAAA,CAAAkB,UAAA,mBAAA0H,2DAAA;YAAA,OAASF,GAAA,CAAA5E,WAAA,EAAa;UAAA,EAAC;UAC7B9D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAS,UAAA,KAAAoI,gDAAA,yBAAiE;UACnE7I,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,iBAGqC;UAA7BD,EAAA,CAAAkB,UAAA,mBAAA4H,2DAAA;YAAA,OAASJ,GAAA,CAAA3C,gBAAA,EAAkB;UAAA,EAAC;UAClC/F,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAS,UAAA,KAAAsI,gDAAA,yBAAkE;UACpE/I,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,kBAEmC;UAA3BD,EAAA,CAAAkB,UAAA,mBAAA8H,2DAAA;YAAA,OAASN,GAAA,CAAAzC,cAAA,EAAgB;UAAA,EAAC;UAChCjG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAS,UAAA,KAAAwI,gDAAA,yBAAkE;UACpEjJ,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,kBAEwC;UAAhCD,EAAA,CAAAkB,UAAA,mBAAAgI,2DAAA;YAAA,OAASR,GAAA,CAAAvC,mBAAA,EAAqB;UAAA,EAAC;UACrCnG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAS,UAAA,KAAA0I,gDAAA,yBAAqE;UACvEnJ,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAS,UAAA,KAAA2I,wCAAA,kBAgEM;UAGNpJ,EAAA,CAAAS,UAAA,KAAA4I,wCAAA,mBAiCM;UAGNrJ,EAAA,CAAAS,UAAA,KAAA6I,wCAAA,kBAIM;UAENtJ,EAAA,CAAAS,UAAA,KAAA8I,wCAAA,mBAQM;UACRvJ,EAAA,CAAAG,YAAA,EAAM;;;UA7LqBH,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAwC,WAAA,YAAAkG,GAAA,CAAAjC,WAAA,GAA+B;UAC1CzG,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAW,iBAAA,CAAA+H,GAAA,CAAAjC,WAAA,wCAAwD;UAC5DzG,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAW,iBAAA,CAAA+H,GAAA,CAAAjC,WAAA,2CAA4C;UAK9BzG,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAAnI,IAAA,CAAU;UA0BtBP,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAgB,UAAA,aAAA0H,GAAA,CAAA7G,OAAA,CAAAkD,OAAA,CAA4B;UAIpB/E,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAA7G,OAAA,CAAAkD,OAAA,CAAqB;UAM7B/E,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAgB,UAAA,aAAA0H,GAAA,CAAA7G,OAAA,CAAAmD,QAAA,CAA6B;UAIrBhF,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAA7G,OAAA,CAAAmD,QAAA,CAAsB;UAK9BhF,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAgB,UAAA,aAAA0H,GAAA,CAAA7G,OAAA,CAAAoD,QAAA,CAA6B;UAIrBjF,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAA7G,OAAA,CAAAoD,QAAA,CAAsB;UAK9BjF,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAgB,UAAA,aAAA0H,GAAA,CAAA7G,OAAA,CAAAiB,WAAA,CAAgC;UAIxB9C,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAA7G,OAAA,CAAAiB,WAAA,CAAyB;UAMhB9C,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAAvF,WAAA,CAAAe,MAAA,KAA4B;UAmElClE,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAAjC,WAAA,GAAmB;UAoChBzG,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAgB,UAAA,UAAA0H,GAAA,CAAAjC,WAAA,GAAoB;UAMpBzG,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAgB,UAAA,SAAA0H,GAAA,CAAAjC,WAAA,MAAAiC,GAAA,CAAAvF,WAAA,CAAAe,MAAA,OAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}