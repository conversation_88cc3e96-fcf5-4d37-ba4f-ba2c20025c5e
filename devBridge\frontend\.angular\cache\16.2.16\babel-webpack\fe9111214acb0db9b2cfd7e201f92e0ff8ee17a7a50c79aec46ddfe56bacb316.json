{"ast": null, "code": "import { Kind } from \"graphql\";\nimport { <PERSON>Impl, SetImpl, warnOnImproperCacheImplementation } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport equal from \"@wry/equality\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { createFragmentMap, getFragmentDefinitions } from \"../utilities/index.js\";\n/** @internal */\nexport function maskFragment(data, document, cache, fragmentName) {\n  if (!cache.fragmentMatches) {\n    if (globalThis.__DEV__ !== false) {\n      warnOnImproperCacheImplementation();\n    }\n    return data;\n  }\n  var fragments = document.definitions.filter(function (node) {\n    return node.kind === Kind.FRAGMENT_DEFINITION;\n  });\n  if (typeof fragmentName === \"undefined\") {\n    invariant(fragments.length === 1, 49, fragments.length);\n    fragmentName = fragments[0].name.value;\n  }\n  var fragment = fragments.find(function (fragment) {\n    return fragment.name.value === fragmentName;\n  });\n  invariant(!!fragment, 50, fragmentName);\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n  if (equal(data, {})) {\n    // Return early and skip the masking algorithm if we don't have any data\n    // yet. This can happen when cache.diff returns an empty object which is\n    // used from watchFragment.\n    return data;\n  }\n  return maskDefinition(data, fragment.selectionSet, {\n    operationType: \"fragment\",\n    operationName: fragment.name.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache: cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl()\n  });\n}\n//# sourceMappingURL=maskFragment.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}