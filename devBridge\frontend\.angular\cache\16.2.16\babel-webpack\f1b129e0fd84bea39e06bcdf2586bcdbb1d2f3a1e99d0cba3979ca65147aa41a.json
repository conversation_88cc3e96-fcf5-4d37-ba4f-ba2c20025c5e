{"ast": null, "code": "import { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { getLocation } from '../language/location.mjs';\nimport { printLocation, printSourceLocation } from '../language/printLocation.mjs';\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5]\n    };\n  }\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nexport class GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n    const {\n      nodes,\n      source,\n      positions,\n      path,\n      originalError,\n      extensions\n    } = toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError = originalError !== null && originalError !== void 0 ? originalError : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined);\n    const nodeLocations = undefinedIfEmpty((_this$nodes = this.nodes) === null || _this$nodes === void 0 ? void 0 : _this$nodes.map(node => node.loc).filter(loc => loc != null)); // Compute locations in the source for the given nodes/positions.\n\n    this.source = source !== null && source !== void 0 ? source : nodeLocations === null || nodeLocations === void 0 ? void 0 : (_nodeLocations$ = nodeLocations[0]) === null || _nodeLocations$ === void 0 ? void 0 : _nodeLocations$.source;\n    this.positions = positions !== null && positions !== void 0 ? positions : nodeLocations === null || nodeLocations === void 0 ? void 0 : nodeLocations.map(loc => loc.start);\n    this.locations = positions && source ? positions.map(pos => getLocation(source, pos)) : nodeLocations === null || nodeLocations === void 0 ? void 0 : nodeLocations.map(loc => getLocation(loc.source, loc.start));\n    const originalExtensions = isObjectLike(originalError === null || originalError === void 0 ? void 0 : originalError.extensions) ? originalError === null || originalError === void 0 ? void 0 : originalError.extensions : undefined;\n    this.extensions = (_ref = extensions !== null && extensions !== void 0 ? extensions : originalExtensions) !== null && _ref !== void 0 ? _ref : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true\n      },\n      name: {\n        enumerable: false\n      },\n      nodes: {\n        enumerable: false\n      },\n      source: {\n        enumerable: false\n      },\n      positions: {\n        enumerable: false\n      },\n      originalError: {\n        enumerable: false\n      }\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (originalError !== null && originalError !== void 0 && originalError.stack) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n  toString() {\n    let output = this.message;\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + printLocation(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + printSourceLocation(this.source, location);\n      }\n    }\n    return output;\n  }\n  toJSON() {\n    const formattedError = {\n      message: this.message\n    };\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n    return formattedError;\n  }\n}\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nexport function printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nexport function formatError(error) {\n  return error.toJSON();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}