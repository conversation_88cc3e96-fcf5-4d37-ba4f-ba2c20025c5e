{"ast": null, "code": "/**\n * Groups array items into a Map, given a function to produce grouping key.\n */\nexport function groupBy(list, keyFn) {\n  const result = new Map();\n  for (const item of list) {\n    const key = keyFn(item);\n    const group = result.get(key);\n    if (group === undefined) {\n      result.set(key, [item]);\n    } else {\n      group.push(item);\n    }\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}