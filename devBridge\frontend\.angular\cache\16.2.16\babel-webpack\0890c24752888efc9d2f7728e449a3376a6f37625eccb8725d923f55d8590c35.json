{"ast": null, "code": "import { Kind } from \"graphql\";\nimport { getFragmentMaskMode, maybeDeep<PERSON>reeze, resultKeyNameFromField } from \"../utilities/index.js\";\nimport { disableWarningsSlot } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nexport function maskDefinition(data, selectionSet, context) {\n  return disableWarningsSlot.withValue(true, function () {\n    var masked = maskSelectionSet(data, selectionSet, context, false);\n    if (Object.isFrozen(data)) {\n      maybeDeepFreeze(masked);\n    }\n    return masked;\n  });\n}\nfunction getMutableTarget(data, mutableTargets) {\n  if (mutableTargets.has(data)) {\n    return mutableTargets.get(data);\n  }\n  var mutableTarget = Array.isArray(data) ? [] : Object.create(null);\n  mutableTargets.set(data, mutableTarget);\n  return mutableTarget;\n}\nfunction maskSelectionSet(data, selectionSet, context, migration, path) {\n  var _a;\n  var knownChanged = context.knownChanged;\n  var memo = getMutableTarget(data, context.mutableTargets);\n  if (Array.isArray(data)) {\n    for (var _i = 0, _b = Array.from(data.entries()); _i < _b.length; _i++) {\n      var _c = _b[_i],\n        index = _c[0],\n        item = _c[1];\n      if (item === null) {\n        memo[index] = null;\n        continue;\n      }\n      var masked = maskSelectionSet(item, selectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \"[\").concat(index, \"]\") : void 0);\n      if (knownChanged.has(masked)) {\n        knownChanged.add(memo);\n      }\n      memo[index] = masked;\n    }\n    return knownChanged.has(memo) ? memo : data;\n  }\n  for (var _d = 0, _e = selectionSet.selections; _d < _e.length; _d++) {\n    var selection = _e[_d];\n    var value = void 0;\n    // we later want to add acessor warnings to the final result\n    // so we need a new object to add the accessor warning to\n    if (migration) {\n      knownChanged.add(memo);\n    }\n    if (selection.kind === Kind.FIELD) {\n      var keyName = resultKeyNameFromField(selection);\n      var childSelectionSet = selection.selectionSet;\n      value = memo[keyName] || data[keyName];\n      if (value === void 0) {\n        continue;\n      }\n      if (childSelectionSet && value !== null) {\n        var masked = maskSelectionSet(data[keyName], childSelectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \".\").concat(keyName) : void 0);\n        if (knownChanged.has(masked)) {\n          value = masked;\n        }\n      }\n      if (!(globalThis.__DEV__ !== false)) {\n        memo[keyName] = value;\n      }\n      if (globalThis.__DEV__ !== false) {\n        if (migration && keyName !== \"__typename\" &&\n        // either the field is not present in the memo object\n        // or it has a `get` descriptor, not a `value` descriptor\n        // => it is a warning accessor and we can overwrite it\n        // with another accessor\n        !((_a = Object.getOwnPropertyDescriptor(memo, keyName)) === null || _a === void 0 ? void 0 : _a.value)) {\n          Object.defineProperty(memo, keyName, getAccessorWarningDescriptor(keyName, value, path || \"\", context.operationName, context.operationType));\n        } else {\n          delete memo[keyName];\n          memo[keyName] = value;\n        }\n      }\n    }\n    if (selection.kind === Kind.INLINE_FRAGMENT && (!selection.typeCondition || context.cache.fragmentMatches(selection, data.__typename))) {\n      value = maskSelectionSet(data, selection.selectionSet, context, migration, path);\n    }\n    if (selection.kind === Kind.FRAGMENT_SPREAD) {\n      var fragmentName = selection.name.value;\n      var fragment = context.fragmentMap[fragmentName] || (context.fragmentMap[fragmentName] = context.cache.lookupFragment(fragmentName));\n      invariant(fragment, 47, fragmentName);\n      var mode = getFragmentMaskMode(selection);\n      if (mode !== \"mask\") {\n        value = maskSelectionSet(data, fragment.selectionSet, context, mode === \"migrate\", path);\n      }\n    }\n    if (knownChanged.has(value)) {\n      knownChanged.add(memo);\n    }\n  }\n  if (\"__typename\" in data && !(\"__typename\" in memo)) {\n    memo.__typename = data.__typename;\n  }\n  // This check prevents cases where masked fields may accidentally be\n  // returned as part of this object when the fragment also selects\n  // additional fields from the same child selection.\n  if (Object.keys(memo).length !== Object.keys(data).length) {\n    knownChanged.add(memo);\n  }\n  return knownChanged.has(memo) ? memo : data;\n}\nfunction getAccessorWarningDescriptor(fieldName, value, path, operationName, operationType) {\n  var getValue = function () {\n    if (disableWarningsSlot.getValue()) {\n      return value;\n    }\n    globalThis.__DEV__ !== false && invariant.warn(48, operationName ? \"\".concat(operationType, \" '\").concat(operationName, \"'\") : \"anonymous \".concat(operationType), \"\".concat(path, \".\").concat(fieldName).replace(/^\\./, \"\"));\n    getValue = function () {\n      return value;\n    };\n    return value;\n  };\n  return {\n    get: function () {\n      return getValue();\n    },\n    set: function (newValue) {\n      getValue = function () {\n        return newValue;\n      };\n    },\n    enumerable: true,\n    configurable: true\n  };\n}\n//# sourceMappingURL=maskDefinition.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}