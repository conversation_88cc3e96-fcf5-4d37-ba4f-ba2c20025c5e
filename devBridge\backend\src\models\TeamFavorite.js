const mongoose = require('mongoose');

// Schéma pour les équipes favorites
const teamFavoriteSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'L\'utilisateur est requis']
  },

  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team',
    required: [true, 'L\'équipe est requise']
  },

  // Catégorie personnalisée pour organiser les favoris
  category: {
    type: String,
    enum: ['work', 'personal', 'study', 'project', 'other'],
    default: 'other'
  },

  // Notes personnelles sur l'équipe
  notes: {
    type: String,
    maxlength: [500, 'Les notes ne peuvent pas dépasser 500 caractères'],
    trim: true
  },

  // Tags personnels pour filtrage
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Un tag ne peut pas dépasser 50 caractères']
  }],

  // Priorité pour tri
  priority: {
    type: Number,
    min: 1,
    max: 5,
    default: 3
  },

  // Notifications pour cette équipe
  notifications: {
    enabled: {
      type: Boolean,
      default: true
    },
    types: [{
      type: String,
      enum: ['new_member', 'new_project', 'task_assigned', 'meeting', 'announcement'],
      default: ['new_member', 'new_project', 'task_assigned']
    }]
  },

  // Métadonnées
  metadata: {
    addedFrom: {
      type: String,
      enum: ['team_page', 'search', 'invitation', 'recommendation'],
      default: 'team_page'
    },
    source: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    }
  },

  // Statistiques d'utilisation
  stats: {
    viewCount: {
      type: Number,
      default: 0
    },
    lastViewed: {
      type: Date,
      default: Date.now
    },
    interactionCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index composé unique pour éviter les doublons
teamFavoriteSchema.index({ user: 1, team: 1 }, { unique: true });

// Index pour améliorer les performances
teamFavoriteSchema.index({ user: 1, category: 1 });
teamFavoriteSchema.index({ user: 1, priority: -1 });
teamFavoriteSchema.index({ user: 1, 'stats.lastViewed': -1 });
teamFavoriteSchema.index({ tags: 1 });
teamFavoriteSchema.index({ createdAt: -1 });

// Propriétés virtuelles
teamFavoriteSchema.virtual('daysSinceFavorited').get(function() {
  const diff = Date.now() - this.createdAt.getTime();
  return Math.floor(diff / (1000 * 60 * 60 * 24));
});

teamFavoriteSchema.virtual('isRecentlyViewed').get(function() {
  if (!this.stats.lastViewed) return false;
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.stats.lastViewed > oneDayAgo;
});

teamFavoriteSchema.virtual('priorityLabel').get(function() {
  const labels = {
    1: 'Très faible',
    2: 'Faible',
    3: 'Normale',
    4: 'Élevée',
    5: 'Très élevée'
  };
  return labels[this.priority] || 'Normale';
});

// Méthodes d'instance
teamFavoriteSchema.methods.updateStats = function(action = 'view') {
  this.stats.lastViewed = new Date();
  
  if (action === 'view') {
    this.stats.viewCount += 1;
  } else if (action === 'interaction') {
    this.stats.interactionCount += 1;
  }
  
  return this.save();
};

teamFavoriteSchema.methods.updateNotificationSettings = function(enabled, types = []) {
  this.notifications.enabled = enabled;
  if (types.length > 0) {
    this.notifications.types = types;
  }
  return this.save();
};

teamFavoriteSchema.methods.addTag = function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

teamFavoriteSchema.methods.removeTag = function(tag) {
  const index = this.tags.indexOf(tag);
  if (index > -1) {
    this.tags.splice(index, 1);
    return this.save();
  }
  return Promise.resolve(this);
};

// Méthodes statiques
teamFavoriteSchema.statics.addToFavorites = function(userId, teamId, options = {}) {
  const favorite = new this({
    user: userId,
    team: teamId,
    category: options.category || 'other',
    notes: options.notes || '',
    tags: options.tags || [],
    priority: options.priority || 3,
    metadata: {
      addedFrom: options.addedFrom || 'team_page',
      source: options.source || 'web'
    }
  });

  return favorite.save();
};

teamFavoriteSchema.statics.removeFromFavorites = function(userId, teamId) {
  return this.findOneAndDelete({ user: userId, team: teamId });
};

teamFavoriteSchema.statics.isFavorite = function(userId, teamId) {
  return this.findOne({ user: userId, team: teamId });
};

teamFavoriteSchema.statics.getUserFavorites = function(userId, options = {}) {
  const query = { user: userId };
  
  if (options.category) query.category = options.category;
  if (options.tags && options.tags.length > 0) {
    query.tags = { $in: options.tags };
  }

  let sortBy = { priority: -1, 'stats.lastViewed': -1 };
  
  if (options.sortBy === 'recent') {
    sortBy = { 'stats.lastViewed': -1 };
  } else if (options.sortBy === 'name') {
    sortBy = { 'team.name': 1 };
  } else if (options.sortBy === 'created') {
    sortBy = { createdAt: -1 };
  }

  return this.find(query)
    .populate('team', 'name description admin members status tags isPublic stats')
    .sort(sortBy)
    .limit(options.limit || 50);
};

teamFavoriteSchema.statics.getFavoritesByCategory = function(userId) {
  return this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 },
        teams: { $push: '$team' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

teamFavoriteSchema.statics.getPopularTags = function(userId) {
  return this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId) } },
    { $unwind: '$tags' },
    {
      $group: {
        _id: '$tags',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } },
    { $limit: 20 }
  ]);
};

teamFavoriteSchema.statics.getRecommendations = function(userId, limit = 5) {
  // Logique simple de recommandation basée sur les équipes similaires
  return this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId) } },
    {
      $lookup: {
        from: 'teams',
        localField: 'team',
        foreignField: '_id',
        as: 'favoriteTeam'
      }
    },
    { $unwind: '$favoriteTeam' },
    { $unwind: '$favoriteTeam.tags' },
    {
      $lookup: {
        from: 'teams',
        let: { tag: '$favoriteTeam.tags' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $in: ['$$tag', '$tags'] },
                  { $eq: ['$status', 'active'] },
                  { $eq: ['$isPublic', true] }
                ]
              }
            }
          }
        ],
        as: 'similarTeams'
      }
    },
    { $unwind: '$similarTeams' },
    {
      $group: {
        _id: '$similarTeams._id',
        team: { $first: '$similarTeams' },
        score: { $sum: 1 }
      }
    },
    { $sort: { score: -1 } },
    { $limit: limit }
  ]);
};

// Middleware pre-save
teamFavoriteSchema.pre('save', function(next) {
  // Nettoyer les tags (supprimer les doublons et espaces)
  if (this.tags && this.tags.length > 0) {
    this.tags = [...new Set(this.tags.map(tag => tag.trim().toLowerCase()))];
  }
  next();
});

const TeamFavorite = mongoose.model('TeamFavorite', teamFavoriteSchema);

module.exports = TeamFavorite;
