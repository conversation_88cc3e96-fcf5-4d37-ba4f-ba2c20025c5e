{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { isPromise } from '../jsutils/isPromise.mjs';\nimport { memoize3 } from '../jsutils/memoize3.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { promiseForObject } from '../jsutils/promiseForObject.mjs';\nimport { promiseReduce } from '../jsutils/promiseReduce.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isAbstractType, isLeafType, isListType, isNonNullType, isObjectType } from '../type/definition.mjs';\nimport { SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef } from '../type/introspection.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport { collectFields, collectSubfields as _collectSubfields } from './collectFields.mjs';\nimport { getArgumentValues, getVariableValues } from './values.mjs';\n/**\n * A memoized collection of relevant subfields with regard to the return\n * type. Memoizing ensures the subfields are not repeatedly calculated, which\n * saves overhead when resolving lists of values.\n */\n\nconst collectSubfields = memoize3((exeContext, returnType, fieldNodes) => _collectSubfields(exeContext.schema, exeContext.fragments, exeContext.variableValues, returnType, fieldNodes));\n/**\n * Terminology\n *\n * \"Definitions\" are the generic name for top-level statements in the document.\n * Examples of this include:\n * 1) Operations (such as a query)\n * 2) Fragments\n *\n * \"Operations\" are a generic name for requests in the document.\n * Examples of this include:\n * 1) query,\n * 2) mutation\n *\n * \"Selections\" are the definitions that can appear legally and at\n * single level of the query. These include:\n * 1) field references e.g `a`\n * 2) fragment \"spreads\" e.g. `...c`\n * 3) inline fragment \"spreads\" e.g. `...on Type { a }`\n */\n\n/**\n * Data that must be available at all points during query execution.\n *\n * Namely, schema of the type system that is currently executing,\n * and the fragments defined in the query document\n */\n\n/**\n * Implements the \"Executing requests\" section of the GraphQL specification.\n *\n * Returns either a synchronous ExecutionResult (if all encountered resolvers\n * are synchronous), or a Promise of an ExecutionResult that will eventually be\n * resolved and never rejected.\n *\n * If the arguments to this function do not result in a legal execution context,\n * a GraphQLError will be thrown immediately explaining the invalid input.\n */\nexport function execute(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n  const {\n    schema,\n    document,\n    variableValues,\n    rootValue\n  } = args; // If arguments are missing or incorrect, throw an error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext\n    };\n  } // Return a Promise that will eventually resolve to the data described by\n  // The \"Response\" section of the GraphQL specification.\n  //\n  // If errors are encountered while executing a GraphQL field, only that\n  // field and its descendants will be omitted, and sibling fields will still\n  // be executed. An execution which encounters errors will still result in a\n  // resolved Promise.\n  //\n  // Errors from sub-fields of a NonNull type may propagate to the top level,\n  // at which point we still log the error and null the parent field, which\n  // in this case is the entire response.\n\n  try {\n    const {\n      operation\n    } = exeContext;\n    const result = executeOperation(exeContext, operation, rootValue);\n    if (isPromise(result)) {\n      return result.then(data => buildResponse(data, exeContext.errors), error => {\n        exeContext.errors.push(error);\n        return buildResponse(null, exeContext.errors);\n      });\n    }\n    return buildResponse(result, exeContext.errors);\n  } catch (error) {\n    exeContext.errors.push(error);\n    return buildResponse(null, exeContext.errors);\n  }\n}\n/**\n * Also implements the \"Executing requests\" section of the GraphQL specification.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\n\nexport function executeSync(args) {\n  const result = execute(args); // Assert that the execution was synchronous.\n\n  if (isPromise(result)) {\n    throw new Error('GraphQL execution failed to complete synchronously.');\n  }\n  return result;\n}\n/**\n * Given a completed execution context and data, build the `{ errors, data }`\n * response defined by the \"Response\" section of the GraphQL specification.\n */\n\nfunction buildResponse(data, errors) {\n  return errors.length === 0 ? {\n    data\n  } : {\n    errors,\n    data\n  };\n}\n/**\n * Essential assertions before executing to provide developer feedback for\n * improper use of the GraphQL library.\n *\n * @internal\n */\n\nexport function assertValidExecutionArguments(schema, document, rawVariableValues) {\n  document || devAssert(false, 'Must provide document.'); // If the schema used for execution is invalid, throw an error.\n\n  assertValidSchema(schema); // Variables, if provided, must be an object.\n\n  rawVariableValues == null || isObjectLike(rawVariableValues) || devAssert(false, 'Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.');\n}\n/**\n * Constructs a ExecutionContext object from the arguments passed to\n * execute, which we will pass throughout the other execution methods.\n *\n * Throws a GraphQLError if a valid execution context cannot be created.\n *\n * @internal\n */\n\nexport function buildExecutionContext(args) {\n  var _definition$name, _operation$variableDe;\n  const {\n    schema,\n    document,\n    rootValue,\n    contextValue,\n    variableValues: rawVariableValues,\n    operationName,\n    fieldResolver,\n    typeResolver,\n    subscribeFieldResolver\n  } = args;\n  let operation;\n  const fragments = Object.create(null);\n  for (const definition of document.definitions) {\n    switch (definition.kind) {\n      case Kind.OPERATION_DEFINITION:\n        if (operationName == null) {\n          if (operation !== undefined) {\n            return [new GraphQLError('Must provide operation name if query contains multiple operations.')];\n          }\n          operation = definition;\n        } else if (((_definition$name = definition.name) === null || _definition$name === void 0 ? void 0 : _definition$name.value) === operationName) {\n          operation = definition;\n        }\n        break;\n      case Kind.FRAGMENT_DEFINITION:\n        fragments[definition.name.value] = definition;\n        break;\n      default: // ignore non-executable definitions\n    }\n  }\n\n  if (!operation) {\n    if (operationName != null) {\n      return [new GraphQLError(`Unknown operation named \"${operationName}\".`)];\n    }\n    return [new GraphQLError('Must provide an operation.')];\n  } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n\n  const variableDefinitions = (_operation$variableDe = operation.variableDefinitions) !== null && _operation$variableDe !== void 0 ? _operation$variableDe : [];\n  const coercedVariableValues = getVariableValues(schema, variableDefinitions, rawVariableValues !== null && rawVariableValues !== void 0 ? rawVariableValues : {}, {\n    maxErrors: 50\n  });\n  if (coercedVariableValues.errors) {\n    return coercedVariableValues.errors;\n  }\n  return {\n    schema,\n    fragments,\n    rootValue,\n    contextValue,\n    operation,\n    variableValues: coercedVariableValues.coerced,\n    fieldResolver: fieldResolver !== null && fieldResolver !== void 0 ? fieldResolver : defaultFieldResolver,\n    typeResolver: typeResolver !== null && typeResolver !== void 0 ? typeResolver : defaultTypeResolver,\n    subscribeFieldResolver: subscribeFieldResolver !== null && subscribeFieldResolver !== void 0 ? subscribeFieldResolver : defaultFieldResolver,\n    errors: []\n  };\n}\n/**\n * Implements the \"Executing operations\" section of the spec.\n */\n\nfunction executeOperation(exeContext, operation, rootValue) {\n  const rootType = exeContext.schema.getRootType(operation.operation);\n  if (rootType == null) {\n    throw new GraphQLError(`Schema is not configured to execute ${operation.operation} operation.`, {\n      nodes: operation\n    });\n  }\n  const rootFields = collectFields(exeContext.schema, exeContext.fragments, exeContext.variableValues, rootType, operation.selectionSet);\n  const path = undefined;\n  switch (operation.operation) {\n    case OperationTypeNode.QUERY:\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n    case OperationTypeNode.MUTATION:\n      return executeFieldsSerially(exeContext, rootType, rootValue, path, rootFields);\n    case OperationTypeNode.SUBSCRIPTION:\n      // TODO: deprecate `subscribe` and move all logic here\n      // Temporary solution until we finish merging execute and subscribe together\n      return executeFields(exeContext, rootType, rootValue, path, rootFields);\n  }\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that must be executed serially.\n */\n\nfunction executeFieldsSerially(exeContext, parentType, sourceValue, path, fields) {\n  return promiseReduce(fields.entries(), (results, [responseName, fieldNodes]) => {\n    const fieldPath = addPath(path, responseName, parentType.name);\n    const result = executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath);\n    if (result === undefined) {\n      return results;\n    }\n    if (isPromise(result)) {\n      return result.then(resolvedResult => {\n        results[responseName] = resolvedResult;\n        return results;\n      });\n    }\n    results[responseName] = result;\n    return results;\n  }, Object.create(null));\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that may be executed in parallel.\n */\n\nfunction executeFields(exeContext, parentType, sourceValue, path, fields) {\n  const results = Object.create(null);\n  let containsPromise = false;\n  try {\n    for (const [responseName, fieldNodes] of fields.entries()) {\n      const fieldPath = addPath(path, responseName, parentType.name);\n      const result = executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath);\n      if (result !== undefined) {\n        results[responseName] = result;\n        if (isPromise(result)) {\n          containsPromise = true;\n        }\n      }\n    }\n  } catch (error) {\n    if (containsPromise) {\n      // Ensure that any promises returned by other fields are handled, as they may also reject.\n      return promiseForObject(results).finally(() => {\n        throw error;\n      });\n    }\n    throw error;\n  } // If there are no promises, we can just return the object\n\n  if (!containsPromise) {\n    return results;\n  } // Otherwise, results is a map from field name to the result of resolving that\n  // field, which is possibly a promise. Return a promise that will return this\n  // same map, but with any promises replaced with the values they resolved to.\n\n  return promiseForObject(results);\n}\n/**\n * Implements the \"Executing fields\" section of the spec\n * In particular, this function figures out the value that the field returns by\n * calling its resolve function, then calls completeValue to complete promises,\n * serialize scalars, or execute the sub-selection-set for objects.\n */\n\nfunction executeField(exeContext, parentType, source, fieldNodes, path) {\n  var _fieldDef$resolve;\n  const fieldDef = getFieldDef(exeContext.schema, parentType, fieldNodes[0]);\n  if (!fieldDef) {\n    return;\n  }\n  const returnType = fieldDef.type;\n  const resolveFn = (_fieldDef$resolve = fieldDef.resolve) !== null && _fieldDef$resolve !== void 0 ? _fieldDef$resolve : exeContext.fieldResolver;\n  const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path); // Get the resolve function, regardless of if its result is normal or abrupt (error).\n\n  try {\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    // TODO: find a way to memoize, in case this field is within a List type.\n    const args = getArgumentValues(fieldDef, fieldNodes[0], exeContext.variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue;\n    const result = resolveFn(source, args, contextValue, info);\n    let completed;\n    if (isPromise(result)) {\n      completed = result.then(resolved => completeValue(exeContext, returnType, fieldNodes, info, path, resolved));\n    } else {\n      completed = completeValue(exeContext, returnType, fieldNodes, info, path, result);\n    }\n    if (isPromise(completed)) {\n      // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n      // to take a second callback for the error case.\n      return completed.then(undefined, rawError => {\n        const error = locatedError(rawError, fieldNodes, pathToArray(path));\n        return handleFieldError(error, returnType, exeContext);\n      });\n    }\n    return completed;\n  } catch (rawError) {\n    const error = locatedError(rawError, fieldNodes, pathToArray(path));\n    return handleFieldError(error, returnType, exeContext);\n  }\n}\n/**\n * @internal\n */\n\nexport function buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path) {\n  // The resolve function's optional fourth argument is a collection of\n  // information about the current execution state.\n  return {\n    fieldName: fieldDef.name,\n    fieldNodes,\n    returnType: fieldDef.type,\n    parentType,\n    path,\n    schema: exeContext.schema,\n    fragments: exeContext.fragments,\n    rootValue: exeContext.rootValue,\n    operation: exeContext.operation,\n    variableValues: exeContext.variableValues\n  };\n}\nfunction handleFieldError(error, returnType, exeContext) {\n  // If the field type is non-nullable, then it is resolved without any\n  // protection from errors, however it still properly locates the error.\n  if (isNonNullType(returnType)) {\n    throw error;\n  } // Otherwise, error protection is applied, logging the error and resolving\n  // a null value for this field if one is encountered.\n\n  exeContext.errors.push(error);\n  return null;\n}\n/**\n * Implements the instructions for completeValue as defined in the\n * \"Value Completion\" section of the spec.\n *\n * If the field type is Non-Null, then this recursively completes the value\n * for the inner type. It throws a field error if that completion returns null,\n * as per the \"Nullability\" section of the spec.\n *\n * If the field type is a List, then this recursively completes the value\n * for the inner type on each item in the list.\n *\n * If the field type is a Scalar or Enum, ensures the completed value is a legal\n * value of the type by calling the `serialize` method of GraphQL type\n * definition.\n *\n * If the field is an abstract type, determine the runtime type of the value\n * and then complete based on that type\n *\n * Otherwise, the field type expects a sub-selection set, and will complete the\n * value by executing all sub-selections.\n */\n\nfunction completeValue(exeContext, returnType, fieldNodes, info, path, result) {\n  // If result is an Error, throw a located error.\n  if (result instanceof Error) {\n    throw result;\n  } // If field type is NonNull, complete for inner type, and throw field error\n  // if result is null.\n\n  if (isNonNullType(returnType)) {\n    const completed = completeValue(exeContext, returnType.ofType, fieldNodes, info, path, result);\n    if (completed === null) {\n      throw new Error(`Cannot return null for non-nullable field ${info.parentType.name}.${info.fieldName}.`);\n    }\n    return completed;\n  } // If result value is null or undefined then return null.\n\n  if (result == null) {\n    return null;\n  } // If field type is List, complete each item in the list with the inner type\n\n  if (isListType(returnType)) {\n    return completeListValue(exeContext, returnType, fieldNodes, info, path, result);\n  } // If field type is a leaf type, Scalar or Enum, serialize to a valid value,\n  // returning null if serialization is not possible.\n\n  if (isLeafType(returnType)) {\n    return completeLeafValue(returnType, result);\n  } // If field type is an abstract type, Interface or Union, determine the\n  // runtime Object type and complete for that type.\n\n  if (isAbstractType(returnType)) {\n    return completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result);\n  } // If field type is Object, execute and complete all sub-selections.\n\n  if (isObjectType(returnType)) {\n    return completeObjectValue(exeContext, returnType, fieldNodes, info, path, result);\n  }\n  /* c8 ignore next 6 */\n  // Not reachable, all possible output types have been considered.\n\n  false || invariant(false, 'Cannot complete value of unexpected output type: ' + inspect(returnType));\n}\n/**\n * Complete a list value by completing each item in the list with the\n * inner type\n */\n\nfunction completeListValue(exeContext, returnType, fieldNodes, info, path, result) {\n  if (!isIterableObject(result)) {\n    throw new GraphQLError(`Expected Iterable, but did not find one for field \"${info.parentType.name}.${info.fieldName}\".`);\n  } // This is specified as a simple map, however we're optimizing the path\n  // where the list contains no Promises by avoiding creating another Promise.\n\n  const itemType = returnType.ofType;\n  let containsPromise = false;\n  const completedResults = Array.from(result, (item, index) => {\n    // No need to modify the info object containing the path,\n    // since from here on it is not ever accessed by resolver functions.\n    const itemPath = addPath(path, index, undefined);\n    try {\n      let completedItem;\n      if (isPromise(item)) {\n        completedItem = item.then(resolved => completeValue(exeContext, itemType, fieldNodes, info, itemPath, resolved));\n      } else {\n        completedItem = completeValue(exeContext, itemType, fieldNodes, info, itemPath, item);\n      }\n      if (isPromise(completedItem)) {\n        containsPromise = true; // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n        // to take a second callback for the error case.\n\n        return completedItem.then(undefined, rawError => {\n          const error = locatedError(rawError, fieldNodes, pathToArray(itemPath));\n          return handleFieldError(error, itemType, exeContext);\n        });\n      }\n      return completedItem;\n    } catch (rawError) {\n      const error = locatedError(rawError, fieldNodes, pathToArray(itemPath));\n      return handleFieldError(error, itemType, exeContext);\n    }\n  });\n  return containsPromise ? Promise.all(completedResults) : completedResults;\n}\n/**\n * Complete a Scalar or Enum by serializing to a valid value, returning\n * null if serialization is not possible.\n */\n\nfunction completeLeafValue(returnType, result) {\n  const serializedResult = returnType.serialize(result);\n  if (serializedResult == null) {\n    throw new Error(`Expected \\`${inspect(returnType)}.serialize(${inspect(result)})\\` to ` + `return non-nullable value, returned: ${inspect(serializedResult)}`);\n  }\n  return serializedResult;\n}\n/**\n * Complete a value of an abstract type by determining the runtime object type\n * of that value, then complete the value for that type.\n */\n\nfunction completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result) {\n  var _returnType$resolveTy;\n  const resolveTypeFn = (_returnType$resolveTy = returnType.resolveType) !== null && _returnType$resolveTy !== void 0 ? _returnType$resolveTy : exeContext.typeResolver;\n  const contextValue = exeContext.contextValue;\n  const runtimeType = resolveTypeFn(result, contextValue, info, returnType);\n  if (isPromise(runtimeType)) {\n    return runtimeType.then(resolvedRuntimeType => completeObjectValue(exeContext, ensureValidRuntimeType(resolvedRuntimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result));\n  }\n  return completeObjectValue(exeContext, ensureValidRuntimeType(runtimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result);\n}\nfunction ensureValidRuntimeType(runtimeTypeName, exeContext, returnType, fieldNodes, info, result) {\n  if (runtimeTypeName == null) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\". Either the \"${returnType.name}\" type should provide a \"resolveType\" function or each possible type should provide an \"isTypeOf\" function.`, fieldNodes);\n  } // releases before 16.0.0 supported returning `GraphQLObjectType` from `resolveType`\n  // TODO: remove in 17.0.0 release\n\n  if (isObjectType(runtimeTypeName)) {\n    throw new GraphQLError('Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.');\n  }\n  if (typeof runtimeTypeName !== 'string') {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\" with ` + `value ${inspect(result)}, received \"${inspect(runtimeTypeName)}\".`);\n  }\n  const runtimeType = exeContext.schema.getType(runtimeTypeName);\n  if (runtimeType == null) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" was resolved to a type \"${runtimeTypeName}\" that does not exist inside the schema.`, {\n      nodes: fieldNodes\n    });\n  }\n  if (!isObjectType(runtimeType)) {\n    throw new GraphQLError(`Abstract type \"${returnType.name}\" was resolved to a non-object type \"${runtimeTypeName}\".`, {\n      nodes: fieldNodes\n    });\n  }\n  if (!exeContext.schema.isSubType(returnType, runtimeType)) {\n    throw new GraphQLError(`Runtime Object type \"${runtimeType.name}\" is not a possible type for \"${returnType.name}\".`, {\n      nodes: fieldNodes\n    });\n  }\n  return runtimeType;\n}\n/**\n * Complete an Object value by executing all sub-selections.\n */\n\nfunction completeObjectValue(exeContext, returnType, fieldNodes, info, path, result) {\n  // Collect sub-fields to execute to complete this value.\n  const subFieldNodes = collectSubfields(exeContext, returnType, fieldNodes); // If there is an isTypeOf predicate function, call it with the\n  // current result. If isTypeOf returns false, then raise an error rather\n  // than continuing execution.\n\n  if (returnType.isTypeOf) {\n    const isTypeOf = returnType.isTypeOf(result, exeContext.contextValue, info);\n    if (isPromise(isTypeOf)) {\n      return isTypeOf.then(resolvedIsTypeOf => {\n        if (!resolvedIsTypeOf) {\n          throw invalidReturnTypeError(returnType, result, fieldNodes);\n        }\n        return executeFields(exeContext, returnType, result, path, subFieldNodes);\n      });\n    }\n    if (!isTypeOf) {\n      throw invalidReturnTypeError(returnType, result, fieldNodes);\n    }\n  }\n  return executeFields(exeContext, returnType, result, path, subFieldNodes);\n}\nfunction invalidReturnTypeError(returnType, result, fieldNodes) {\n  return new GraphQLError(`Expected value of type \"${returnType.name}\" but got: ${inspect(result)}.`, {\n    nodes: fieldNodes\n  });\n}\n/**\n * If a resolveType function is not given, then a default resolve behavior is\n * used which attempts two strategies:\n *\n * First, See if the provided value has a `__typename` field defined, if so, use\n * that value as name of the resolved type.\n *\n * Otherwise, test each possible type for the abstract type by calling\n * isTypeOf for the object being coerced, returning the first type that matches.\n */\n\nexport const defaultTypeResolver = function (value, contextValue, info, abstractType) {\n  // First, look for `__typename`.\n  if (isObjectLike(value) && typeof value.__typename === 'string') {\n    return value.__typename;\n  } // Otherwise, test each possible type.\n\n  const possibleTypes = info.schema.getPossibleTypes(abstractType);\n  const promisedIsTypeOfResults = [];\n  for (let i = 0; i < possibleTypes.length; i++) {\n    const type = possibleTypes[i];\n    if (type.isTypeOf) {\n      const isTypeOfResult = type.isTypeOf(value, contextValue, info);\n      if (isPromise(isTypeOfResult)) {\n        promisedIsTypeOfResults[i] = isTypeOfResult;\n      } else if (isTypeOfResult) {\n        return type.name;\n      }\n    }\n  }\n  if (promisedIsTypeOfResults.length) {\n    return Promise.all(promisedIsTypeOfResults).then(isTypeOfResults => {\n      for (let i = 0; i < isTypeOfResults.length; i++) {\n        if (isTypeOfResults[i]) {\n          return possibleTypes[i].name;\n        }\n      }\n    });\n  }\n};\n/**\n * If a resolve function is not given, then a default resolve behavior is used\n * which takes the property of the source object of the same name as the field\n * and returns it as the result, or if it's a function, returns the result\n * of calling that function while passing along args and context value.\n */\n\nexport const defaultFieldResolver = function (source, args, contextValue, info) {\n  // ensure source is a value for which property access is acceptable.\n  if (isObjectLike(source) || typeof source === 'function') {\n    const property = source[info.fieldName];\n    if (typeof property === 'function') {\n      return source[info.fieldName](args, contextValue, info);\n    }\n    return property;\n  }\n};\n/**\n * This method looks up the field on the given type definition.\n * It has special casing for the three introspection fields,\n * __schema, __type and __typename. __typename is special because\n * it can always be queried as a field, even in situations where no\n * other fields are allowed, like on a Union. __schema and __type\n * could get automatically added to the query type, but that would\n * require mutating type definitions, which would cause issues.\n *\n * @internal\n */\n\nexport function getFieldDef(schema, parentType, fieldNode) {\n  const fieldName = fieldNode.name.value;\n  if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return SchemaMetaFieldDef;\n  } else if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return TypeMetaFieldDef;\n  } else if (fieldName === TypeNameMetaFieldDef.name) {\n    return TypeNameMetaFieldDef;\n  }\n  return parentType.getFields()[fieldName];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}