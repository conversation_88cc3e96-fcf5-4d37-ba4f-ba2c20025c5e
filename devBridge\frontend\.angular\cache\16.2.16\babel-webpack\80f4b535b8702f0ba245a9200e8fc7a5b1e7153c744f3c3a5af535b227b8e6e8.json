{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique fragment names\n *\n * A GraphQL document is only valid if all defined fragments have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Name-Uniqueness\n */\nexport function UniqueFragmentNamesRule(context) {\n  const knownFragmentNames = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n    FragmentDefinition(node) {\n      const fragmentName = node.name.value;\n      if (knownFragmentNames[fragmentName]) {\n        context.reportError(new GraphQLError(`There can be only one fragment named \"${fragmentName}\".`, {\n          nodes: [knownFragmentNames[fragmentName], node.name]\n        }));\n      } else {\n        knownFragmentNames[fragmentName] = node.name;\n      }\n      return false;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}