{"version": 3, "file": "cachePolicy.js", "sourceRoot": "", "sources": ["../../src/cachePolicy.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,cAAc;IAC5B,OAAO;QACL,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,SAAS;QAChB,QAAQ,CAAC,IAAe;YACtB,IACE,IAAI,CAAC,MAAM,KAAK,SAAS;gBACzB,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EACxD,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACzD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,OAAO,CAAC,IAAe;YACrB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,iBAAiB;YACf,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,QAAQ,EAAE,CAAC;QAChE,CAAC;KACF,CAAC;AACJ,CAAC"}