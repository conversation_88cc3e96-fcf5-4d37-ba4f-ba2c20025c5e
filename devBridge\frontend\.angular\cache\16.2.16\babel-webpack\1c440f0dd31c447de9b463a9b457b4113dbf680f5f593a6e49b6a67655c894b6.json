{"ast": null, "code": "import { maybe } from \"./maybe.js\";\nexport default maybe(function () {\n  return globalThis;\n}) || maybe(function () {\n  return window;\n}) || maybe(function () {\n  return self;\n}) || maybe(function () {\n  return global;\n}) ||\n// We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n  return maybe.constructor(\"return this\")();\n});\n//# sourceMappingURL=global.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}