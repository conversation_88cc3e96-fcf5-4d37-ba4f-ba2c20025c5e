{"ast": null, "code": "import { ValueSetter } from \"./Setter.js\";\nexport class Parser {\n  run(dateString, token, match, options) {\n    const result = this.parse(dateString, token, match, options);\n    if (!result) {\n      return null;\n    }\n    return {\n      setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),\n      rest: result.rest\n    };\n  }\n  validate(_utcDate, _value, _options) {\n    return true;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}