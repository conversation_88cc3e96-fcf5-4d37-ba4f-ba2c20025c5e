{"ast": null, "code": "// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE = {};\nlet idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot {\n  constructor() {\n    // If you have a Slot object, you can find out its slot.id, but you cannot\n    // guess the slot.id of a Slot you don't have access to, thanks to the\n    // randomized suffix.\n    this.id = [\"slot\", idCounter++, Date.now(), Math.random().toString(36).slice(2)].join(\":\");\n  }\n  hasValue() {\n    for (let context = currentContext; context; context = context.parent) {\n      // We use the Slot object iself as a key to its value, which means the\n      // value cannot be obtained without a reference to the Slot object.\n      if (this.id in context.slots) {\n        const value = context.slots[this.id];\n        if (value === MISSING_VALUE) break;\n        if (context !== currentContext) {\n          // Cache the value in currentContext.slots so the next lookup will\n          // be faster. This caching is safe because the tree of contexts and\n          // the values of the slots are logically immutable.\n          currentContext.slots[this.id] = value;\n        }\n        return true;\n      }\n    }\n    if (currentContext) {\n      // If a value was not found for this Slot, it's never going to be found\n      // no matter how many times we look it up, so we might as well cache\n      // the absence of the value, too.\n      currentContext.slots[this.id] = MISSING_VALUE;\n    }\n    return false;\n  }\n  getValue() {\n    if (this.hasValue()) {\n      return currentContext.slots[this.id];\n    }\n  }\n  withValue(value, callback,\n  // Given the prevalence of arrow functions, specifying arguments is likely\n  // to be much more common than specifying `this`, hence this ordering:\n  args, thisArg) {\n    const slots = {\n      __proto__: null,\n      [this.id]: value\n    };\n    const parent = currentContext;\n    currentContext = {\n      parent,\n      slots\n    };\n    try {\n      // Function.prototype.apply allows the arguments array argument to be\n      // omitted or undefined, so args! is fine here.\n      return callback.apply(thisArg, args);\n    } finally {\n      currentContext = parent;\n    }\n  }\n  // Capture the current context and wrap a callback function so that it\n  // reestablishes the captured context when called.\n  static bind(callback) {\n    const context = currentContext;\n    return function () {\n      const saved = currentContext;\n      try {\n        currentContext = context;\n        return callback.apply(this, arguments);\n      } finally {\n        currentContext = saved;\n      }\n    };\n  }\n  // Immediately run a callback function without any captured context.\n  static noContext(callback,\n  // Given the prevalence of arrow functions, specifying arguments is likely\n  // to be much more common than specifying `this`, hence this ordering:\n  args, thisArg) {\n    if (currentContext) {\n      const saved = currentContext;\n      try {\n        currentContext = null;\n        // Function.prototype.apply allows the arguments array argument to be\n        // omitted or undefined, so args! is fine here.\n        return callback.apply(thisArg, args);\n      } finally {\n        currentContext = saved;\n      }\n    } else {\n      return callback.apply(thisArg, args);\n    }\n  }\n};\nfunction maybe(fn) {\n  try {\n    return fn();\n  } catch (ignored) {}\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\nconst host =\n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(() => globalThis) ||\n// Fall back to global, which works in Node.js and may be converted by some\n// bundlers to the appropriate identifier (window, self, ...) depending on the\n// bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\nmaybe(() => global) ||\n// Otherwise, use a dummy host that's local to this module. We used to fall\n// back to using the Array constructor as a namespace, but that was flagged in\n// https://github.com/benjamn/wryware/issues/347, and can be avoided.\nObject.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost = host;\nexport const Slot = globalHost[globalKey] ||\n// Earlier versions of this package stored the globalKey property on the Array\n// constructor, so we check there as well, to prevent Slot class duplication.\nArray[globalKey] || function (Slot) {\n  try {\n    Object.defineProperty(globalHost, globalKey, {\n      value: Slot,\n      enumerable: false,\n      writable: false,\n      // When it was possible for globalHost to be the Array constructor (a\n      // legacy Slot dedup strategy), it was important for the property to be\n      // configurable:true so it could be deleted. That does not seem to be as\n      // important when globalHost is the global object, but I don't want to\n      // cause similar problems again, and configurable:true seems safest.\n      // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n      configurable: true\n    });\n  } finally {\n    return Slot;\n  }\n}(makeSlotClass());\n//# sourceMappingURL=slot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}