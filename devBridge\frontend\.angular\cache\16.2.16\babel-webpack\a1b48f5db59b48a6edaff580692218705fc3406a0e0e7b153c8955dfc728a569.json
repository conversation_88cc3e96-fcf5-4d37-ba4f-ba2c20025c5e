{"ast": null, "code": "function getDef(f, d) {\n  if (typeof f === 'undefined') {\n    return typeof d === 'undefined' ? f : d;\n  }\n  return f;\n}\nfunction boolean(func, def) {\n  func = getDef(func, def);\n  if (typeof func === 'function') {\n    return function f() {\n      var arguments$1 = arguments;\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments$1[_key];\n      }\n      return !!func.apply(this, args);\n    };\n  }\n  return !!func ? function () {\n    return true;\n  } : function () {\n    return false;\n  };\n}\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\nfunction indexOfElement(elements, element) {\n  element = resolveElement(element, true);\n  if (!isElement$1(element)) {\n    return -1;\n  }\n  for (var i = 0; i < elements.length; i++) {\n    if (elements[i] === element) {\n      return i;\n    }\n  }\n  return -1;\n}\nfunction hasElement(elements, element) {\n  return -1 !== indexOfElement(elements, element);\n}\nfunction pushElements(elements, toAdd) {\n  for (var i = 0; i < toAdd.length; i++) {\n    if (!hasElement(elements, toAdd[i])) {\n      elements.push(toAdd[i]);\n    }\n  }\n  return toAdd;\n}\nfunction addElements(elements) {\n  var arguments$1 = arguments;\n  var toAdd = [],\n    len = arguments.length - 1;\n  while (len-- > 0) {\n    toAdd[len] = arguments$1[len + 1];\n  }\n  toAdd = toAdd.map(resolveElement);\n  return pushElements(elements, toAdd);\n}\nfunction removeElements(elements) {\n  var arguments$1 = arguments;\n  var toRemove = [],\n    len = arguments.length - 1;\n  while (len-- > 0) {\n    toRemove[len] = arguments$1[len + 1];\n  }\n  return toRemove.map(resolveElement).reduce(function (last, e) {\n    var index = indexOfElement(elements, e);\n    if (index !== -1) {\n      return last.concat(elements.splice(index, 1));\n    }\n    return last;\n  }, []);\n}\nfunction resolveElement(element, noThrow) {\n  if (typeof element === 'string') {\n    try {\n      return document.querySelector(element);\n    } catch (e) {\n      throw e;\n    }\n  }\n  if (!isElement$1(element) && !noThrow) {\n    throw new TypeError(element + \" is not a DOM element.\");\n  }\n  return element;\n}\nfunction createPointCB(object, options) {\n  // A persistent object (as opposed to returned object) is used to save memory\n  // This is good to prevent layout thrashing, or for games, and such\n\n  // NOTE\n  // This uses IE fixes which should be OK to remove some day. :)\n  // Some speed will be gained by removal of these.\n\n  // pointCB should be saved in a variable on return\n  // This allows the usage of element.removeEventListener\n\n  options = options || {};\n  var allowUpdate = boolean(options.allowUpdate, true);\n\n  /*if(typeof options.allowUpdate === 'function'){\n      allowUpdate = options.allowUpdate;\n  }else{\n      allowUpdate = function(){return true;};\n  }*/\n\n  return function pointCB(event) {\n    event = event || window.event; // IE-ism\n    object.target = event.target || event.srcElement || event.originalTarget;\n    object.element = this;\n    object.type = event.type;\n    if (!allowUpdate(event)) {\n      return;\n    }\n\n    // Support touch\n    // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n    if (event.targetTouches) {\n      object.x = event.targetTouches[0].clientX;\n      object.y = event.targetTouches[0].clientY;\n      object.pageX = event.targetTouches[0].pageX;\n      object.pageY = event.targetTouches[0].pageY;\n      object.screenX = event.targetTouches[0].screenX;\n      object.screenY = event.targetTouches[0].screenY;\n    } else {\n      // If pageX/Y aren't available and clientX/Y are,\n      // calculate pageX/Y - logic taken from jQuery.\n      // (This is to support old IE)\n      // NOTE Hopefully this can be removed soon.\n\n      if (event.pageX === null && event.clientX !== null) {\n        var eventDoc = event.target && event.target.ownerDocument || document;\n        var doc = eventDoc.documentElement;\n        var body = eventDoc.body;\n        object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n        object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n      } else {\n        object.pageX = event.pageX;\n        object.pageY = event.pageY;\n      }\n\n      // pageX, and pageY change with page scroll\n      // so we're not going to use those for x, and y.\n      // NOTE Most browsers also alias clientX/Y with x/y\n      // so that's something to consider down the road.\n\n      object.x = event.clientX;\n      object.y = event.clientY;\n      object.screenX = event.screenX;\n      object.screenY = event.screenY;\n    }\n    object.clientX = object.x;\n    object.clientY = object.y;\n  };\n\n  //NOTE Remember accessibility, Aria roles, and labels.\n}\n\nfunction createWindowRect() {\n  var props = {\n    top: {\n      value: 0,\n      enumerable: true\n    },\n    left: {\n      value: 0,\n      enumerable: true\n    },\n    right: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    bottom: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    width: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    height: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    x: {\n      value: 0,\n      enumerable: true\n    },\n    y: {\n      value: 0,\n      enumerable: true\n    }\n  };\n  if (Object.create) {\n    return Object.create({}, props);\n  } else {\n    var rect = {};\n    Object.defineProperties(rect, props);\n    return rect;\n  }\n}\nfunction getClientRect(el) {\n  if (el === window) {\n    return createWindowRect();\n  } else {\n    try {\n      var rect = el.getBoundingClientRect();\n      if (rect.x === undefined) {\n        rect.x = rect.left;\n        rect.y = rect.top;\n      }\n      return rect;\n    } catch (e) {\n      throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n    }\n  }\n}\nfunction pointInside(point, el) {\n  var rect = getClientRect(el);\n  return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined$1) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined$1) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\nvar objectCreate$1 = objectCreate;\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\nfunction createDispatcher(element) {\n  var defaultSettings = {\n    screenX: 0,\n    screenY: 0,\n    clientX: 0,\n    clientY: 0,\n    ctrlKey: false,\n    shiftKey: false,\n    altKey: false,\n    metaKey: false,\n    button: 0,\n    buttons: 1,\n    relatedTarget: null,\n    region: null\n  };\n  if (element !== undefined) {\n    element.addEventListener('mousemove', onMove);\n  }\n  function onMove(e) {\n    for (var i = 0; i < mouseEventProps.length; i++) {\n      defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n    }\n  }\n  var dispatch = function () {\n    if (MouseEvent) {\n      return function m1(element, initMove, data) {\n        var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEvent === 'function') {\n      return function m2(element, initMove, data) {\n        var settings = createMoveInit(defaultSettings, initMove);\n        var evt = document.createEvent('MouseEvents');\n        evt.initMouseEvent(\"mousemove\", true,\n        //can bubble\n        true,\n        //cancelable\n        window,\n        //view\n        0,\n        //detail\n        settings.screenX,\n        //0, //screenX\n        settings.screenY,\n        //0, //screenY\n        settings.clientX,\n        //80, //clientX\n        settings.clientY,\n        //20, //clientY\n        settings.ctrlKey,\n        //false, //ctrlKey\n        settings.altKey,\n        //false, //altKey\n        settings.shiftKey,\n        //false, //shiftKey\n        settings.metaKey,\n        //false, //metaKey\n        settings.button,\n        //0, //button\n        settings.relatedTarget //null //relatedTarget\n        );\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEventObject === 'function') {\n      return function m3(element, initMove, data) {\n        var evt = document.createEventObject();\n        var settings = createMoveInit(defaultSettings, initMove);\n        for (var name in settings) {\n          evt[name] = settings[name];\n        }\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    }\n  }();\n  function destroy() {\n    if (element) {\n      element.removeEventListener('mousemove', onMove, false);\n    }\n    defaultSettings = null;\n  }\n  return {\n    destroy: destroy,\n    dispatch: dispatch\n  };\n}\nfunction createMoveInit(defaultSettings, initMove) {\n  initMove = initMove || {};\n  var settings = objectCreate$1(defaultSettings);\n  for (var i = 0; i < mouseEventProps.length; i++) {\n    if (initMove[mouseEventProps[i]] !== undefined) {\n      settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n    }\n  }\n  return settings;\n}\nfunction setSpecial(e, data) {\n  console.log('data ', data);\n  e.data = data || {};\n  e.dispatched = 'mousemove';\n}\nvar prefix = ['webkit', 'moz', 'ms', 'o'];\nvar requestFrame = function () {\n  if (typeof window === \"undefined\") {\n    return function () {};\n  }\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n  if (!window.requestAnimationFrame) {\n    var lastTime = 0;\n    window.requestAnimationFrame = function (callback) {\n      var now = new Date().getTime();\n      var ttc = Math.max(0, 16 - now - lastTime);\n      var timer = window.setTimeout(function () {\n        return callback(now + ttc);\n      }, ttc);\n      lastTime = now + ttc;\n      return timer;\n    };\n  }\n  return window.requestAnimationFrame.bind(window);\n}();\nvar cancelFrame = function () {\n  if (typeof window === \"undefined\") {\n    return function () {};\n  }\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n  return window.cancelAnimationFrame.bind(window);\n}();\nfunction AutoScroller(elements, options) {\n  if (options === void 0) options = {};\n  var self = this;\n  var maxSpeed = 4,\n    scrolling = false;\n  if (typeof options.margin !== 'object') {\n    var margin = options.margin || -1;\n    this.margin = {\n      left: margin,\n      right: margin,\n      top: margin,\n      bottom: margin\n    };\n  } else {\n    this.margin = options.margin;\n  }\n\n  //this.scrolling = false;\n  this.scrollWhenOutside = options.scrollWhenOutside || false;\n  var point = {},\n    pointCB = createPointCB(point),\n    dispatcher = createDispatcher(),\n    down = false;\n  window.addEventListener('mousemove', pointCB, false);\n  window.addEventListener('touchmove', pointCB, false);\n  if (!isNaN(options.maxSpeed)) {\n    maxSpeed = options.maxSpeed;\n  }\n  if (typeof maxSpeed !== 'object') {\n    maxSpeed = {\n      left: maxSpeed,\n      right: maxSpeed,\n      top: maxSpeed,\n      bottom: maxSpeed\n    };\n  }\n  this.autoScroll = boolean(options.autoScroll);\n  this.syncMove = boolean(options.syncMove, false);\n  this.destroy = function (forceCleanAnimation) {\n    window.removeEventListener('mousemove', pointCB, false);\n    window.removeEventListener('touchmove', pointCB, false);\n    window.removeEventListener('mousedown', onDown, false);\n    window.removeEventListener('touchstart', onDown, false);\n    window.removeEventListener('mouseup', onUp, false);\n    window.removeEventListener('touchend', onUp, false);\n    window.removeEventListener('pointerup', onUp, false);\n    window.removeEventListener('mouseleave', onMouseOut, false);\n    window.removeEventListener('mousemove', onMove, false);\n    window.removeEventListener('touchmove', onMove, false);\n    window.removeEventListener('scroll', setScroll, true);\n    elements = [];\n    if (forceCleanAnimation) {\n      cleanAnimation();\n    }\n  };\n  this.add = function () {\n    var element = [],\n      len = arguments.length;\n    while (len--) element[len] = arguments[len];\n    addElements.apply(void 0, [elements].concat(element));\n    return this;\n  };\n  this.remove = function () {\n    var element = [],\n      len = arguments.length;\n    while (len--) element[len] = arguments[len];\n    return removeElements.apply(void 0, [elements].concat(element));\n  };\n  var hasWindow = null,\n    windowAnimationFrame;\n  if (Object.prototype.toString.call(elements) !== '[object Array]') {\n    elements = [elements];\n  }\n  (function (temp) {\n    elements = [];\n    temp.forEach(function (element) {\n      if (element === window) {\n        hasWindow = window;\n      } else {\n        self.add(element);\n      }\n    });\n  })(elements);\n  Object.defineProperties(this, {\n    down: {\n      get: function () {\n        return down;\n      }\n    },\n    maxSpeed: {\n      get: function () {\n        return maxSpeed;\n      }\n    },\n    point: {\n      get: function () {\n        return point;\n      }\n    },\n    scrolling: {\n      get: function () {\n        return scrolling;\n      }\n    }\n  });\n  var current = null,\n    animationFrame;\n  window.addEventListener('mousedown', onDown, false);\n  window.addEventListener('touchstart', onDown, false);\n  window.addEventListener('mouseup', onUp, false);\n  window.addEventListener('touchend', onUp, false);\n\n  /*\n  IE does not trigger mouseup event when scrolling.\n  It is a known issue that Microsoft won't fix.\n  https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n  IE supports pointer events instead\n  */\n  window.addEventListener('pointerup', onUp, false);\n  window.addEventListener('mousemove', onMove, false);\n  window.addEventListener('touchmove', onMove, false);\n  window.addEventListener('mouseleave', onMouseOut, false);\n  window.addEventListener('scroll', setScroll, true);\n  function setScroll(e) {\n    for (var i = 0; i < elements.length; i++) {\n      if (elements[i] === e.target) {\n        scrolling = true;\n        break;\n      }\n    }\n    if (scrolling) {\n      requestFrame(function () {\n        return scrolling = false;\n      });\n    }\n  }\n  function onDown() {\n    down = true;\n  }\n  function onUp() {\n    down = false;\n    cleanAnimation();\n  }\n  function cleanAnimation() {\n    cancelFrame(animationFrame);\n    cancelFrame(windowAnimationFrame);\n  }\n  function onMouseOut() {\n    down = false;\n  }\n  function getTarget(target) {\n    if (!target) {\n      return null;\n    }\n    if (current === target) {\n      return target;\n    }\n    if (hasElement(elements, target)) {\n      return target;\n    }\n    while (target = target.parentNode) {\n      if (hasElement(elements, target)) {\n        return target;\n      }\n    }\n    return null;\n  }\n  function getElementUnderPoint() {\n    var underPoint = null;\n    for (var i = 0; i < elements.length; i++) {\n      if (inside(point, elements[i])) {\n        underPoint = elements[i];\n      }\n    }\n    return underPoint;\n  }\n  function onMove(event) {\n    if (!self.autoScroll()) {\n      return;\n    }\n    if (event['dispatched']) {\n      return;\n    }\n    var target = event.target,\n      body = document.body;\n    if (current && !inside(point, current)) {\n      if (!self.scrollWhenOutside) {\n        current = null;\n      }\n    }\n    if (target && target.parentNode === body) {\n      //The special condition to improve speed.\n      target = getElementUnderPoint();\n    } else {\n      target = getTarget(target);\n      if (!target) {\n        target = getElementUnderPoint();\n      }\n    }\n    if (target && target !== current) {\n      current = target;\n    }\n    if (hasWindow) {\n      cancelFrame(windowAnimationFrame);\n      windowAnimationFrame = requestFrame(scrollWindow);\n    }\n    if (!current) {\n      return;\n    }\n    cancelFrame(animationFrame);\n    animationFrame = requestFrame(scrollTick);\n  }\n  function scrollWindow() {\n    autoScroll(hasWindow);\n    cancelFrame(windowAnimationFrame);\n    windowAnimationFrame = requestFrame(scrollWindow);\n  }\n  function scrollTick() {\n    if (!current) {\n      return;\n    }\n    autoScroll(current);\n    cancelFrame(animationFrame);\n    animationFrame = requestFrame(scrollTick);\n  }\n  function autoScroll(el) {\n    var rect = getClientRect(el),\n      scrollx,\n      scrolly;\n    if (point.x < rect.left + self.margin.left) {\n      scrollx = Math.floor(Math.max(-1, (point.x - rect.left) / self.margin.left - 1) * self.maxSpeed.left);\n    } else if (point.x > rect.right - self.margin.right) {\n      scrollx = Math.ceil(Math.min(1, (point.x - rect.right) / self.margin.right + 1) * self.maxSpeed.right);\n    } else {\n      scrollx = 0;\n    }\n    if (point.y < rect.top + self.margin.top) {\n      scrolly = Math.floor(Math.max(-1, (point.y - rect.top) / self.margin.top - 1) * self.maxSpeed.top);\n    } else if (point.y > rect.bottom - self.margin.bottom) {\n      scrolly = Math.ceil(Math.min(1, (point.y - rect.bottom) / self.margin.bottom + 1) * self.maxSpeed.bottom);\n    } else {\n      scrolly = 0;\n    }\n    if (self.syncMove()) {\n      /*\n      Notes about mousemove event dispatch.\n      screen(X/Y) should need to be updated.\n      Some other properties might need to be set.\n      Keep the syncMove option default false until all inconsistencies are taken care of.\n      */\n      dispatcher.dispatch(el, {\n        pageX: point.pageX + scrollx,\n        pageY: point.pageY + scrolly,\n        clientX: point.x + scrollx,\n        clientY: point.y + scrolly\n      });\n    }\n    setTimeout(function () {\n      if (scrolly) {\n        scrollY(el, scrolly);\n      }\n      if (scrollx) {\n        scrollX(el, scrollx);\n      }\n    });\n  }\n  function scrollY(el, amount) {\n    if (el === window) {\n      window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n    } else {\n      el.scrollTop += amount;\n    }\n  }\n  function scrollX(el, amount) {\n    if (el === window) {\n      window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n    } else {\n      el.scrollLeft += amount;\n    }\n  }\n}\nfunction AutoScrollerFactory(element, options) {\n  return new AutoScroller(element, options);\n}\nfunction inside(point, el, rect) {\n  if (!rect) {\n    return pointInside(point, el);\n  } else {\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n  }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n\nexport default AutoScrollerFactory;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}