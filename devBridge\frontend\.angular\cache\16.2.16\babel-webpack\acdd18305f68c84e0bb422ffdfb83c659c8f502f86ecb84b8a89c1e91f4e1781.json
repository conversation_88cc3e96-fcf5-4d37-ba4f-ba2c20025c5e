{"ast": null, "code": "const MAX_SUGGESTIONS = 5;\n/**\n * Given [ A, B, C ] return ' Did you mean A, B, or C?'.\n */\n\nexport function didYouMean(firstArg, secondArg) {\n  const [subMessage, suggestionsArg] = secondArg ? [firstArg, secondArg] : [undefined, firstArg];\n  let message = ' Did you mean ';\n  if (subMessage) {\n    message += subMessage + ' ';\n  }\n  const suggestions = suggestionsArg.map(x => `\"${x}\"`);\n  switch (suggestions.length) {\n    case 0:\n      return '';\n    case 1:\n      return message + suggestions[0] + '?';\n    case 2:\n      return message + suggestions[0] + ' or ' + suggestions[1] + '?';\n  }\n  const selected = suggestions.slice(0, MAX_SUGGESTIONS);\n  const lastItem = selected.pop();\n  return message + selected.join(', ') + ', or ' + lastItem + '?';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}