{"name": "@apollo/utils.keyvaluecache", "version": "2.1.1", "description": "Minimal key-value cache interface", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/keyValueCache/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}, "dependencies": {"@apollo/utils.logger": "^2.0.1", "lru-cache": "^7.14.1"}}