"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHash = void 0;
const utils_isnodelike_1 = require("@apollo/utils.isnodelike");
function createHash(kind) {
    if (utils_isnodelike_1.isNodeLike && module.require) {
        return module.require("crypto").createHash(kind);
    }
    return require("sha.js")(kind);
}
exports.createHash = createHash;
//# sourceMappingURL=index.js.map