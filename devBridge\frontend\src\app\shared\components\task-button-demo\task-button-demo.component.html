<!-- Démonstration des boutons de tâches améliorés -->
<div class="demo-container p-8 bg-gradient-to-br from-[#f0f4f8] to-[#e2e8f0] dark:from-[#0a0a0a] dark:to-[#1a1a1a] min-h-screen">
  <div class="max-w-6xl mx-auto">
    <!-- Titre de la démonstration -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-4">
        🎨 Démonstration des Boutons "Gérer les Tâches"
      </h1>
      <p class="text-lg text-[#6d6870] dark:text-[#a0a0a0]">
        Design moderne avec animations avancées et indicateurs intelligents
      </p>
    </div>

    <!-- Grille de démonstration -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      
      <!-- Carte d'équipe - Style Utilisateur -->
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff]">
              Équipe Frontend
            </h3>
            <span class="bg-[#00ff9d]/20 text-[#00ff9d] px-3 py-1 rounded-full text-sm font-medium">
              Actif
            </span>
          </div>
          
          <p class="text-[#6d6870] dark:text-[#a0a0a0] mb-6">
            Développement de l'interface utilisateur moderne avec Angular et TailwindCSS.
          </p>

          <!-- Bouton Tasks Principal - Style Utilisateur -->
          <div class="mb-4 relative">
            <button
              class="w-full bg-gradient-to-r from-[#00ff9d] via-[#00e68a] to-[#00d4aa] hover:from-[#00e68a] hover:via-[#00d4aa] hover:to-[#00c199] text-white font-bold py-4 px-6 rounded-xl transition-all duration-500 hover:scale-[1.03] hover:shadow-2xl hover:shadow-[#00ff9d]/30 flex items-center justify-center group relative overflow-hidden task-button-enhanced"
            >
              <!-- Effet de brillance animé -->
              <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              
              <!-- Icône avec animation -->
              <div class="relative z-10 flex items-center justify-center">
                <div class="bg-white/20 rounded-full p-2 mr-3 group-hover:bg-white/30 transition-all duration-300">
                  <i class="fas fa-tasks text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 task-icon-animated"></i>
                </div>
                <span class="text-lg tracking-wide">Gérer les Tâches</span>
                <div class="ml-3 bg-white/20 rounded-full p-2 group-hover:bg-white/30 group-hover:translate-x-1 transition-all duration-300">
                  <i class="fas fa-arrow-right text-sm"></i>
                </div>
              </div>
              
              <!-- Particules d'effet -->
              <div class="task-particles">
                <div class="absolute top-2 right-4 w-1 h-1 bg-white/60 rounded-full particle"></div>
                <div class="absolute bottom-3 left-6 w-1 h-1 bg-white/40 rounded-full particle"></div>
                <div class="absolute top-4 left-1/3 w-0.5 h-0.5 bg-white/50 rounded-full particle"></div>
              </div>
            </button>
            
            <!-- Indicateur de tâches -->
            <div class="absolute -top-2 -right-2 bg-[#ff6b69] text-white text-xs font-bold rounded-full min-w-[24px] h-6 px-2 flex items-center justify-center task-badge-animated">
              12
            </div>
            
            <!-- Barre de progression des tâches -->
            <div class="absolute -bottom-1 left-2 right-2 bg-white/20 rounded-full h-1 overflow-hidden">
              <div class="h-full bg-white/60 rounded-full task-progress-bar" style="width: 75%;"></div>
            </div>
            
            <!-- Informations sur les tâches -->
            <div class="mt-3 text-center">
              <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1">
                9 / 12 tâches terminées
              </div>
              <div class="flex items-center justify-center space-x-2 text-xs">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-[#00ff9d] rounded-full mr-1"></div>
                  <span class="text-[#6d6870] dark:text-[#a0a0a0]">75% complété</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-clock text-[#f59e0b] mr-1"></i>
                  <span class="text-[#6d6870] dark:text-[#a0a0a0]">Actif</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Carte d'équipe - Style Admin -->
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff]">
              Équipe Backend
            </h3>
            <span class="bg-[#4f5fad]/20 text-[#4f5fad] px-3 py-1 rounded-full text-sm font-medium">
              Admin
            </span>
          </div>
          
          <p class="text-[#6d6870] dark:text-[#a0a0a0] mb-6">
            Développement de l'API REST et intégration des services backend.
          </p>

          <!-- Bouton Tasks Principal - Style Admin -->
          <div class="mb-4 relative">
            <button
              class="w-full bg-gradient-to-r from-[#00ff9d] via-[#00e68a] to-[#00d4aa] hover:from-[#00e68a] hover:via-[#00d4aa] hover:to-[#00c199] text-white font-bold py-4 px-6 rounded-xl transition-all duration-500 hover:scale-[1.03] hover:shadow-2xl hover:shadow-[#00ff9d]/30 flex items-center justify-center group relative overflow-hidden task-button-enhanced"
            >
              <!-- Effet de brillance animé -->
              <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              
              <!-- Icône avec animation -->
              <div class="relative z-10 flex items-center justify-center">
                <div class="bg-white/20 rounded-full p-2 mr-3 group-hover:bg-white/30 transition-all duration-300">
                  <i class="fas fa-tasks text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 task-icon-animated"></i>
                </div>
                <span class="text-lg tracking-wide">Gérer les Tâches</span>
                <div class="ml-3 bg-white/20 rounded-full p-2 group-hover:bg-white/30 group-hover:translate-x-1 transition-all duration-300">
                  <i class="fas fa-arrow-right text-sm"></i>
                </div>
              </div>
              
              <!-- Particules d'effet -->
              <div class="task-particles">
                <div class="absolute top-2 right-4 w-1 h-1 bg-white/60 rounded-full particle"></div>
                <div class="absolute bottom-3 left-6 w-1 h-1 bg-white/40 rounded-full particle"></div>
                <div class="absolute top-4 left-1/3 w-0.5 h-0.5 bg-white/50 rounded-full particle"></div>
              </div>
            </button>
            
            <!-- Indicateur de tâches avec badge admin -->
            <div class="absolute -top-2 -right-2 bg-[#4f5fad] text-white text-xs font-bold rounded-full min-w-[24px] h-6 px-2 flex items-center justify-center task-badge-animated">
              18
            </div>
            
            <!-- Badge Admin (couronne) -->
            <div class="absolute -top-2 -left-2 bg-[#f59e0b] text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
              <i class="fas fa-crown text-xs"></i>
            </div>
            
            <!-- Barre de progression des tâches -->
            <div class="absolute -bottom-1 left-2 right-2 bg-white/20 rounded-full h-1 overflow-hidden">
              <div class="h-full bg-white/60 rounded-full task-progress-bar" style="width: 85%;"></div>
            </div>
            
            <!-- Informations sur les tâches -->
            <div class="mt-3 text-center">
              <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1">
                15 / 18 tâches terminées
              </div>
              <div class="flex items-center justify-center space-x-2 text-xs">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-[#00ff9d] rounded-full mr-1"></div>
                  <span class="text-[#6d6870] dark:text-[#a0a0a0]">85% complété</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-shield-alt text-[#4f5fad] mr-1"></i>
                  <span class="text-[#6d6870] dark:text-[#a0a0a0]">Admin</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Carte d'équipe - Style Détails -->
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff]">
              Actions Rapides
            </h3>
            <span class="bg-[#8e2de2]/20 text-[#8e2de2] px-3 py-1 rounded-full text-sm font-medium">
              Détails
            </span>
          </div>
          
          <p class="text-[#6d6870] dark:text-[#a0a0a0] mb-6">
            Bouton dans la page de détails d'équipe avec style adapté.
          </p>

          <!-- Bouton Tasks - Style Détails -->
          <button
            class="w-full bg-gradient-to-r from-[#4f5fad] via-[#7826b5] to-[#8e2de2] dark:from-[#00f7ff] dark:via-[#4f5fad] dark:to-[#7826b5] text-white px-6 py-4 rounded-xl font-bold transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#4f5fad]/30 dark:hover:shadow-[#00f7ff]/30 group relative overflow-hidden task-button-enhanced"
          >
            <!-- Effet de brillance -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/15 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            
            <!-- Contenu du bouton -->
            <div class="relative z-10 flex items-center justify-center">
              <div class="bg-white/20 rounded-full p-2 mr-3 group-hover:bg-white/30 transition-all duration-300">
                <i class="fas fa-tasks text-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 task-icon-animated"></i>
              </div>
              <span class="text-lg tracking-wide">Gérer les Tâches</span>
              <div class="ml-3 bg-white/20 rounded-full p-1.5 group-hover:bg-white/30 group-hover:translate-x-1 transition-all duration-300">
                <i class="fas fa-arrow-right text-sm"></i>
              </div>
            </div>
            
            <!-- Particules décoratives -->
            <div class="task-particles">
              <div class="absolute top-2 right-4 w-1 h-1 bg-white/50 rounded-full particle"></div>
              <div class="absolute bottom-2 left-4 w-0.5 h-0.5 bg-white/40 rounded-full particle"></div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Légende des fonctionnalités -->
    <div class="mt-12 bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg p-8">
      <h2 class="text-2xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-6 text-center">
        🚀 Fonctionnalités Implémentées
      </h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="bg-[#00ff9d]/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-magic text-[#00ff9d] text-2xl"></i>
          </div>
          <h3 class="font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2">Animations Avancées</h3>
          <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Effets de brillance, rotations et transitions fluides</p>
        </div>
        
        <div class="text-center">
          <div class="bg-[#ff6b69]/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-chart-pie text-[#ff6b69] text-2xl"></i>
          </div>
          <h3 class="font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2">Indicateurs Intelligents</h3>
          <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Compteurs de tâches et barres de progression</p>
        </div>
        
        <div class="text-center">
          <div class="bg-[#4f5fad]/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-crown text-[#4f5fad] text-2xl"></i>
          </div>
          <h3 class="font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2">Badges Contextuels</h3>
          <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Différenciation utilisateur/admin avec icônes</p>
        </div>
        
        <div class="text-center">
          <div class="bg-[#8e2de2]/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-mobile-alt text-[#8e2de2] text-2xl"></i>
          </div>
          <h3 class="font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2">Design Responsive</h3>
          <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Adaptatif mobile et desktop avec accessibilité</p>
        </div>
      </div>
    </div>
  </div>
</div>
