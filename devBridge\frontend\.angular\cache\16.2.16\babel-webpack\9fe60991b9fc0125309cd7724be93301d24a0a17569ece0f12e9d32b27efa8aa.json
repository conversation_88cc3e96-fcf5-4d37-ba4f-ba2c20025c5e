{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class Hour1to12Parser extends Parser {\n  priority = 70;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"h\":\n        return parseNumericPattern(numericPatterns.hour12h, dateString);\n      case \"ho\":\n        return match.ordinalNumber(dateString, {\n          unit: \"hour\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 12;\n  }\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else if (!isPM && value === 12) {\n      date.setHours(0, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n  incompatibleTokens = [\"H\", \"K\", \"k\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}