{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\n// === Symbol Support ===\nvar hasSymbols = function () {\n  return typeof Symbol === 'function';\n};\nvar hasSymbol = function (name) {\n  return hasSymbols() && Boolean(Symbol[name]);\n};\nvar getSymbol = function (name) {\n  return hasSymbol(name) ? Symbol[name] : '@@' + name;\n};\nif (hasSymbols() && !hasSymbol('observable')) {\n  Symbol.observable = Symbol('observable');\n}\nvar SymbolIterator = getSymbol('iterator');\nvar SymbolObservable = getSymbol('observable');\nvar SymbolSpecies = getSymbol('species'); // === Abstract Operations ===\n\nfunction getMethod(obj, key) {\n  var value = obj[key];\n  if (value == null) return undefined;\n  if (typeof value !== 'function') throw new TypeError(value + ' is not a function');\n  return value;\n}\nfunction getSpecies(obj) {\n  var ctor = obj.constructor;\n  if (ctor !== undefined) {\n    ctor = ctor[SymbolSpecies];\n    if (ctor === null) {\n      ctor = undefined;\n    }\n  }\n  return ctor !== undefined ? ctor : Observable;\n}\nfunction isObservable(x) {\n  return x instanceof Observable; // SPEC: Brand check\n}\n\nfunction hostReportError(e) {\n  if (hostReportError.log) {\n    hostReportError.log(e);\n  } else {\n    setTimeout(function () {\n      throw e;\n    });\n  }\n}\nfunction enqueue(fn) {\n  Promise.resolve().then(function () {\n    try {\n      fn();\n    } catch (e) {\n      hostReportError(e);\n    }\n  });\n}\nfunction cleanupSubscription(subscription) {\n  var cleanup = subscription._cleanup;\n  if (cleanup === undefined) return;\n  subscription._cleanup = undefined;\n  if (!cleanup) {\n    return;\n  }\n  try {\n    if (typeof cleanup === 'function') {\n      cleanup();\n    } else {\n      var unsubscribe = getMethod(cleanup, 'unsubscribe');\n      if (unsubscribe) {\n        unsubscribe.call(cleanup);\n      }\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n}\nfunction closeSubscription(subscription) {\n  subscription._observer = undefined;\n  subscription._queue = undefined;\n  subscription._state = 'closed';\n}\nfunction flushSubscription(subscription) {\n  var queue = subscription._queue;\n  if (!queue) {\n    return;\n  }\n  subscription._queue = undefined;\n  subscription._state = 'ready';\n  for (var i = 0; i < queue.length; ++i) {\n    notifySubscription(subscription, queue[i].type, queue[i].value);\n    if (subscription._state === 'closed') break;\n  }\n}\nfunction notifySubscription(subscription, type, value) {\n  subscription._state = 'running';\n  var observer = subscription._observer;\n  try {\n    var m = getMethod(observer, type);\n    switch (type) {\n      case 'next':\n        if (m) m.call(observer, value);\n        break;\n      case 'error':\n        closeSubscription(subscription);\n        if (m) m.call(observer, value);else throw value;\n        break;\n      case 'complete':\n        closeSubscription(subscription);\n        if (m) m.call(observer);\n        break;\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n  if (subscription._state === 'closed') cleanupSubscription(subscription);else if (subscription._state === 'running') subscription._state = 'ready';\n}\nfunction onNotify(subscription, type, value) {\n  if (subscription._state === 'closed') return;\n  if (subscription._state === 'buffering') {\n    subscription._queue.push({\n      type: type,\n      value: value\n    });\n    return;\n  }\n  if (subscription._state !== 'ready') {\n    subscription._state = 'buffering';\n    subscription._queue = [{\n      type: type,\n      value: value\n    }];\n    enqueue(function () {\n      return flushSubscription(subscription);\n    });\n    return;\n  }\n  notifySubscription(subscription, type, value);\n}\nvar Subscription = /*#__PURE__*/function () {\n  function Subscription(observer, subscriber) {\n    // ASSERT: observer is an object\n    // ASSERT: subscriber is callable\n    this._cleanup = undefined;\n    this._observer = observer;\n    this._queue = undefined;\n    this._state = 'initializing';\n    var subscriptionObserver = new SubscriptionObserver(this);\n    try {\n      this._cleanup = subscriber.call(undefined, subscriptionObserver);\n    } catch (e) {\n      subscriptionObserver.error(e);\n    }\n    if (this._state === 'initializing') this._state = 'ready';\n  }\n  var _proto = Subscription.prototype;\n  _proto.unsubscribe = function unsubscribe() {\n    if (this._state !== 'closed') {\n      closeSubscription(this);\n      cleanupSubscription(this);\n    }\n  };\n  _createClass(Subscription, [{\n    key: \"closed\",\n    get: function () {\n      return this._state === 'closed';\n    }\n  }]);\n  return Subscription;\n}();\nvar SubscriptionObserver = /*#__PURE__*/function () {\n  function SubscriptionObserver(subscription) {\n    this._subscription = subscription;\n  }\n  var _proto2 = SubscriptionObserver.prototype;\n  _proto2.next = function next(value) {\n    onNotify(this._subscription, 'next', value);\n  };\n  _proto2.error = function error(value) {\n    onNotify(this._subscription, 'error', value);\n  };\n  _proto2.complete = function complete() {\n    onNotify(this._subscription, 'complete');\n  };\n  _createClass(SubscriptionObserver, [{\n    key: \"closed\",\n    get: function () {\n      return this._subscription._state === 'closed';\n    }\n  }]);\n  return SubscriptionObserver;\n}();\nvar Observable = /*#__PURE__*/function () {\n  function Observable(subscriber) {\n    if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');\n    if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');\n    this._subscriber = subscriber;\n  }\n  var _proto3 = Observable.prototype;\n  _proto3.subscribe = function subscribe(observer) {\n    if (typeof observer !== 'object' || observer === null) {\n      observer = {\n        next: observer,\n        error: arguments[1],\n        complete: arguments[2]\n      };\n    }\n    return new Subscription(observer, this._subscriber);\n  };\n  _proto3.forEach = function forEach(fn) {\n    var _this = this;\n    return new Promise(function (resolve, reject) {\n      if (typeof fn !== 'function') {\n        reject(new TypeError(fn + ' is not a function'));\n        return;\n      }\n      function done() {\n        subscription.unsubscribe();\n        resolve();\n      }\n      var subscription = _this.subscribe({\n        next: function (value) {\n          try {\n            fn(value, done);\n          } catch (e) {\n            reject(e);\n            subscription.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n    });\n  };\n  _proto3.map = function map(fn) {\n    var _this2 = this;\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this2.subscribe({\n        next: function (value) {\n          try {\n            value = fn(value);\n          } catch (e) {\n            return observer.error(e);\n          }\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n  _proto3.filter = function filter(fn) {\n    var _this3 = this;\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this3.subscribe({\n        next: function (value) {\n          try {\n            if (!fn(value)) return;\n          } catch (e) {\n            return observer.error(e);\n          }\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n  _proto3.reduce = function reduce(fn) {\n    var _this4 = this;\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    var hasSeed = arguments.length > 1;\n    var hasValue = false;\n    var seed = arguments[1];\n    var acc = seed;\n    return new C(function (observer) {\n      return _this4.subscribe({\n        next: function (value) {\n          var first = !hasValue;\n          hasValue = true;\n          if (!first || hasSeed) {\n            try {\n              acc = fn(acc, value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          } else {\n            acc = value;\n          }\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));\n          observer.next(acc);\n          observer.complete();\n        }\n      });\n    });\n  };\n  _proto3.concat = function concat() {\n    var _this5 = this;\n    for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n      sources[_key] = arguments[_key];\n    }\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscription;\n      var index = 0;\n      function startNext(next) {\n        subscription = next.subscribe({\n          next: function (v) {\n            observer.next(v);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            if (index === sources.length) {\n              subscription = undefined;\n              observer.complete();\n            } else {\n              startNext(C.from(sources[index++]));\n            }\n          }\n        });\n      }\n      startNext(_this5);\n      return function () {\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = undefined;\n        }\n      };\n    });\n  };\n  _proto3.flatMap = function flatMap(fn) {\n    var _this6 = this;\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscriptions = [];\n      var outer = _this6.subscribe({\n        next: function (value) {\n          if (fn) {\n            try {\n              value = fn(value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          }\n          var inner = C.from(value).subscribe({\n            next: function (value) {\n              observer.next(value);\n            },\n            error: function (e) {\n              observer.error(e);\n            },\n            complete: function () {\n              var i = subscriptions.indexOf(inner);\n              if (i >= 0) subscriptions.splice(i, 1);\n              completeIfDone();\n            }\n          });\n          subscriptions.push(inner);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          completeIfDone();\n        }\n      });\n      function completeIfDone() {\n        if (outer.closed && subscriptions.length === 0) observer.complete();\n      }\n      return function () {\n        subscriptions.forEach(function (s) {\n          return s.unsubscribe();\n        });\n        outer.unsubscribe();\n      };\n    });\n  };\n  _proto3[SymbolObservable] = function () {\n    return this;\n  };\n  Observable.from = function from(x) {\n    var C = typeof this === 'function' ? this : Observable;\n    if (x == null) throw new TypeError(x + ' is not an object');\n    var method = getMethod(x, SymbolObservable);\n    if (method) {\n      var observable = method.call(x);\n      if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');\n      if (isObservable(observable) && observable.constructor === C) return observable;\n      return new C(function (observer) {\n        return observable.subscribe(observer);\n      });\n    }\n    if (hasSymbol('iterator')) {\n      method = getMethod(x, SymbolIterator);\n      if (method) {\n        return new C(function (observer) {\n          enqueue(function () {\n            if (observer.closed) return;\n            for (var _iterator = _createForOfIteratorHelperLoose(method.call(x)), _step; !(_step = _iterator()).done;) {\n              var item = _step.value;\n              observer.next(item);\n              if (observer.closed) return;\n            }\n            observer.complete();\n          });\n        });\n      }\n    }\n    if (Array.isArray(x)) {\n      return new C(function (observer) {\n        enqueue(function () {\n          if (observer.closed) return;\n          for (var i = 0; i < x.length; ++i) {\n            observer.next(x[i]);\n            if (observer.closed) return;\n          }\n          observer.complete();\n        });\n      });\n    }\n    throw new TypeError(x + ' is not observable');\n  };\n  Observable.of = function of() {\n    for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      items[_key2] = arguments[_key2];\n    }\n    var C = typeof this === 'function' ? this : Observable;\n    return new C(function (observer) {\n      enqueue(function () {\n        if (observer.closed) return;\n        for (var i = 0; i < items.length; ++i) {\n          observer.next(items[i]);\n          if (observer.closed) return;\n        }\n        observer.complete();\n      });\n    });\n  };\n  _createClass(Observable, null, [{\n    key: SymbolSpecies,\n    get: function () {\n      return this;\n    }\n  }]);\n  return Observable;\n}();\nif (hasSymbols()) {\n  Object.defineProperty(Observable, Symbol('extensions'), {\n    value: {\n      symbol: SymbolObservable,\n      hostReportError: hostReportError\n    },\n    configurable: true\n  });\n}\nexport { Observable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}