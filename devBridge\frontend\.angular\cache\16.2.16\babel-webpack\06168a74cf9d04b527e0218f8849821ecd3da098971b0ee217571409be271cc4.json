{"ast": null, "code": "import { toError } from '../jsutils/toError.mjs';\nimport { GraphQLError } from './GraphQLError.mjs';\n/**\n * Given an arbitrary value, presumably thrown while attempting to execute a\n * GraphQL operation, produce a new GraphQLError aware of the location in the\n * document responsible for the original Error.\n */\n\nexport function locatedError(rawOriginalError, nodes, path) {\n  var _nodes;\n  const originalError = toError(rawOriginalError); // Note: this uses a brand-check to support GraphQL errors originating from other contexts.\n\n  if (isLocatedGraphQLError(originalError)) {\n    return originalError;\n  }\n  return new GraphQLError(originalError.message, {\n    nodes: (_nodes = originalError.nodes) !== null && _nodes !== void 0 ? _nodes : nodes,\n    source: originalError.source,\n    positions: originalError.positions,\n    path,\n    originalError\n  });\n}\nfunction isLocatedGraphQLError(error) {\n  return Array.isArray(error.path);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}