{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nexport function isLeapYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}\n\n// Fallback for modularized imports:\nexport default isLeapYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}