# Script PowerShell pour changer le layout de la page équipes
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("1", "2", "3", "original")]
    [string]$Option
)

$basePath = "src/app/views/front/equipes/equipe-list"
$mainFile = "$basePath/equipe-list.component.html"
$backupFile = "$basePath/equipe-list.component.html.backup"

# Créer une sauvegarde si elle n'existe pas
if (-not (Test-Path $backupFile)) {
    Copy-Item $mainFile $backupFile
    Write-Host "✅ Sauvegarde créée: $backupFile" -ForegroundColor Green
}

switch ($Option) {
    "1" {
        Write-Host "🔄 Application de l'Option 1: Layout Horizontal avec bouton Tasks proéminent..." -ForegroundColor Yellow
        # L'Option 1 est déjà appliquée dans le fichier principal
        Write-Host "✅ Option 1 appliquée! Layout horizontal avec bouton Tasks vert très visible." -ForegroundColor Green
    }
    "2" {
        Write-Host "🔄 Application de l'Option 2: Grille compacte avec header coloré..." -ForegroundColor Yellow
        Copy-Item "$basePath/equipe-list-option2.component.html" $mainFile
        Write-Host "✅ Option 2 appliquée! Grille compacte avec headers bleus et bouton Tasks vert." -ForegroundColor Green
    }
    "3" {
        Write-Host "🔄 Application de l'Option 3: Tableau moderne..." -ForegroundColor Yellow
        Copy-Item "$basePath/equipe-list-option3.component.html" $mainFile
        Write-Host "✅ Option 3 appliquée! Layout en tableau avec bouton Tasks dans les actions." -ForegroundColor Green
    }
    "original" {
        Write-Host "🔄 Restauration du layout original..." -ForegroundColor Yellow
        Copy-Item $backupFile $mainFile
        Write-Host "✅ Layout original restauré!" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "📋 Options disponibles:" -ForegroundColor Cyan
Write-Host "  Option 1: Layout horizontal avec bouton Tasks très visible (actuel)" -ForegroundColor White
Write-Host "  Option 2: Grille compacte avec headers colorés" -ForegroundColor White
Write-Host "  Option 3: Tableau moderne et professionnel" -ForegroundColor White
Write-Host "  original: Restaurer le design original" -ForegroundColor White
Write-Host ""
Write-Host "💡 Pour changer: .\change-equipe-layout.ps1 -Option [1|2|3|original]" -ForegroundColor Gray
Write-Host "🌐 Accédez à: http://localhost:4200/equipes pour voir les changements" -ForegroundColor Gray
