import { DirectiveDefinitionNode, ScalarTypeDefinitionNode, ScalarTypeExtensionNode } from 'graphql';
import { Config } from './merge-typedefs.cjs';
export declare function mergeScalar(node: ScalarTypeDefinitionNode | ScalarTypeExtensionNode, existingNode: ScalarTypeDefinitionNode | ScalarTypeExtensionNode, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): ScalarTypeDefinitionNode | ScalarTypeExtensionNode;
