{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\n// CDK\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { OverlayModule } from '@angular/cdk/overlay';\n// Shared Components\nimport { KanbanBoardComponent } from '../components/kanban-board/kanban-board.component';\nimport { TaskAiAssistantComponent } from '../components/task-ai-assistant/task-ai-assistant.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule,\n      // Angular Material\n      MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n      // CDK\n      DragDropModule, OverlayModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [KanbanBoardComponent, TaskAiAssistantComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule,\n    // Angular Material\n    MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n    // CDK\n    DragDropModule, OverlayModule],\n    exports: [\n    // Components\n    KanbanBoardComponent, TaskAiAssistantComponent,\n    // Angular Material (pour réutilisation)\n    MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n    // CDK\n    DragDropModule, OverlayModule,\n    // Forms\n    ReactiveFormsModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatChipsModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatDialogModule", "MatMenuModule", "MatTooltipModule", "MatTabsModule", "MatSlideToggleModule", "MatCheckboxModule", "MatBadgeModule", "MatExpansionModule", "MatListModule", "MatDividerModule", "DragDropModule", "OverlayModule", "KanbanBoardComponent", "TaskAiAssistantComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// CDK\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { OverlayModule } from '@angular/cdk/overlay';\n\n// Shared Components\nimport { KanbanBoardComponent } from '../components/kanban-board/kanban-board.component';\nimport { TaskAiAssistantComponent } from '../components/task-ai-assistant/task-ai-assistant.component';\n\n@NgModule({\n  declarations: [\n    KanbanBoardComponent,\n    TaskAiAssistantComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    \n    // Angular Material\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDialogModule,\n    MatMenuModule,\n    MatTooltipModule,\n    MatTabsModule,\n    MatSlideToggleModule,\n    MatCheckboxModule,\n    MatBadgeModule,\n    MatExpansionModule,\n    MatListModule,\n    MatDividerModule,\n    \n    // CDK\n    DragDropModule,\n    OverlayModule\n  ],\n  exports: [\n    // Components\n    KanbanBoardComponent,\n    TaskAiAssistantComponent,\n    \n    // Angular Material (pour réutilisation)\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDialogModule,\n    MatMenuModule,\n    MatTooltipModule,\n    MatTabsModule,\n    MatSlideToggleModule,\n    MatCheckboxModule,\n    MatBadgeModule,\n    MatExpansionModule,\n    MatListModule,\n    MatDividerModule,\n    \n    // CDK\n    DragDropModule,\n    OverlayModule,\n    \n    // Forms\n    ReactiveFormsModule,\n    FormsModule\n  ]\n})\nexport class SharedModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD;AACA,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,wBAAwB,QAAQ,6DAA6D;;AA4EtG,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBApErB5B,YAAY,EACZC,mBAAmB,EACnBC,WAAW;MAEX;MACAC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;MAEhB;MACAC,cAAc,EACdC,aAAa;IAAA;EAAA;;;2EAuCJG,YAAY;IAAAC,YAAA,GAxErBH,oBAAoB,EACpBC,wBAAwB;IAAAG,OAAA,GAGxB9B,YAAY,EACZC,mBAAmB,EACnBC,WAAW;IAEX;IACAC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;IAEhB;IACAC,cAAc,EACdC,aAAa;IAAAM,OAAA;IAGb;IACAL,oBAAoB,EACpBC,wBAAwB;IAExB;IACAxB,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;IAEhB;IACAC,cAAc,EACdC,aAAa;IAEb;IACAxB,mBAAmB,EACnBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}