{"ast": null, "code": "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601. +00:00 is `'Z'`)\nexport class ISOTimezoneWithZParser extends Parser {\n  priority = 10;\n  parse(dateString, token) {\n    switch (token) {\n      case \"X\":\n        return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, dateString);\n      case \"XX\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"XXXX\":\n        return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, dateString);\n      case \"XXXXX\":\n        return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, dateString);\n      case \"XXX\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, date.getTime() - getTimezoneOffsetInMilliseconds(date) - value);\n  }\n  incompatibleTokens = [\"t\", \"T\", \"x\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}