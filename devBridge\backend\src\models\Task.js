const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const TaskSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Le titre est requis'],
    trim: true,
    maxlength: [200, 'Le titre ne peut pas dépasser 200 caractères']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [2000, 'La description ne peut pas dépasser 2000 caractères']
  },

  // Statut Kanban étendu
  status: {
    type: String,
    enum: ['backlog', 'todo', 'in-progress', 'review', 'testing', 'done', 'archived'],
    default: 'todo'
  },

  // Position dans la colonne Kanban
  position: {
    type: Number,
    default: 0
  },

  priority: {
    type: String,
    enum: ['lowest', 'low', 'medium', 'high', 'highest', 'critical'],
    default: 'medium'
  },

  // Dates importantes
  dueDate: {
    type: Date
  },
  startDate: {
    type: Date
  },
  completedDate: {
    type: Date
  },

  // Assignation multiple
  assignedTo: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],

  teamId: {
    type: Schema.Types.ObjectId,
    ref: 'Team',
    required: [true, 'L\'ID de l\'équipe est requis']
  },

  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Le créateur est requis']
  },

  // Catégorie et labels
  category: {
    type: String,
    enum: ['feature', 'bug', 'improvement', 'task', 'epic', 'story'],
    default: 'task'
  },

  labels: [{
    name: { type: String, required: true },
    color: { type: String, default: '#gray' }
  }],

  // Estimation et temps
  estimatedHours: {
    type: Number,
    min: 0,
    max: 1000
  },

  actualHours: {
    type: Number,
    min: 0,
    default: 0
  },

  // Complexité (pour planning poker)
  storyPoints: {
    type: Number,
    enum: [1, 2, 3, 5, 8, 13, 21, 34, 55, 89],
    default: null
  },

  // Dépendances
  dependencies: [{
    task: { type: Schema.Types.ObjectId, ref: 'Task' },
    type: { type: String, enum: ['blocks', 'blocked_by', 'relates_to'], default: 'relates_to' }
  }],

  // Tâche parent (pour sous-tâches)
  parentTask: {
    type: Schema.Types.ObjectId,
    ref: 'Task',
    default: null
  },

  // Sous-tâches
  subtasks: [{
    type: Schema.Types.ObjectId,
    ref: 'Task'
  }],

  // Pièces jointes
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
    uploadedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    uploadedAt: { type: Date, default: Date.now }
  }],

  // Commentaires
  comments: [{
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    content: { type: String, required: true, maxlength: 1000 },
    createdAt: { type: Date, default: Date.now },
    editedAt: { type: Date },
    isAIGenerated: { type: Boolean, default: false }
  }],

  // Intégration IA
  aiSuggestions: {
    enabled: { type: Boolean, default: true },
    lastAnalysis: { type: Date },
    suggestions: [{
      type: { type: String, enum: ['optimization', 'assignment', 'deadline', 'priority', 'breakdown'] },
      content: String,
      confidence: { type: Number, min: 0, max: 1 },
      createdAt: { type: Date, default: Date.now },
      applied: { type: Boolean, default: false }
    }],
    autoGenerated: {
      description: { type: Boolean, default: false },
      subtasks: { type: Boolean, default: false },
      estimation: { type: Boolean, default: false }
    }
  },

  // Métriques et tracking
  metrics: {
    viewCount: { type: Number, default: 0 },
    editCount: { type: Number, default: 0 },
    lastViewed: { type: Date },
    lastEdited: { type: Date },
    timeInStatus: [{
      status: String,
      duration: Number, // en minutes
      startTime: Date,
      endTime: Date
    }]
  },

  // Récurrence (pour tâches répétitives)
  recurrence: {
    enabled: { type: Boolean, default: false },
    pattern: { type: String, enum: ['daily', 'weekly', 'monthly', 'yearly'] },
    interval: { type: Number, default: 1 },
    endDate: Date,
    nextDue: Date
  },

  // Workflow personnalisé
  workflow: {
    currentStep: { type: Number, default: 0 },
    steps: [{
      name: String,
      status: { type: String, enum: ['pending', 'completed', 'skipped'] },
      assignee: { type: Schema.Types.ObjectId, ref: 'User' },
      completedAt: Date,
      notes: String
    }]
  },

  // Archivage et suppression
  isArchived: {
    type: Boolean,
    default: false
  },

  archivedAt: {
    type: Date
  },

  archivedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
TaskSchema.index({ teamId: 1, status: 1 });
TaskSchema.index({ assignedTo: 1, status: 1 });
TaskSchema.index({ createdBy: 1 });
TaskSchema.index({ dueDate: 1 });
TaskSchema.index({ priority: 1 });
TaskSchema.index({ position: 1 });
TaskSchema.index({ parentTask: 1 });
TaskSchema.index({ 'labels.name': 1 });
TaskSchema.index({ category: 1 });
TaskSchema.index({ isArchived: 1 });

// Propriétés virtuelles
TaskSchema.virtual('isOverdue').get(function() {
  return this.dueDate && this.dueDate < new Date() && this.status !== 'done';
});

TaskSchema.virtual('daysUntilDue').get(function() {
  if (!this.dueDate) return null;
  const diff = this.dueDate.getTime() - Date.now();
  return Math.ceil(diff / (1000 * 60 * 60 * 24));
});

TaskSchema.virtual('completionRate').get(function() {
  if (!this.subtasks || this.subtasks.length === 0) return 100;
  // Cette propriété sera calculée lors du populate des sous-tâches
  return 0;
});

TaskSchema.virtual('totalTimeSpent').get(function() {
  if (!this.metrics.timeInStatus) return 0;
  return this.metrics.timeInStatus.reduce((total, time) => total + (time.duration || 0), 0);
});

TaskSchema.virtual('isBlocked').get(function() {
  return this.dependencies && this.dependencies.some(dep => dep.type === 'blocked_by');
});

// Méthodes d'instance
TaskSchema.methods.addComment = function(userId, content, isAI = false) {
  this.comments.push({
    user: userId,
    content: content,
    isAIGenerated: isAI
  });
  return this.save();
};

TaskSchema.methods.updatePosition = function(newPosition) {
  this.position = newPosition;
  return this.save();
};

TaskSchema.methods.changeStatus = function(newStatus, userId) {
  const oldStatus = this.status;
  this.status = newStatus;

  // Enregistrer le temps passé dans l'ancien statut
  if (this.metrics.timeInStatus) {
    const lastStatusEntry = this.metrics.timeInStatus.find(t => t.status === oldStatus && !t.endTime);
    if (lastStatusEntry) {
      lastStatusEntry.endTime = new Date();
      lastStatusEntry.duration = (lastStatusEntry.endTime - lastStatusEntry.startTime) / (1000 * 60); // en minutes
    }
  }

  // Commencer le tracking du nouveau statut
  this.metrics.timeInStatus.push({
    status: newStatus,
    startTime: new Date()
  });

  // Marquer comme complété si done
  if (newStatus === 'done' && !this.completedDate) {
    this.completedDate = new Date();
  }

  this.metrics.lastEdited = new Date();
  this.metrics.editCount += 1;

  return this.save();
};

TaskSchema.methods.assignTo = function(userIds) {
  this.assignedTo = Array.isArray(userIds) ? userIds : [userIds];
  this.metrics.lastEdited = new Date();
  this.metrics.editCount += 1;
  return this.save();
};

TaskSchema.methods.addLabel = function(name, color = '#gray') {
  if (!this.labels.find(label => label.name === name)) {
    this.labels.push({ name, color });
  }
  return this.save();
};

TaskSchema.methods.removeLabel = function(name) {
  this.labels = this.labels.filter(label => label.name !== name);
  return this.save();
};

TaskSchema.methods.archive = function(userId) {
  this.isArchived = true;
  this.archivedAt = new Date();
  this.archivedBy = userId;
  return this.save();
};

TaskSchema.methods.unarchive = function() {
  this.isArchived = false;
  this.archivedAt = null;
  this.archivedBy = null;
  return this.save();
};

// Méthodes statiques
TaskSchema.statics.getKanbanBoard = function(teamId, options = {}) {
  const { includeArchived = false, assignedTo, labels, category } = options;

  const filter = { teamId, parentTask: null }; // Seulement les tâches principales
  if (!includeArchived) filter.isArchived = false;
  if (assignedTo) filter.assignedTo = assignedTo;
  if (labels && labels.length > 0) filter['labels.name'] = { $in: labels };
  if (category) filter.category = category;

  return this.find(filter)
    .populate('assignedTo', 'fullName email profileImage')
    .populate('createdBy', 'fullName email profileImage')
    .populate('subtasks')
    .sort({ position: 1, createdAt: -1 });
};

TaskSchema.statics.getTasksByStatus = function(teamId, status) {
  return this.find({ teamId, status, isArchived: false })
    .populate('assignedTo', 'fullName email profileImage')
    .populate('createdBy', 'fullName email profileImage')
    .sort({ position: 1, createdAt: -1 });
};

TaskSchema.statics.getTaskStatistics = function(teamId, period = 30) {
  const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000);

  return this.aggregate([
    { $match: { teamId: mongoose.Types.ObjectId(teamId), createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgHours: { $avg: '$actualHours' },
        totalHours: { $sum: '$actualHours' }
      }
    }
  ]);
};

// Middleware pre-save
TaskSchema.pre('save', function(next) {
  // Initialiser les métriques si nécessaire
  if (!this.metrics.timeInStatus || this.metrics.timeInStatus.length === 0) {
    this.metrics.timeInStatus = [{
      status: this.status,
      startTime: new Date()
    }];
  }

  // Mettre à jour lastEdited si modifié
  if (this.isModified() && !this.isNew) {
    this.metrics.lastEdited = new Date();
    this.metrics.editCount += 1;
  }

  next();
});

module.exports = mongoose.model('Task', TaskSchema);