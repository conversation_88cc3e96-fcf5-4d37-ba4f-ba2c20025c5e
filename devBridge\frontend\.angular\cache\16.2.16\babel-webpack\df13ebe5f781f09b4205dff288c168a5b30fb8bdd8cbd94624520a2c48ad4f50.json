{"ast": null, "code": "import { parentEntrySlot } from \"./context.js\";\nimport { maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n  if (!condition) {\n    throw new Error(optionalMessage || \"assertion failure\");\n  }\n}\nfunction valueIs(a, b) {\n  const len = a.length;\n  return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n    // Both values must be ordinary (or both exceptional) to be equal.\n    len === b.length &&\n    // The underlying value or exception must be the same.\n    a[len - 1] === b[len - 1]\n  );\n}\nfunction valueGet(value) {\n  switch (value.length) {\n    case 0:\n      throw new Error(\"unknown value\");\n    case 1:\n      return value[0];\n    case 2:\n      throw value[1];\n  }\n}\nfunction valueCopy(value) {\n  return value.slice(0);\n}\nexport let Entry = /*#__PURE__*/(() => {\n  class Entry {\n    constructor(fn) {\n      this.fn = fn;\n      this.parents = new Set();\n      this.childValues = new Map();\n      // When this Entry has children that are dirty, this property becomes\n      // a Set containing other Entry objects, borrowed from emptySetPool.\n      // When the set becomes empty, it gets recycled back to emptySetPool.\n      this.dirtyChildren = null;\n      this.dirty = true;\n      this.recomputing = false;\n      this.value = [];\n      this.deps = null;\n      ++Entry.count;\n    }\n    peek() {\n      if (this.value.length === 1 && !mightBeDirty(this)) {\n        rememberParent(this);\n        return this.value[0];\n      }\n    }\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    recompute(args) {\n      assert(!this.recomputing, \"already recomputing\");\n      rememberParent(this);\n      return mightBeDirty(this) ? reallyRecompute(this, args) : valueGet(this.value);\n    }\n    setDirty() {\n      if (this.dirty) return;\n      this.dirty = true;\n      reportDirty(this);\n      // We can go ahead and unsubscribe here, since any further dirty\n      // notifications we receive will be redundant, and unsubscribing may\n      // free up some resources, e.g. file watchers.\n      maybeUnsubscribe(this);\n    }\n    dispose() {\n      this.setDirty();\n      // Sever any dependency relationships with our own children, so those\n      // children don't retain this parent Entry in their child.parents sets,\n      // thereby preventing it from being fully garbage collected.\n      forgetChildren(this);\n      // Because this entry has been kicked out of the cache (in index.js),\n      // we've lost the ability to find out if/when this entry becomes dirty,\n      // whether that happens through a subscription, because of a direct call\n      // to entry.setDirty(), or because one of its children becomes dirty.\n      // Because of this loss of future information, we have to assume the\n      // worst (that this entry might have become dirty very soon), so we must\n      // immediately mark this entry's parents as dirty. Normally we could\n      // just call entry.setDirty() rather than calling parent.setDirty() for\n      // each parent, but that would leave this entry in parent.childValues\n      // and parent.dirtyChildren, which would prevent the child from being\n      // truly forgotten.\n      eachParent(this, (parent, child) => {\n        parent.setDirty();\n        forgetChild(parent, this);\n      });\n    }\n    forget() {\n      // The code that creates Entry objects in index.ts will replace this method\n      // with one that actually removes the Entry from the cache, which will also\n      // trigger the entry.dispose method.\n      this.dispose();\n    }\n    dependOn(dep) {\n      dep.add(this);\n      if (!this.deps) {\n        this.deps = emptySetPool.pop() || new Set();\n      }\n      this.deps.add(dep);\n    }\n    forgetDeps() {\n      if (this.deps) {\n        arrayFromSet(this.deps).forEach(dep => dep.delete(this));\n        this.deps.clear();\n        emptySetPool.push(this.deps);\n        this.deps = null;\n      }\n    }\n  }\n  Entry.count = 0;\n  return Entry;\n})();\nfunction rememberParent(child) {\n  const parent = parentEntrySlot.getValue();\n  if (parent) {\n    child.parents.add(parent);\n    if (!parent.childValues.has(child)) {\n      parent.childValues.set(child, []);\n    }\n    if (mightBeDirty(child)) {\n      reportDirtyChild(parent, child);\n    } else {\n      reportCleanChild(parent, child);\n    }\n    return parent;\n  }\n}\nfunction reallyRecompute(entry, args) {\n  forgetChildren(entry);\n  // Set entry as the parent entry while calling recomputeNewValue(entry).\n  parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n  if (maybeSubscribe(entry, args)) {\n    // If we successfully recomputed entry.value and did not fail to\n    // (re)subscribe, then this Entry is no longer explicitly dirty.\n    setClean(entry);\n  }\n  return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n  entry.recomputing = true;\n  const {\n    normalizeResult\n  } = entry;\n  let oldValueCopy;\n  if (normalizeResult && entry.value.length === 1) {\n    oldValueCopy = valueCopy(entry.value);\n  }\n  // Make entry.value an empty array, representing an unknown value.\n  entry.value.length = 0;\n  try {\n    // If entry.fn succeeds, entry.value will become a normal Value.\n    entry.value[0] = entry.fn.apply(null, args);\n    // If we have a viable oldValueCopy to compare with the (successfully\n    // recomputed) new entry.value, and they are not already === identical, give\n    // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n    // and/or entry.value[0] to determine the final cached entry.value.\n    if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n      try {\n        entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n      } catch (_a) {\n        // If normalizeResult throws, just use the newer value, rather than\n        // saving the exception as entry.value[1].\n      }\n    }\n  } catch (e) {\n    // If entry.fn throws, entry.value will hold that exception.\n    entry.value[1] = e;\n  }\n  // Either way, this line is always reached.\n  entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n  return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n  entry.dirty = false;\n  if (mightBeDirty(entry)) {\n    // This Entry may still have dirty children, in which case we can't\n    // let our parents know we're clean just yet.\n    return;\n  }\n  reportClean(entry);\n}\nfunction reportDirty(child) {\n  eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n  eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n  const parentCount = child.parents.size;\n  if (parentCount) {\n    const parents = arrayFromSet(child.parents);\n    for (let i = 0; i < parentCount; ++i) {\n      callback(parents[i], child);\n    }\n  }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n  // Must have called rememberParent(child) before calling\n  // reportDirtyChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(mightBeDirty(child));\n  const parentWasClean = !mightBeDirty(parent);\n  if (!parent.dirtyChildren) {\n    parent.dirtyChildren = emptySetPool.pop() || new Set();\n  } else if (parent.dirtyChildren.has(child)) {\n    // If we already know this child is dirty, then we must have already\n    // informed our own parents that we are dirty, so we can terminate\n    // the recursion early.\n    return;\n  }\n  parent.dirtyChildren.add(child);\n  // If parent was clean before, it just became (possibly) dirty (according to\n  // mightBeDirty), since we just added child to parent.dirtyChildren.\n  if (parentWasClean) {\n    reportDirty(parent);\n  }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n  // Must have called rememberChild(child) before calling\n  // reportCleanChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(!mightBeDirty(child));\n  const childValue = parent.childValues.get(child);\n  if (childValue.length === 0) {\n    parent.childValues.set(child, valueCopy(child.value));\n  } else if (!valueIs(childValue, child.value)) {\n    parent.setDirty();\n  }\n  removeDirtyChild(parent, child);\n  if (mightBeDirty(parent)) {\n    return;\n  }\n  reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n  const dc = parent.dirtyChildren;\n  if (dc) {\n    dc.delete(child);\n    if (dc.size === 0) {\n      if (emptySetPool.length < POOL_TARGET_SIZE) {\n        emptySetPool.push(dc);\n      }\n      parent.dirtyChildren = null;\n    }\n  }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n  if (parent.childValues.size > 0) {\n    parent.childValues.forEach((_value, child) => {\n      forgetChild(parent, child);\n    });\n  }\n  // Remove this parent Entry from any sets to which it was added by the\n  // addToSet method.\n  parent.forgetDeps();\n  // After we forget all our children, this.dirtyChildren must be empty\n  // and therefore must have been reset to null.\n  assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n  child.parents.delete(parent);\n  parent.childValues.delete(child);\n  removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n  if (typeof entry.subscribe === \"function\") {\n    try {\n      maybeUnsubscribe(entry); // Prevent double subscriptions.\n      entry.unsubscribe = entry.subscribe.apply(null, args);\n    } catch (e) {\n      // If this Entry has a subscribe function and it threw an exception\n      // (or an unsubscribe function it previously returned now throws),\n      // return false to indicate that we were not able to subscribe (or\n      // unsubscribe), and this Entry should remain dirty.\n      entry.setDirty();\n      return false;\n    }\n  }\n  // Returning true indicates either that there was no entry.subscribe\n  // function or that it succeeded.\n  return true;\n}\n//# sourceMappingURL=entry.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}