{"ast": null, "code": "// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst {\n  forEach,\n  slice\n} = Array.prototype;\nconst {\n  hasOwnProperty\n} = Object.prototype;\nexport class Trie {\n  constructor(weakness = true, makeData = defaultMakeData) {\n    this.weakness = weakness;\n    this.makeData = makeData;\n  }\n  lookup() {\n    return this.lookupArray(arguments);\n  }\n  lookupArray(array) {\n    let node = this;\n    forEach.call(array, key => node = node.getChildTrie(key));\n    return hasOwnProperty.call(node, \"data\") ? node.data : node.data = this.makeData(slice.call(array));\n  }\n  peek() {\n    return this.peekArray(arguments);\n  }\n  peekArray(array) {\n    let node = this;\n    for (let i = 0, len = array.length; node && i < len; ++i) {\n      const map = node.mapFor(array[i], false);\n      node = map && map.get(array[i]);\n    }\n    return node && node.data;\n  }\n  remove() {\n    return this.removeArray(arguments);\n  }\n  removeArray(array) {\n    let data;\n    if (array.length) {\n      const head = array[0];\n      const map = this.mapFor(head, false);\n      const child = map && map.get(head);\n      if (child) {\n        data = child.removeArray(slice.call(array, 1));\n        if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n          map.delete(head);\n        }\n      }\n    } else {\n      data = this.data;\n      delete this.data;\n    }\n    return data;\n  }\n  getChildTrie(key) {\n    const map = this.mapFor(key, true);\n    let child = map.get(key);\n    if (!child) map.set(key, child = new Trie(this.weakness, this.makeData));\n    return child;\n  }\n  mapFor(key, create) {\n    return this.weakness && isObjRef(key) ? this.weak || (create ? this.weak = new WeakMap() : void 0) : this.strong || (create ? this.strong = new Map() : void 0);\n  }\n}\nfunction isObjRef(value) {\n  switch (typeof value) {\n    case \"object\":\n      if (value === null) break;\n    // Fall through to return true...\n    case \"function\":\n      return true;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["defaultMakeData", "Object", "create", "for<PERSON>ach", "slice", "Array", "prototype", "hasOwnProperty", "<PERSON><PERSON>", "constructor", "weakness", "makeData", "lookup", "lookupArray", "arguments", "array", "node", "call", "key", "get<PERSON><PERSON>dTrie", "data", "peek", "peekArray", "i", "len", "length", "map", "mapFor", "get", "remove", "removeArray", "head", "child", "weak", "strong", "size", "delete", "set", "isObjRef", "WeakMap", "Map", "value"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@wry/trie/lib/index.js"], "sourcesContent": ["// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\nexport class Trie {\n    constructor(weakness = true, makeData = defaultMakeData) {\n        this.weakness = weakness;\n        this.makeData = makeData;\n    }\n    lookup() {\n        return this.lookupArray(arguments);\n    }\n    lookupArray(array) {\n        let node = this;\n        forEach.call(array, key => node = node.getChildTrie(key));\n        return hasOwnProperty.call(node, \"data\")\n            ? node.data\n            : node.data = this.makeData(slice.call(array));\n    }\n    peek() {\n        return this.peekArray(arguments);\n    }\n    peekArray(array) {\n        let node = this;\n        for (let i = 0, len = array.length; node && i < len; ++i) {\n            const map = node.mapFor(array[i], false);\n            node = map && map.get(array[i]);\n        }\n        return node && node.data;\n    }\n    remove() {\n        return this.removeArray(arguments);\n    }\n    removeArray(array) {\n        let data;\n        if (array.length) {\n            const head = array[0];\n            const map = this.mapFor(head, false);\n            const child = map && map.get(head);\n            if (child) {\n                data = child.removeArray(slice.call(array, 1));\n                if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n                    map.delete(head);\n                }\n            }\n        }\n        else {\n            data = this.data;\n            delete this.data;\n        }\n        return data;\n    }\n    getChildTrie(key) {\n        const map = this.mapFor(key, true);\n        let child = map.get(key);\n        if (!child)\n            map.set(key, child = new Trie(this.weakness, this.makeData));\n        return child;\n    }\n    mapFor(key, create) {\n        return this.weakness && isObjRef(key)\n            ? this.weak || (create ? this.weak = new WeakMap : void 0)\n            : this.strong || (create ? this.strong = new Map : void 0);\n    }\n}\nfunction isObjRef(value) {\n    switch (typeof value) {\n        case \"object\":\n            if (value === null)\n                break;\n        // Fall through to return true...\n        case \"function\":\n            return true;\n    }\n    return false;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,GAAGA,CAAA,KAAMC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACjD;AACA,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,GAAGC,KAAK,CAACC,SAAS;AAC1C,MAAM;EAAEC;AAAe,CAAC,GAAGN,MAAM,CAACK,SAAS;AAC3C,OAAO,MAAME,IAAI,CAAC;EACdC,WAAWA,CAACC,QAAQ,GAAG,IAAI,EAAEC,QAAQ,GAAGX,eAAe,EAAE;IACrD,IAAI,CAACU,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC;EACtC;EACAD,WAAWA,CAACE,KAAK,EAAE;IACf,IAAIC,IAAI,GAAG,IAAI;IACfb,OAAO,CAACc,IAAI,CAACF,KAAK,EAAEG,GAAG,IAAIF,IAAI,GAAGA,IAAI,CAACG,YAAY,CAACD,GAAG,CAAC,CAAC;IACzD,OAAOX,cAAc,CAACU,IAAI,CAACD,IAAI,EAAE,MAAM,CAAC,GAClCA,IAAI,CAACI,IAAI,GACTJ,IAAI,CAACI,IAAI,GAAG,IAAI,CAACT,QAAQ,CAACP,KAAK,CAACa,IAAI,CAACF,KAAK,CAAC,CAAC;EACtD;EACAM,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACC,SAAS,CAACR,SAAS,CAAC;EACpC;EACAQ,SAASA,CAACP,KAAK,EAAE;IACb,IAAIC,IAAI,GAAG,IAAI;IACf,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGT,KAAK,CAACU,MAAM,EAAET,IAAI,IAAIO,CAAC,GAAGC,GAAG,EAAE,EAAED,CAAC,EAAE;MACtD,MAAMG,GAAG,GAAGV,IAAI,CAACW,MAAM,CAACZ,KAAK,CAACQ,CAAC,CAAC,EAAE,KAAK,CAAC;MACxCP,IAAI,GAAGU,GAAG,IAAIA,GAAG,CAACE,GAAG,CAACb,KAAK,CAACQ,CAAC,CAAC,CAAC;IACnC;IACA,OAAOP,IAAI,IAAIA,IAAI,CAACI,IAAI;EAC5B;EACAS,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACC,WAAW,CAAChB,SAAS,CAAC;EACtC;EACAgB,WAAWA,CAACf,KAAK,EAAE;IACf,IAAIK,IAAI;IACR,IAAIL,KAAK,CAACU,MAAM,EAAE;MACd,MAAMM,IAAI,GAAGhB,KAAK,CAAC,CAAC,CAAC;MACrB,MAAMW,GAAG,GAAG,IAAI,CAACC,MAAM,CAACI,IAAI,EAAE,KAAK,CAAC;MACpC,MAAMC,KAAK,GAAGN,GAAG,IAAIA,GAAG,CAACE,GAAG,CAACG,IAAI,CAAC;MAClC,IAAIC,KAAK,EAAE;QACPZ,IAAI,GAAGY,KAAK,CAACF,WAAW,CAAC1B,KAAK,CAACa,IAAI,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACiB,KAAK,CAACZ,IAAI,IAAI,CAACY,KAAK,CAACC,IAAI,IAAI,EAAED,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACE,MAAM,CAACC,IAAI,CAAC,EAAE;UACpET,GAAG,CAACU,MAAM,CAACL,IAAI,CAAC;QACpB;MACJ;IACJ,CAAC,MACI;MACDX,IAAI,GAAG,IAAI,CAACA,IAAI;MAChB,OAAO,IAAI,CAACA,IAAI;IACpB;IACA,OAAOA,IAAI;EACf;EACAD,YAAYA,CAACD,GAAG,EAAE;IACd,MAAMQ,GAAG,GAAG,IAAI,CAACC,MAAM,CAACT,GAAG,EAAE,IAAI,CAAC;IAClC,IAAIc,KAAK,GAAGN,GAAG,CAACE,GAAG,CAACV,GAAG,CAAC;IACxB,IAAI,CAACc,KAAK,EACNN,GAAG,CAACW,GAAG,CAACnB,GAAG,EAAEc,KAAK,GAAG,IAAIxB,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;IAChE,OAAOqB,KAAK;EAChB;EACAL,MAAMA,CAACT,GAAG,EAAEhB,MAAM,EAAE;IAChB,OAAO,IAAI,CAACQ,QAAQ,IAAI4B,QAAQ,CAACpB,GAAG,CAAC,GAC/B,IAAI,CAACe,IAAI,KAAK/B,MAAM,GAAG,IAAI,CAAC+B,IAAI,GAAG,IAAIM,OAAO,CAAD,CAAC,GAAG,KAAK,CAAC,CAAC,GACxD,IAAI,CAACL,MAAM,KAAKhC,MAAM,GAAG,IAAI,CAACgC,MAAM,GAAG,IAAIM,GAAG,CAAD,CAAC,GAAG,KAAK,CAAC,CAAC;EAClE;AACJ;AACA,SAASF,QAAQA,CAACG,KAAK,EAAE;EACrB,QAAQ,OAAOA,KAAK;IAChB,KAAK,QAAQ;MACT,IAAIA,KAAK,KAAK,IAAI,EACd;IACR;IACA,KAAK,UAAU;MACX,OAAO,IAAI;EACnB;EACA,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}