{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isEnumType, isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\nimport { GraphQLID } from '../type/scalars.mjs';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\n\nexport function astFromValue(value, type) {\n  if (isNonNullType(type)) {\n    const astValue = astFromValue(value, type.ofType);\n    if ((astValue === null || astValue === void 0 ? void 0 : astValue.kind) === Kind.NULL) {\n      return null;\n    }\n    return astValue;\n  } // only explicit null, not undefined, NaN\n\n  if (value === null) {\n    return {\n      kind: Kind.NULL\n    };\n  } // undefined\n\n  if (value === undefined) {\n    return null;\n  } // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n  // the value is not an array, convert the value using the list's item type.\n\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (isIterableObject(value)) {\n      const valuesNodes = [];\n      for (const item of value) {\n        const itemNode = astFromValue(item, itemType);\n        if (itemNode != null) {\n          valuesNodes.push(itemNode);\n        }\n      }\n      return {\n        kind: Kind.LIST,\n        values: valuesNodes\n      };\n    }\n    return astFromValue(value, itemType);\n  } // Populate the fields of the input object by creating ASTs from each value\n  // in the JavaScript object according to the fields in the input type.\n\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(value)) {\n      return null;\n    }\n    const fieldNodes = [];\n    for (const field of Object.values(type.getFields())) {\n      const fieldValue = astFromValue(value[field.name], field.type);\n      if (fieldValue) {\n        fieldNodes.push({\n          kind: Kind.OBJECT_FIELD,\n          name: {\n            kind: Kind.NAME,\n            value: field.name\n          },\n          value: fieldValue\n        });\n      }\n    }\n    return {\n      kind: Kind.OBJECT,\n      fields: fieldNodes\n    };\n  }\n  if (isLeafType(type)) {\n    // Since value is an internally represented value, it must be serialized\n    // to an externally represented value before converting into an AST.\n    const serialized = type.serialize(value);\n    if (serialized == null) {\n      return null;\n    } // Others serialize based on their corresponding JavaScript scalar types.\n\n    if (typeof serialized === 'boolean') {\n      return {\n        kind: Kind.BOOLEAN,\n        value: serialized\n      };\n    } // JavaScript numbers can be Int or Float values.\n\n    if (typeof serialized === 'number' && Number.isFinite(serialized)) {\n      const stringNum = String(serialized);\n      return integerStringRegExp.test(stringNum) ? {\n        kind: Kind.INT,\n        value: stringNum\n      } : {\n        kind: Kind.FLOAT,\n        value: stringNum\n      };\n    }\n    if (typeof serialized === 'string') {\n      // Enum types use Enum literals.\n      if (isEnumType(type)) {\n        return {\n          kind: Kind.ENUM,\n          value: serialized\n        };\n      } // ID types can use Int literals.\n\n      if (type === GraphQLID && integerStringRegExp.test(serialized)) {\n        return {\n          kind: Kind.INT,\n          value: serialized\n        };\n      }\n      return {\n        kind: Kind.STRING,\n        value: serialized\n      };\n    }\n    throw new TypeError(`Cannot convert value to AST: ${inspect(serialized)}.`);\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\n\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;", "map": {"version": 3, "names": ["inspect", "invariant", "isIterableObject", "isObjectLike", "Kind", "isEnumType", "isInputObjectType", "isLeafType", "isListType", "isNonNullType", "GraphQLID", "astFromValue", "value", "type", "astValue", "ofType", "kind", "NULL", "undefined", "itemType", "valuesNodes", "item", "itemNode", "push", "LIST", "values", "fieldNodes", "field", "Object", "getFields", "fieldValue", "name", "OBJECT_FIELD", "NAME", "OBJECT", "fields", "serialized", "serialize", "BOOLEAN", "Number", "isFinite", "stringNum", "String", "integerStringRegExp", "test", "INT", "FLOAT", "ENUM", "STRING", "TypeError"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/astFromValue.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport {\n  isEnumType,\n  isInputObjectType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n} from '../type/definition.mjs';\nimport { GraphQLID } from '../type/scalars.mjs';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\n\nexport function astFromValue(value, type) {\n  if (isNonNullType(type)) {\n    const astValue = astFromValue(value, type.ofType);\n\n    if (\n      (astValue === null || astValue === void 0 ? void 0 : astValue.kind) ===\n      Kind.NULL\n    ) {\n      return null;\n    }\n\n    return astValue;\n  } // only explicit null, not undefined, NaN\n\n  if (value === null) {\n    return {\n      kind: Kind.NULL,\n    };\n  } // undefined\n\n  if (value === undefined) {\n    return null;\n  } // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n  // the value is not an array, convert the value using the list's item type.\n\n  if (isListType(type)) {\n    const itemType = type.ofType;\n\n    if (isIterableObject(value)) {\n      const valuesNodes = [];\n\n      for (const item of value) {\n        const itemNode = astFromValue(item, itemType);\n\n        if (itemNode != null) {\n          valuesNodes.push(itemNode);\n        }\n      }\n\n      return {\n        kind: Kind.LIST,\n        values: valuesNodes,\n      };\n    }\n\n    return astFromValue(value, itemType);\n  } // Populate the fields of the input object by creating ASTs from each value\n  // in the JavaScript object according to the fields in the input type.\n\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(value)) {\n      return null;\n    }\n\n    const fieldNodes = [];\n\n    for (const field of Object.values(type.getFields())) {\n      const fieldValue = astFromValue(value[field.name], field.type);\n\n      if (fieldValue) {\n        fieldNodes.push({\n          kind: Kind.OBJECT_FIELD,\n          name: {\n            kind: Kind.NAME,\n            value: field.name,\n          },\n          value: fieldValue,\n        });\n      }\n    }\n\n    return {\n      kind: Kind.OBJECT,\n      fields: fieldNodes,\n    };\n  }\n\n  if (isLeafType(type)) {\n    // Since value is an internally represented value, it must be serialized\n    // to an externally represented value before converting into an AST.\n    const serialized = type.serialize(value);\n\n    if (serialized == null) {\n      return null;\n    } // Others serialize based on their corresponding JavaScript scalar types.\n\n    if (typeof serialized === 'boolean') {\n      return {\n        kind: Kind.BOOLEAN,\n        value: serialized,\n      };\n    } // JavaScript numbers can be Int or Float values.\n\n    if (typeof serialized === 'number' && Number.isFinite(serialized)) {\n      const stringNum = String(serialized);\n      return integerStringRegExp.test(stringNum)\n        ? {\n            kind: Kind.INT,\n            value: stringNum,\n          }\n        : {\n            kind: Kind.FLOAT,\n            value: stringNum,\n          };\n    }\n\n    if (typeof serialized === 'string') {\n      // Enum types use Enum literals.\n      if (isEnumType(type)) {\n        return {\n          kind: Kind.ENUM,\n          value: serialized,\n        };\n      } // ID types can use Int literals.\n\n      if (type === GraphQLID && integerStringRegExp.test(serialized)) {\n        return {\n          kind: Kind.INT,\n          value: serialized,\n        };\n      }\n\n      return {\n        kind: Kind.STRING,\n        value: serialized,\n      };\n    }\n\n    throw new TypeError(`Cannot convert value to AST: ${inspect(serialized)}.`);\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\n\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SACEC,UAAU,EACVC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,aAAa,QACR,wBAAwB;AAC/B,SAASC,SAAS,QAAQ,qBAAqB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACxC,IAAIJ,aAAa,CAACI,IAAI,CAAC,EAAE;IACvB,MAAMC,QAAQ,GAAGH,YAAY,CAACC,KAAK,EAAEC,IAAI,CAACE,MAAM,CAAC;IAEjD,IACE,CAACD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,IAAI,MAClEZ,IAAI,CAACa,IAAI,EACT;MACA,OAAO,IAAI;IACb;IAEA,OAAOH,QAAQ;EACjB,CAAC,CAAC;;EAEF,IAAIF,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO;MACLI,IAAI,EAAEZ,IAAI,CAACa;IACb,CAAC;EACH,CAAC,CAAC;;EAEF,IAAIL,KAAK,KAAKM,SAAS,EAAE;IACvB,OAAO,IAAI;EACb,CAAC,CAAC;EACF;;EAEA,IAAIV,UAAU,CAACK,IAAI,CAAC,EAAE;IACpB,MAAMM,QAAQ,GAAGN,IAAI,CAACE,MAAM;IAE5B,IAAIb,gBAAgB,CAACU,KAAK,CAAC,EAAE;MAC3B,MAAMQ,WAAW,GAAG,EAAE;MAEtB,KAAK,MAAMC,IAAI,IAAIT,KAAK,EAAE;QACxB,MAAMU,QAAQ,GAAGX,YAAY,CAACU,IAAI,EAAEF,QAAQ,CAAC;QAE7C,IAAIG,QAAQ,IAAI,IAAI,EAAE;UACpBF,WAAW,CAACG,IAAI,CAACD,QAAQ,CAAC;QAC5B;MACF;MAEA,OAAO;QACLN,IAAI,EAAEZ,IAAI,CAACoB,IAAI;QACfC,MAAM,EAAEL;MACV,CAAC;IACH;IAEA,OAAOT,YAAY,CAACC,KAAK,EAAEO,QAAQ,CAAC;EACtC,CAAC,CAAC;EACF;;EAEA,IAAIb,iBAAiB,CAACO,IAAI,CAAC,EAAE;IAC3B,IAAI,CAACV,YAAY,CAACS,KAAK,CAAC,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,MAAMc,UAAU,GAAG,EAAE;IAErB,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAAC,CAAC,CAAC,EAAE;MACnD,MAAMC,UAAU,GAAGnB,YAAY,CAACC,KAAK,CAACe,KAAK,CAACI,IAAI,CAAC,EAAEJ,KAAK,CAACd,IAAI,CAAC;MAE9D,IAAIiB,UAAU,EAAE;QACdJ,UAAU,CAACH,IAAI,CAAC;UACdP,IAAI,EAAEZ,IAAI,CAAC4B,YAAY;UACvBD,IAAI,EAAE;YACJf,IAAI,EAAEZ,IAAI,CAAC6B,IAAI;YACfrB,KAAK,EAAEe,KAAK,CAACI;UACf,CAAC;UACDnB,KAAK,EAAEkB;QACT,CAAC,CAAC;MACJ;IACF;IAEA,OAAO;MACLd,IAAI,EAAEZ,IAAI,CAAC8B,MAAM;MACjBC,MAAM,EAAET;IACV,CAAC;EACH;EAEA,IAAInB,UAAU,CAACM,IAAI,CAAC,EAAE;IACpB;IACA;IACA,MAAMuB,UAAU,GAAGvB,IAAI,CAACwB,SAAS,CAACzB,KAAK,CAAC;IAExC,IAAIwB,UAAU,IAAI,IAAI,EAAE;MACtB,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF,IAAI,OAAOA,UAAU,KAAK,SAAS,EAAE;MACnC,OAAO;QACLpB,IAAI,EAAEZ,IAAI,CAACkC,OAAO;QAClB1B,KAAK,EAAEwB;MACT,CAAC;IACH,CAAC,CAAC;;IAEF,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAIG,MAAM,CAACC,QAAQ,CAACJ,UAAU,CAAC,EAAE;MACjE,MAAMK,SAAS,GAAGC,MAAM,CAACN,UAAU,CAAC;MACpC,OAAOO,mBAAmB,CAACC,IAAI,CAACH,SAAS,CAAC,GACtC;QACEzB,IAAI,EAAEZ,IAAI,CAACyC,GAAG;QACdjC,KAAK,EAAE6B;MACT,CAAC,GACD;QACEzB,IAAI,EAAEZ,IAAI,CAAC0C,KAAK;QAChBlC,KAAK,EAAE6B;MACT,CAAC;IACP;IAEA,IAAI,OAAOL,UAAU,KAAK,QAAQ,EAAE;MAClC;MACA,IAAI/B,UAAU,CAACQ,IAAI,CAAC,EAAE;QACpB,OAAO;UACLG,IAAI,EAAEZ,IAAI,CAAC2C,IAAI;UACfnC,KAAK,EAAEwB;QACT,CAAC;MACH,CAAC,CAAC;;MAEF,IAAIvB,IAAI,KAAKH,SAAS,IAAIiC,mBAAmB,CAACC,IAAI,CAACR,UAAU,CAAC,EAAE;QAC9D,OAAO;UACLpB,IAAI,EAAEZ,IAAI,CAACyC,GAAG;UACdjC,KAAK,EAAEwB;QACT,CAAC;MACH;MAEA,OAAO;QACLpB,IAAI,EAAEZ,IAAI,CAAC4C,MAAM;QACjBpC,KAAK,EAAEwB;MACT,CAAC;IACH;IAEA,MAAM,IAAIa,SAAS,CAAE,gCAA+BjD,OAAO,CAACoC,UAAU,CAAE,GAAE,CAAC;EAC7E;EACA;EACA;;EAEA,KAAK,IAAInC,SAAS,CAAC,KAAK,EAAE,yBAAyB,GAAGD,OAAO,CAACa,IAAI,CAAC,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM8B,mBAAmB,GAAG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}