{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/task.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/cdk/drag-drop\";\nfunction KanbanBoardComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6, \"En retard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.kanbanData.stats.overdue);\n  }\n}\nfunction KanbanBoardComponent_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"block\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6, \"Bloqu\\u00E9es\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.kanbanData.stats.blocked);\n  }\n}\nfunction KanbanBoardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 9);\n    i0.ɵɵtext(8, \"Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, KanbanBoardComponent_div_1_div_9_Template, 7, 1, \"div\", 10);\n    i0.ɵɵtemplate(10, KanbanBoardComponent_div_1_div_10_Template, 7, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.refresh());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 14)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"filter_list\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.kanbanData.stats.total);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.kanbanData.stats.overdue > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.kanbanData.stats.blocked > 0);\n  }\n}\nfunction KanbanBoardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"mat-spinner\", 18);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement du tableau Kanban...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction KanbanBoardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\", 20);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.loadKanbanBoard());\n    });\n    i0.ɵɵtext(6, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 54);\n    i0.ɵɵtext(1, \" schedule \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1, \" block \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_p_14_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_p_14_span_3_Template, 2, 0, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 2, task_r14.description, 0, 100), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.description && task_r14.description.length > 100);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r30 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", label_r30.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", label_r30.name, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", task_r14.labels.length - 3, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_15_span_1_Template, 2, 3, \"span\", 59);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_div_15_span_3_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 2, task_r14.labels, 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.labels && task_r14.labels.length > 3);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 68);\n  }\n  if (rf & 2) {\n    const assignee_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"src\", ctx_r36.getAssigneeImage(assignee_r35), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r36.getAssigneeName(assignee_r35));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.getAssigneeInitials(assignee_r35), \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_img_1_Template, 1, 2, \"img\", 67);\n    i0.ɵɵtemplate(2, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_span_2_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r35 = ctx.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r33.getAssigneeName(assignee_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.hasAssigneeImage(assignee_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.hasAssigneeImage(assignee_r35));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", task_r14.assignedTo.length - 3, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_Template, 3, 3, \"div\", 64);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_div_17_span_3_Template, 2, 1, \"span\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 2, task_r14.assignedTo, 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.assignedTo && task_r14.assignedTo.length > 3);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"overdue\", task_r14.isOverdue);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 3, task_r14.dueDate, \"dd/MM\"), \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.estimatedHours, \"h \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"list\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.subtasks.length, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.comments.length, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions == null ? null : task_r14.aiSuggestions.suggestions.length) || 0);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r14.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", task_r14.category, \" \\u2022 \", task_r14.priority, \"\");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_div_13_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r49);\n      const task_r14 = restoredCtx.$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.selectTask(task_r14));\n    });\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵelementStart(2, \"div\", 34)(3, \"div\", 35)(4, \"div\", 36)(5, \"mat-icon\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵtemplate(10, KanbanBoardComponent_div_4_div_1_div_13_mat_icon_10_Template, 2, 0, \"mat-icon\", 40);\n    i0.ɵɵtemplate(11, KanbanBoardComponent_div_4_div_1_div_13_mat_icon_11_Template, 2, 0, \"mat-icon\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"h4\", 42);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, KanbanBoardComponent_div_4_div_1_div_13_p_14_Template, 4, 6, \"p\", 43);\n    i0.ɵɵtemplate(15, KanbanBoardComponent_div_4_div_1_div_13_div_15_Template, 4, 6, \"div\", 44);\n    i0.ɵɵelementStart(16, \"div\", 45);\n    i0.ɵɵtemplate(17, KanbanBoardComponent_div_4_div_1_div_13_div_17_Template, 4, 6, \"div\", 46);\n    i0.ɵɵelementStart(18, \"div\", 47);\n    i0.ɵɵtemplate(19, KanbanBoardComponent_div_4_div_1_div_13_span_19_Template, 5, 6, \"span\", 48);\n    i0.ɵɵtemplate(20, KanbanBoardComponent_div_4_div_1_div_13_span_20_Template, 4, 1, \"span\", 49);\n    i0.ɵɵtemplate(21, KanbanBoardComponent_div_4_div_1_div_13_span_21_Template, 4, 1, \"span\", 50);\n    i0.ɵɵtemplate(22, KanbanBoardComponent_div_4_div_1_div_13_span_22_Template, 4, 1, \"span\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, KanbanBoardComponent_div_4_div_1_div_13_div_23_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, KanbanBoardComponent_div_4_div_1_div_13_div_24_Template, 6, 3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"cdkDragData\", task_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r12.getPriorityColor(task_r14.priority));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r12.getCategoryIcon(task_r14.category || \"task\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", task_r14._id == null ? null : task_r14._id.slice(-6), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.isOverdue);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.isBlocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(task_r14.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.labels && task_r14.labels.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.assignedTo && task_r14.assignedTo.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.dueDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.estimatedHours);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.subtasks && task_r14.subtasks.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.comments && task_r14.comments.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions) && (task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions == null ? null : task_r14.aiSuggestions.suggestions.length) > 0);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_div_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const column_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.createTaskInColumn(column_r11.id));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Ajouter une t\\u00E2che \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const column_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(column_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.getEmptyColumnText(column_r11.id));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const column_r11 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.createTaskInColumn(column_r11.id));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"add\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 29);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function KanbanBoardComponent_div_4_div_1_Template_div_cdkDropListDropped_12_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onTaskDrop($event));\n    });\n    i0.ɵɵtemplate(13, KanbanBoardComponent_div_4_div_1_div_13_Template, 25, 16, \"div\", 30);\n    i0.ɵɵtemplate(14, KanbanBoardComponent_div_4_div_1_div_14_Template, 9, 2, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const column_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-top-color\", column_r11.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", column_r11.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(column_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(column_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r10.getColumnCount(column_r11.id), \")\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"id\", column_r11.id)(\"cdkDropListData\", ctx_r10.getColumnTasks(column_r11.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getColumnTasks(column_r11.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isColumnEmpty(column_r11.id));\n  }\n}\nfunction KanbanBoardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_Template, 15, 11, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.columns);\n  }\n}\nexport let KanbanBoardComponent = /*#__PURE__*/(() => {\n  class KanbanBoardComponent {\n    constructor(taskService, dialog, snackBar) {\n      this.taskService = taskService;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.filters = {};\n      this.taskSelected = new EventEmitter();\n      this.taskCreated = new EventEmitter();\n      this.kanbanData = null;\n      this.loading = false;\n      this.error = null;\n      // Configuration des colonnes Kanban\n      this.columns = [{\n        id: 'backlog',\n        title: 'Backlog',\n        color: '#6b7280',\n        icon: 'inventory_2'\n      }, {\n        id: 'todo',\n        title: 'À faire',\n        color: '#3b82f6',\n        icon: 'assignment'\n      }, {\n        id: 'in-progress',\n        title: 'En cours',\n        color: '#f59e0b',\n        icon: 'play_circle'\n      }, {\n        id: 'review',\n        title: 'Révision',\n        color: '#8b5cf6',\n        icon: 'rate_review'\n      }, {\n        id: 'testing',\n        title: 'Tests',\n        color: '#06b6d4',\n        icon: 'bug_report'\n      }, {\n        id: 'done',\n        title: 'Terminé',\n        color: '#10b981',\n        icon: 'check_circle'\n      }];\n    }\n    ngOnInit() {\n      this.loadKanbanBoard();\n    }\n    // Charger le tableau Kanban\n    loadKanbanBoard() {\n      if (!this.teamId) return;\n      this.loading = true;\n      this.error = null;\n      this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({\n        next: data => {\n          this.kanbanData = data;\n          this.loading = false;\n        },\n        error: error => {\n          this.error = 'Erreur lors du chargement du tableau Kanban';\n          this.loading = false;\n          console.error('Erreur Kanban:', error);\n          this.snackBar.open('Erreur lors du chargement', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Gérer le drag & drop\n    onTaskDrop(event) {\n      const task = event.item.data;\n      const newStatus = event.container.id;\n      const oldStatus = event.previousContainer.id;\n      if (event.previousContainer === event.container) {\n        // Réorganisation dans la même colonne\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n        this.updateTaskPosition(task, event.currentIndex);\n      } else {\n        // Déplacement vers une autre colonne\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n        this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);\n      }\n    }\n    // Déplacer une tâche vers une nouvelle colonne\n    moveTaskToColumn(task, newStatus, newPosition, oldStatus) {\n      const moveRequest = {\n        newStatus,\n        newPosition,\n        oldStatus\n      };\n      this.taskService.moveTask(task._id, moveRequest).subscribe({\n        next: response => {\n          this.snackBar.open('Tâche déplacée avec succès', 'Fermer', {\n            duration: 2000\n          });\n          // Mettre à jour les statistiques\n          this.updateStats();\n        },\n        error: error => {\n          console.error('Erreur déplacement tâche:', error);\n          this.snackBar.open('Erreur lors du déplacement', 'Fermer', {\n            duration: 3000\n          });\n          // Annuler le déplacement visuel\n          this.loadKanbanBoard();\n        }\n      });\n    }\n    // Mettre à jour la position d'une tâche\n    updateTaskPosition(task, newPosition) {\n      const moveRequest = {\n        newStatus: task.status,\n        newPosition,\n        oldStatus: task.status\n      };\n      this.taskService.moveTask(task._id, moveRequest).subscribe({\n        error: error => {\n          console.error('Erreur mise à jour position:', error);\n          this.loadKanbanBoard(); // Recharger en cas d'erreur\n        }\n      });\n    }\n    // Créer une nouvelle tâche dans une colonne\n    createTaskInColumn(status) {\n      const taskData = {\n        title: 'Nouvelle tâche',\n        description: '',\n        status,\n        priority: 'medium',\n        category: 'task'\n      };\n      this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({\n        next: response => {\n          this.taskCreated.emit(response.task);\n          this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche\n          this.snackBar.open('Tâche créée avec succès', 'Fermer', {\n            duration: 2000\n          });\n        },\n        error: error => {\n          console.error('Erreur création tâche:', error);\n          this.snackBar.open('Erreur lors de la création', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Sélectionner une tâche\n    selectTask(task) {\n      this.taskSelected.emit(task);\n    }\n    // Obtenir les tâches d'une colonne\n    getColumnTasks(columnId) {\n      if (!this.kanbanData) return [];\n      return this.kanbanData.kanbanBoard[columnId] || [];\n    }\n    // Obtenir la couleur de priorité\n    getPriorityColor(priority) {\n      const colors = {\n        'lowest': '#6b7280',\n        'low': '#3b82f6',\n        'medium': '#f59e0b',\n        'high': '#ef4444',\n        'highest': '#dc2626',\n        'critical': '#991b1b'\n      };\n      return colors[priority] || '#6b7280';\n    }\n    // Obtenir l'icône de catégorie\n    getCategoryIcon(category) {\n      const icons = {\n        'feature': 'new_releases',\n        'bug': 'bug_report',\n        'improvement': 'trending_up',\n        'task': 'assignment',\n        'epic': 'flag',\n        'story': 'book'\n      };\n      return icons[category] || 'assignment';\n    }\n    // Mettre à jour les statistiques\n    updateStats() {\n      if (this.kanbanData) {\n        // Recalculer les statistiques localement\n        const tasks = Object.values(this.kanbanData.kanbanBoard).flat();\n        this.kanbanData.stats.total = tasks.length;\n        this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;\n        this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;\n        // Mettre à jour les statistiques par statut\n        const validStatuses = ['backlog', 'todo', 'in-progress', 'review', 'testing', 'done', 'archived'];\n        validStatuses.forEach(status => {\n          const columnTasks = this.kanbanData.kanbanBoard[status];\n          this.kanbanData.stats.byStatus[status] = columnTasks ? columnTasks.length : 0;\n        });\n      }\n    }\n    // Appliquer des filtres\n    applyFilters(newFilters) {\n      this.filters = {\n        ...this.filters,\n        ...newFilters\n      };\n      this.loadKanbanBoard();\n    }\n    // Réinitialiser les filtres\n    resetFilters() {\n      this.filters = {};\n      this.loadKanbanBoard();\n    }\n    // Actualiser le tableau\n    refresh() {\n      this.loadKanbanBoard();\n    }\n    // Obtenir le nombre de tâches dans une colonne\n    getColumnCount(columnId) {\n      return this.getColumnTasks(columnId).length;\n    }\n    // Vérifier si une colonne est vide\n    isColumnEmpty(columnId) {\n      return this.getColumnTasks(columnId).length === 0;\n    }\n    // Obtenir le texte de placeholder pour une colonne vide\n    getEmptyColumnText(columnId) {\n      const texts = {\n        'backlog': 'Aucune tâche en attente',\n        'todo': 'Aucune tâche à faire',\n        'in-progress': 'Aucune tâche en cours',\n        'review': 'Aucune tâche en révision',\n        'testing': 'Aucune tâche en test',\n        'done': 'Aucune tâche terminée'\n      };\n      return texts[columnId] || 'Aucune tâche';\n    }\n    // Helper pour obtenir le nom d'un assigné\n    getAssigneeName(assignee) {\n      if (typeof assignee === 'string') {\n        return assignee;\n      }\n      return assignee.fullName || assignee.username || assignee.email || 'Utilisateur';\n    }\n    // Helper pour obtenir l'image de profil d'un assigné\n    getAssigneeImage(assignee) {\n      if (typeof assignee === 'string') {\n        return null;\n      }\n      return assignee.profileImage || null;\n    }\n    // Helper pour vérifier si un assigné a une image\n    hasAssigneeImage(assignee) {\n      return typeof assignee !== 'string' && !!assignee.profileImage;\n    }\n    // Helper pour obtenir les initiales d'un assigné\n    getAssigneeInitials(assignee) {\n      const name = this.getAssigneeName(assignee);\n      return name.charAt(0).toUpperCase();\n    }\n    static {\n      this.ɵfac = function KanbanBoardComponent_Factory(t) {\n        return new (t || KanbanBoardComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: KanbanBoardComponent,\n        selectors: [[\"app-kanban-board\"]],\n        inputs: {\n          teamId: \"teamId\",\n          filters: \"filters\"\n        },\n        outputs: {\n          taskSelected: \"taskSelected\",\n          taskCreated: \"taskCreated\"\n        },\n        decls: 5,\n        vars: 4,\n        consts: [[1, \"kanban-board-container\"], [\"class\", \"kanban-header\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"kanban-board\", \"cdkDropListGroup\", \"\", 4, \"ngIf\"], [1, \"kanban-header\"], [1, \"stats-container\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item overdue\", 4, \"ngIf\"], [\"class\", \"stat-item blocked\", 4, \"ngIf\"], [1, \"actions-container\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Actualiser\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Filtres\"], [1, \"stat-item\", \"overdue\"], [1, \"stat-item\", \"blocked\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"cdkDropListGroup\", \"\", 1, \"kanban-board\"], [\"class\", \"kanban-column\", 3, \"border-top-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"kanban-column\"], [1, \"column-header\"], [1, \"column-title\"], [1, \"task-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Ajouter une t\\u00E2che\", 1, \"add-task-btn\", 3, \"click\"], [\"cdkDropList\", \"\", 1, \"tasks-container\", 3, \"id\", \"cdkDropListData\", \"cdkDropListDropped\"], [\"class\", \"task-card\", \"cdkDrag\", \"\", 3, \"cdkDragData\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-column\", 4, \"ngIf\"], [\"cdkDrag\", \"\", 1, \"task-card\", 3, \"cdkDragData\", \"click\"], [1, \"priority-indicator\"], [1, \"task-content\"], [1, \"task-header\"], [1, \"task-category\"], [1, \"category-icon\"], [1, \"task-id\"], [1, \"task-actions\"], [\"class\", \"overdue-icon\", \"matTooltip\", \"En retard\", 4, \"ngIf\"], [\"class\", \"blocked-icon\", \"matTooltip\", \"Bloqu\\u00E9e\", 4, \"ngIf\"], [1, \"task-title\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-labels\", 4, \"ngIf\"], [1, \"task-footer\"], [\"class\", \"task-assignees\", 4, \"ngIf\"], [1, \"task-meta\"], [\"class\", \"due-date\", 3, \"overdue\", 4, \"ngIf\"], [\"class\", \"estimated-hours\", 4, \"ngIf\"], [\"class\", \"subtasks-count\", 4, \"ngIf\"], [\"class\", \"comments-count\", 4, \"ngIf\"], [\"class\", \"ai-indicator\", \"matTooltip\", \"Suggestions IA disponibles\", 4, \"ngIf\"], [\"class\", \"task-drag-preview\", 4, \"cdkDragPreview\"], [\"matTooltip\", \"En retard\", 1, \"overdue-icon\"], [\"matTooltip\", \"Bloqu\\u00E9e\", 1, \"blocked-icon\"], [1, \"task-description\"], [4, \"ngIf\"], [1, \"task-labels\"], [\"class\", \"task-label\", 3, \"background-color\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-labels\", 4, \"ngIf\"], [1, \"task-label\"], [1, \"more-labels\"], [1, \"task-assignees\"], [\"class\", \"assignee-avatar\", 3, \"matTooltip\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-assignees\", 4, \"ngIf\"], [1, \"assignee-avatar\", 3, \"matTooltip\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [3, \"src\", \"alt\"], [1, \"more-assignees\"], [1, \"due-date\"], [1, \"estimated-hours\"], [1, \"subtasks-count\"], [1, \"comments-count\"], [\"matTooltip\", \"Suggestions IA disponibles\", 1, \"ai-indicator\"], [1, \"task-drag-preview\"], [1, \"drag-preview-content\"], [1, \"empty-column\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n        template: function KanbanBoardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, KanbanBoardComponent_div_1_Template, 18, 3, \"div\", 1);\n            i0.ɵɵtemplate(2, KanbanBoardComponent_div_2_Template, 4, 0, \"div\", 2);\n            i0.ɵɵtemplate(3, KanbanBoardComponent_div_3_Template, 7, 1, \"div\", 3);\n            i0.ɵɵtemplate(4, KanbanBoardComponent_div_4_Template, 2, 1, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.kanbanData);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.kanbanData && !ctx.loading && !ctx.error);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton, i5.MatIconButton, i6.MatIcon, i7.MatProgressSpinner, i8.MatTooltip, i9.CdkDropList, i9.CdkDropListGroup, i9.CdkDrag, i9.CdkDragPreview, i4.SlicePipe, i4.DatePipe],\n        styles: [\".kanban-board-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;background:#f8fafc}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem 1.5rem;background:white;border-bottom:1px solid #e2e8f0;box-shadow:0 1px 3px #0000001a}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]{display:flex;gap:2rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.25rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#1e293b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.875rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.overdue[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.overdue[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{color:#ef4444}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.blocked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.blocked[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{color:#f59e0b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%]{display:flex;gap:.5rem}.kanban-board-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;flex:1;gap:1rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]{display:flex;gap:1rem;padding:1.5rem;overflow-x:auto;flex:1}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]{min-width:300px;max-width:350px;background:white;border-radius:8px;box-shadow:0 1px 3px #0000001a;border-top:4px solid;display:flex;flex-direction:column;max-height:calc(100vh - 200px)}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]{padding:1rem;color:#fff;display:flex;justify-content:space-between;align-items:center;border-radius:4px 4px 0 0}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-weight:600}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%]   .task-count[_ngcontent-%COMP%]{opacity:.8;font-size:.875rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .add-task-btn[_ngcontent-%COMP%]{color:#fff}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .add-task-btn[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.1)}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]{flex:1;padding:1rem;overflow-y:auto;min-height:200px}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]{background:white;border:1px solid #e2e8f0;border-radius:6px;margin-bottom:.75rem;cursor:pointer;transition:all .2s ease;position:relative;overflow:hidden}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #00000026;transform:translateY(-1px)}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%]{position:absolute;left:0;top:0;bottom:0;width:4px}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]{padding:1rem 1rem 1rem 1.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{font-size:1rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%]   .task-id[_ngcontent-%COMP%]{font-size:.75rem;color:#94a3b8;font-family:monospace}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]{display:flex;gap:.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   .overdue-icon[_ngcontent-%COMP%]{color:#ef4444;font-size:1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   .blocked-icon[_ngcontent-%COMP%]{color:#f59e0b;font-size:1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-title[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:.875rem;font-weight:600;color:#1e293b;line-height:1.4}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]{margin:0 0 .75rem;font-size:.75rem;color:#64748b;line-height:1.4}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.25rem;margin-bottom:.75rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%]{padding:.125rem .5rem;border-radius:12px;font-size:.625rem;font-weight:500;color:#fff}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .more-labels[_ngcontent-%COMP%]{font-size:.625rem;color:#64748b;padding:.125rem .25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;background:#e2e8f0;display:flex;align-items:center;justify-content:center;font-size:.625rem;font-weight:600;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .more-assignees[_ngcontent-%COMP%]{font-size:.625rem;color:#64748b;margin-left:.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.125rem;font-size:.625rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.875rem;width:.875rem;height:.875rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] > span.overdue[_ngcontent-%COMP%]{color:#ef4444}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .ai-indicator[_ngcontent-%COMP%]{position:absolute;top:.5rem;right:.5rem;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;border-radius:12px;padding:.125rem .375rem;display:flex;align-items:center;gap:.125rem;font-size:.625rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .ai-indicator[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.75rem;width:.75rem;height:.75rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]{background:white;border:1px solid #e2e8f0;border-radius:6px;box-shadow:0 8px 25px #00000026}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%]{padding:1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:.875rem;font-weight:600}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.75rem;color:#64748b}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;text-align:center;color:#94a3b8}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem;margin-bottom:1rem;opacity:.5}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:.875rem}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:.4;background:#f1f5f9;border:2px dashed #cbd5e1}.cdk-drop-list-dragging[_ngcontent-%COMP%]   .cdk-drag[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}@media (max-width: 768px){.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]{padding:.75rem 1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]{gap:1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.25rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{display:none}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]{padding:1rem}.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]{min-width:280px}}\"]\n      });\n    }\n  }\n  return KanbanBoardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}