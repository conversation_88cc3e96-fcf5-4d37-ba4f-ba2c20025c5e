const { Team } = require('../models/Team');
const User = require('../models/User');
const TeamInvitation = require('../models/TeamInvitation');
const JoinRequest = require('../models/JoinRequest');
const TeamHistory = require('../models/TeamHistory');
const TeamFavorite = require('../models/TeamFavorite');
const { logger } = require('../utils/logger');
const { sendEmail } = require('../utils/sendEmail');
const teamInvitationController = require('./teamInvitationController');
const teamAdvancedController = require('./teamAdvancedController');
const teamStatsController = require('./teamStatsController');

// Créer une nouvelle équipe
exports.createTeam = async (req, res) => {
  try {
    const { name, description, maxMembers, tags, isPublic } = req.body;
    const adminId = req.user.id;

    // Vérifier si l'équipe existe déjà
    const existingTeam = await Team.findOne({ name });
    if (existingTeam) {
      return res.status(400).json({
        message: "Une équipe avec ce nom existe déjà."
      });
    }

    // Créer une nouvelle équipe
    const newTeam = new Team({
      name,
      description,
      admin: adminId,
      members: [adminId], // L'admin est automatiquement membre
      maxMembers: maxMembers || 10,
      tags: tags || [],
      isPublic: isPublic !== undefined ? isPublic : true
    });

    await newTeam.save();

    // Populer les données pour la réponse
    await newTeam.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Équipe créée: ${newTeam.name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Équipe créée avec succès",
      team: newTeam
    });
  } catch (error) {
    logger.error('Erreur création équipe:', error);
    res.status(500).json({
      message: "Erreur lors de la création de l'équipe",
      error: error.message
    });
  }
};

// Obtenir toutes les équipes
exports.getAllTeams = async (req, res) => {
  try {
    const { status, isPublic, search, page = 1, limit = 10 } = req.query;

    // Construire le filtre
    const filter = {};
    if (status) filter.status = status;
    if (isPublic !== undefined) filter.isPublic = isPublic === 'true';
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const teams = await Team.find(filter)
      .populate('admin', 'fullName email profileImage')
      .populate('members', 'fullName email profileImage')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Team.countDocuments(filter);

    res.json({
      teams,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: teams.length,
        totalItems: total
      }
    });
  } catch (error) {
    logger.error('Erreur récupération équipes:', error);
    res.status(500).json({
      message: "Erreur lors de la récupération des équipes",
      error: error.message
    });
  }
};

// Obtenir une équipe par ID
exports.getTeamById = async (req, res) => {
  try {
    const team = await Team.findById(req.params.id)
      .populate('admin', 'fullName email profileImage role')
      .populate('members', 'fullName email profileImage role')
      .populate('project', 'titre description dateLimite');

    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    res.json(team);
  } catch (error) {
    logger.error('Erreur récupération équipe:', error);
    res.status(500).json({
      message: "Erreur lors de la récupération de l'équipe",
      error: error.message
    });
  }
};

// Mettre à jour une équipe
exports.updateTeam = async (req, res) => {
  try {
    const { name, description, maxMembers, tags, isPublic, status } = req.body;
    const teamId = req.params.id;
    const userId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions (admin de l'équipe ou admin système)
    if (!team.isAdmin(userId) && req.user.role !== 'admin') {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour modifier cette équipe"
      });
    }

    // Mettre à jour les champs
    if (name) team.name = name;
    if (description !== undefined) team.description = description;
    if (maxMembers) team.maxMembers = maxMembers;
    if (tags) team.tags = tags;
    if (isPublic !== undefined) team.isPublic = isPublic;
    if (status) team.status = status;

    await team.save();
    await team.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Équipe mise à jour: ${team.name} par ${req.user.fullName}`);

    res.json({
      message: "Équipe mise à jour avec succès",
      team
    });
  } catch (error) {
    logger.error('Erreur mise à jour équipe:', error);
    res.status(500).json({
      message: "Erreur lors de la mise à jour de l'équipe",
      error: error.message
    });
  }
};

// Ajouter un membre à une équipe
exports.addMemberToTeam = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.isAdmin(requesterId) && req.user.role !== 'admin') {
      return res.status(403).json({
        message: "Seul l'administrateur de l'équipe peut ajouter des membres"
      });
    }

    // Vérifier si l'utilisateur existe
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    // Vérifier si l'utilisateur est déjà membre
    if (team.isMember(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur est déjà membre de l'équipe"
      });
    }

    // Vérifier si l'équipe est pleine
    if (team.isFullTeam) {
      return res.status(400).json({
        message: "L'équipe a atteint sa capacité maximale"
      });
    }

    // Ajouter le membre
    team.addMember(userId);
    await team.save();
    await team.populate('members', 'fullName email profileImage');

    logger.info(`Membre ajouté à l'équipe ${team.name}: ${user.fullName}`);

    res.json({
      message: "Membre ajouté avec succès",
      team,
      newMember: user
    });
  } catch (error) {
    logger.error('Erreur ajout membre:', error);
    res.status(500).json({
      message: "Erreur lors de l'ajout du membre",
      error: error.message
    });
  }
};

// Supprimer un membre d'une équipe
exports.removeMemberFromTeam = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions (admin de l'équipe, admin système, ou l'utilisateur lui-même)
    if (!team.isAdmin(requesterId) && req.user.role !== 'admin' && requesterId !== userId) {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour retirer ce membre"
      });
    }

    // Empêcher la suppression de l'admin
    if (team.isAdmin(userId)) {
      return res.status(400).json({
        message: "L'administrateur ne peut pas être retiré de l'équipe"
      });
    }

    // Vérifier si l'utilisateur est membre
    if (!team.isMember(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur n'est pas membre de l'équipe"
      });
    }

    // Retirer le membre
    team.removeMember(userId);
    await team.save();
    await team.populate('members', 'fullName email profileImage');

    logger.info(`Membre retiré de l'équipe ${team.name}: ${userId}`);

    res.json({
      message: "Membre retiré avec succès",
      team
    });
  } catch (error) {
    logger.error('Erreur suppression membre:', error);
    res.status(500).json({
      message: "Erreur lors de la suppression du membre",
      error: error.message
    });
  }
};

// Supprimer une équipe
exports.deleteTeam = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions (admin de l'équipe ou admin système)
    if (!team.isAdmin(userId) && req.user.role !== 'admin') {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour supprimer cette équipe"
      });
    }

    await Team.findByIdAndDelete(teamId);

    logger.info(`Équipe supprimée: ${team.name} par ${req.user.fullName}`);

    res.json({ message: "Équipe supprimée avec succès" });
  } catch (error) {
    logger.error('Erreur suppression équipe:', error);
    res.status(500).json({
      message: "Erreur lors de la suppression de l'équipe",
      error: error.message
    });
  }
};

// Obtenir les équipes d'un utilisateur
exports.getUserTeams = async (req, res) => {
  try {
    const userId = req.params.userId || req.user.id;

    const teams = await Team.find({
      $or: [
        { admin: userId },
        { coAdmins: userId },
        { moderators: userId },
        { members: userId }
      ],
      status: 'active'
    })
    .populate('admin', 'fullName email profileImage')
    .populate('coAdmins', 'fullName email profileImage')
    .populate('moderators', 'fullName email profileImage')
    .populate('members', 'fullName email profileImage')
    .sort({ createdAt: -1 });

    // Ajouter le rôle de l'utilisateur dans chaque équipe
    const teamsWithRoles = teams.map(team => {
      const teamObj = team.toObject();
      teamObj.userRole = team.getUserRole(userId);
      return teamObj;
    });

    res.json({
      teams: teamsWithRoles,
      count: teams.length
    });
  } catch (error) {
    logger.error('Erreur récupération équipes utilisateur:', error);
    res.status(500).json({
      message: "Erreur lors de la récupération des équipes",
      error: error.message
    });
  }
};

// ==================== RÔLES AVANCÉS ====================

// Ajouter un co-administrateur
exports.addCoAdmin = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Seul l'admin principal peut ajouter des co-admins
    if (!team.isAdmin(requesterId)) {
      return res.status(403).json({
        message: "Seul l'administrateur principal peut ajouter des co-administrateurs"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    if (!team.isMember(userId)) {
      return res.status(400).json({
        message: "L'utilisateur doit être membre de l'équipe"
      });
    }

    if (team.isCoAdmin(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur est déjà co-administrateur"
      });
    }

    team.addCoAdmin(userId);
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      requesterId,
      'member_promoted',
      `${user.fullName} a été promu co-administrateur`,
      {
        affectedUser: userId,
        newData: { role: 'co-admin' },
        category: 'member',
        severity: 'medium'
      }
    );

    await team.populate('coAdmins', 'fullName email profileImage');

    logger.info(`Co-admin ajouté à l'équipe ${team.name}: ${user.fullName}`);

    res.json({
      message: "Co-administrateur ajouté avec succès",
      team,
      newCoAdmin: user
    });
  } catch (error) {
    logger.error('Erreur ajout co-admin:', error);
    res.status(500).json({
      message: "Erreur lors de l'ajout du co-administrateur",
      error: error.message
    });
  }
};

// Retirer un co-administrateur
exports.removeCoAdmin = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.isAdmin(requesterId)) {
      return res.status(403).json({
        message: "Seul l'administrateur principal peut retirer des co-administrateurs"
      });
    }

    if (!team.isCoAdmin(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur n'est pas co-administrateur"
      });
    }

    const user = await User.findById(userId);
    team.removeCoAdmin(userId);
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      requesterId,
      'member_demoted',
      `${user.fullName} n'est plus co-administrateur`,
      {
        affectedUser: userId,
        previousData: { role: 'co-admin' },
        newData: { role: 'member' },
        category: 'member'
      }
    );

    logger.info(`Co-admin retiré de l'équipe ${team.name}: ${user.fullName}`);

    res.json({
      message: "Co-administrateur retiré avec succès",
      team
    });
  } catch (error) {
    logger.error('Erreur suppression co-admin:', error);
    res.status(500).json({
      message: "Erreur lors de la suppression du co-administrateur",
      error: error.message
    });
  }
};

// Ajouter un modérateur
exports.addModerator = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Admin ou co-admin peuvent ajouter des modérateurs
    if (!team.hasAdminRights(requesterId)) {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour ajouter des modérateurs"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    if (!team.isMember(userId)) {
      return res.status(400).json({
        message: "L'utilisateur doit être membre de l'équipe"
      });
    }

    if (team.isModerator(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur est déjà modérateur"
      });
    }

    team.addModerator(userId);
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      requesterId,
      'member_promoted',
      `${user.fullName} a été promu modérateur`,
      {
        affectedUser: userId,
        newData: { role: 'moderator' },
        category: 'member'
      }
    );

    await team.populate('moderators', 'fullName email profileImage');

    logger.info(`Modérateur ajouté à l'équipe ${team.name}: ${user.fullName}`);

    res.json({
      message: "Modérateur ajouté avec succès",
      team,
      newModerator: user
    });
  } catch (error) {
    logger.error('Erreur ajout modérateur:', error);
    res.status(500).json({
      message: "Erreur lors de l'ajout du modérateur",
      error: error.message
    });
  }
};

// Retirer un modérateur
exports.removeModerator = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const requesterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (!team.hasAdminRights(requesterId)) {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour retirer des modérateurs"
      });
    }

    if (!team.isModerator(userId)) {
      return res.status(400).json({
        message: "Cet utilisateur n'est pas modérateur"
      });
    }

    const user = await User.findById(userId);
    team.removeModerator(userId);
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      requesterId,
      'member_demoted',
      `${user.fullName} n'est plus modérateur`,
      {
        affectedUser: userId,
        previousData: { role: 'moderator' },
        newData: { role: 'member' },
        category: 'member'
      }
    );

    logger.info(`Modérateur retiré de l'équipe ${team.name}: ${user.fullName}`);

    res.json({
      message: "Modérateur retiré avec succès",
      team
    });
  } catch (error) {
    logger.error('Erreur suppression modérateur:', error);
    res.status(500).json({
      message: "Erreur lors de la suppression du modérateur",
      error: error.message
    });
  }
};

// Transférer l'administration
exports.transferAdmin = async (req, res) => {
  try {
    const { userId } = req.body;
    const teamId = req.params.id;
    const currentAdminId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Seul l'admin actuel peut transférer
    if (!team.isAdmin(currentAdminId)) {
      return res.status(403).json({
        message: "Seul l'administrateur actuel peut transférer l'administration"
      });
    }

    const newAdmin = await User.findById(userId);
    if (!newAdmin) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }

    if (!team.isMember(userId)) {
      return res.status(400).json({
        message: "L'utilisateur doit être membre de l'équipe"
      });
    }

    // Effectuer le transfert
    const previousAdmin = team.admin;
    team.admin = userId;

    // Retirer le nouvel admin des co-admins/modérateurs s'il y était
    team.removeCoAdmin(userId);
    team.removeModerator(userId);

    // Ajouter l'ancien admin comme co-admin
    if (!team.isMember(previousAdmin)) {
      team.addMember(previousAdmin);
    }
    team.addCoAdmin(previousAdmin);

    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      currentAdminId,
      'admin_transferred',
      `Administration transférée à ${newAdmin.fullName}`,
      {
        affectedUser: userId,
        previousData: { admin: previousAdmin },
        newData: { admin: userId },
        category: 'admin',
        severity: 'high'
      }
    );

    await team.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'coAdmins', select: 'fullName email profileImage' },
      { path: 'moderators', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Administration transférée pour l'équipe ${team.name} de ${req.user.fullName} à ${newAdmin.fullName}`);

    res.json({
      message: "Administration transférée avec succès",
      team,
      newAdmin: newAdmin,
      previousAdmin: req.user
    });
  } catch (error) {
    logger.error('Erreur transfert admin:', error);
    res.status(500).json({
      message: "Erreur lors du transfert d'administration",
      error: error.message
    });
  }
};

// ==================== INVITATIONS ====================
// Déléguer aux méthodes du contrôleur d'invitation
exports.sendInvitation = teamInvitationController.sendInvitation;
exports.getTeamInvitations = teamInvitationController.getTeamInvitations;
exports.acceptInvitation = teamInvitationController.acceptInvitation;
exports.declineInvitation = teamInvitationController.declineInvitation;
exports.cancelInvitation = teamInvitationController.cancelInvitation;

// ==================== DEMANDES D'ADHÉSION ====================
// Déléguer aux méthodes du contrôleur avancé
exports.sendJoinRequest = teamAdvancedController.sendJoinRequest;
exports.getJoinRequests = teamAdvancedController.getJoinRequests;
exports.approveJoinRequest = teamAdvancedController.approveJoinRequest;
exports.rejectJoinRequest = teamAdvancedController.rejectJoinRequest;
exports.cancelJoinRequest = teamAdvancedController.cancelJoinRequest;

// ==================== FAVORIS ====================
// Déléguer aux méthodes du contrôleur avancé
exports.addToFavorites = teamAdvancedController.addToFavorites;
exports.removeFromFavorites = teamAdvancedController.removeFromFavorites;
exports.getUserFavorites = teamAdvancedController.getUserFavorites;

// ==================== STATISTIQUES ====================
// Déléguer aux méthodes du contrôleur de statistiques
exports.getTeamStats = teamStatsController.getTeamStats;
exports.getTeamAnalytics = teamStatsController.getTeamAnalytics;
exports.getTeamActivity = teamStatsController.getTeamActivity;

// ==================== CODES D'INVITATION ====================

// Générer un code d'invitation
exports.generateInviteCode = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.hasAdminRights(userId)) {
      return res.status(403).json({
        message: "Vous n'avez pas les permissions pour générer un code d'invitation"
      });
    }

    const inviteCode = team.generateInviteCode();
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId,
      userId,
      'invite_code_generated',
      `Code d'invitation généré`,
      {
        newData: { inviteCode, expiresAt: team.inviteCodeExpiry },
        category: 'invitation'
      }
    );

    logger.info(`Code d'invitation généré pour l'équipe ${team.name} par ${req.user.fullName}`);

    res.json({
      message: "Code d'invitation généré avec succès",
      inviteCode,
      expiresAt: team.inviteCodeExpiry,
      inviteLink: `${process.env.FRONTEND_URL}/teams/join/${inviteCode}`
    });
  } catch (error) {
    logger.error('Erreur génération code invitation:', error);
    res.status(500).json({
      message: "Erreur lors de la génération du code",
      error: error.message
    });
  }
};

// Rejoindre une équipe par code
exports.joinByCode = async (req, res) => {
  try {
    const { code } = req.params;
    const userId = req.user.id;

    const team = await Team.findOne({
      inviteCode: code,
      inviteCodeExpiry: { $gt: new Date() }
    });

    if (!team) {
      return res.status(404).json({
        message: "Code d'invitation invalide ou expiré"
      });
    }

    if (team.isMember(userId)) {
      return res.status(400).json({
        message: "Vous êtes déjà membre de cette équipe"
      });
    }

    if (team.isFullTeam) {
      return res.status(400).json({
        message: "L'équipe a atteint sa capacité maximale"
      });
    }

    // Ajouter l'utilisateur à l'équipe
    team.addMember(userId);
    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      team._id,
      userId,
      'joined_by_code',
      `${req.user.fullName} a rejoint l'équipe via un code d'invitation`,
      {
        affectedUser: userId,
        newData: { inviteCode: code },
        category: 'member'
      }
    );

    await team.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`${req.user.fullName} a rejoint l'équipe ${team.name} via code d'invitation`);

    res.json({
      message: "Vous avez rejoint l'équipe avec succès",
      team
    });
  } catch (error) {
    logger.error('Erreur adhésion par code:', error);
    res.status(500).json({
      message: "Erreur lors de l'adhésion à l'équipe",
      error: error.message
    });
  }
};

// ==================== TEMPLATES ====================

// Obtenir les templates disponibles
exports.getTemplates = async (req, res) => {
  try {
    const { category, search, limit = 20 } = req.query;

    const filter = { isTemplate: true };
    if (category) filter.templateCategory = category;
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    const templates = await Team.find(filter)
      .populate('admin', 'fullName email profileImage')
      .select('name description templateCategory tags maxMembers settings createdAt admin')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    res.json({
      templates,
      count: templates.length
    });
  } catch (error) {
    logger.error('Erreur récupération templates:', error);
    res.status(500).json({
      message: "Erreur lors de la récupération des templates",
      error: error.message
    });
  }
};

// Utiliser un template pour créer une équipe
exports.useTemplate = async (req, res) => {
  try {
    const { templateId } = req.params;
    const { name, description } = req.body;
    const userId = req.user.id;

    const template = await Team.findOne({
      _id: templateId,
      isTemplate: true
    });

    if (!template) {
      return res.status(404).json({ message: "Template non trouvé" });
    }

    // Vérifier que le nom n'existe pas
    const existingTeam = await Team.findOne({ name });
    if (existingTeam) {
      return res.status(400).json({
        message: "Une équipe avec ce nom existe déjà"
      });
    }

    // Créer l'équipe à partir du template
    const newTeam = new Team({
      name,
      description: description || template.description,
      admin: userId,
      members: [userId],
      maxMembers: template.maxMembers,
      tags: [...template.tags],
      isPublic: template.isPublic,
      allowJoinRequests: template.allowJoinRequests,
      requireApproval: template.requireApproval,
      settings: { ...template.settings },
      originalTeam: templateId
    });

    await newTeam.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      newTeam._id,
      userId,
      'created',
      `Équipe créée à partir du template "${template.name}"`,
      {
        newData: { templateId, templateName: template.name },
        category: 'team'
      }
    );

    await newTeam.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Équipe ${name} créée à partir du template ${template.name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Équipe créée avec succès à partir du template",
      team: newTeam,
      template: {
        _id: template._id,
        name: template.name
      }
    });
  } catch (error) {
    logger.error('Erreur utilisation template:', error);
    res.status(500).json({
      message: "Erreur lors de l'utilisation du template",
      error: error.message
    });
  }
};

// ==================== HISTORIQUE ====================

// Obtenir l'historique d'une équipe
exports.getTeamHistory = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { category, action, limit = 50, skip = 0 } = req.query;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.isMember(userId)) {
      return res.status(403).json({
        message: "Vous n'avez pas accès à l'historique de cette équipe"
      });
    }

    const history = await TeamHistory.getTeamHistory(teamId, {
      category,
      action,
      limit: parseInt(limit),
      skip: parseInt(skip)
    });

    res.json({
      teamId: team._id,
      teamName: team.name,
      history,
      count: history.length
    });
  } catch (error) {
    logger.error('Erreur récupération historique:', error);
    res.status(500).json({
      message: "Erreur lors de la récupération de l'historique",
      error: error.message
    });
  }
};

// ==================== TEMPLATES ET DUPLICATION ====================
// Déléguer aux méthodes du contrôleur de statistiques
exports.duplicateTeam = teamStatsController.duplicateTeam;
exports.createTemplate = teamStatsController.createTemplate;