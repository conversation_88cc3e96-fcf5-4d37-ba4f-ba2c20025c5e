import { Config } from './merge-typedefs.cjs';
import { DirectiveDefinitionNode, InterfaceTypeDefinitionNode, InterfaceTypeExtensionNode } from 'graphql';
export declare function mergeInterface(node: InterfaceTypeDefinitionNode | InterfaceTypeExtensionNode, existingNode: InterfaceTypeDefinitionNode | InterfaceTypeExtensionNode, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): InterfaceTypeDefinitionNode | InterfaceTypeExtensionNode;
