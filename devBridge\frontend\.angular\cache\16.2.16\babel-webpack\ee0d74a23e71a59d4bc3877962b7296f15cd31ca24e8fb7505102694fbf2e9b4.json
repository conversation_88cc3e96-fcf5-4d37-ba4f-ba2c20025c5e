{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { toObjMap } from '../jsutils/toObjMap.mjs';\nimport { DirectiveLocation } from '../language/directiveLocation.mjs';\nimport { assertName } from './assertName.mjs';\nimport { argsToArgsConfig, defineArguments, GraphQLNonNull } from './definition.mjs';\nimport { GraphQLBoolean, GraphQLString } from './scalars.mjs';\n/**\n * Test if the given value is a GraphQL directive.\n */\n\nexport function isDirective(directive) {\n  return instanceOf(directive, GraphQLDirective);\n}\nexport function assertDirective(directive) {\n  if (!isDirective(directive)) {\n    throw new Error(`Expected ${inspect(directive)} to be a GraphQL directive.`);\n  }\n  return directive;\n}\n/**\n * Custom extensions\n *\n * @remarks\n * Use a unique identifier name for your extension, for example the name of\n * your library or project. Do not use a shortened identifier as this increases\n * the risk of conflicts. We recommend you add at most one extension field,\n * an object which can contain all the values you need.\n */\n\n/**\n * Directives are used by the GraphQL runtime as a way of modifying execution\n * behavior. Type system creators will usually not create these directly.\n */\nexport class GraphQLDirective {\n  constructor(config) {\n    var _config$isRepeatable, _config$args;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.locations = config.locations;\n    this.isRepeatable = (_config$isRepeatable = config.isRepeatable) !== null && _config$isRepeatable !== void 0 ? _config$isRepeatable : false;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    Array.isArray(config.locations) || devAssert(false, `@${config.name} locations must be an Array.`);\n    const args = (_config$args = config.args) !== null && _config$args !== void 0 ? _config$args : {};\n    isObjectLike(args) && !Array.isArray(args) || devAssert(false, `@${config.name} args must be an object with argument names as keys.`);\n    this.args = defineArguments(args);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLDirective';\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      locations: this.locations,\n      args: argsToArgsConfig(this.args),\n      isRepeatable: this.isRepeatable,\n      extensions: this.extensions,\n      astNode: this.astNode\n    };\n  }\n  toString() {\n    return '@' + this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n\n/**\n * Used to conditionally include fields or fragments.\n */\nexport const GraphQLIncludeDirective = new GraphQLDirective({\n  name: 'include',\n  description: 'Directs the executor to include this field or fragment only when the `if` argument is true.',\n  locations: [DirectiveLocation.FIELD, DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Included when true.'\n    }\n  }\n});\n/**\n * Used to conditionally skip (exclude) fields or fragments.\n */\n\nexport const GraphQLSkipDirective = new GraphQLDirective({\n  name: 'skip',\n  description: 'Directs the executor to skip this field or fragment when the `if` argument is true.',\n  locations: [DirectiveLocation.FIELD, DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],\n  args: {\n    if: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      description: 'Skipped when true.'\n    }\n  }\n});\n/**\n * Constant string used for default reason for a deprecation.\n */\n\nexport const DEFAULT_DEPRECATION_REASON = 'No longer supported';\n/**\n * Used to declare element of a GraphQL schema as deprecated.\n */\n\nexport const GraphQLDeprecatedDirective = new GraphQLDirective({\n  name: 'deprecated',\n  description: 'Marks an element of a GraphQL schema as no longer supported.',\n  locations: [DirectiveLocation.FIELD_DEFINITION, DirectiveLocation.ARGUMENT_DEFINITION, DirectiveLocation.INPUT_FIELD_DEFINITION, DirectiveLocation.ENUM_VALUE],\n  args: {\n    reason: {\n      type: GraphQLString,\n      description: 'Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).',\n      defaultValue: DEFAULT_DEPRECATION_REASON\n    }\n  }\n});\n/**\n * Used to provide a URL for specifying the behavior of custom scalar definitions.\n */\n\nexport const GraphQLSpecifiedByDirective = new GraphQLDirective({\n  name: 'specifiedBy',\n  description: 'Exposes a URL that specifies the behavior of this scalar.',\n  locations: [DirectiveLocation.SCALAR],\n  args: {\n    url: {\n      type: new GraphQLNonNull(GraphQLString),\n      description: 'The URL that specifies the behavior of this scalar.'\n    }\n  }\n});\n/**\n * The full list of specified directives.\n */\n\nexport const specifiedDirectives = Object.freeze([GraphQLIncludeDirective, GraphQLSkipDirective, GraphQLDeprecatedDirective, GraphQLSpecifiedByDirective]);\nexport function isSpecifiedDirective(directive) {\n  return specifiedDirectives.some(({\n    name\n  }) => name === directive.name);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}