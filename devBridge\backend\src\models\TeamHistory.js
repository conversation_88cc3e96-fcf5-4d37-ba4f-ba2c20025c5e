const mongoose = require('mongoose');

// Schéma pour l'historique des modifications d'équipe
const teamHistorySchema = new mongoose.Schema({
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team',
    required: [true, 'L\'équipe est requise']
  },

  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'L\'utilisateur est requis']
  },

  action: {
    type: String,
    enum: [
      'created',
      'updated',
      'deleted',
      'archived',
      'restored',
      'member_added',
      'member_removed',
      'member_promoted',
      'member_demoted',
      'role_changed',
      'settings_changed',
      'invite_sent',
      'invite_accepted',
      'invite_declined',
      'join_request_approved',
      'join_request_rejected',
      'admin_transferred',
      'duplicated',
      'template_created'
    ],
    required: [true, 'L\'action est requise']
  },

  description: {
    type: String,
    required: [true, 'La description est requise'],
    maxlength: [500, 'La description ne peut pas dépasser 500 caractères']
  },

  // Données avant modification (pour rollback)
  previousData: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },

  // Nouvelles données
  newData: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },

  // Utilisateur affecté (pour les actions sur les membres)
  affectedUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  // Métadonnées
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: {
      type: String,
      enum: ['web', 'mobile', 'api', 'system'],
      default: 'web'
    },
    version: {
      type: String,
      default: '1.0'
    }
  },

  // Catégorie pour filtrage
  category: {
    type: String,
    enum: ['team', 'member', 'settings', 'invitation', 'request', 'admin'],
    required: [true, 'La catégorie est requise']
  },

  // Niveau d'importance
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },

  // Tags pour recherche
  tags: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
teamHistorySchema.index({ team: 1, createdAt: -1 });
teamHistorySchema.index({ user: 1, createdAt: -1 });
teamHistorySchema.index({ action: 1, createdAt: -1 });
teamHistorySchema.index({ category: 1, createdAt: -1 });
teamHistorySchema.index({ severity: 1, createdAt: -1 });
teamHistorySchema.index({ affectedUser: 1, createdAt: -1 });
teamHistorySchema.index({ tags: 1 });

// Propriétés virtuelles
teamHistorySchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'À l\'instant';
  if (minutes < 60) return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
  if (hours < 24) return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
  return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
});

teamHistorySchema.virtual('isRecent').get(function() {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.createdAt > oneDayAgo;
});

// Méthodes statiques
teamHistorySchema.statics.logAction = function(teamId, userId, action, description, options = {}) {
  const historyEntry = new this({
    team: teamId,
    user: userId,
    action: action,
    description: description,
    previousData: options.previousData || null,
    newData: options.newData || null,
    affectedUser: options.affectedUser || null,
    category: options.category || this.getCategoryFromAction(action),
    severity: options.severity || 'medium',
    tags: options.tags || [],
    metadata: {
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      source: options.source || 'web',
      version: options.version || '1.0'
    }
  });

  return historyEntry.save();
};

teamHistorySchema.statics.getCategoryFromAction = function(action) {
  const categoryMap = {
    'created': 'team',
    'updated': 'team',
    'deleted': 'team',
    'archived': 'team',
    'restored': 'team',
    'member_added': 'member',
    'member_removed': 'member',
    'member_promoted': 'member',
    'member_demoted': 'member',
    'role_changed': 'member',
    'settings_changed': 'settings',
    'invite_sent': 'invitation',
    'invite_accepted': 'invitation',
    'invite_declined': 'invitation',
    'join_request_approved': 'request',
    'join_request_rejected': 'request',
    'admin_transferred': 'admin',
    'duplicated': 'team',
    'template_created': 'team'
  };

  return categoryMap[action] || 'team';
};

teamHistorySchema.statics.getTeamHistory = function(teamId, options = {}) {
  const query = { team: teamId };
  
  if (options.category) query.category = options.category;
  if (options.action) query.action = options.action;
  if (options.user) query.user = options.user;
  if (options.severity) query.severity = options.severity;
  
  const limit = options.limit || 50;
  const skip = options.skip || 0;

  return this.find(query)
    .populate('user', 'fullName email profileImage')
    .populate('affectedUser', 'fullName email profileImage')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

teamHistorySchema.statics.getUserActivity = function(userId, options = {}) {
  const query = { user: userId };
  
  if (options.team) query.team = options.team;
  if (options.category) query.category = options.category;
  
  const limit = options.limit || 20;

  return this.find(query)
    .populate('team', 'name')
    .populate('affectedUser', 'fullName email')
    .sort({ createdAt: -1 })
    .limit(limit);
};

teamHistorySchema.statics.getActivityStats = function(teamId, days = 30) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        team: mongoose.Types.ObjectId(teamId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          category: '$category',
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.date': 1 }
    }
  ]);
};

teamHistorySchema.statics.cleanupOldHistory = function(daysToKeep = 365) {
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
  
  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    severity: { $in: ['low', 'medium'] } // Garder les événements critiques plus longtemps
  });
};

// TTL Index pour suppression automatique des anciens logs (2 ans)
teamHistorySchema.index({ createdAt: 1 }, { expireAfterSeconds: 2 * 365 * 24 * 60 * 60 });

const TeamHistory = mongoose.model('TeamHistory', teamHistorySchema);

module.exports = TeamHistory;
