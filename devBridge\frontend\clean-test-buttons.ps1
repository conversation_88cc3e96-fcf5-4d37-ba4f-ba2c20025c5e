# Script pour nettoyer les boutons de test
Write-Host "🧹 Nettoyage des Boutons de Test" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

$filePath = "src/app/views/front/equipes/equipe-list/equipe-list.component.html"

if (-not (Test-Path $filePath)) {
    Write-Host "❌ Fichier non trouvé: $filePath" -ForegroundColor Red
    exit 1
}

Write-Host "🔄 Suppression des boutons de test..." -ForegroundColor Yellow

# Lire le contenu du fichier
$content = Get-Content $filePath -Raw

# Supprimer la section des boutons de test
$content = $content -replace '(?s)    <!-- BOUTONS TEST TASKS - TRÈS VISIBLES -->.*?    </div>\s*\n\s*\n', ''

# Supprimer la section debug
$content = $content -replace '(?s)    <!-- Debug: Nombre d''équipes -->.*?    </div>\s*\n\s*\n', ''

# Écrire le contenu nettoyé
$content | Set-Content $filePath -NoNewline

Write-Host "✅ Boutons de test supprimés!" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Les boutons Tasks normaux sont toujours présents:" -ForegroundColor Cyan
Write-Host "• Dans chaque carte d'équipe (si vous avez des équipes)" -ForegroundColor White
Write-Host "• Bouton vert 'GÉRER LES TÂCHES' avec gradient et animations" -ForegroundColor White
Write-Host "• Badge rouge avec le nombre de tâches" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Rafraîchissez votre navigateur pour voir les changements" -ForegroundColor Gray
Write-Host "📍 URL: http://localhost:4200/equipes" -ForegroundColor Blue
