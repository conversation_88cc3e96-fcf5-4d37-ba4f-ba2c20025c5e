import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { TaskService } from '../../../services/task.service';
import { EquipeService } from '../../../services/equipe.service';
import { AuthuserService } from '../../../services/authuser.service';
import {
  Task,
  KanbanBoard,
  KanbanFilters,
  TaskStatistics
} from '../../../models/task.model';
import { Equipe } from '../../../models/equipe.model';

@Component({
  selector: 'app-tasks',
  templateUrl: './tasks.component.html',
  styleUrls: ['./tasks.component.scss']
})
export class TasksComponent implements OnInit, On<PERSON><PERSON>roy {
  private destroy$ = new Subject<void>();

  teamId!: string;
  team: Equipe | null = null;
  currentUser: any;

  // Vue actuelle
  currentView: 'kanban' | 'list' | 'calendar' = 'kanban';

  // Données Kanban
  kanbanData: KanbanBoard | null = null;
  kanbanFilters: KanbanFilters = {};

  // Statistiques
  taskStats: TaskStatistics | null = null;

  // États
  loading = false;
  error: string | null = null;

  // Tâche sélectionnée
  selectedTask: Task | null = null;
  showTaskDetails = false;
  showAIAssistant = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private taskService: TaskService,
    private equipeService: EquipeService,
    private authService: AuthuserService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.route.params.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.teamId = params['teamId'];
      if (this.teamId) {
        this.loadTeamData();
        this.loadKanbanBoard();
        this.loadTaskStatistics();
      } else {
        this.error = 'ID d\'équipe manquant';
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Charger les données de l'équipe
  loadTeamData(): void {
    this.equipeService.getEquipeById(this.teamId).subscribe({
      next: (team) => {
        this.team = team;
        // Vérifier les permissions
        if (!this.hasTeamAccess()) {
          this.error = 'Vous n\'avez pas accès aux tâches de cette équipe';
          return;
        }
      },
      error: (error) => {
        console.error('Erreur chargement équipe:', error);
        this.error = 'Équipe non trouvée';
      }
    });
  }

  // Charger le tableau Kanban
  loadKanbanBoard(): void {
    this.loading = true;
    this.error = null;

    this.taskService.getKanbanBoard(this.teamId, this.kanbanFilters).subscribe({
      next: (data) => {
        this.kanbanData = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur chargement Kanban:', error);
        this.error = 'Erreur lors du chargement des tâches';
        this.loading = false;
        this.snackBar.open('Erreur lors du chargement des tâches', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Charger les statistiques
  loadTaskStatistics(): void {
    this.taskService.getTaskStatistics(this.teamId).subscribe({
      next: (stats) => {
        this.taskStats = stats;
      },
      error: (error) => {
        console.error('Erreur chargement statistiques:', error);
      }
    });
  }

  // Vérifier l'accès à l'équipe
  hasTeamAccess(): boolean {
    if (!this.team || !this.currentUser) return false;

    // Admin système a accès à tout
    if (this.currentUser.role === 'admin') return true;

    // Vérifier si l'utilisateur est membre de l'équipe
    return this.equipeService.isTeamMember(this.team, this.currentUser.id);
  }

  // Changer de vue
  switchView(view: 'kanban' | 'list' | 'calendar'): void {
    this.currentView = view;

    if (view === 'kanban') {
      this.loadKanbanBoard();
    }
    // Ajouter d'autres vues plus tard
  }

  // Gérer la sélection d'une tâche
  onTaskSelected(task: Task): void {
    this.selectedTask = task;
    this.showTaskDetails = true;
  }

  // Gérer la création d'une tâche
  onTaskCreated(task: Task): void {
    this.snackBar.open('Tâche créée avec succès', 'Fermer', { duration: 2000 });
    this.loadKanbanBoard();
    this.loadTaskStatistics();
  }

  // Gérer la mise à jour d'une tâche
  onTaskUpdated(task: Task): void {
    this.selectedTask = task;
    this.loadKanbanBoard();
    this.loadTaskStatistics();
  }

  // Appliquer des filtres
  applyFilters(filters: KanbanFilters): void {
    this.kanbanFilters = { ...filters };
    this.loadKanbanBoard();
  }

  // Réinitialiser les filtres
  resetFilters(): void {
    this.kanbanFilters = {};
    this.loadKanbanBoard();
  }

  // Actualiser les données
  refresh(): void {
    this.loadKanbanBoard();
    this.loadTaskStatistics();
  }

  // Ouvrir l'assistant IA
  openAIAssistant(): void {
    if (this.selectedTask) {
      this.showAIAssistant = true;
    } else {
      this.snackBar.open('Veuillez sélectionner une tâche', 'Fermer', { duration: 2000 });
    }
  }

  // Fermer les panneaux
  closeTaskDetails(): void {
    this.showTaskDetails = false;
    this.selectedTask = null;
  }

  closeAIAssistant(): void {
    this.showAIAssistant = false;
  }

  // Naviguer vers l'équipe
  navigateToTeam(): void {
    this.router.navigate(['/equipes/detail', this.teamId]);
  }

  // Obtenir le nom de l'équipe
  getTeamName(): string {
    return this.team?.name || 'Équipe';
  }

  // Vérifier si l'utilisateur peut créer des tâches
  canCreateTasks(): boolean {
    if (!this.team || !this.currentUser) return false;

    // Admin système peut tout faire
    if (this.currentUser.role === 'admin') return true;

    // Membres de l'équipe peuvent créer des tâches
    return this.hasTeamAccess();
  }

  // Vérifier si l'utilisateur peut modifier des tâches
  canEditTasks(): boolean {
    return this.canCreateTasks();
  }

  // Obtenir les statistiques rapides
  getQuickStats(): any {
    if (!this.kanbanData) return null;

    return {
      total: this.kanbanData.stats.total,
      todo: this.kanbanData.stats.byStatus['todo'] || 0,
      inProgress: this.kanbanData.stats.byStatus['in-progress'] || 0,
      done: this.kanbanData.stats.byStatus['done'] || 0,
      overdue: this.kanbanData.stats.overdue,
      blocked: this.kanbanData.stats.blocked
    };
  }

  // Obtenir la couleur du statut de l'équipe
  getTeamStatusColor(): string {
    if (!this.team) return '#6b7280';

    switch (this.team.status) {
      case 'active': return '#10b981';
      case 'inactive': return '#f59e0b';
      case 'archived': return '#6b7280';
      default: return '#6b7280';
    }
  }

  // Helper pour obtenir le nom d'un assigné
  getAssigneeName(assignee: string | any): string {
    if (typeof assignee === 'string') {
      return assignee;
    }
    return assignee?.fullName || assignee?.username || assignee?.email || 'Utilisateur';
  }

  // Helper pour obtenir l'image de profil d'un assigné
  getAssigneeImage(assignee: string | any): string | null {
    if (typeof assignee === 'string') {
      return null;
    }
    return assignee?.profileImage || null;
  }

  // Helper pour vérifier si un assigné a une image
  hasAssigneeImage(assignee: string | any): boolean {
    return typeof assignee !== 'string' && !!assignee?.profileImage;
  }

  // Helper pour obtenir les initiales d'un assigné
  getAssigneeInitials(assignee: string | any): string {
    const name = this.getAssigneeName(assignee);
    return name.charAt(0).toUpperCase();
  }
}
