import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TaskService } from '../../services/task.service';
import { 
  Task, 
  AISuggestion,
  AIAnalysisResponse,
  GenerateSubtasksResponse,
  EstimateEffortResponse,
  GenerateDescriptionResponse
} from '../../models/task.model';

@Component({
  selector: 'app-task-ai-assistant',
  templateUrl: './task-ai-assistant.component.html',
  styleUrls: ['./task-ai-assistant.component.scss']
})
export class TaskAiAssistantComponent implements OnInit {
  @Input() task!: Task;
  @Output() taskUpdated = new EventEmitter<Task>();
  @Output() subtasksGenerated = new EventEmitter<Task[]>();

  loading = {
    analyze: false,
    subtasks: false,
    estimate: false,
    description: false,
    applySuggestion: false
  };

  suggestions: AISuggestion[] = [];
  showSuggestions = true;

  constructor(
    private taskService: TaskService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadSuggestions();
  }

  // Charger les suggestions existantes
  loadSuggestions(): void {
    if (this.task.aiSuggestions?.suggestions) {
      this.suggestions = this.task.aiSuggestions.suggestions;
    }
  }

  // Analyser la tâche avec l'IA
  analyzeTask(): void {
    if (!this.task._id) return;

    this.loading.analyze = true;

    this.taskService.analyzeTaskWithAI(this.task._id).subscribe({
      next: (response: AIAnalysisResponse) => {
        this.suggestions = response.suggestions;
        this.task = response.task;
        this.taskUpdated.emit(this.task);
        this.loading.analyze = false;
        
        this.snackBar.open(
          `Analyse terminée - ${response.suggestions.length} suggestion(s) trouvée(s)`, 
          'Fermer', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Erreur analyse IA:', error);
        this.loading.analyze = false;
        this.snackBar.open('Erreur lors de l\'analyse IA', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Générer des sous-tâches avec l'IA
  generateSubtasks(): void {
    if (!this.task._id) return;

    this.loading.subtasks = true;

    this.taskService.generateSubtasksWithAI(this.task._id).subscribe({
      next: (response: GenerateSubtasksResponse) => {
        this.task = response.task;
        this.taskUpdated.emit(this.task);
        this.subtasksGenerated.emit(response.subtasks);
        this.loading.subtasks = false;
        
        this.snackBar.open(
          `${response.subtasks.length} sous-tâche(s) générée(s) avec succès`, 
          'Fermer', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Erreur génération sous-tâches:', error);
        this.loading.subtasks = false;
        this.snackBar.open('Erreur lors de la génération des sous-tâches', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Estimer l'effort avec l'IA
  estimateEffort(): void {
    if (!this.task._id) return;

    this.loading.estimate = true;

    this.taskService.estimateEffortWithAI(this.task._id).subscribe({
      next: (response: EstimateEffortResponse) => {
        this.task = response.task;
        this.taskUpdated.emit(this.task);
        this.loading.estimate = false;
        
        this.snackBar.open(
          `Effort estimé: ${response.estimatedHours}h`, 
          'Fermer', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Erreur estimation effort:', error);
        this.loading.estimate = false;
        this.snackBar.open('Erreur lors de l\'estimation d\'effort', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Générer une description avec l'IA
  generateDescription(): void {
    if (!this.task._id) return;

    this.loading.description = true;

    this.taskService.generateDescriptionWithAI(this.task._id).subscribe({
      next: (response: GenerateDescriptionResponse) => {
        this.task = response.task;
        this.taskUpdated.emit(this.task);
        this.loading.description = false;
        
        this.snackBar.open('Description générée avec succès', 'Fermer', { duration: 3000 });
      },
      error: (error) => {
        console.error('Erreur génération description:', error);
        this.loading.description = false;
        this.snackBar.open('Erreur lors de la génération de description', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Appliquer une suggestion IA
  applySuggestion(suggestionIndex: number): void {
    if (!this.task._id) return;

    this.loading.applySuggestion = true;

    this.taskService.applySuggestion(this.task._id, suggestionIndex).subscribe({
      next: (response) => {
        this.task = response.task;
        this.suggestions[suggestionIndex] = response.suggestion;
        this.taskUpdated.emit(this.task);
        this.loading.applySuggestion = false;
        
        this.snackBar.open('Suggestion appliquée avec succès', 'Fermer', { duration: 3000 });
      },
      error: (error) => {
        console.error('Erreur application suggestion:', error);
        this.loading.applySuggestion = false;
        this.snackBar.open('Erreur lors de l\'application de la suggestion', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Obtenir l'icône pour un type de suggestion
  getSuggestionIcon(type: string): string {
    const icons = {
      'optimization': 'tune',
      'assignment': 'person_add',
      'deadline': 'schedule',
      'priority': 'flag',
      'breakdown': 'list'
    };
    return icons[type as keyof typeof icons] || 'lightbulb';
  }

  // Obtenir la couleur pour un niveau de confiance
  getConfidenceColor(confidence: number): string {
    if (confidence >= 0.8) return '#10b981'; // Vert
    if (confidence >= 0.6) return '#f59e0b'; // Orange
    return '#ef4444'; // Rouge
  }

  // Obtenir le texte pour un niveau de confiance
  getConfidenceText(confidence: number): string {
    if (confidence >= 0.8) return 'Haute confiance';
    if (confidence >= 0.6) return 'Confiance moyenne';
    return 'Faible confiance';
  }

  // Vérifier si une suggestion peut être appliquée automatiquement
  canApplySuggestion(suggestion: AISuggestion): boolean {
    return !suggestion.applied && ['priority', 'deadline'].includes(suggestion.type);
  }

  // Basculer l'affichage des suggestions
  toggleSuggestions(): void {
    this.showSuggestions = !this.showSuggestions;
  }

  // Vérifier si l'IA est activée pour cette tâche
  isAIEnabled(): boolean {
    return this.task.aiSuggestions?.enabled !== false;
  }

  // Vérifier si la tâche a été générée automatiquement
  isAutoGenerated(field: string): boolean {
    return this.task.aiSuggestions?.autoGenerated?.[field as keyof typeof this.task.aiSuggestions.autoGenerated] || false;
  }

  // Obtenir le temps depuis la dernière analyse
  getLastAnalysisTime(): string {
    if (!this.task.aiSuggestions?.lastAnalysis) return 'Jamais';
    
    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);
    const now = new Date();
    const diffMs = now.getTime() - lastAnalysis.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) return `Il y a ${diffDays} jour(s)`;
    if (diffHours > 0) return `Il y a ${diffHours} heure(s)`;
    return 'Récemment';
  }

  // Vérifier si une nouvelle analyse est recommandée
  shouldReanalyze(): boolean {
    if (!this.task.aiSuggestions?.lastAnalysis) return true;
    
    const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);
    const now = new Date();
    const diffHours = (now.getTime() - lastAnalysis.getTime()) / (1000 * 60 * 60);
    
    return diffHours > 24; // Recommander une nouvelle analyse après 24h
  }

  // Obtenir le nombre de suggestions non appliquées
  getUnappliedSuggestionsCount(): number {
    return this.suggestions.filter(s => !s.applied).length;
  }

  // Obtenir le score de confiance moyen
  getAverageConfidence(): number {
    if (this.suggestions.length === 0) return 0;
    const total = this.suggestions.reduce((sum, s) => sum + s.confidence, 0);
    return total / this.suggestions.length;
  }
}
