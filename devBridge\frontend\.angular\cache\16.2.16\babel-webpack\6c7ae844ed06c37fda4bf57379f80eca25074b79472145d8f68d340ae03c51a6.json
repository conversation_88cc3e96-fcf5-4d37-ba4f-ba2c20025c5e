{"ast": null, "code": "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n  return function (message) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (typeof message === \"number\") {\n      var arg0 = message;\n      message = getHandledErrorMsg(arg0);\n      if (!message) {\n        message = getFallbackErrorMsg(arg0, args);\n        args = [];\n      }\n    }\n    fn.apply(void 0, [message].concat(args));\n  };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  if (!condition) {\n    originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n  }\n}, {\n  debug: wrap(originalInvariant.debug),\n  log: wrap(originalInvariant.log),\n  warn: wrap(originalInvariant.warn),\n  error: wrap(originalInvariant.error)\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n  var optionalParams = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    optionalParams[_i - 1] = arguments[_i];\n  }\n  return new InvariantError(getHandledErrorMsg(message, optionalParams) || getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n  if (typeof arg == \"string\") {\n    return arg;\n  }\n  try {\n    return stringifyForDisplay(arg, 2).slice(0, 1000);\n  } catch (_a) {\n    return \"<non-serializable>\";\n  }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n  if (messageArgs === void 0) {\n    messageArgs = [];\n  }\n  if (!message) return;\n  return global[ApolloErrorMessageHandler] && global[ApolloErrorMessageHandler](message, messageArgs.map(stringify));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n  if (messageArgs === void 0) {\n    messageArgs = [];\n  }\n  if (!message) return;\n  return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n    version: version,\n    message: message,\n    args: messageArgs.map(stringify)\n  })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler };\n//# sourceMappingURL=invariantWrappers.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}