{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Known fragment names\n *\n * A GraphQL document is only valid if all `...Fragment` fragment spreads refer\n * to fragments defined in the same document.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spread-target-defined\n */\nexport function KnownFragmentNamesRule(context) {\n  return {\n    FragmentSpread(node) {\n      const fragmentName = node.name.value;\n      const fragment = context.getFragment(fragmentName);\n      if (!fragment) {\n        context.reportError(new GraphQLError(`Unknown fragment \"${fragmentName}\".`, {\n          nodes: node.name\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "KnownFragmentNamesRule", "context", "FragmentSpread", "node", "fragmentName", "name", "value", "fragment", "getFragment", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/KnownFragmentNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Known fragment names\n *\n * A GraphQL document is only valid if all `...Fragment` fragment spreads refer\n * to fragments defined in the same document.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spread-target-defined\n */\nexport function KnownFragmentNamesRule(context) {\n  return {\n    FragmentSpread(node) {\n      const fragmentName = node.name.value;\n      const fragment = context.getFragment(fragmentName);\n\n      if (!fragment) {\n        context.reportError(\n          new GraphQLError(`Unknown fragment \"${fragmentName}\".`, {\n            nodes: node.name,\n          }),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EAC9C,OAAO;IACLC,cAAcA,CAACC,IAAI,EAAE;MACnB,MAAMC,YAAY,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;MACpC,MAAMC,QAAQ,GAAGN,OAAO,CAACO,WAAW,CAACJ,YAAY,CAAC;MAElD,IAAI,CAACG,QAAQ,EAAE;QACbN,OAAO,CAACQ,WAAW,CACjB,IAAIV,YAAY,CAAE,qBAAoBK,YAAa,IAAG,EAAE;UACtDM,KAAK,EAAEP,IAAI,CAACE;QACd,CAAC,CACH,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}