{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isTypeDefinitionNode } from '../../language/predicates.mjs';\nimport { isEnumType, isInputObjectType, isInterfaceType, isObjectType, isScalarType, isUnionType } from '../../type/definition.mjs';\n\n/**\n * Possible type extension\n *\n * A type extension is only valid if the type is defined and has the same kind.\n */\nexport function PossibleTypeExtensionsRule(context) {\n  const schema = context.getSchema();\n  const definedTypes = Object.create(null);\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = def;\n    }\n  }\n  return {\n    ScalarTypeExtension: checkExtension,\n    ObjectTypeExtension: checkExtension,\n    InterfaceTypeExtension: checkExtension,\n    UnionTypeExtension: checkExtension,\n    EnumTypeExtension: checkExtension,\n    InputObjectTypeExtension: checkExtension\n  };\n  function checkExtension(node) {\n    const typeName = node.name.value;\n    const defNode = definedTypes[typeName];\n    const existingType = schema === null || schema === void 0 ? void 0 : schema.getType(typeName);\n    let expectedKind;\n    if (defNode) {\n      expectedKind = defKindToExtKind[defNode.kind];\n    } else if (existingType) {\n      expectedKind = typeToExtKind(existingType);\n    }\n    if (expectedKind) {\n      if (expectedKind !== node.kind) {\n        const kindStr = extensionKindToTypeName(node.kind);\n        context.reportError(new GraphQLError(`Cannot extend non-${kindStr} type \"${typeName}\".`, {\n          nodes: defNode ? [defNode, node] : node\n        }));\n      }\n    } else {\n      const allTypeNames = Object.keys({\n        ...definedTypes,\n        ...(schema === null || schema === void 0 ? void 0 : schema.getTypeMap())\n      });\n      const suggestedTypes = suggestionList(typeName, allTypeNames);\n      context.reportError(new GraphQLError(`Cannot extend type \"${typeName}\" because it is not defined.` + didYouMean(suggestedTypes), {\n        nodes: node.name\n      }));\n    }\n  }\n}\nconst defKindToExtKind = {\n  [Kind.SCALAR_TYPE_DEFINITION]: Kind.SCALAR_TYPE_EXTENSION,\n  [Kind.OBJECT_TYPE_DEFINITION]: Kind.OBJECT_TYPE_EXTENSION,\n  [Kind.INTERFACE_TYPE_DEFINITION]: Kind.INTERFACE_TYPE_EXTENSION,\n  [Kind.UNION_TYPE_DEFINITION]: Kind.UNION_TYPE_EXTENSION,\n  [Kind.ENUM_TYPE_DEFINITION]: Kind.ENUM_TYPE_EXTENSION,\n  [Kind.INPUT_OBJECT_TYPE_DEFINITION]: Kind.INPUT_OBJECT_TYPE_EXTENSION\n};\nfunction typeToExtKind(type) {\n  if (isScalarType(type)) {\n    return Kind.SCALAR_TYPE_EXTENSION;\n  }\n  if (isObjectType(type)) {\n    return Kind.OBJECT_TYPE_EXTENSION;\n  }\n  if (isInterfaceType(type)) {\n    return Kind.INTERFACE_TYPE_EXTENSION;\n  }\n  if (isUnionType(type)) {\n    return Kind.UNION_TYPE_EXTENSION;\n  }\n  if (isEnumType(type)) {\n    return Kind.ENUM_TYPE_EXTENSION;\n  }\n  if (isInputObjectType(type)) {\n    return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable. All possible types have been considered\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\nfunction extensionKindToTypeName(kind) {\n  switch (kind) {\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return 'scalar';\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return 'object';\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return 'interface';\n    case Kind.UNION_TYPE_EXTENSION:\n      return 'union';\n    case Kind.ENUM_TYPE_EXTENSION:\n      return 'enum';\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return 'input object';\n    // Not reachable. All possible types have been considered\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(kind));\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "inspect", "invariant", "suggestionList", "GraphQLError", "Kind", "isTypeDefinitionNode", "isEnumType", "isInputObjectType", "isInterfaceType", "isObjectType", "isScalarType", "isUnionType", "PossibleTypeExtensionsRule", "context", "schema", "getSchema", "definedTypes", "Object", "create", "def", "getDocument", "definitions", "name", "value", "ScalarTypeExtension", "checkExtension", "ObjectTypeExtension", "InterfaceTypeExtension", "UnionTypeExtension", "EnumTypeExtension", "InputObjectTypeExtension", "node", "typeName", "defNode", "existingType", "getType", "expectedKind", "defKindToExtKind", "kind", "typeToExtKind", "kindStr", "extensionKindToTypeName", "reportError", "nodes", "allTypeNames", "keys", "getTypeMap", "suggestedTypes", "SCALAR_TYPE_DEFINITION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_DEFINITION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_DEFINITION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_DEFINITION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_DEFINITION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_DEFINITION", "INPUT_OBJECT_TYPE_EXTENSION", "type"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/PossibleTypeExtensionsRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isTypeDefinitionNode } from '../../language/predicates.mjs';\nimport {\n  isEnumType,\n  isInputObjectType,\n  isInterfaceType,\n  isObjectType,\n  isScalarType,\n  isUnionType,\n} from '../../type/definition.mjs';\n\n/**\n * Possible type extension\n *\n * A type extension is only valid if the type is defined and has the same kind.\n */\nexport function PossibleTypeExtensionsRule(context) {\n  const schema = context.getSchema();\n  const definedTypes = Object.create(null);\n\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = def;\n    }\n  }\n\n  return {\n    ScalarTypeExtension: checkExtension,\n    ObjectTypeExtension: checkExtension,\n    InterfaceTypeExtension: checkExtension,\n    UnionTypeExtension: checkExtension,\n    EnumTypeExtension: checkExtension,\n    InputObjectTypeExtension: checkExtension,\n  };\n\n  function checkExtension(node) {\n    const typeName = node.name.value;\n    const defNode = definedTypes[typeName];\n    const existingType =\n      schema === null || schema === void 0 ? void 0 : schema.getType(typeName);\n    let expectedKind;\n\n    if (defNode) {\n      expectedKind = defKindToExtKind[defNode.kind];\n    } else if (existingType) {\n      expectedKind = typeToExtKind(existingType);\n    }\n\n    if (expectedKind) {\n      if (expectedKind !== node.kind) {\n        const kindStr = extensionKindToTypeName(node.kind);\n        context.reportError(\n          new GraphQLError(`Cannot extend non-${kindStr} type \"${typeName}\".`, {\n            nodes: defNode ? [defNode, node] : node,\n          }),\n        );\n      }\n    } else {\n      const allTypeNames = Object.keys({\n        ...definedTypes,\n        ...(schema === null || schema === void 0\n          ? void 0\n          : schema.getTypeMap()),\n      });\n      const suggestedTypes = suggestionList(typeName, allTypeNames);\n      context.reportError(\n        new GraphQLError(\n          `Cannot extend type \"${typeName}\" because it is not defined.` +\n            didYouMean(suggestedTypes),\n          {\n            nodes: node.name,\n          },\n        ),\n      );\n    }\n  }\n}\nconst defKindToExtKind = {\n  [Kind.SCALAR_TYPE_DEFINITION]: Kind.SCALAR_TYPE_EXTENSION,\n  [Kind.OBJECT_TYPE_DEFINITION]: Kind.OBJECT_TYPE_EXTENSION,\n  [Kind.INTERFACE_TYPE_DEFINITION]: Kind.INTERFACE_TYPE_EXTENSION,\n  [Kind.UNION_TYPE_DEFINITION]: Kind.UNION_TYPE_EXTENSION,\n  [Kind.ENUM_TYPE_DEFINITION]: Kind.ENUM_TYPE_EXTENSION,\n  [Kind.INPUT_OBJECT_TYPE_DEFINITION]: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n};\n\nfunction typeToExtKind(type) {\n  if (isScalarType(type)) {\n    return Kind.SCALAR_TYPE_EXTENSION;\n  }\n\n  if (isObjectType(type)) {\n    return Kind.OBJECT_TYPE_EXTENSION;\n  }\n\n  if (isInterfaceType(type)) {\n    return Kind.INTERFACE_TYPE_EXTENSION;\n  }\n\n  if (isUnionType(type)) {\n    return Kind.UNION_TYPE_EXTENSION;\n  }\n\n  if (isEnumType(type)) {\n    return Kind.ENUM_TYPE_EXTENSION;\n  }\n\n  if (isInputObjectType(type)) {\n    return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable. All possible types have been considered\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\n\nfunction extensionKindToTypeName(kind) {\n  switch (kind) {\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return 'scalar';\n\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return 'object';\n\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return 'interface';\n\n    case Kind.UNION_TYPE_EXTENSION:\n      return 'union';\n\n    case Kind.ENUM_TYPE_EXTENSION:\n      return 'enum';\n\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return 'input object';\n    // Not reachable. All possible types have been considered\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(kind));\n  }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SACEC,UAAU,EACVC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,WAAW,QACN,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EAClD,MAAMC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAClC,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAExC,KAAK,MAAMC,GAAG,IAAIN,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,WAAW,EAAE;IACnD,IAAIhB,oBAAoB,CAACc,GAAG,CAAC,EAAE;MAC7BH,YAAY,CAACG,GAAG,CAACG,IAAI,CAACC,KAAK,CAAC,GAAGJ,GAAG;IACpC;EACF;EAEA,OAAO;IACLK,mBAAmB,EAAEC,cAAc;IACnCC,mBAAmB,EAAED,cAAc;IACnCE,sBAAsB,EAAEF,cAAc;IACtCG,kBAAkB,EAAEH,cAAc;IAClCI,iBAAiB,EAAEJ,cAAc;IACjCK,wBAAwB,EAAEL;EAC5B,CAAC;EAED,SAASA,cAAcA,CAACM,IAAI,EAAE;IAC5B,MAAMC,QAAQ,GAAGD,IAAI,CAACT,IAAI,CAACC,KAAK;IAChC,MAAMU,OAAO,GAAGjB,YAAY,CAACgB,QAAQ,CAAC;IACtC,MAAME,YAAY,GAChBpB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACqB,OAAO,CAACH,QAAQ,CAAC;IAC1E,IAAII,YAAY;IAEhB,IAAIH,OAAO,EAAE;MACXG,YAAY,GAAGC,gBAAgB,CAACJ,OAAO,CAACK,IAAI,CAAC;IAC/C,CAAC,MAAM,IAAIJ,YAAY,EAAE;MACvBE,YAAY,GAAGG,aAAa,CAACL,YAAY,CAAC;IAC5C;IAEA,IAAIE,YAAY,EAAE;MAChB,IAAIA,YAAY,KAAKL,IAAI,CAACO,IAAI,EAAE;QAC9B,MAAME,OAAO,GAAGC,uBAAuB,CAACV,IAAI,CAACO,IAAI,CAAC;QAClDzB,OAAO,CAAC6B,WAAW,CACjB,IAAIvC,YAAY,CAAE,qBAAoBqC,OAAQ,UAASR,QAAS,IAAG,EAAE;UACnEW,KAAK,EAAEV,OAAO,GAAG,CAACA,OAAO,EAAEF,IAAI,CAAC,GAAGA;QACrC,CAAC,CACH,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAMa,YAAY,GAAG3B,MAAM,CAAC4B,IAAI,CAAC;QAC/B,GAAG7B,YAAY;QACf,IAAIF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GACpC,KAAK,CAAC,GACNA,MAAM,CAACgC,UAAU,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,MAAMC,cAAc,GAAG7C,cAAc,CAAC8B,QAAQ,EAAEY,YAAY,CAAC;MAC7D/B,OAAO,CAAC6B,WAAW,CACjB,IAAIvC,YAAY,CACb,uBAAsB6B,QAAS,8BAA6B,GAC3DjC,UAAU,CAACgD,cAAc,CAAC,EAC5B;QACEJ,KAAK,EAAEZ,IAAI,CAACT;MACd,CACF,CACF,CAAC;IACH;EACF;AACF;AACA,MAAMe,gBAAgB,GAAG;EACvB,CAACjC,IAAI,CAAC4C,sBAAsB,GAAG5C,IAAI,CAAC6C,qBAAqB;EACzD,CAAC7C,IAAI,CAAC8C,sBAAsB,GAAG9C,IAAI,CAAC+C,qBAAqB;EACzD,CAAC/C,IAAI,CAACgD,yBAAyB,GAAGhD,IAAI,CAACiD,wBAAwB;EAC/D,CAACjD,IAAI,CAACkD,qBAAqB,GAAGlD,IAAI,CAACmD,oBAAoB;EACvD,CAACnD,IAAI,CAACoD,oBAAoB,GAAGpD,IAAI,CAACqD,mBAAmB;EACrD,CAACrD,IAAI,CAACsD,4BAA4B,GAAGtD,IAAI,CAACuD;AAC5C,CAAC;AAED,SAASpB,aAAaA,CAACqB,IAAI,EAAE;EAC3B,IAAIlD,YAAY,CAACkD,IAAI,CAAC,EAAE;IACtB,OAAOxD,IAAI,CAAC6C,qBAAqB;EACnC;EAEA,IAAIxC,YAAY,CAACmD,IAAI,CAAC,EAAE;IACtB,OAAOxD,IAAI,CAAC+C,qBAAqB;EACnC;EAEA,IAAI3C,eAAe,CAACoD,IAAI,CAAC,EAAE;IACzB,OAAOxD,IAAI,CAACiD,wBAAwB;EACtC;EAEA,IAAI1C,WAAW,CAACiD,IAAI,CAAC,EAAE;IACrB,OAAOxD,IAAI,CAACmD,oBAAoB;EAClC;EAEA,IAAIjD,UAAU,CAACsD,IAAI,CAAC,EAAE;IACpB,OAAOxD,IAAI,CAACqD,mBAAmB;EACjC;EAEA,IAAIlD,iBAAiB,CAACqD,IAAI,CAAC,EAAE;IAC3B,OAAOxD,IAAI,CAACuD,2BAA2B;EACzC;EACA;EACA;;EAEA,KAAK,IAAI1D,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAAC4D,IAAI,CAAC,CAAC;AAChE;AAEA,SAASnB,uBAAuBA,CAACH,IAAI,EAAE;EACrC,QAAQA,IAAI;IACV,KAAKlC,IAAI,CAAC6C,qBAAqB;MAC7B,OAAO,QAAQ;IAEjB,KAAK7C,IAAI,CAAC+C,qBAAqB;MAC7B,OAAO,QAAQ;IAEjB,KAAK/C,IAAI,CAACiD,wBAAwB;MAChC,OAAO,WAAW;IAEpB,KAAKjD,IAAI,CAACmD,oBAAoB;MAC5B,OAAO,OAAO;IAEhB,KAAKnD,IAAI,CAACqD,mBAAmB;MAC3B,OAAO,MAAM;IAEf,KAAKrD,IAAI,CAACuD,2BAA2B;MACnC,OAAO,cAAc;IACvB;;IAEA;;IAEA;MACE,KAAK,IAAI1D,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAACsC,IAAI,CAAC,CAAC;EAClE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}