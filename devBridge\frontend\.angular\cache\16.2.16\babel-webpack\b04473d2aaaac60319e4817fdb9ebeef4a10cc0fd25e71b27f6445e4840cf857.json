{"ast": null, "code": "import { addBusinessDays } from \"./addBusinessDays.js\";\n\n/**\n * The {@link subBusinessDays} function options.\n */\n\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Subtract the specified number of business days (mon - fri) from the given date.\n *\n * @description\n * Subtract the specified number of business days (mon - fri) from the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the business days subtracted\n *\n * @example\n * // Subtract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\nexport function subBusinessDays(date, amount, options) {\n  return addBusinessDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subBusinessDays;", "map": {"version": 3, "names": ["addBusinessDays", "subBusinessDays", "date", "amount", "options"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/subBusinessDays.js"], "sourcesContent": ["import { addBusinessDays } from \"./addBusinessDays.js\";\n\n/**\n * The {@link subBusinessDays} function options.\n */\n\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Subtract the specified number of business days (mon - fri) from the given date.\n *\n * @description\n * Subtract the specified number of business days (mon - fri) from the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the business days subtracted\n *\n * @example\n * // Subtract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\nexport function subBusinessDays(date, amount, options) {\n  return addBusinessDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subBusinessDays;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,OAAOJ,eAAe,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAChD;;AAEA;AACA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}