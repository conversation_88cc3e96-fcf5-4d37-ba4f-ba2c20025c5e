{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { assertName } from '../type/assertName.mjs';\n/* c8 ignore start */\n\n/**\n * Upholds the spec rules about naming.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function assertValidName(name) {\n  const error = isValidNameError(name);\n  if (error) {\n    throw error;\n  }\n  return name;\n}\n/**\n * Returns an Error if a name is invalid.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function isValidNameError(name) {\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n  if (name.startsWith('__')) {\n    return new GraphQLError(`Name \"${name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`);\n  }\n  try {\n    assertName(name);\n  } catch (error) {\n    return error;\n  }\n}\n/* c8 ignore stop */", "map": {"version": 3, "names": ["devAssert", "GraphQLError", "assertName", "assertValidName", "name", "error", "isValidNameError", "startsWith"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/assertValidName.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { assertName } from '../type/assertName.mjs';\n/* c8 ignore start */\n\n/**\n * Upholds the spec rules about naming.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function assertValidName(name) {\n  const error = isValidNameError(name);\n\n  if (error) {\n    throw error;\n  }\n\n  return name;\n}\n/**\n * Returns an Error if a name is invalid.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function isValidNameError(name) {\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n\n  if (name.startsWith('__')) {\n    return new GraphQLError(\n      `Name \"${name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`,\n    );\n  }\n\n  try {\n    assertName(name);\n  } catch (error) {\n    return error;\n  }\n}\n/* c8 ignore stop */\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,UAAU,QAAQ,wBAAwB;AACnD;;AAEA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,MAAMC,KAAK,GAAGC,gBAAgB,CAACF,IAAI,CAAC;EAEpC,IAAIC,KAAK,EAAE;IACT,MAAMA,KAAK;EACb;EAEA,OAAOD,IAAI;AACb;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,gBAAgBA,CAACF,IAAI,EAAE;EACrC,OAAOA,IAAI,KAAK,QAAQ,IAAIJ,SAAS,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAE7E,IAAII,IAAI,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;IACzB,OAAO,IAAIN,YAAY,CACpB,SAAQG,IAAK,yEAChB,CAAC;EACH;EAEA,IAAI;IACFF,UAAU,CAACE,IAAI,CAAC;EAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK;EACd;AACF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}