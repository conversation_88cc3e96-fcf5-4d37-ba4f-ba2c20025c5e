<div class="kanban-board-container">
  <!-- Header avec statistiques -->
  <div class="kanban-header" *ngIf="kanbanData">
    <div class="stats-container">
      <div class="stat-item">
        <mat-icon>assignment</mat-icon>
        <span class="stat-value">{{ kanbanData.stats.total }}</span>
        <span class="stat-label">Total</span>
      </div>
      <div class="stat-item overdue" *ngIf="kanbanData.stats.overdue > 0">
        <mat-icon>schedule</mat-icon>
        <span class="stat-value">{{ kanbanData.stats.overdue }}</span>
        <span class="stat-label">En retard</span>
      </div>
      <div class="stat-item blocked" *ngIf="kanbanData.stats.blocked > 0">
        <mat-icon>block</mat-icon>
        <span class="stat-value">{{ kanbanData.stats.blocked }}</span>
        <span class="stat-label">Bloquées</span>
      </div>
    </div>

    <div class="actions-container">
      <button mat-icon-button (click)="refresh()" matTooltip="Actualiser">
        <mat-icon>refresh</mat-icon>
      </button>
      <button mat-icon-button matTooltip="Filtres">
        <mat-icon>filter_list</mat-icon>
      </button>
    </div>
  </div>

  <!-- Loading state -->
  <div class="loading-container" *ngIf="loading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Chargement du tableau Kanban...</p>
  </div>

  <!-- Error state -->
  <div class="error-container" *ngIf="error && !loading">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadKanbanBoard()">
      Réessayer
    </button>
  </div>

  <!-- Kanban Board -->
  <div class="kanban-board" *ngIf="kanbanData && !loading && !error"
       cdkDropListGroup>

    <div class="kanban-column"
         *ngFor="let column of columns"
         [style.border-top-color]="column.color">

      <!-- Column Header -->
      <div class="column-header" [style.background-color]="column.color">
        <div class="column-title">
          <mat-icon>{{ column.icon }}</mat-icon>
          <span>{{ column.title }}</span>
          <span class="task-count">({{ getColumnCount(column.id) }})</span>
        </div>

        <button mat-icon-button
                class="add-task-btn"
                (click)="createTaskInColumn(column.id)"
                matTooltip="Ajouter une tâche">
          <mat-icon>add</mat-icon>
        </button>
      </div>

      <!-- Tasks Container -->
      <div class="tasks-container"
           cdkDropList
           [id]="column.id"
           [cdkDropListData]="getColumnTasks(column.id)"
           (cdkDropListDropped)="onTaskDrop($event)">

        <!-- Task Cards -->
        <div class="task-card"
             *ngFor="let task of getColumnTasks(column.id)"
             cdkDrag
             [cdkDragData]="task"
             (click)="selectTask(task)">

          <!-- Task Priority Indicator -->
          <div class="priority-indicator"
               [style.background-color]="getPriorityColor(task.priority)">
          </div>

          <!-- Task Content -->
          <div class="task-content">
            <!-- Task Header -->
            <div class="task-header">
              <div class="task-category">
                <mat-icon class="category-icon">{{ getCategoryIcon(task.category || 'task') }}</mat-icon>
                <span class="task-id">#{{ task._id?.slice(-6) }}</span>
              </div>

              <div class="task-actions">
                <mat-icon *ngIf="task.isOverdue"
                         class="overdue-icon"
                         matTooltip="En retard">
                  schedule
                </mat-icon>
                <mat-icon *ngIf="task.isBlocked"
                         class="blocked-icon"
                         matTooltip="Bloquée">
                  block
                </mat-icon>
              </div>
            </div>

            <!-- Task Title -->
            <h4 class="task-title">{{ task.title }}</h4>

            <!-- Task Description -->
            <p class="task-description" *ngIf="task.description">
              {{ task.description | slice:0:100 }}
              <span *ngIf="task.description && task.description.length > 100">...</span>
            </p>

            <!-- Task Labels -->
            <div class="task-labels" *ngIf="task.labels && task.labels.length > 0">
              <span class="task-label"
                    *ngFor="let label of task.labels | slice:0:3"
                    [style.background-color]="label.color">
                {{ label.name }}
              </span>
              <span class="more-labels" *ngIf="task.labels && task.labels.length > 3">
                +{{ task.labels.length - 3 }}
              </span>
            </div>

            <!-- Task Footer -->
            <div class="task-footer">
              <!-- Assignees -->
              <div class="task-assignees" *ngIf="task.assignedTo && task.assignedTo.length > 0">
                <div class="assignee-avatar"
                     *ngFor="let assignee of task.assignedTo | slice:0:3"
                     [matTooltip]="getAssigneeName(assignee)">
                  <img *ngIf="hasAssigneeImage(assignee)"
                       [src]="getAssigneeImage(assignee)"
                       [alt]="getAssigneeName(assignee)">
                  <span *ngIf="!hasAssigneeImage(assignee)">
                    {{ getAssigneeInitials(assignee) }}
                  </span>
                </div>
                <span class="more-assignees" *ngIf="task.assignedTo && task.assignedTo.length > 3">
                  +{{ task.assignedTo.length - 3 }}
                </span>
              </div>

              <!-- Task Meta -->
              <div class="task-meta">
                <!-- Due Date -->
                <span class="due-date"
                      *ngIf="task.dueDate"
                      [class.overdue]="task.isOverdue">
                  <mat-icon>schedule</mat-icon>
                  {{ task.dueDate | date:'dd/MM' }}
                </span>

                <!-- Estimated Hours -->
                <span class="estimated-hours" *ngIf="task.estimatedHours">
                  <mat-icon>access_time</mat-icon>
                  {{ task.estimatedHours }}h
                </span>

                <!-- Subtasks Count -->
                <span class="subtasks-count" *ngIf="task.subtasks && task.subtasks.length > 0">
                  <mat-icon>list</mat-icon>
                  {{ task.subtasks.length }}
                </span>

                <!-- Comments Count -->
                <span class="comments-count" *ngIf="task.comments && task.comments.length > 0">
                  <mat-icon>comment</mat-icon>
                  {{ task.comments.length }}
                </span>
              </div>
            </div>

            <!-- AI Suggestions Indicator -->
            <div class="ai-indicator"
                 *ngIf="task.aiSuggestions?.suggestions && (task.aiSuggestions?.suggestions?.length || 0) > 0"
                 matTooltip="Suggestions IA disponibles">
              <mat-icon>psychology</mat-icon>
              <span>{{ task.aiSuggestions?.suggestions?.length || 0 }}</span>
            </div>
          </div>

          <!-- Drag Preview -->
          <div class="task-drag-preview" *cdkDragPreview>
            <div class="drag-preview-content">
              <h4>{{ task.title }}</h4>
              <p>{{ task.category }} • {{ task.priority }}</p>
            </div>
          </div>
        </div>

        <!-- Empty Column Placeholder -->
        <div class="empty-column" *ngIf="isColumnEmpty(column.id)">
          <mat-icon>{{ column.icon }}</mat-icon>
          <p>{{ getEmptyColumnText(column.id) }}</p>
          <button mat-stroked-button
                  color="primary"
                  (click)="createTaskInColumn(column.id)">
            <mat-icon>add</mat-icon>
            Ajouter une tâche
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
