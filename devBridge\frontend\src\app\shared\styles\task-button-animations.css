/* Animations personnalisées pour les boutons de tâches */

/* Animation de pulsation pour les badges */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 107, 105, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 107, 105, 0.8);
    transform: scale(1.05);
  }
}

/* Animation de rotation pour les icônes */
@keyframes rotate-bounce {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(5deg) scale(1.1); }
  50% { transform: rotate(-5deg) scale(1.1); }
  75% { transform: rotate(3deg) scale(1.05); }
  100% { transform: rotate(0deg) scale(1); }
}

/* Animation de brillance qui traverse le bouton */
@keyframes shine-sweep {
  0% {
    transform: translateX(-100%) skewX(-12deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
    opacity: 0;
  }
}

/* Animation de particules flottantes */
@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-3px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(2px) rotate(240deg);
    opacity: 0.8;
  }
}

/* Animation de progression de la barre */
@keyframes progress-fill {
  0% {
    width: 0%;
    background: linear-gradient(90deg, #00ff9d, #00e68a);
  }
  50% {
    background: linear-gradient(90deg, #00e68a, #00d4aa);
  }
  100% {
    background: linear-gradient(90deg, #00d4aa, #00c199);
  }
}

/* Animation de rebond pour les badges */
@keyframes bounce-in {
  0% {
    transform: scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Animation de hover pour les boutons */
@keyframes button-hover-glow {
  0% {
    box-shadow: 0 4px 15px rgba(0, 255, 157, 0.2);
  }
  50% {
    box-shadow: 0 8px 25px rgba(0, 255, 157, 0.4);
  }
  100% {
    box-shadow: 0 12px 35px rgba(0, 255, 157, 0.3);
  }
}

/* Classes utilitaires pour les animations */
.task-button-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.task-button-enhanced:hover {
  animation: button-hover-glow 2s ease-in-out infinite;
}

.task-button-enhanced .shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.task-button-enhanced:hover .shine-effect {
  left: 100%;
}

.task-badge-animated {
  animation: bounce-in 0.6s ease-out;
}

.task-badge-animated:hover {
  animation: pulse-glow 1.5s ease-in-out infinite;
}

.task-icon-animated {
  transition: all 0.3s ease;
}

.task-icon-animated:hover {
  animation: rotate-bounce 0.8s ease-in-out;
}

.task-particles {
  pointer-events: none;
}

.task-particles .particle {
  animation: float-particles 3s ease-in-out infinite;
}

.task-particles .particle:nth-child(2) {
  animation-delay: 1s;
}

.task-particles .particle:nth-child(3) {
  animation-delay: 2s;
}

.task-progress-bar {
  animation: progress-fill 2s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .task-button-enhanced {
    transform: none !important;
  }
  
  .task-button-enhanced:hover {
    animation: none;
    box-shadow: 0 4px 15px rgba(0, 255, 157, 0.3);
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .task-button-enhanced:hover {
    animation: button-hover-glow 2s ease-in-out infinite;
    box-shadow: 0 8px 25px rgba(0, 247, 255, 0.3);
  }
}

/* Accessibility - Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .task-button-enhanced,
  .task-badge-animated,
  .task-icon-animated,
  .task-particles .particle,
  .task-progress-bar {
    animation: none !important;
    transition: none !important;
  }
}
