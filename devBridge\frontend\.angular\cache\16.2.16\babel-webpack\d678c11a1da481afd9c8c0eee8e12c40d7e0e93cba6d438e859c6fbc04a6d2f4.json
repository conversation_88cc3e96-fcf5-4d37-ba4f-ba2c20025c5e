{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\n\n/**\n * Lone anonymous operation\n *\n * A GraphQL document is only valid if when it contains an anonymous operation\n * (the query short-hand) that it contains only that one operation definition.\n *\n * See https://spec.graphql.org/draft/#sec-Lone-Anonymous-Operation\n */\nexport function LoneAnonymousOperationRule(context) {\n  let operationCount = 0;\n  return {\n    Document(node) {\n      operationCount = node.definitions.filter(definition => definition.kind === Kind.OPERATION_DEFINITION).length;\n    },\n    OperationDefinition(node) {\n      if (!node.name && operationCount > 1) {\n        context.reportError(new GraphQLError('This anonymous operation must be the only defined operation.', {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}