import { DirectiveNode, DirectiveDefinitionNode } from 'graphql';
import { Config } from './merge-typedefs.cjs';
export declare function mergeDirectives(d1?: ReadonlyArray<DirectiveNode>, d2?: ReadonlyArray<DirectiveNode>, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): DirectiveNode[];
export declare function mergeDirective(node: DirectiveDefinitionNode, existingNode?: DirectiveDefinitionNode): DirectiveDefinitionNode;
