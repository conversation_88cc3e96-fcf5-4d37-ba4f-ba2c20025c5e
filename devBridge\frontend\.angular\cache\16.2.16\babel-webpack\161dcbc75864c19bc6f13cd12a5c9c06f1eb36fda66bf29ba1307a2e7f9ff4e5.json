{"ast": null, "code": "/**\n * Groups array items into a Map, given a function to produce grouping key.\n */\nexport function groupBy(list, keyFn) {\n  const result = new Map();\n  for (const item of list) {\n    const key = keyFn(item);\n    const group = result.get(key);\n    if (group === undefined) {\n      result.set(key, [item]);\n    } else {\n      group.push(item);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["groupBy", "list", "keyFn", "result", "Map", "item", "key", "group", "get", "undefined", "set", "push"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/jsutils/groupBy.mjs"], "sourcesContent": ["/**\n * Groups array items into a Map, given a function to produce grouping key.\n */\nexport function groupBy(list, keyFn) {\n  const result = new Map();\n\n  for (const item of list) {\n    const key = keyFn(item);\n    const group = result.get(key);\n\n    if (group === undefined) {\n      result.set(key, [item]);\n    } else {\n      group.push(item);\n    }\n  }\n\n  return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACnC,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAExB,KAAK,MAAMC,IAAI,IAAIJ,IAAI,EAAE;IACvB,MAAMK,GAAG,GAAGJ,KAAK,CAACG,IAAI,CAAC;IACvB,MAAME,KAAK,GAAGJ,MAAM,CAACK,GAAG,CAACF,GAAG,CAAC;IAE7B,IAAIC,KAAK,KAAKE,SAAS,EAAE;MACvBN,MAAM,CAACO,GAAG,CAACJ,GAAG,EAAE,CAACD,IAAI,CAAC,CAAC;IACzB,CAAC,MAAM;MACLE,KAAK,CAACI,IAAI,CAACN,IAAI,CAAC;IAClB;EACF;EAEA,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}