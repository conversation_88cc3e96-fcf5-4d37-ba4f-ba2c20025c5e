{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation types\n *\n * A GraphQL document is only valid if it has only one type per operation.\n */\nexport function UniqueOperationTypesRule(context) {\n  const schema = context.getSchema();\n  const definedOperationTypes = Object.create(null);\n  const existingOperationTypes = schema ? {\n    query: schema.getQueryType(),\n    mutation: schema.getMutationType(),\n    subscription: schema.getSubscriptionType()\n  } : {};\n  return {\n    SchemaDefinition: checkOperationTypes,\n    SchemaExtension: checkOperationTypes\n  };\n  function checkOperationTypes(node) {\n    var _node$operationTypes;\n\n    // See: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const operationTypesNodes = (_node$operationTypes = node.operationTypes) !== null && _node$operationTypes !== void 0 ? _node$operationTypes : [];\n    for (const operationType of operationTypesNodes) {\n      const operation = operationType.operation;\n      const alreadyDefinedOperationType = definedOperationTypes[operation];\n      if (existingOperationTypes[operation]) {\n        context.reportError(new GraphQLError(`Type for ${operation} already defined in the schema. It cannot be redefined.`, {\n          nodes: operationType\n        }));\n      } else if (alreadyDefinedOperationType) {\n        context.reportError(new GraphQLError(`There can be only one ${operation} type in schema.`, {\n          nodes: [alreadyDefinedOperationType, operationType]\n        }));\n      } else {\n        definedOperationTypes[operation] = operationType;\n      }\n    }\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}