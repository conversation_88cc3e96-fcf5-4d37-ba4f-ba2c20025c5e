{"ast": null, "code": "'use strict';\n\n/**\n * Used to mark a\n * [React Native `File` substitute]{@link ReactNativeFileSubstitute}\n * in an object tree for [`extractFiles`]{@link extractFiles}. It’s too risky to\n * assume all objects with `uri`, `type` and `name` properties are files to\n * extract.\n * @kind class\n * @name ReactNativeFile\n * @param {ReactNativeFileSubstitute} file A [React Native](https://reactnative.dev) [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File) substitute.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { ReactNativeFile } from 'extract-files';\n * ```\n *\n * ```js\n * import ReactNativeFile from 'extract-files/public/ReactNativeFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { ReactNativeFile } = require('extract-files');\n * ```\n *\n * ```js\n * const ReactNativeFile = require('extract-files/public/ReactNativeFile.js');\n * ```\n * @example <caption>An extractable file in [React Native](https://reactnative.dev).</caption>\n * ```js\n * const file = new ReactNativeFile({\n *   uri: uriFromCameraRoll,\n *   name: 'a.jpg',\n *   type: 'image/jpeg',\n * });\n * ```\n */\nmodule.exports = class ReactNativeFile {\n  constructor({\n    uri,\n    name,\n    type\n  }) {\n    this.uri = uri;\n    this.name = name;\n    this.type = type;\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}