{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { parseNDigits } from \"../utils.js\";\nexport class QuarterParser extends Parser {\n  priority = 120;\n  parse(dateString, token, match) {\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n      case \"QQ\":\n        // 01, 02, 03, 04\n        return parseNDigits(token.length, dateString);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return match.ordinalNumber(dateString, {\n          unit: \"quarter\"\n        });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return match.quarter(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return match.quarter(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.quarter(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 4;\n  }\n  set(date, _flags, value) {\n    date.setMonth((value - 1) * 3, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"Y\", \"R\", \"q\", \"M\", \"L\", \"w\", \"I\", \"d\", \"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}