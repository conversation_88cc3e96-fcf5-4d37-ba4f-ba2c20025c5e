{"ast": null, "code": "\"use strict\";\n\n// Most JavaScript environments do not need the workarounds implemented in\n// fixPolyfills.native.ts, so importing fixPolyfills.ts merely imports\n// this empty module, adding nothing to bundle sizes or execution times.\n// When bundling for React Native, we substitute fixPolyfills.native.js\n// for fixPolyfills.js (see the \"react-native\" section of package.json),\n// to work around problems with Map and Set polyfills in older versions of\n// React Native (which should have been fixed in react-native@0.59.0):\n// https://github.com/apollographql/apollo-client/pull/5962", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/fixPolyfills.js"], "sourcesContent": ["\"use strict\";\n// Most JavaScript environments do not need the workarounds implemented in\n// fixPolyfills.native.ts, so importing fixPolyfills.ts merely imports\n// this empty module, adding nothing to bundle sizes or execution times.\n// When bundling for React Native, we substitute fixPolyfills.native.js\n// for fixPolyfills.js (see the \"react-native\" section of package.json),\n// to work around problems with Map and Set polyfills in older versions of\n// React Native (which should have been fixed in react-native@0.59.0):\n// https://github.com/apollographql/apollo-client/pull/5962\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}