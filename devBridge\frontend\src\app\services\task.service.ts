import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  Task,
  KanbanBoard,
  KanbanFilters,
  MoveTaskRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  AddCommentRequest,
  AddLabelRequest,
  AssignTaskRequest,
  TaskStatistics,
  AIAnalysisResponse,
  GenerateSubtasksResponse,
  EstimateEffortResponse,
  GenerateDescriptionResponse
} from '../models/task.model';

@Injectable({
  providedIn: 'root',
})
export class TaskService {
  private apiUrl = `${environment.urlBackend}tasks`;

  constructor(private http: HttpClient) {
    console.log('Task API URL:', this.apiUrl);
  }

  // Récupérer toutes les tâches
  getTasks(): Observable<Task[]> {
    return this.http.get<Task[]>(this.apiUrl).pipe(
      tap((data) => console.log('Tasks received:', data)),
      catchError(this.handleError)
    );
  }

  // Récupérer les tâches d'une équipe spécifique
  getTasksByTeam(teamId: string): Observable<Task[]> {
    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`).pipe(
      tap((data) => console.log(`Tasks for team ${teamId} received:`, data)),
      catchError(this.handleError)
    );
  }

  // Récupérer une tâche par son ID
  getTask(id: string): Observable<Task> {
    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(
      tap((data) => console.log('Task received:', data)),
      catchError(this.handleError)
    );
  }

  // Créer une nouvelle tâche
  createTask(task: Task): Observable<Task> {
    return this.http.post<Task>(this.apiUrl, task).pipe(
      tap((data) => console.log('Task created:', data)),
      catchError(this.handleError)
    );
  }

  // Mettre à jour une tâche existante
  updateTask(id: string, task: Task): Observable<Task> {
    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(
      tap((data) => console.log('Task updated:', data)),
      catchError(this.handleError)
    );
  }

  // Supprimer une tâche
  deleteTask(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`).pipe(
      tap((data) => console.log('Task deleted:', data)),
      catchError(this.handleError)
    );
  }

  // Mettre à jour le statut d'une tâche
  updateTaskStatus(
    id: string,
    status: 'todo' | 'in-progress' | 'done'
  ): Observable<Task> {
    return this.http
      .patch<Task>(`${this.apiUrl}/${id}/status`, { status })
      .pipe(
        tap((data) => console.log('Task status updated:', data)),
        catchError(this.handleError)
      );
  }

  // ==================== KANBAN ====================

  // Obtenir le tableau Kanban d'une équipe
  getKanbanBoard(teamId: string, filters?: KanbanFilters): Observable<KanbanBoard> {
    let params = new HttpParams();

    if (filters) {
      if (filters.includeArchived !== undefined) {
        params = params.set('includeArchived', filters.includeArchived.toString());
      }
      if (filters.assignedTo && filters.assignedTo.length > 0) {
        params = params.set('assignedTo', filters.assignedTo.join(','));
      }
      if (filters.labels && filters.labels.length > 0) {
        params = params.set('labels', filters.labels.join(','));
      }
      if (filters.category) {
        params = params.set('category', filters.category);
      }
    }

    return this.http.get<KanbanBoard>(`${this.apiUrl}/kanban/${teamId}`, { params }).pipe(
      tap((data) => console.log('Kanban board received:', data)),
      catchError(this.handleError)
    );
  }

  // Créer une tâche dans une colonne spécifique
  createTaskInColumn(teamId: string, task: CreateTaskRequest): Observable<{ message: string; task: Task }> {
    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/kanban/${teamId}`, task).pipe(
      tap((data) => console.log('Task created in column:', data)),
      catchError(this.handleError)
    );
  }

  // Déplacer une tâche (drag & drop)
  moveTask(taskId: string, moveRequest: MoveTaskRequest): Observable<{ message: string; task: Task }> {
    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/move`, moveRequest).pipe(
      tap((data) => console.log('Task moved:', data)),
      catchError(this.handleError)
    );
  }

  // Archiver/désarchiver une tâche
  toggleArchiveTask(taskId: string): Observable<{ message: string; task: Task }> {
    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/archive`, {}).pipe(
      tap((data) => console.log('Task archive toggled:', data)),
      catchError(this.handleError)
    );
  }

  // Obtenir les statistiques des tâches
  getTaskStatistics(teamId: string, period: number = 30): Observable<TaskStatistics> {
    const params = new HttpParams().set('period', period.toString());

    return this.http.get<TaskStatistics>(`${this.apiUrl}/stats/${teamId}`, { params }).pipe(
      tap((data) => console.log('Task statistics received:', data)),
      catchError(this.handleError)
    );
  }

  // ==================== FONCTIONNALITÉS AVANCÉES ====================

  // Ajouter un commentaire
  addComment(taskId: string, comment: AddCommentRequest): Observable<{ message: string; task: Task }> {
    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/comments`, comment).pipe(
      tap((data) => console.log('Comment added:', data)),
      catchError(this.handleError)
    );
  }

  // Gérer les labels
  manageLabel(taskId: string, label: AddLabelRequest): Observable<{ message: string; task: Task }> {
    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/labels`, label).pipe(
      tap((data) => console.log('Label managed:', data)),
      catchError(this.handleError)
    );
  }

  // Assigner des utilisateurs
  assignUsers(taskId: string, assignment: AssignTaskRequest): Observable<{ message: string; task: Task }> {
    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/assign`, assignment).pipe(
      tap((data) => console.log('Users assigned:', data)),
      catchError(this.handleError)
    );
  }

  // Obtenir les sous-tâches
  getSubtasks(taskId: string): Observable<Task[]> {
    return this.http.get<Task[]>(`${this.apiUrl}/${taskId}/subtasks`).pipe(
      tap((data) => console.log('Subtasks received:', data)),
      catchError(this.handleError)
    );
  }

  // Enregistrer une vue
  recordView(taskId: string): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(`${this.apiUrl}/${taskId}/view`, {}).pipe(
      tap((data) => console.log('View recorded:', data)),
      catchError(this.handleError)
    );
  }

  // ==================== INTÉGRATION IA ====================

  // Analyser une tâche avec l'IA
  analyzeTaskWithAI(taskId: string): Observable<AIAnalysisResponse> {
    return this.http.post<AIAnalysisResponse>(`${this.apiUrl}/${taskId}/ai/analyze`, {}).pipe(
      tap((data) => console.log('AI analysis completed:', data)),
      catchError(this.handleError)
    );
  }

  // Générer des sous-tâches avec l'IA
  generateSubtasksWithAI(taskId: string): Observable<GenerateSubtasksResponse> {
    return this.http.post<GenerateSubtasksResponse>(`${this.apiUrl}/${taskId}/ai/subtasks`, {}).pipe(
      tap((data) => console.log('AI subtasks generated:', data)),
      catchError(this.handleError)
    );
  }

  // Estimer l'effort avec l'IA
  estimateEffortWithAI(taskId: string): Observable<EstimateEffortResponse> {
    return this.http.post<EstimateEffortResponse>(`${this.apiUrl}/${taskId}/ai/estimate`, {}).pipe(
      tap((data) => console.log('AI effort estimated:', data)),
      catchError(this.handleError)
    );
  }

  // Générer une description avec l'IA
  generateDescriptionWithAI(taskId: string): Observable<GenerateDescriptionResponse> {
    return this.http.post<GenerateDescriptionResponse>(`${this.apiUrl}/${taskId}/ai/description`, {}).pipe(
      tap((data) => console.log('AI description generated:', data)),
      catchError(this.handleError)
    );
  }

  // Appliquer une suggestion IA
  applySuggestion(taskId: string, suggestionIndex: number): Observable<{ message: string; task: Task; suggestion: any }> {
    return this.http.post<{ message: string; task: Task; suggestion: any }>(`${this.apiUrl}/${taskId}/ai/suggestions/${suggestionIndex}/apply`, {}).pipe(
      tap((data) => console.log('AI suggestion applied:', data)),
      catchError(this.handleError)
    );
  }

  // ==================== MÉTHODES UTILITAIRES ====================

  // Créer une tâche avancée
  createAdvancedTask(task: CreateTaskRequest): Observable<Task> {
    return this.http.post<Task>(this.apiUrl, task).pipe(
      tap((data) => console.log('Advanced task created:', data)),
      catchError(this.handleError)
    );
  }

  // Mettre à jour une tâche avancée
  updateAdvancedTask(id: string, task: UpdateTaskRequest): Observable<Task> {
    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(
      tap((data) => console.log('Advanced task updated:', data)),
      catchError(this.handleError)
    );
  }

  // Dupliquer une tâche
  duplicateTask(taskId: string): Observable<{ message: string; task: Task }> {
    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/duplicate`, {}).pipe(
      tap((data) => console.log('Task duplicated:', data)),
      catchError(this.handleError)
    );
  }

  // Obtenir les tâches avec filtres avancés
  getTasksWithFilters(teamId: string, filters: any): Observable<Task[]> {
    let params = new HttpParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null) {
        if (Array.isArray(filters[key])) {
          params = params.set(key, filters[key].join(','));
        } else {
          params = params.set(key, filters[key].toString());
        }
      }
    });

    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(
      tap((data) => console.log('Filtered tasks received:', data)),
      catchError(this.handleError)
    );
  }

  // Rechercher des tâches
  searchTasks(teamId: string, query: string): Observable<Task[]> {
    const params = new HttpParams().set('search', query);

    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(
      tap((data) => console.log('Search results:', data)),
      catchError(this.handleError)
    );
  }

  // Obtenir les tâches assignées à un utilisateur
  getUserTasks(userId: string, status?: string): Observable<Task[]> {
    let params = new HttpParams().set('assignedTo', userId);
    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<Task[]>(`${this.apiUrl}/user/${userId}`, { params }).pipe(
      tap((data) => console.log('User tasks received:', data)),
      catchError(this.handleError)
    );
  }

  // Obtenir les tâches en retard
  getOverdueTasks(teamId: string): Observable<Task[]> {
    const params = new HttpParams().set('overdue', 'true');

    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(
      tap((data) => console.log('Overdue tasks received:', data)),
      catchError(this.handleError)
    );
  }

  // Gérer les erreurs HTTP
  private handleError(error: HttpErrorResponse) {
    let errorMessage = '';
    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
