{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { isAbstractType } from '../type/definition.mjs';\nimport { GraphQLIncludeDirective, GraphQLSkipDirective } from '../type/directives.mjs';\nimport { typeFromAST } from '../utilities/typeFromAST.mjs';\nimport { getDirectiveValues } from './values.mjs';\n/**\n * Given a selectionSet, collects all of the fields and returns them.\n *\n * CollectFields requires the \"runtime type\" of an object. For a field that\n * returns an Interface or Union type, the \"runtime type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectFields(schema, fragments, variableValues, runtimeType, selectionSet) {\n  const fields = new Map();\n  collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, new Set());\n  return fields;\n}\n/**\n * Given an array of field nodes, collects all of the subfields of the passed\n * in fields, and returns them at the end.\n *\n * CollectSubFields requires the \"return type\" of an object. For a field that\n * returns an Interface or Union type, the \"return type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {\n  const subFieldNodes = new Map();\n  const visitedFragmentNames = new Set();\n  for (const node of fieldNodes) {\n    if (node.selectionSet) {\n      collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, visitedFragmentNames);\n    }\n  }\n  return subFieldNodes;\n}\nfunction collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, visitedFragmentNames) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD:\n        {\n          if (!shouldIncludeNode(variableValues, selection)) {\n            continue;\n          }\n          const name = getFieldEntryKey(selection);\n          const fieldList = fields.get(name);\n          if (fieldList !== undefined) {\n            fieldList.push(selection);\n          } else {\n            fields.set(name, [selection]);\n          }\n          break;\n        }\n      case Kind.INLINE_FRAGMENT:\n        {\n          if (!shouldIncludeNode(variableValues, selection) || !doesFragmentConditionMatch(schema, selection, runtimeType)) {\n            continue;\n          }\n          collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, visitedFragmentNames);\n          break;\n        }\n      case Kind.FRAGMENT_SPREAD:\n        {\n          const fragName = selection.name.value;\n          if (visitedFragmentNames.has(fragName) || !shouldIncludeNode(variableValues, selection)) {\n            continue;\n          }\n          visitedFragmentNames.add(fragName);\n          const fragment = fragments[fragName];\n          if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {\n            continue;\n          }\n          collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, visitedFragmentNames);\n          break;\n        }\n    }\n  }\n}\n/**\n * Determines if a field should be included based on the `@include` and `@skip`\n * directives, where `@skip` has higher precedence than `@include`.\n */\n\nfunction shouldIncludeNode(variableValues, node) {\n  const skip = getDirectiveValues(GraphQLSkipDirective, node, variableValues);\n  if ((skip === null || skip === void 0 ? void 0 : skip.if) === true) {\n    return false;\n  }\n  const include = getDirectiveValues(GraphQLIncludeDirective, node, variableValues);\n  if ((include === null || include === void 0 ? void 0 : include.if) === false) {\n    return false;\n  }\n  return true;\n}\n/**\n * Determines if a fragment is applicable to the given type.\n */\n\nfunction doesFragmentConditionMatch(schema, fragment, type) {\n  const typeConditionNode = fragment.typeCondition;\n  if (!typeConditionNode) {\n    return true;\n  }\n  const conditionalType = typeFromAST(schema, typeConditionNode);\n  if (conditionalType === type) {\n    return true;\n  }\n  if (isAbstractType(conditionalType)) {\n    return schema.isSubType(conditionalType, type);\n  }\n  return false;\n}\n/**\n * Implements the logic to compute the key of a given field's entry\n */\n\nfunction getFieldEntryKey(node) {\n  return node.alias ? node.alias.value : node.name.value;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}