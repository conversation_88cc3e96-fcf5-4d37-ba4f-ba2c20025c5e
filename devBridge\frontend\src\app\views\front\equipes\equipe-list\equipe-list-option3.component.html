<!-- Option 3: Design en Tableau Moderne -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Mes Équipes</h1>
            <p class="text-gray-600 dark:text-gray-400">Gérez et organisez vos équipes de travail</p>
          </div>
          <div class="flex items-center gap-3">
            <button
              (click)="refreshEquipes()"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              [disabled]="loading"
            >
              <i class="fas fa-sync-alt mr-2" [class.animate-spin]="loading"></i>
              Rafraîchir
            </button>
            <button
              (click)="navigateToAddEquipe()"
              class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors shadow-sm"
            >
              <i class="fas fa-plus mr-2"></i>
              Nouvelle Équipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-16">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p class="mt-4 text-gray-600 dark:text-gray-400">Chargement des équipes...</p>
    </div>

    <!-- Table Layout -->
    <div *ngIf="!loading && equipes.length > 0" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">Équipe</th>
              <th class="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">Membres</th>
              <th class="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">Tâches</th>
              <th class="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">Progression</th>
              <th class="px-6 py-4 text-center text-sm font-medium text-gray-900 dark:text-white">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let equipe of equipes" class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
              <!-- Équipe -->
              <td class="px-6 py-4">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                  </div>
                  <div>
                    <div class="font-semibold text-gray-900 dark:text-white">{{ equipe.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
                      {{ equipe.description || "Aucune description" }}
                    </div>
                  </div>
                </div>
              </td>

              <!-- Membres -->
              <td class="px-6 py-4">
                <div class="flex items-center text-sm text-gray-900 dark:text-white">
                  <i class="fas fa-users mr-2 text-gray-400"></i>
                  {{ equipe.members?.length || 0 }} membres
                </div>
              </td>

              <!-- Tâches -->
              <td class="px-6 py-4">
                <div class="flex items-center text-sm text-gray-900 dark:text-white">
                  <i class="fas fa-tasks mr-2 text-gray-400"></i>
                  {{ getTaskCount(equipe._id || '') }} tâches
                </div>
              </td>

              <!-- Progression -->
              <td class="px-6 py-4">
                <div class="flex items-center space-x-3">
                  <div class="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-green-500 h-2 rounded-full transition-all duration-500"
                      [style.width.%]="getTaskStatus(equipe._id || '').percentage">
                    </div>
                  </div>
                  <span class="text-sm font-medium text-gray-900 dark:text-white min-w-[3rem]">
                    {{ getTaskStatus(equipe._id || '').percentage }}%
                  </span>
                </div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4">
                <div class="flex items-center justify-center space-x-2">
                  <!-- Bouton Tasks PRINCIPAL -->
                  <button
                    (click)="equipe._id && navigateToTasks(equipe._id)"
                    class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg flex items-center group"
                  >
                    <i class="fas fa-tasks mr-2 group-hover:scale-110 transition-transform"></i>
                    <span class="font-bold">TASKS</span>
                  </button>

                  <!-- Actions secondaires -->
                  <button
                    (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
                    class="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all"
                    title="Voir détails"
                  >
                    <i class="fas fa-eye"></i>
                  </button>

                  <button
                    (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                    class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all"
                    title="Modifier"
                  >
                    <i class="fas fa-edit"></i>
                  </button>

                  <button
                    (click)="equipe._id && deleteEquipe(equipe._id)"
                    class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all"
                    title="Supprimer"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty state -->
    <div *ngIf="!loading && !error && equipes.length === 0" class="text-center py-16">
      <div class="w-20 h-20 mx-auto mb-6 text-gray-400 dark:text-gray-500">
        <i class="fas fa-users text-5xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Aucune équipe trouvée</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">Commencez par créer une nouvelle équipe</p>
      <button
        (click)="navigateToAddEquipe()"
        class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors shadow-sm"
      >
        <i class="fas fa-plus-circle mr-2"></i>
        Créer une équipe
      </button>
    </div>
  </div>
</div>
