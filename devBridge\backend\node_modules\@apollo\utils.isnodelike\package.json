{"name": "@apollo/utils.isnodelike", "version": "2.0.1", "description": "Node environment detection utility", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/isNodeLike/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}}