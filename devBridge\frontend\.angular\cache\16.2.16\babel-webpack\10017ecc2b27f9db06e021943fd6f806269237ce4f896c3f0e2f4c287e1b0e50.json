{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFirstDayOfMonth} function options.\n */\n\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the first day of a month\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport function isFirstDayOfMonth(date, options) {\n  return toDate(date, options?.in).getDate() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isFirstDayOfMonth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}