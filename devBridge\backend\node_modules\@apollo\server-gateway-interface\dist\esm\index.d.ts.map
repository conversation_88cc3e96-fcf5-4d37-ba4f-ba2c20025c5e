{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,KAAK,EACV,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,qBAAqB,EACrB,aAAa,EACb,uBAAuB,EACxB,MAAM,SAAS,CAAC;AACjB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAE5D,MAAM,WAAW,gBAAgB;IAC/B,oBAAoB,CAClB,QAAQ,EAAE,iCAAiC,GAC1C,mBAAmB,CAAC;IACvB,IAAI,CAAC,OAAO,EAAE;QAAE,MAAM,EAAE,mBAAmB,CAAA;KAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC3E,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;CACvB;AAED,MAAM,MAAM,iCAAiC,GAAG,CAAC,aAAa,EAAE;IAC9D,SAAS,EAAE,aAAa,CAAC;IACzB,iBAAiB,EAAE,MAAM,CAAC;CAC3B,KAAK,IAAI,CAAC;AAEX,MAAM,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC;AAE7C,MAAM,WAAW,mBAAmB;IAClC,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,eAAe,GAAG,IAAI,CAAC;CAClC;AAED,MAAM,MAAM,eAAe,GAAG,CAC5B,cAAc,EAAE,4BAA4B,KACzC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAErC,MAAM,MAAM,sBAAsB,GAAG,eAAe,CAClD,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACnB,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CACpB,CAAC;AAIF,MAAM,WAAW,4BAA4B,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAC1E,QAAQ,CAAC,OAAO,EAAE,qBAAqB,CAAC;IACxC,QAAQ,CAAC,QAAQ,CAAC,EAAE,sBAAsB,CAAC;IAC3C,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC;IAC/B,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;IACvC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC3B,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;IAC9B,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;IAChC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACtC,QAAQ,CAAC,SAAS,EAAE,uBAAuB,CAAC;IAC5C,QAAQ,CAAC,MAAM,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;IAC9C,QAAQ,CAAC,OAAO,EAAE,4BAA4B,CAAC;IAC/C,KAAK,CAAC,EAAE,OAAO,CAAC;IAQhB,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC;IAGjC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;CACrC;AAED,MAAM,WAAW,qBAAqB;IACpC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,EAAE,kBAAkB,CAAC;CAC3B;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;CAClC;AAED,MAAM,WAAW,sBAAsB;IACrC,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;IAClC,MAAM,CAAC,EAAE,aAAa,CAAC,qBAAqB,CAAC,CAAC;IAC9C,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,EAAE,mBAAmB,CAAC;CAC5B;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG;IAAE,WAAW,EAAE,YAAY,CAAA;CAAE,CAAC;AAEvE,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;CAC5B;AAED,MAAM,WAAW,4BAA4B;IAC3C,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/B,cAAc,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC;IACrC,iBAAiB,CAAC,EAAE,gBAAgB,EAAE,CAAC;CACxC;AAED,MAAM,WAAW,kBAAmB,SAAQ,gBAAgB;IAC1D,OAAO,CAAC,IAAI,EAAE,gBAAgB,GAAG,IAAI,CAAC;IACtC,QAAQ,CAAC,IAAI,EAAE,gBAAgB,GAAG,IAAI,CAAC;IACvC,iBAAiB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;CACxD;AAED,MAAM,WAAW,gBAAgB;IAC/B,MAAM,CAAC,EAAE,MAAM,CAAC;IAOhB,KAAK,CAAC,EAAE,GAAG,CAAC;CACb"}