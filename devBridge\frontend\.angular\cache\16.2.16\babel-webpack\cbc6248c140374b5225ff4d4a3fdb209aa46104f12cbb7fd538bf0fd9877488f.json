{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nexport let KanbanBoardComponent = class KanbanBoardComponent {\n  constructor(taskService, dialog, snackBar) {\n    this.taskService = taskService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.filters = {};\n    this.taskSelected = new EventEmitter();\n    this.taskCreated = new EventEmitter();\n    this.kanbanData = null;\n    this.loading = false;\n    this.error = null;\n    // Configuration des colonnes Kanban\n    this.columns = [{\n      id: 'backlog',\n      title: 'Backlog',\n      color: '#6b7280',\n      icon: 'inventory_2'\n    }, {\n      id: 'todo',\n      title: 'À faire',\n      color: '#3b82f6',\n      icon: 'assignment'\n    }, {\n      id: 'in-progress',\n      title: 'En cours',\n      color: '#f59e0b',\n      icon: 'play_circle'\n    }, {\n      id: 'review',\n      title: 'Révision',\n      color: '#8b5cf6',\n      icon: 'rate_review'\n    }, {\n      id: 'testing',\n      title: 'Tests',\n      color: '#06b6d4',\n      icon: 'bug_report'\n    }, {\n      id: 'done',\n      title: 'Terminé',\n      color: '#10b981',\n      icon: 'check_circle'\n    }];\n  }\n  ngOnInit() {\n    this.loadKanbanBoard();\n  }\n  // Charger le tableau Kanban\n  loadKanbanBoard() {\n    if (!this.teamId) return;\n    this.loading = true;\n    this.error = null;\n    this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({\n      next: data => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Erreur lors du chargement du tableau Kanban';\n        this.loading = false;\n        console.error('Erreur Kanban:', error);\n        this.snackBar.open('Erreur lors du chargement', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Gérer le drag & drop\n  onTaskDrop(event) {\n    const task = event.item.data;\n    const newStatus = event.container.id;\n    const oldStatus = event.previousContainer.id;\n    if (event.previousContainer === event.container) {\n      // Réorganisation dans la même colonne\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      this.updateTaskPosition(task, event.currentIndex);\n    } else {\n      // Déplacement vers une autre colonne\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);\n    }\n  }\n  // Déplacer une tâche vers une nouvelle colonne\n  moveTaskToColumn(task, newStatus, newPosition, oldStatus) {\n    const moveRequest = {\n      newStatus,\n      newPosition,\n      oldStatus\n    };\n    this.taskService.moveTask(task._id, moveRequest).subscribe({\n      next: response => {\n        this.snackBar.open('Tâche déplacée avec succès', 'Fermer', {\n          duration: 2000\n        });\n        // Mettre à jour les statistiques\n        this.updateStats();\n      },\n      error: error => {\n        console.error('Erreur déplacement tâche:', error);\n        this.snackBar.open('Erreur lors du déplacement', 'Fermer', {\n          duration: 3000\n        });\n        // Annuler le déplacement visuel\n        this.loadKanbanBoard();\n      }\n    });\n  }\n  // Mettre à jour la position d'une tâche\n  updateTaskPosition(task, newPosition) {\n    const moveRequest = {\n      newStatus: task.status,\n      newPosition,\n      oldStatus: task.status\n    };\n    this.taskService.moveTask(task._id, moveRequest).subscribe({\n      error: error => {\n        console.error('Erreur mise à jour position:', error);\n        this.loadKanbanBoard(); // Recharger en cas d'erreur\n      }\n    });\n  }\n  // Créer une nouvelle tâche dans une colonne\n  createTaskInColumn(status) {\n    const taskData = {\n      title: 'Nouvelle tâche',\n      description: '',\n      status,\n      priority: 'medium',\n      category: 'task'\n    };\n    this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({\n      next: response => {\n        this.taskCreated.emit(response.task);\n        this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche\n        this.snackBar.open('Tâche créée avec succès', 'Fermer', {\n          duration: 2000\n        });\n      },\n      error: error => {\n        console.error('Erreur création tâche:', error);\n        this.snackBar.open('Erreur lors de la création', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Sélectionner une tâche\n  selectTask(task) {\n    this.taskSelected.emit(task);\n  }\n  // Obtenir les tâches d'une colonne\n  getColumnTasks(columnId) {\n    if (!this.kanbanData) return [];\n    return this.kanbanData.kanbanBoard[columnId] || [];\n  }\n  // Obtenir la couleur de priorité\n  getPriorityColor(priority) {\n    const colors = {\n      'lowest': '#6b7280',\n      'low': '#3b82f6',\n      'medium': '#f59e0b',\n      'high': '#ef4444',\n      'highest': '#dc2626',\n      'critical': '#991b1b'\n    };\n    return colors[priority] || '#6b7280';\n  }\n  // Obtenir l'icône de catégorie\n  getCategoryIcon(category) {\n    const icons = {\n      'feature': 'new_releases',\n      'bug': 'bug_report',\n      'improvement': 'trending_up',\n      'task': 'assignment',\n      'epic': 'flag',\n      'story': 'book'\n    };\n    return icons[category] || 'assignment';\n  }\n  // Mettre à jour les statistiques\n  updateStats() {\n    if (this.kanbanData) {\n      // Recalculer les statistiques localement\n      const tasks = Object.values(this.kanbanData.kanbanBoard).flat();\n      this.kanbanData.stats.total = tasks.length;\n      this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;\n      this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;\n      // Mettre à jour les statistiques par statut\n      Object.keys(this.kanbanData.kanbanBoard).forEach(status => {\n        const columnTasks = this.kanbanData.kanbanBoard[status].kanbanBoard;\n        this.kanbanData.stats.byStatus[status] = columnTasks ? columnTasks.length : 0;\n      });\n    }\n  }\n  // Appliquer des filtres\n  applyFilters(newFilters) {\n    this.filters = {\n      ...this.filters,\n      ...newFilters\n    };\n    this.loadKanbanBoard();\n  }\n  // Réinitialiser les filtres\n  resetFilters() {\n    this.filters = {};\n    this.loadKanbanBoard();\n  }\n  // Actualiser le tableau\n  refresh() {\n    this.loadKanbanBoard();\n  }\n  // Obtenir le nombre de tâches dans une colonne\n  getColumnCount(columnId) {\n    return this.getColumnTasks(columnId).length;\n  }\n  // Vérifier si une colonne est vide\n  isColumnEmpty(columnId) {\n    return this.getColumnTasks(columnId).length === 0;\n  }\n  // Obtenir le texte de placeholder pour une colonne vide\n  getEmptyColumnText(columnId) {\n    const texts = {\n      'backlog': 'Aucune tâche en attente',\n      'todo': 'Aucune tâche à faire',\n      'in-progress': 'Aucune tâche en cours',\n      'review': 'Aucune tâche en révision',\n      'testing': 'Aucune tâche en test',\n      'done': 'Aucune tâche terminée'\n    };\n    return texts[columnId] || 'Aucune tâche';\n  }\n};\n__decorate([Input()], KanbanBoardComponent.prototype, \"teamId\", void 0);\n__decorate([Input()], KanbanBoardComponent.prototype, \"filters\", void 0);\n__decorate([Output()], KanbanBoardComponent.prototype, \"taskSelected\", void 0);\n__decorate([Output()], KanbanBoardComponent.prototype, \"taskCreated\", void 0);\nKanbanBoardComponent = __decorate([Component({\n  selector: 'app-kanban-board',\n  templateUrl: './kanban-board.component.html',\n  styleUrls: ['./kanban-board.component.scss']\n})], KanbanBoardComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "moveItemInArray", "transferArrayItem", "KanbanBoardComponent", "constructor", "taskService", "dialog", "snackBar", "filters", "taskSelected", "taskCreated", "kanbanData", "loading", "error", "columns", "id", "title", "color", "icon", "ngOnInit", "loadKanbanBoard", "teamId", "getKanbanBoard", "subscribe", "next", "data", "console", "open", "duration", "onTaskDrop", "event", "task", "item", "newStatus", "container", "oldStatus", "previousContainer", "previousIndex", "currentIndex", "updateTaskPosition", "moveTaskToColumn", "newPosition", "moveRequest", "moveTask", "_id", "response", "updateStats", "status", "createTaskInColumn", "taskData", "description", "priority", "category", "emit", "selectTask", "getColumnTasks", "columnId", "kanbanBoard", "getPriorityColor", "colors", "getCategoryIcon", "icons", "tasks", "Object", "values", "flat", "stats", "total", "length", "overdue", "filter", "isOverdue", "blocked", "isBlocked", "keys", "for<PERSON>ach", "columnTasks", "byStatus", "applyFilters", "newFilters", "resetFilters", "refresh", "getColumnCount", "isColumnEmpty", "getEmptyColumnText", "texts", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\components\\kanban-board\\kanban-board.component.ts"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\nimport { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { TaskService } from '../../services/task.service';\nimport {\n  Task,\n  KanbanBoard,\n  KanbanFilters,\n  MoveTaskRequest,\n  CreateTaskRequest\n} from '../../models/task.model';\n\n@Component({\n  selector: 'app-kanban-board',\n  templateUrl: './kanban-board.component.html',\n  styleUrls: ['./kanban-board.component.scss']\n})\nexport class KanbanBoardComponent implements OnInit {\n  @Input() teamId!: string;\n  @Input() filters: KanbanFilters = {};\n  @Output() taskSelected = new EventEmitter<Task>();\n  @Output() taskCreated = new EventEmitter<Task>();\n\n  kanbanData: KanbanBoard | null = null;\n  loading = false;\n  error: string | null = null;\n\n  // Configuration des colonnes Kanban\n  columns = [\n    { id: 'backlog', title: 'Backlog', color: '#6b7280', icon: 'inventory_2' },\n    { id: 'todo', title: 'À faire', color: '#3b82f6', icon: 'assignment' },\n    { id: 'in-progress', title: 'En cours', color: '#f59e0b', icon: 'play_circle' },\n    { id: 'review', title: 'Révision', color: '#8b5cf6', icon: 'rate_review' },\n    { id: 'testing', title: 'Tests', color: '#06b6d4', icon: 'bug_report' },\n    { id: 'done', title: 'Terminé', color: '#10b981', icon: 'check_circle' }\n  ];\n\n  constructor(\n    private taskService: TaskService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadKanbanBoard();\n  }\n\n  // Charger le tableau Kanban\n  loadKanbanBoard(): void {\n    if (!this.teamId) return;\n\n    this.loading = true;\n    this.error = null;\n\n    this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({\n      next: (data) => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Erreur lors du chargement du tableau Kanban';\n        this.loading = false;\n        console.error('Erreur Kanban:', error);\n        this.snackBar.open('Erreur lors du chargement', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Gérer le drag & drop\n  onTaskDrop(event: CdkDragDrop<Task[]>): void {\n    const task = event.item.data as Task;\n    const newStatus = event.container.id;\n    const oldStatus = event.previousContainer.id;\n\n    if (event.previousContainer === event.container) {\n      // Réorganisation dans la même colonne\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      this.updateTaskPosition(task, event.currentIndex);\n    } else {\n      // Déplacement vers une autre colonne\n      transferArrayItem(\n        event.previousContainer.data,\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n      this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);\n    }\n  }\n\n  // Déplacer une tâche vers une nouvelle colonne\n  private moveTaskToColumn(task: Task, newStatus: string, newPosition: number, oldStatus: string): void {\n    const moveRequest: MoveTaskRequest = {\n      newStatus,\n      newPosition,\n      oldStatus\n    };\n\n    this.taskService.moveTask(task._id!, moveRequest).subscribe({\n      next: (response) => {\n        this.snackBar.open('Tâche déplacée avec succès', 'Fermer', { duration: 2000 });\n        // Mettre à jour les statistiques\n        this.updateStats();\n      },\n      error: (error) => {\n        console.error('Erreur déplacement tâche:', error);\n        this.snackBar.open('Erreur lors du déplacement', 'Fermer', { duration: 3000 });\n        // Annuler le déplacement visuel\n        this.loadKanbanBoard();\n      }\n    });\n  }\n\n  // Mettre à jour la position d'une tâche\n  private updateTaskPosition(task: Task, newPosition: number): void {\n    const moveRequest: MoveTaskRequest = {\n      newStatus: task.status,\n      newPosition,\n      oldStatus: task.status\n    };\n\n    this.taskService.moveTask(task._id!, moveRequest).subscribe({\n      error: (error) => {\n        console.error('Erreur mise à jour position:', error);\n        this.loadKanbanBoard(); // Recharger en cas d'erreur\n      }\n    });\n  }\n\n  // Créer une nouvelle tâche dans une colonne\n  createTaskInColumn(status: string): void {\n    const taskData: CreateTaskRequest = {\n      title: 'Nouvelle tâche',\n      description: '',\n      status,\n      priority: 'medium',\n      category: 'task'\n    };\n\n    this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({\n      next: (response) => {\n        this.taskCreated.emit(response.task);\n        this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche\n        this.snackBar.open('Tâche créée avec succès', 'Fermer', { duration: 2000 });\n      },\n      error: (error) => {\n        console.error('Erreur création tâche:', error);\n        this.snackBar.open('Erreur lors de la création', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Sélectionner une tâche\n  selectTask(task: Task): void {\n    this.taskSelected.emit(task);\n  }\n\n  // Obtenir les tâches d'une colonne\n  getColumnTasks(columnId: string): Task[] {\n    if (!this.kanbanData) return [];\n    return this.kanbanData.kanbanBoard[columnId as keyof typeof this.kanbanData.kanbanBoard] || [];\n  }\n\n  // Obtenir la couleur de priorité\n  getPriorityColor(priority: string): string {\n    const colors = {\n      'lowest': '#6b7280',\n      'low': '#3b82f6',\n      'medium': '#f59e0b',\n      'high': '#ef4444',\n      'highest': '#dc2626',\n      'critical': '#991b1b'\n    };\n    return colors[priority as keyof typeof colors] || '#6b7280';\n  }\n\n  // Obtenir l'icône de catégorie\n  getCategoryIcon(category: string): string {\n    const icons = {\n      'feature': 'new_releases',\n      'bug': 'bug_report',\n      'improvement': 'trending_up',\n      'task': 'assignment',\n      'epic': 'flag',\n      'story': 'book'\n    };\n    return icons[category as keyof typeof icons] || 'assignment';\n  }\n\n  // Mettre à jour les statistiques\n  private updateStats(): void {\n    if (this.kanbanData) {\n      // Recalculer les statistiques localement\n      const tasks = Object.values(this.kanbanData.kanbanBoard).flat();\n      this.kanbanData.stats.total = tasks.length;\n      this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;\n      this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;\n\n      // Mettre à jour les statistiques par statut\n      Object.keys(this.kanbanData.kanbanBoard).forEach(status => {\n        const columnTasks = this.kanbanData!.kanbanBoard[status as keyof typeof this.kanbanData!.kanbanBoard];\n        this.kanbanData!.stats.byStatus[status] = columnTasks ? columnTasks.length : 0;\n      });\n    }\n  }\n\n  // Appliquer des filtres\n  applyFilters(newFilters: KanbanFilters): void {\n    this.filters = { ...this.filters, ...newFilters };\n    this.loadKanbanBoard();\n  }\n\n  // Réinitialiser les filtres\n  resetFilters(): void {\n    this.filters = {};\n    this.loadKanbanBoard();\n  }\n\n  // Actualiser le tableau\n  refresh(): void {\n    this.loadKanbanBoard();\n  }\n\n  // Obtenir le nombre de tâches dans une colonne\n  getColumnCount(columnId: string): number {\n    return this.getColumnTasks(columnId).length;\n  }\n\n  // Vérifier si une colonne est vide\n  isColumnEmpty(columnId: string): boolean {\n    return this.getColumnTasks(columnId).length === 0;\n  }\n\n  // Obtenir le texte de placeholder pour une colonne vide\n  getEmptyColumnText(columnId: string): string {\n    const texts = {\n      'backlog': 'Aucune tâche en attente',\n      'todo': 'Aucune tâche à faire',\n      'in-progress': 'Aucune tâche en cours',\n      'review': 'Aucune tâche en révision',\n      'testing': 'Aucune tâche en test',\n      'done': 'Aucune tâche terminée'\n    };\n    return texts[columnId as keyof typeof texts] || 'Aucune tâche';\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,KAAK,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AAC9E,SAAsBC,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;AAiBjF,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAoB/BC,YACUC,WAAwB,EACxBC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IArBT,KAAAC,OAAO,GAAkB,EAAE;IAC1B,KAAAC,YAAY,GAAG,IAAIT,YAAY,EAAQ;IACvC,KAAAU,WAAW,GAAG,IAAIV,YAAY,EAAQ;IAEhD,KAAAW,UAAU,GAAuB,IAAI;IACrC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAC,OAAO,GAAG,CACR;MAAEC,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAa,CAAE,EAC1E;MAAEH,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAY,CAAE,EACtE;MAAEH,EAAE,EAAE,aAAa;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAa,CAAE,EAC/E;MAAEH,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAa,CAAE,EAC1E;MAAEH,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAY,CAAE,EACvE;MAAEH,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAc,CAAE,CACzE;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAA,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;IAElB,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACR,WAAW,CAACiB,cAAc,CAAC,IAAI,CAACD,MAAM,EAAE,IAAI,CAACb,OAAO,CAAC,CAACe,SAAS,CAAC;MACnEC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACd,UAAU,GAAGc,IAAI;QACtB,IAAI,CAACb,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,6CAA6C;QAC1D,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBc,OAAO,CAACb,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAACN,QAAQ,CAACoB,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/E;KACD,CAAC;EACJ;EAEA;EACAC,UAAUA,CAACC,KAA0B;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,IAAI,CAACP,IAAY;IACpC,MAAMQ,SAAS,GAAGH,KAAK,CAACI,SAAS,CAACnB,EAAE;IACpC,MAAMoB,SAAS,GAAGL,KAAK,CAACM,iBAAiB,CAACrB,EAAE;IAE5C,IAAIe,KAAK,CAACM,iBAAiB,KAAKN,KAAK,CAACI,SAAS,EAAE;MAC/C;MACAjC,eAAe,CAAC6B,KAAK,CAACI,SAAS,CAACT,IAAI,EAAEK,KAAK,CAACO,aAAa,EAAEP,KAAK,CAACQ,YAAY,CAAC;MAC9E,IAAI,CAACC,kBAAkB,CAACR,IAAI,EAAED,KAAK,CAACQ,YAAY,CAAC;KAClD,MAAM;MACL;MACApC,iBAAiB,CACf4B,KAAK,CAACM,iBAAiB,CAACX,IAAI,EAC5BK,KAAK,CAACI,SAAS,CAACT,IAAI,EACpBK,KAAK,CAACO,aAAa,EACnBP,KAAK,CAACQ,YAAY,CACnB;MACD,IAAI,CAACE,gBAAgB,CAACT,IAAI,EAAEE,SAAS,EAAEH,KAAK,CAACQ,YAAY,EAAEH,SAAS,CAAC;;EAEzE;EAEA;EACQK,gBAAgBA,CAACT,IAAU,EAAEE,SAAiB,EAAEQ,WAAmB,EAAEN,SAAiB;IAC5F,MAAMO,WAAW,GAAoB;MACnCT,SAAS;MACTQ,WAAW;MACXN;KACD;IAED,IAAI,CAAC9B,WAAW,CAACsC,QAAQ,CAACZ,IAAI,CAACa,GAAI,EAAEF,WAAW,CAAC,CAACnB,SAAS,CAAC;MAC1DC,IAAI,EAAGqB,QAAQ,IAAI;QACjB,IAAI,CAACtC,QAAQ,CAACoB,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E;QACA,IAAI,CAACkB,WAAW,EAAE;MACpB,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACfa,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACN,QAAQ,CAACoB,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E;QACA,IAAI,CAACR,eAAe,EAAE;MACxB;KACD,CAAC;EACJ;EAEA;EACQmB,kBAAkBA,CAACR,IAAU,EAAEU,WAAmB;IACxD,MAAMC,WAAW,GAAoB;MACnCT,SAAS,EAAEF,IAAI,CAACgB,MAAM;MACtBN,WAAW;MACXN,SAAS,EAAEJ,IAAI,CAACgB;KACjB;IAED,IAAI,CAAC1C,WAAW,CAACsC,QAAQ,CAACZ,IAAI,CAACa,GAAI,EAAEF,WAAW,CAAC,CAACnB,SAAS,CAAC;MAC1DV,KAAK,EAAGA,KAAK,IAAI;QACfa,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACO,eAAe,EAAE,CAAC,CAAC;MAC1B;KACD,CAAC;EACJ;EAEA;EACA4B,kBAAkBA,CAACD,MAAc;IAC/B,MAAME,QAAQ,GAAsB;MAClCjC,KAAK,EAAE,gBAAgB;MACvBkC,WAAW,EAAE,EAAE;MACfH,MAAM;MACNI,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;KACX;IAED,IAAI,CAAC/C,WAAW,CAAC2C,kBAAkB,CAAC,IAAI,CAAC3B,MAAM,EAAE4B,QAAQ,CAAC,CAAC1B,SAAS,CAAC;MACnEC,IAAI,EAAGqB,QAAQ,IAAI;QACjB,IAAI,CAACnC,WAAW,CAAC2C,IAAI,CAACR,QAAQ,CAACd,IAAI,CAAC;QACpC,IAAI,CAACX,eAAe,EAAE,CAAC,CAAC;QACxB,IAAI,CAACb,QAAQ,CAACoB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC7E,CAAC;MACDf,KAAK,EAAGA,KAAK,IAAI;QACfa,OAAO,CAACb,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACN,QAAQ,CAACoB,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAChF;KACD,CAAC;EACJ;EAEA;EACA0B,UAAUA,CAACvB,IAAU;IACnB,IAAI,CAACtB,YAAY,CAAC4C,IAAI,CAACtB,IAAI,CAAC;EAC9B;EAEA;EACAwB,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAAC,IAAI,CAAC7C,UAAU,EAAE,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACA,UAAU,CAAC8C,WAAW,CAACD,QAAoD,CAAC,IAAI,EAAE;EAChG;EAEA;EACAE,gBAAgBA,CAACP,QAAgB;IAC/B,MAAMQ,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE;KACb;IACD,OAAOA,MAAM,CAACR,QAA+B,CAAC,IAAI,SAAS;EAC7D;EAEA;EACAS,eAAeA,CAACR,QAAgB;IAC9B,MAAMS,KAAK,GAAG;MACZ,SAAS,EAAE,cAAc;MACzB,KAAK,EAAE,YAAY;MACnB,aAAa,EAAE,aAAa;MAC5B,MAAM,EAAE,YAAY;MACpB,MAAM,EAAE,MAAM;MACd,OAAO,EAAE;KACV;IACD,OAAOA,KAAK,CAACT,QAA8B,CAAC,IAAI,YAAY;EAC9D;EAEA;EACQN,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACnC,UAAU,EAAE;MACnB;MACA,MAAMmD,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACrD,UAAU,CAAC8C,WAAW,CAAC,CAACQ,IAAI,EAAE;MAC/D,IAAI,CAACtD,UAAU,CAACuD,KAAK,CAACC,KAAK,GAAGL,KAAK,CAACM,MAAM;MAC1C,IAAI,CAACzD,UAAU,CAACuD,KAAK,CAACG,OAAO,GAAGP,KAAK,CAACQ,MAAM,CAACvC,IAAI,IAAIA,IAAI,CAACwC,SAAS,CAAC,CAACH,MAAM;MAC3E,IAAI,CAACzD,UAAU,CAACuD,KAAK,CAACM,OAAO,GAAGV,KAAK,CAACQ,MAAM,CAACvC,IAAI,IAAIA,IAAI,CAAC0C,SAAS,CAAC,CAACL,MAAM;MAE3E;MACAL,MAAM,CAACW,IAAI,CAAC,IAAI,CAAC/D,UAAU,CAAC8C,WAAW,CAAC,CAACkB,OAAO,CAAC5B,MAAM,IAAG;QACxD,MAAM6B,WAAW,GAAG,IAAI,CAACjE,UAAW,CAAC8C,WAAW,CAACV,MAAuC,EAACU,WAAY;QACrG,IAAI,CAAC9C,UAAW,CAACuD,KAAK,CAACW,QAAQ,CAAC9B,MAAM,CAAC,GAAG6B,WAAW,GAAGA,WAAW,CAACR,MAAM,GAAG,CAAC;MAChF,CAAC,CAAC;;EAEN;EAEA;EACAU,YAAYA,CAACC,UAAyB;IACpC,IAAI,CAACvE,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAGuE;IAAU,CAAE;IACjD,IAAI,CAAC3D,eAAe,EAAE;EACxB;EAEA;EACA4D,YAAYA,CAAA;IACV,IAAI,CAACxE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACY,eAAe,EAAE;EACxB;EAEA;EACA6D,OAAOA,CAAA;IACL,IAAI,CAAC7D,eAAe,EAAE;EACxB;EAEA;EACA8D,cAAcA,CAAC1B,QAAgB;IAC7B,OAAO,IAAI,CAACD,cAAc,CAACC,QAAQ,CAAC,CAACY,MAAM;EAC7C;EAEA;EACAe,aAAaA,CAAC3B,QAAgB;IAC5B,OAAO,IAAI,CAACD,cAAc,CAACC,QAAQ,CAAC,CAACY,MAAM,KAAK,CAAC;EACnD;EAEA;EACAgB,kBAAkBA,CAAC5B,QAAgB;IACjC,MAAM6B,KAAK,GAAG;MACZ,SAAS,EAAE,yBAAyB;MACpC,MAAM,EAAE,sBAAsB;MAC9B,aAAa,EAAE,uBAAuB;MACtC,QAAQ,EAAE,0BAA0B;MACpC,SAAS,EAAE,sBAAsB;MACjC,MAAM,EAAE;KACT;IACD,OAAOA,KAAK,CAAC7B,QAA8B,CAAC,IAAI,cAAc;EAChE;CACD;AAnOU8B,UAAA,EAARxF,KAAK,EAAE,C,mDAAiB;AAChBwF,UAAA,EAARxF,KAAK,EAAE,C,oDAA6B;AAC3BwF,UAAA,EAATvF,MAAM,EAAE,C,yDAAyC;AACxCuF,UAAA,EAATvF,MAAM,EAAE,C,wDAAwC;AAJtCI,oBAAoB,GAAAmF,UAAA,EALhCzF,SAAS,CAAC;EACT0F,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWtF,oBAAoB,CAoOhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}