{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { format } from \"./format.js\";\n\n/**\n * The {@link formatRelative} function options.\n */\n\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param date - The date to format\n * @param baseDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The date in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(subDays(new Date(), 6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport function formatRelative(date, baseDate, options) {\n  const [date_, baseDate_] = normalizeDates(options?.in, date, baseDate);\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  const diff = differenceInCalendarDays(date_, baseDate_);\n  if (isNaN(diff)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  let token;\n  if (diff < -6) {\n    token = \"other\";\n  } else if (diff < -1) {\n    token = \"lastWeek\";\n  } else if (diff < 0) {\n    token = \"yesterday\";\n  } else if (diff < 1) {\n    token = \"today\";\n  } else if (diff < 2) {\n    token = \"tomorrow\";\n  } else if (diff < 7) {\n    token = \"nextWeek\";\n  } else {\n    token = \"other\";\n  }\n  const formatStr = locale.formatRelative(token, date_, baseDate_, {\n    locale,\n    weekStartsOn\n  });\n  return format(date_, formatStr, {\n    locale,\n    weekStartsOn\n  });\n}\n\n// Fallback for modularized imports:\nexport default formatRelative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}