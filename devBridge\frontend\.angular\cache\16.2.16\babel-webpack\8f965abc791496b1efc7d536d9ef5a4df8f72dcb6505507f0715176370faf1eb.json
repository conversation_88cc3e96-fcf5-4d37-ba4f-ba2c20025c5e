{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique directive names\n *\n * A GraphQL document is only valid if all defined directives have unique names.\n */\nexport function UniqueDirectiveNamesRule(context) {\n  const knownDirectiveNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    DirectiveDefinition(node) {\n      const directiveName = node.name.value;\n      if (schema !== null && schema !== void 0 && schema.getDirective(directiveName)) {\n        context.reportError(new GraphQLError(`Directive \"@${directiveName}\" already exists in the schema. It cannot be redefined.`, {\n          nodes: node.name\n        }));\n        return;\n      }\n      if (knownDirectiveNames[directiveName]) {\n        context.reportError(new GraphQLError(`There can be only one directive named \"@${directiveName}\".`, {\n          nodes: [knownDirectiveNames[directiveName], node.name]\n        }));\n      } else {\n        knownDirectiveNames[directiveName] = node.name;\n      }\n      return false;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}