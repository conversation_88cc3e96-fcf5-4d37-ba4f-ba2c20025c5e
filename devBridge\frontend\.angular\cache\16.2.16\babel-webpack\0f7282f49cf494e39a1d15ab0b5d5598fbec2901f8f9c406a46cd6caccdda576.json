{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\n/**\n * Returns an operation AST given a document AST and optionally an operation\n * name. If a name is not provided, an operation is only returned if only one is\n * provided in the document.\n */\n\nexport function getOperationAST(documentAST, operationName) {\n  let operation = null;\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === Kind.OPERATION_DEFINITION) {\n      var _definition$name;\n      if (operationName == null) {\n        // If no operation name was provided, only return an Operation if there\n        // is one defined in the document. Upon encountering the second, return\n        // null.\n        if (operation) {\n          return null;\n        }\n        operation = definition;\n      } else if (((_definition$name = definition.name) === null || _definition$name === void 0 ? void 0 : _definition$name.value) === operationName) {\n        return definition;\n      }\n    }\n  }\n  return operation;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}