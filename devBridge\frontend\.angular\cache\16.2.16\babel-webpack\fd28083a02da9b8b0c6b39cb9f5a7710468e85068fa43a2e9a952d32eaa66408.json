{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isToday} function options.\n */\n\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is today\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\nexport function isToday(date, options) {\n  return isSameDay(constructFrom(options?.in || date, date), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isToday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}