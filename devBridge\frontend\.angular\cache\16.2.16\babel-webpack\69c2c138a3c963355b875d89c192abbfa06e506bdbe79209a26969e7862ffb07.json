{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/task.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"@angular/cdk/drag-drop\";\nfunction KanbanBoardComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6, \"En retard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.kanbanData.stats.overdue);\n  }\n}\nfunction KanbanBoardComponent_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"block\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6, \"Bloqu\\u00E9es\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.kanbanData.stats.blocked);\n  }\n}\nfunction KanbanBoardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 9);\n    i0.ɵɵtext(8, \"Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, KanbanBoardComponent_div_1_div_9_Template, 7, 1, \"div\", 10);\n    i0.ɵɵtemplate(10, KanbanBoardComponent_div_1_div_10_Template, 7, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.refresh());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 14)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"filter_list\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.kanbanData.stats.total);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.kanbanData.stats.overdue > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.kanbanData.stats.blocked > 0);\n  }\n}\nfunction KanbanBoardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"mat-spinner\", 18);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement du tableau Kanban...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction KanbanBoardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\", 20);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.loadKanbanBoard());\n    });\n    i0.ɵɵtext(6, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 54);\n    i0.ɵɵtext(1, \" schedule \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1, \" block \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_p_14_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_p_14_span_3_Template, 2, 0, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 2, task_r14.description, 0, 100), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.description && task_r14.description.length > 100);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r30 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", label_r30.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", label_r30.name, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", task_r14.labels.length - 3, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_15_span_1_Template, 2, 3, \"span\", 59);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_div_15_span_3_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 2, task_r14.labels, 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.labels && task_r14.labels.length > 3);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 68);\n  }\n  if (rf & 2) {\n    const assignee_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"src\", ctx_r36.getAssigneeImage(assignee_r35), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r36.getAssigneeName(assignee_r35));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.getAssigneeInitials(assignee_r35), \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_img_1_Template, 1, 2, \"img\", 67);\n    i0.ɵɵtemplate(2, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_span_2_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r35 = ctx.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r33.getAssigneeName(assignee_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.hasAssigneeImage(assignee_r35));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.hasAssigneeImage(assignee_r35));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", task_r14.assignedTo.length - 3, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_Template, 3, 3, \"div\", 64);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, KanbanBoardComponent_div_4_div_1_div_13_div_17_span_3_Template, 2, 1, \"span\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 2, task_r14.assignedTo, 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.assignedTo && task_r14.assignedTo.length > 3);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"overdue\", task_r14.isOverdue);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 3, task_r14.dueDate, \"dd/MM\"), \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.estimatedHours, \"h \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"list\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.subtasks.length, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r14.comments.length, \" \");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions == null ? null : task_r14.aiSuggestions.suggestions.length) || 0);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const task_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r14.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", task_r14.category, \" \\u2022 \", task_r14.priority, \"\");\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_div_13_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r49);\n      const task_r14 = restoredCtx.$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.selectTask(task_r14));\n    });\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵelementStart(2, \"div\", 34)(3, \"div\", 35)(4, \"div\", 36)(5, \"mat-icon\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵtemplate(10, KanbanBoardComponent_div_4_div_1_div_13_mat_icon_10_Template, 2, 0, \"mat-icon\", 40);\n    i0.ɵɵtemplate(11, KanbanBoardComponent_div_4_div_1_div_13_mat_icon_11_Template, 2, 0, \"mat-icon\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"h4\", 42);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, KanbanBoardComponent_div_4_div_1_div_13_p_14_Template, 4, 6, \"p\", 43);\n    i0.ɵɵtemplate(15, KanbanBoardComponent_div_4_div_1_div_13_div_15_Template, 4, 6, \"div\", 44);\n    i0.ɵɵelementStart(16, \"div\", 45);\n    i0.ɵɵtemplate(17, KanbanBoardComponent_div_4_div_1_div_13_div_17_Template, 4, 6, \"div\", 46);\n    i0.ɵɵelementStart(18, \"div\", 47);\n    i0.ɵɵtemplate(19, KanbanBoardComponent_div_4_div_1_div_13_span_19_Template, 5, 6, \"span\", 48);\n    i0.ɵɵtemplate(20, KanbanBoardComponent_div_4_div_1_div_13_span_20_Template, 4, 1, \"span\", 49);\n    i0.ɵɵtemplate(21, KanbanBoardComponent_div_4_div_1_div_13_span_21_Template, 4, 1, \"span\", 50);\n    i0.ɵɵtemplate(22, KanbanBoardComponent_div_4_div_1_div_13_span_22_Template, 4, 1, \"span\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, KanbanBoardComponent_div_4_div_1_div_13_div_23_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, KanbanBoardComponent_div_4_div_1_div_13_div_24_Template, 6, 3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r14 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"cdkDragData\", task_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r12.getPriorityColor(task_r14.priority));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r12.getCategoryIcon(task_r14.category || \"task\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", task_r14._id == null ? null : task_r14._id.slice(-6), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.isOverdue);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.isBlocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(task_r14.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.labels && task_r14.labels.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.assignedTo && task_r14.assignedTo.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r14.dueDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.estimatedHours);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.subtasks && task_r14.subtasks.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r14.comments && task_r14.comments.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions) && ((task_r14.aiSuggestions == null ? null : task_r14.aiSuggestions.suggestions == null ? null : task_r14.aiSuggestions.suggestions.length) || 0) > 0);\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_div_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const column_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.createTaskInColumn(column_r11.id));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Ajouter une t\\u00E2che \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const column_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(column_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.getEmptyColumnText(column_r11.id));\n  }\n}\nfunction KanbanBoardComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function KanbanBoardComponent_div_4_div_1_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const column_r11 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.createTaskInColumn(column_r11.id));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"add\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 29);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function KanbanBoardComponent_div_4_div_1_Template_div_cdkDropListDropped_12_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onTaskDrop($event));\n    });\n    i0.ɵɵtemplate(13, KanbanBoardComponent_div_4_div_1_div_13_Template, 25, 16, \"div\", 30);\n    i0.ɵɵtemplate(14, KanbanBoardComponent_div_4_div_1_div_14_Template, 9, 2, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const column_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-top-color\", column_r11.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", column_r11.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(column_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(column_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r10.getColumnCount(column_r11.id), \")\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"id\", column_r11.id)(\"cdkDropListData\", ctx_r10.getColumnTasks(column_r11.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getColumnTasks(column_r11.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isColumnEmpty(column_r11.id));\n  }\n}\nfunction KanbanBoardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, KanbanBoardComponent_div_4_div_1_Template, 15, 11, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.columns);\n  }\n}\nexport class KanbanBoardComponent {\n  constructor(taskService, dialog, snackBar) {\n    this.taskService = taskService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.filters = {};\n    this.taskSelected = new EventEmitter();\n    this.taskCreated = new EventEmitter();\n    this.kanbanData = null;\n    this.loading = false;\n    this.error = null;\n    // Configuration des colonnes Kanban\n    this.columns = [{\n      id: 'backlog',\n      title: 'Backlog',\n      color: '#6b7280',\n      icon: 'inventory_2'\n    }, {\n      id: 'todo',\n      title: 'À faire',\n      color: '#3b82f6',\n      icon: 'assignment'\n    }, {\n      id: 'in-progress',\n      title: 'En cours',\n      color: '#f59e0b',\n      icon: 'play_circle'\n    }, {\n      id: 'review',\n      title: 'Révision',\n      color: '#8b5cf6',\n      icon: 'rate_review'\n    }, {\n      id: 'testing',\n      title: 'Tests',\n      color: '#06b6d4',\n      icon: 'bug_report'\n    }, {\n      id: 'done',\n      title: 'Terminé',\n      color: '#10b981',\n      icon: 'check_circle'\n    }];\n  }\n  ngOnInit() {\n    this.loadKanbanBoard();\n  }\n  // Charger le tableau Kanban\n  loadKanbanBoard() {\n    if (!this.teamId) return;\n    this.loading = true;\n    this.error = null;\n    this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({\n      next: data => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = 'Erreur lors du chargement du tableau Kanban';\n        this.loading = false;\n        console.error('Erreur Kanban:', error);\n        this.snackBar.open('Erreur lors du chargement', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Gérer le drag & drop\n  onTaskDrop(event) {\n    const task = event.item.data;\n    const newStatus = event.container.id;\n    const oldStatus = event.previousContainer.id;\n    if (event.previousContainer === event.container) {\n      // Réorganisation dans la même colonne\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      this.updateTaskPosition(task, event.currentIndex);\n    } else {\n      // Déplacement vers une autre colonne\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);\n    }\n  }\n  // Déplacer une tâche vers une nouvelle colonne\n  moveTaskToColumn(task, newStatus, newPosition, oldStatus) {\n    const moveRequest = {\n      newStatus,\n      newPosition,\n      oldStatus\n    };\n    this.taskService.moveTask(task._id, moveRequest).subscribe({\n      next: response => {\n        this.snackBar.open('Tâche déplacée avec succès', 'Fermer', {\n          duration: 2000\n        });\n        // Mettre à jour les statistiques\n        this.updateStats();\n      },\n      error: error => {\n        console.error('Erreur déplacement tâche:', error);\n        this.snackBar.open('Erreur lors du déplacement', 'Fermer', {\n          duration: 3000\n        });\n        // Annuler le déplacement visuel\n        this.loadKanbanBoard();\n      }\n    });\n  }\n  // Mettre à jour la position d'une tâche\n  updateTaskPosition(task, newPosition) {\n    const moveRequest = {\n      newStatus: task.status,\n      newPosition,\n      oldStatus: task.status\n    };\n    this.taskService.moveTask(task._id, moveRequest).subscribe({\n      error: error => {\n        console.error('Erreur mise à jour position:', error);\n        this.loadKanbanBoard(); // Recharger en cas d'erreur\n      }\n    });\n  }\n  // Créer une nouvelle tâche dans une colonne\n  createTaskInColumn(status) {\n    const taskData = {\n      title: 'Nouvelle tâche',\n      description: '',\n      status,\n      priority: 'medium',\n      category: 'task'\n    };\n    this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({\n      next: response => {\n        this.taskCreated.emit(response.task);\n        this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche\n        this.snackBar.open('Tâche créée avec succès', 'Fermer', {\n          duration: 2000\n        });\n      },\n      error: error => {\n        console.error('Erreur création tâche:', error);\n        this.snackBar.open('Erreur lors de la création', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Sélectionner une tâche\n  selectTask(task) {\n    this.taskSelected.emit(task);\n  }\n  // Obtenir les tâches d'une colonne\n  getColumnTasks(columnId) {\n    if (!this.kanbanData) return [];\n    return this.kanbanData.kanbanBoard[columnId] || [];\n  }\n  // Obtenir la couleur de priorité\n  getPriorityColor(priority) {\n    const colors = {\n      'lowest': '#6b7280',\n      'low': '#3b82f6',\n      'medium': '#f59e0b',\n      'high': '#ef4444',\n      'highest': '#dc2626',\n      'critical': '#991b1b'\n    };\n    return colors[priority] || '#6b7280';\n  }\n  // Obtenir l'icône de catégorie\n  getCategoryIcon(category) {\n    const icons = {\n      'feature': 'new_releases',\n      'bug': 'bug_report',\n      'improvement': 'trending_up',\n      'task': 'assignment',\n      'epic': 'flag',\n      'story': 'book'\n    };\n    return icons[category] || 'assignment';\n  }\n  // Mettre à jour les statistiques\n  updateStats() {\n    if (this.kanbanData) {\n      // Recalculer les statistiques localement\n      const tasks = Object.values(this.kanbanData.kanbanBoard).flat();\n      this.kanbanData.stats.total = tasks.length;\n      this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;\n      this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;\n      // Mettre à jour les statistiques par statut\n      const validStatuses = ['backlog', 'todo', 'in-progress', 'review', 'testing', 'done', 'archived'];\n      validStatuses.forEach(status => {\n        const columnTasks = this.kanbanData.kanbanBoard[status];\n        this.kanbanData.stats.byStatus[status] = columnTasks ? columnTasks.length : 0;\n      });\n    }\n  }\n  // Appliquer des filtres\n  applyFilters(newFilters) {\n    this.filters = {\n      ...this.filters,\n      ...newFilters\n    };\n    this.loadKanbanBoard();\n  }\n  // Réinitialiser les filtres\n  resetFilters() {\n    this.filters = {};\n    this.loadKanbanBoard();\n  }\n  // Actualiser le tableau\n  refresh() {\n    this.loadKanbanBoard();\n  }\n  // Obtenir le nombre de tâches dans une colonne\n  getColumnCount(columnId) {\n    return this.getColumnTasks(columnId).length;\n  }\n  // Vérifier si une colonne est vide\n  isColumnEmpty(columnId) {\n    return this.getColumnTasks(columnId).length === 0;\n  }\n  // Obtenir le texte de placeholder pour une colonne vide\n  getEmptyColumnText(columnId) {\n    const texts = {\n      'backlog': 'Aucune tâche en attente',\n      'todo': 'Aucune tâche à faire',\n      'in-progress': 'Aucune tâche en cours',\n      'review': 'Aucune tâche en révision',\n      'testing': 'Aucune tâche en test',\n      'done': 'Aucune tâche terminée'\n    };\n    return texts[columnId] || 'Aucune tâche';\n  }\n  // Helper pour obtenir le nom d'un assigné\n  getAssigneeName(assignee) {\n    if (typeof assignee === 'string') {\n      return assignee;\n    }\n    return assignee.fullName || assignee.username || assignee.email || 'Utilisateur';\n  }\n  // Helper pour obtenir l'image de profil d'un assigné\n  getAssigneeImage(assignee) {\n    if (typeof assignee === 'string') {\n      return null;\n    }\n    return assignee.profileImage || null;\n  }\n  // Helper pour vérifier si un assigné a une image\n  hasAssigneeImage(assignee) {\n    return typeof assignee !== 'string' && !!assignee.profileImage;\n  }\n  // Helper pour obtenir les initiales d'un assigné\n  getAssigneeInitials(assignee) {\n    const name = this.getAssigneeName(assignee);\n    return name.charAt(0).toUpperCase();\n  }\n  static {\n    this.ɵfac = function KanbanBoardComponent_Factory(t) {\n      return new (t || KanbanBoardComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: KanbanBoardComponent,\n      selectors: [[\"app-kanban-board\"]],\n      inputs: {\n        teamId: \"teamId\",\n        filters: \"filters\"\n      },\n      outputs: {\n        taskSelected: \"taskSelected\",\n        taskCreated: \"taskCreated\"\n      },\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"kanban-board-container\"], [\"class\", \"kanban-header\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"kanban-board\", \"cdkDropListGroup\", \"\", 4, \"ngIf\"], [1, \"kanban-header\"], [1, \"stats-container\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item overdue\", 4, \"ngIf\"], [\"class\", \"stat-item blocked\", 4, \"ngIf\"], [1, \"actions-container\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Actualiser\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Filtres\"], [1, \"stat-item\", \"overdue\"], [1, \"stat-item\", \"blocked\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"cdkDropListGroup\", \"\", 1, \"kanban-board\"], [\"class\", \"kanban-column\", 3, \"border-top-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"kanban-column\"], [1, \"column-header\"], [1, \"column-title\"], [1, \"task-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Ajouter une t\\u00E2che\", 1, \"add-task-btn\", 3, \"click\"], [\"cdkDropList\", \"\", 1, \"tasks-container\", 3, \"id\", \"cdkDropListData\", \"cdkDropListDropped\"], [\"class\", \"task-card\", \"cdkDrag\", \"\", 3, \"cdkDragData\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-column\", 4, \"ngIf\"], [\"cdkDrag\", \"\", 1, \"task-card\", 3, \"cdkDragData\", \"click\"], [1, \"priority-indicator\"], [1, \"task-content\"], [1, \"task-header\"], [1, \"task-category\"], [1, \"category-icon\"], [1, \"task-id\"], [1, \"task-actions\"], [\"class\", \"overdue-icon\", \"matTooltip\", \"En retard\", 4, \"ngIf\"], [\"class\", \"blocked-icon\", \"matTooltip\", \"Bloqu\\u00E9e\", 4, \"ngIf\"], [1, \"task-title\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-labels\", 4, \"ngIf\"], [1, \"task-footer\"], [\"class\", \"task-assignees\", 4, \"ngIf\"], [1, \"task-meta\"], [\"class\", \"due-date\", 3, \"overdue\", 4, \"ngIf\"], [\"class\", \"estimated-hours\", 4, \"ngIf\"], [\"class\", \"subtasks-count\", 4, \"ngIf\"], [\"class\", \"comments-count\", 4, \"ngIf\"], [\"class\", \"ai-indicator\", \"matTooltip\", \"Suggestions IA disponibles\", 4, \"ngIf\"], [\"class\", \"task-drag-preview\", 4, \"cdkDragPreview\"], [\"matTooltip\", \"En retard\", 1, \"overdue-icon\"], [\"matTooltip\", \"Bloqu\\u00E9e\", 1, \"blocked-icon\"], [1, \"task-description\"], [4, \"ngIf\"], [1, \"task-labels\"], [\"class\", \"task-label\", 3, \"background-color\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-labels\", 4, \"ngIf\"], [1, \"task-label\"], [1, \"more-labels\"], [1, \"task-assignees\"], [\"class\", \"assignee-avatar\", 3, \"matTooltip\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-assignees\", 4, \"ngIf\"], [1, \"assignee-avatar\", 3, \"matTooltip\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [3, \"src\", \"alt\"], [1, \"more-assignees\"], [1, \"due-date\"], [1, \"estimated-hours\"], [1, \"subtasks-count\"], [1, \"comments-count\"], [\"matTooltip\", \"Suggestions IA disponibles\", 1, \"ai-indicator\"], [1, \"task-drag-preview\"], [1, \"drag-preview-content\"], [1, \"empty-column\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function KanbanBoardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, KanbanBoardComponent_div_1_Template, 18, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, KanbanBoardComponent_div_2_Template, 4, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, KanbanBoardComponent_div_3_Template, 7, 1, \"div\", 3);\n          i0.ɵɵtemplate(4, KanbanBoardComponent_div_4_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.kanbanData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.kanbanData && !ctx.loading && !ctx.error);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton, i5.MatIconButton, i6.MatIcon, i7.MatProgressSpinner, i8.MatTooltip, i9.CdkDropList, i9.CdkDropListGroup, i9.CdkDrag, i9.CdkDragPreview, i4.SlicePipe, i4.DatePipe],\n      styles: [\".kanban-board-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background: #f8fafc;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  background: white;\\n  border-bottom: 1px solid #e2e8f0;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.overdue[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.overdue[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.blocked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item.blocked[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  flex: 1;\\n  gap: 1rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .kanban-board-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n  overflow-x: auto;\\n  flex: 1;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n  max-width: 350px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  border-top: 4px solid;\\n  display: flex;\\n  flex-direction: column;\\n  max-height: calc(100vh - 200px);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  color: white;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 4px 4px 0 0;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-weight: 600;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%]   .task-count[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  font-size: 0.875rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .add-task-btn[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .add-task-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  overflow-y: auto;\\n  min-height: 200px;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  margin-bottom: 0.75rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-1px);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  padding-left: 1.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-category[_ngcontent-%COMP%]   .task-id[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #94a3b8;\\n  font-family: monospace;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   .overdue-icon[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-size: 1rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   .blocked-icon[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n  font-size: 1rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n  line-height: 1.4;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%] {\\n  margin: 0 0 0.75rem 0;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  line-height: 1.4;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  margin-bottom: 0.75rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%] {\\n  padding: 0.125rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.625rem;\\n  font-weight: 500;\\n  color: white;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .more-labels[_ngcontent-%COMP%] {\\n  font-size: 0.625rem;\\n  color: #64748b;\\n  padding: 0.125rem 0.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.625rem;\\n  font-weight: 600;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .more-assignees[_ngcontent-%COMP%] {\\n  font-size: 0.625rem;\\n  color: #64748b;\\n  margin-left: 0.25rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.125rem;\\n  font-size: 0.625rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  width: 0.875rem;\\n  height: 0.875rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .task-footer[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]    > span.overdue[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .ai-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.5rem;\\n  right: 0.5rem;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 12px;\\n  padding: 0.125rem 0.375rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.125rem;\\n  font-size: 0.625rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%]   .ai-indicator[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  width: 0.75rem;\\n  height: 0.75rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]   .task-drag-preview[_ngcontent-%COMP%]   .drag-preview-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.75rem;\\n  color: #64748b;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  text-align: center;\\n  color: #94a3b8;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%]   .tasks-container[_ngcontent-%COMP%]   .empty-column[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 0.875rem;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  background: #f1f5f9;\\n  border: 2px dashed #cbd5e1;\\n}\\n\\n.cdk-drop-list-dragging[_ngcontent-%COMP%]   .cdk-drag[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n@media (max-width: 768px) {\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-header[_ngcontent-%COMP%]   .stats-container[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .kanban-board-container[_ngcontent-%COMP%]   .kanban-board[_ngcontent-%COMP%]   .kanban-column[_ngcontent-%COMP%] {\\n    min-width: 280px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "moveItemInArray", "transferArrayItem", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r4", "kanbanData", "stats", "overdue", "ctx_r5", "blocked", "ɵɵtemplate", "KanbanBoardComponent_div_1_div_9_Template", "KanbanBoardComponent_div_1_div_10_Template", "ɵɵlistener", "KanbanBoardComponent_div_1_Template_button_click_12_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "refresh", "ctx_r0", "total", "ɵɵproperty", "ɵɵelement", "KanbanBoardComponent_div_3_Template_button_click_5_listener", "_r9", "ctx_r8", "loadKanbanBoard", "ctx_r2", "error", "KanbanBoardComponent_div_4_div_1_div_13_p_14_span_3_Template", "ɵɵtextInterpolate1", "ɵɵpipeBind3", "task_r14", "description", "length", "ɵɵstyleProp", "label_r30", "color", "name", "labels", "KanbanBoardComponent_div_4_div_1_div_13_div_15_span_1_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_15_span_3_Template", "ctx_r36", "getAssigneeImage", "assignee_r35", "ɵɵsanitizeUrl", "getAs<PERSON>eeName", "ctx_r37", "getAssigneeInitials", "KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_img_1_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_span_2_Template", "ctx_r33", "hasAssigneeImage", "assignedTo", "KanbanBoardComponent_div_4_div_1_div_13_div_17_div_1_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_17_span_3_Template", "ɵɵclassProp", "isOverdue", "ɵɵpipeBind2", "dueDate", "estimatedHours", "subtasks", "comments", "aiSuggestions", "suggestions", "title", "ɵɵtextInterpolate2", "category", "priority", "KanbanBoardComponent_div_4_div_1_div_13_Template_div_click_0_listener", "restoredCtx", "_r49", "$implicit", "ctx_r48", "selectTask", "KanbanBoardComponent_div_4_div_1_div_13_mat_icon_10_Template", "KanbanBoardComponent_div_4_div_1_div_13_mat_icon_11_Template", "KanbanBoardComponent_div_4_div_1_div_13_p_14_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_15_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_17_Template", "KanbanBoardComponent_div_4_div_1_div_13_span_19_Template", "KanbanBoardComponent_div_4_div_1_div_13_span_20_Template", "KanbanBoardComponent_div_4_div_1_div_13_span_21_Template", "KanbanBoardComponent_div_4_div_1_div_13_span_22_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_23_Template", "KanbanBoardComponent_div_4_div_1_div_13_div_24_Template", "ctx_r12", "getPriorityColor", "getCategoryIcon", "_id", "slice", "isBlocked", "KanbanBoardComponent_div_4_div_1_div_14_Template_button_click_5_listener", "_r52", "column_r11", "ctx_r50", "createTaskInColumn", "id", "icon", "ctx_r13", "getEmptyColumnText", "KanbanBoardComponent_div_4_div_1_Template_button_click_9_listener", "_r55", "ctx_r54", "KanbanBoardComponent_div_4_div_1_Template_div_cdkDropListDropped_12_listener", "$event", "ctx_r56", "onTaskDrop", "KanbanBoardComponent_div_4_div_1_div_13_Template", "KanbanBoardComponent_div_4_div_1_div_14_Template", "ctx_r10", "getColumnCount", "getColumnTasks", "isColumnEmpty", "KanbanBoardComponent_div_4_div_1_Template", "ctx_r3", "columns", "KanbanBoardComponent", "constructor", "taskService", "dialog", "snackBar", "filters", "taskSelected", "taskCreated", "loading", "ngOnInit", "teamId", "getKanbanBoard", "subscribe", "next", "data", "console", "open", "duration", "event", "task", "item", "newStatus", "container", "oldStatus", "previousContainer", "previousIndex", "currentIndex", "updateTaskPosition", "moveTaskToColumn", "newPosition", "moveRequest", "moveTask", "response", "updateStats", "status", "taskData", "emit", "columnId", "kanbanBoard", "colors", "icons", "tasks", "Object", "values", "flat", "filter", "validStatuses", "for<PERSON>ach", "columnTasks", "byStatus", "applyFilters", "newFilters", "resetFilters", "texts", "assignee", "fullName", "username", "email", "profileImage", "char<PERSON>t", "toUpperCase", "ɵɵdirectiveInject", "i1", "TaskService", "i2", "MatDialog", "i3", "MatSnackBar", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "KanbanBoardComponent_Template", "rf", "ctx", "KanbanBoardComponent_div_1_Template", "KanbanBoardComponent_div_2_Template", "KanbanBoardComponent_div_3_Template", "KanbanBoardComponent_div_4_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\components\\kanban-board\\kanban-board.component.ts", "C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\components\\kanban-board\\kanban-board.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\nimport { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { TaskService } from '../../services/task.service';\nimport {\n  Task,\n  KanbanBoard,\n  KanbanFilters,\n  MoveTaskRequest,\n  CreateTaskRequest\n} from '../../models/task.model';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-kanban-board',\n  templateUrl: './kanban-board.component.html',\n  styleUrls: ['./kanban-board.component.scss']\n})\nexport class KanbanBoardComponent implements OnInit {\n  @Input() teamId!: string;\n  @Input() filters: KanbanFilters = {};\n  @Output() taskSelected = new EventEmitter<Task>();\n  @Output() taskCreated = new EventEmitter<Task>();\n\n  kanbanData: KanbanBoard | null = null;\n  loading = false;\n  error: string | null = null;\n\n  // Configuration des colonnes Kanban\n  columns = [\n    { id: 'backlog', title: 'Backlog', color: '#6b7280', icon: 'inventory_2' },\n    { id: 'todo', title: 'À faire', color: '#3b82f6', icon: 'assignment' },\n    { id: 'in-progress', title: 'En cours', color: '#f59e0b', icon: 'play_circle' },\n    { id: 'review', title: 'Révision', color: '#8b5cf6', icon: 'rate_review' },\n    { id: 'testing', title: 'Tests', color: '#06b6d4', icon: 'bug_report' },\n    { id: 'done', title: 'Terminé', color: '#10b981', icon: 'check_circle' }\n  ];\n\n  constructor(\n    private taskService: TaskService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadKanbanBoard();\n  }\n\n  // Charger le tableau Kanban\n  loadKanbanBoard(): void {\n    if (!this.teamId) return;\n\n    this.loading = true;\n    this.error = null;\n\n    this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({\n      next: (data) => {\n        this.kanbanData = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = 'Erreur lors du chargement du tableau Kanban';\n        this.loading = false;\n        console.error('Erreur Kanban:', error);\n        this.snackBar.open('Erreur lors du chargement', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Gérer le drag & drop\n  onTaskDrop(event: CdkDragDrop<Task[]>): void {\n    const task = event.item.data as Task;\n    const newStatus = event.container.id;\n    const oldStatus = event.previousContainer.id;\n\n    if (event.previousContainer === event.container) {\n      // Réorganisation dans la même colonne\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      this.updateTaskPosition(task, event.currentIndex);\n    } else {\n      // Déplacement vers une autre colonne\n      transferArrayItem(\n        event.previousContainer.data,\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n      this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);\n    }\n  }\n\n  // Déplacer une tâche vers une nouvelle colonne\n  private moveTaskToColumn(task: Task, newStatus: string, newPosition: number, oldStatus: string): void {\n    const moveRequest: MoveTaskRequest = {\n      newStatus,\n      newPosition,\n      oldStatus\n    };\n\n    this.taskService.moveTask(task._id!, moveRequest).subscribe({\n      next: (response) => {\n        this.snackBar.open('Tâche déplacée avec succès', 'Fermer', { duration: 2000 });\n        // Mettre à jour les statistiques\n        this.updateStats();\n      },\n      error: (error) => {\n        console.error('Erreur déplacement tâche:', error);\n        this.snackBar.open('Erreur lors du déplacement', 'Fermer', { duration: 3000 });\n        // Annuler le déplacement visuel\n        this.loadKanbanBoard();\n      }\n    });\n  }\n\n  // Mettre à jour la position d'une tâche\n  private updateTaskPosition(task: Task, newPosition: number): void {\n    const moveRequest: MoveTaskRequest = {\n      newStatus: task.status,\n      newPosition,\n      oldStatus: task.status\n    };\n\n    this.taskService.moveTask(task._id!, moveRequest).subscribe({\n      error: (error) => {\n        console.error('Erreur mise à jour position:', error);\n        this.loadKanbanBoard(); // Recharger en cas d'erreur\n      }\n    });\n  }\n\n  // Créer une nouvelle tâche dans une colonne\n  createTaskInColumn(status: string): void {\n    const taskData: CreateTaskRequest = {\n      title: 'Nouvelle tâche',\n      description: '',\n      status,\n      priority: 'medium',\n      category: 'task'\n    };\n\n    this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({\n      next: (response) => {\n        this.taskCreated.emit(response.task);\n        this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche\n        this.snackBar.open('Tâche créée avec succès', 'Fermer', { duration: 2000 });\n      },\n      error: (error) => {\n        console.error('Erreur création tâche:', error);\n        this.snackBar.open('Erreur lors de la création', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Sélectionner une tâche\n  selectTask(task: Task): void {\n    this.taskSelected.emit(task);\n  }\n\n  // Obtenir les tâches d'une colonne\n  getColumnTasks(columnId: string): Task[] {\n    if (!this.kanbanData) return [];\n    return this.kanbanData.kanbanBoard[columnId as keyof typeof this.kanbanData.kanbanBoard] || [];\n  }\n\n  // Obtenir la couleur de priorité\n  getPriorityColor(priority: string): string {\n    const colors = {\n      'lowest': '#6b7280',\n      'low': '#3b82f6',\n      'medium': '#f59e0b',\n      'high': '#ef4444',\n      'highest': '#dc2626',\n      'critical': '#991b1b'\n    };\n    return colors[priority as keyof typeof colors] || '#6b7280';\n  }\n\n  // Obtenir l'icône de catégorie\n  getCategoryIcon(category: string): string {\n    const icons = {\n      'feature': 'new_releases',\n      'bug': 'bug_report',\n      'improvement': 'trending_up',\n      'task': 'assignment',\n      'epic': 'flag',\n      'story': 'book'\n    };\n    return icons[category as keyof typeof icons] || 'assignment';\n  }\n\n  // Mettre à jour les statistiques\n  private updateStats(): void {\n    if (this.kanbanData) {\n      // Recalculer les statistiques localement\n      const tasks = Object.values(this.kanbanData.kanbanBoard).flat();\n      this.kanbanData.stats.total = tasks.length;\n      this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;\n      this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;\n\n      // Mettre à jour les statistiques par statut\n      const validStatuses: (keyof typeof this.kanbanData.kanbanBoard)[] = ['backlog', 'todo', 'in-progress', 'review', 'testing', 'done', 'archived'];\n      validStatuses.forEach(status => {\n        const columnTasks = this.kanbanData!.kanbanBoard[status];\n        this.kanbanData!.stats.byStatus[status] = columnTasks ? columnTasks.length : 0;\n      });\n    }\n  }\n\n  // Appliquer des filtres\n  applyFilters(newFilters: KanbanFilters): void {\n    this.filters = { ...this.filters, ...newFilters };\n    this.loadKanbanBoard();\n  }\n\n  // Réinitialiser les filtres\n  resetFilters(): void {\n    this.filters = {};\n    this.loadKanbanBoard();\n  }\n\n  // Actualiser le tableau\n  refresh(): void {\n    this.loadKanbanBoard();\n  }\n\n  // Obtenir le nombre de tâches dans une colonne\n  getColumnCount(columnId: string): number {\n    return this.getColumnTasks(columnId).length;\n  }\n\n  // Vérifier si une colonne est vide\n  isColumnEmpty(columnId: string): boolean {\n    return this.getColumnTasks(columnId).length === 0;\n  }\n\n  // Obtenir le texte de placeholder pour une colonne vide\n  getEmptyColumnText(columnId: string): string {\n    const texts = {\n      'backlog': 'Aucune tâche en attente',\n      'todo': 'Aucune tâche à faire',\n      'in-progress': 'Aucune tâche en cours',\n      'review': 'Aucune tâche en révision',\n      'testing': 'Aucune tâche en test',\n      'done': 'Aucune tâche terminée'\n    };\n    return texts[columnId as keyof typeof texts] || 'Aucune tâche';\n  }\n\n  // Helper pour obtenir le nom d'un assigné\n  getAssigneeName(assignee: string | User): string {\n    if (typeof assignee === 'string') {\n      return assignee;\n    }\n    return assignee.fullName || assignee.username || assignee.email || 'Utilisateur';\n  }\n\n  // Helper pour obtenir l'image de profil d'un assigné\n  getAssigneeImage(assignee: string | User): string | null {\n    if (typeof assignee === 'string') {\n      return null;\n    }\n    return assignee.profileImage || null;\n  }\n\n  // Helper pour vérifier si un assigné a une image\n  hasAssigneeImage(assignee: string | User): boolean {\n    return typeof assignee !== 'string' && !!assignee.profileImage;\n  }\n\n  // Helper pour obtenir les initiales d'un assigné\n  getAssigneeInitials(assignee: string | User): string {\n    const name = this.getAssigneeName(assignee);\n    return name.charAt(0).toUpperCase();\n  }\n}\n", "<div class=\"kanban-board-container\">\n  <!-- Header avec statistiques -->\n  <div class=\"kanban-header\" *ngIf=\"kanbanData\">\n    <div class=\"stats-container\">\n      <div class=\"stat-item\">\n        <mat-icon>assignment</mat-icon>\n        <span class=\"stat-value\">{{ kanbanData.stats.total }}</span>\n        <span class=\"stat-label\">Total</span>\n      </div>\n      <div class=\"stat-item overdue\" *ngIf=\"kanbanData.stats.overdue > 0\">\n        <mat-icon>schedule</mat-icon>\n        <span class=\"stat-value\">{{ kanbanData.stats.overdue }}</span>\n        <span class=\"stat-label\">En retard</span>\n      </div>\n      <div class=\"stat-item blocked\" *ngIf=\"kanbanData.stats.blocked > 0\">\n        <mat-icon>block</mat-icon>\n        <span class=\"stat-value\">{{ kanbanData.stats.blocked }}</span>\n        <span class=\"stat-label\">Bloquées</span>\n      </div>\n    </div>\n\n    <div class=\"actions-container\">\n      <button mat-icon-button (click)=\"refresh()\" matTooltip=\"Actualiser\">\n        <mat-icon>refresh</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Filtres\">\n        <mat-icon>filter_list</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Loading state -->\n  <div class=\"loading-container\" *ngIf=\"loading\">\n    <mat-spinner diameter=\"40\"></mat-spinner>\n    <p>Chargement du tableau Kanban...</p>\n  </div>\n\n  <!-- Error state -->\n  <div class=\"error-container\" *ngIf=\"error && !loading\">\n    <mat-icon color=\"warn\">error</mat-icon>\n    <p>{{ error }}</p>\n    <button mat-raised-button color=\"primary\" (click)=\"loadKanbanBoard()\">\n      Réessayer\n    </button>\n  </div>\n\n  <!-- Kanban Board -->\n  <div class=\"kanban-board\" *ngIf=\"kanbanData && !loading && !error\"\n       cdkDropListGroup>\n\n    <div class=\"kanban-column\"\n         *ngFor=\"let column of columns\"\n         [style.border-top-color]=\"column.color\">\n\n      <!-- Column Header -->\n      <div class=\"column-header\" [style.background-color]=\"column.color\">\n        <div class=\"column-title\">\n          <mat-icon>{{ column.icon }}</mat-icon>\n          <span>{{ column.title }}</span>\n          <span class=\"task-count\">({{ getColumnCount(column.id) }})</span>\n        </div>\n\n        <button mat-icon-button\n                class=\"add-task-btn\"\n                (click)=\"createTaskInColumn(column.id)\"\n                matTooltip=\"Ajouter une tâche\">\n          <mat-icon>add</mat-icon>\n        </button>\n      </div>\n\n      <!-- Tasks Container -->\n      <div class=\"tasks-container\"\n           cdkDropList\n           [id]=\"column.id\"\n           [cdkDropListData]=\"getColumnTasks(column.id)\"\n           (cdkDropListDropped)=\"onTaskDrop($event)\">\n\n        <!-- Task Cards -->\n        <div class=\"task-card\"\n             *ngFor=\"let task of getColumnTasks(column.id)\"\n             cdkDrag\n             [cdkDragData]=\"task\"\n             (click)=\"selectTask(task)\">\n\n          <!-- Task Priority Indicator -->\n          <div class=\"priority-indicator\"\n               [style.background-color]=\"getPriorityColor(task.priority)\">\n          </div>\n\n          <!-- Task Content -->\n          <div class=\"task-content\">\n            <!-- Task Header -->\n            <div class=\"task-header\">\n              <div class=\"task-category\">\n                <mat-icon class=\"category-icon\">{{ getCategoryIcon(task.category || 'task') }}</mat-icon>\n                <span class=\"task-id\">#{{ task._id?.slice(-6) }}</span>\n              </div>\n\n              <div class=\"task-actions\">\n                <mat-icon *ngIf=\"task.isOverdue\"\n                         class=\"overdue-icon\"\n                         matTooltip=\"En retard\">\n                  schedule\n                </mat-icon>\n                <mat-icon *ngIf=\"task.isBlocked\"\n                         class=\"blocked-icon\"\n                         matTooltip=\"Bloquée\">\n                  block\n                </mat-icon>\n              </div>\n            </div>\n\n            <!-- Task Title -->\n            <h4 class=\"task-title\">{{ task.title }}</h4>\n\n            <!-- Task Description -->\n            <p class=\"task-description\" *ngIf=\"task.description\">\n              {{ task.description | slice:0:100 }}\n              <span *ngIf=\"task.description && task.description.length > 100\">...</span>\n            </p>\n\n            <!-- Task Labels -->\n            <div class=\"task-labels\" *ngIf=\"task.labels && task.labels.length > 0\">\n              <span class=\"task-label\"\n                    *ngFor=\"let label of task.labels | slice:0:3\"\n                    [style.background-color]=\"label.color\">\n                {{ label.name }}\n              </span>\n              <span class=\"more-labels\" *ngIf=\"task.labels && task.labels.length > 3\">\n                +{{ task.labels.length - 3 }}\n              </span>\n            </div>\n\n            <!-- Task Footer -->\n            <div class=\"task-footer\">\n              <!-- Assignees -->\n              <div class=\"task-assignees\" *ngIf=\"task.assignedTo && task.assignedTo.length > 0\">\n                <div class=\"assignee-avatar\"\n                     *ngFor=\"let assignee of task.assignedTo | slice:0:3\"\n                     [matTooltip]=\"getAssigneeName(assignee)\">\n                  <img *ngIf=\"hasAssigneeImage(assignee)\"\n                       [src]=\"getAssigneeImage(assignee)\"\n                       [alt]=\"getAssigneeName(assignee)\">\n                  <span *ngIf=\"!hasAssigneeImage(assignee)\">\n                    {{ getAssigneeInitials(assignee) }}\n                  </span>\n                </div>\n                <span class=\"more-assignees\" *ngIf=\"task.assignedTo && task.assignedTo.length > 3\">\n                  +{{ task.assignedTo.length - 3 }}\n                </span>\n              </div>\n\n              <!-- Task Meta -->\n              <div class=\"task-meta\">\n                <!-- Due Date -->\n                <span class=\"due-date\"\n                      *ngIf=\"task.dueDate\"\n                      [class.overdue]=\"task.isOverdue\">\n                  <mat-icon>schedule</mat-icon>\n                  {{ task.dueDate | date:'dd/MM' }}\n                </span>\n\n                <!-- Estimated Hours -->\n                <span class=\"estimated-hours\" *ngIf=\"task.estimatedHours\">\n                  <mat-icon>access_time</mat-icon>\n                  {{ task.estimatedHours }}h\n                </span>\n\n                <!-- Subtasks Count -->\n                <span class=\"subtasks-count\" *ngIf=\"task.subtasks && task.subtasks.length > 0\">\n                  <mat-icon>list</mat-icon>\n                  {{ task.subtasks.length }}\n                </span>\n\n                <!-- Comments Count -->\n                <span class=\"comments-count\" *ngIf=\"task.comments && task.comments.length > 0\">\n                  <mat-icon>comment</mat-icon>\n                  {{ task.comments.length }}\n                </span>\n              </div>\n            </div>\n\n            <!-- AI Suggestions Indicator -->\n            <div class=\"ai-indicator\"\n                 *ngIf=\"task.aiSuggestions?.suggestions && (task.aiSuggestions?.suggestions?.length || 0) > 0\"\n                 matTooltip=\"Suggestions IA disponibles\">\n              <mat-icon>psychology</mat-icon>\n              <span>{{ task.aiSuggestions?.suggestions?.length || 0 }}</span>\n            </div>\n          </div>\n\n          <!-- Drag Preview -->\n          <div class=\"task-drag-preview\" *cdkDragPreview>\n            <div class=\"drag-preview-content\">\n              <h4>{{ task.title }}</h4>\n              <p>{{ task.category }} • {{ task.priority }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty Column Placeholder -->\n        <div class=\"empty-column\" *ngIf=\"isColumnEmpty(column.id)\">\n          <mat-icon>{{ column.icon }}</mat-icon>\n          <p>{{ getEmptyColumnText(column.id) }}</p>\n          <button mat-stroked-button\n                  color=\"primary\"\n                  (click)=\"createTaskInColumn(column.id)\">\n            <mat-icon>add</mat-icon>\n            Ajouter une tâche\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAAsBC,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;;;;;;;;;;;;;ICQlFC,EAAA,CAAAC,cAAA,cAAoE;IACxDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADhBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,KAAA,CAAAC,OAAA,CAA8B;;;;;IAGzDT,EAAA,CAAAC,cAAA,cAAoE;IACxDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,oBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADfH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAAK,MAAA,CAAAH,UAAA,CAAAC,KAAA,CAAAG,OAAA,CAA8B;;;;;;IAd7DX,EAAA,CAAAC,cAAA,aAA8C;IAG9BD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvCH,EAAA,CAAAY,UAAA,IAAAC,yCAAA,kBAIM;IACNb,EAAA,CAAAY,UAAA,KAAAE,0CAAA,kBAIM;IACRd,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA+B;IACLD,EAAA,CAAAe,UAAA,mBAAAC,6DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IACzCtB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE9BH,EAAA,CAAAC,cAAA,kBAA6C;IACjCD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IApBPH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAkB,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAAAgB,KAAA,CAA4B;IAGvBxB,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAyB,UAAA,SAAAF,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAAAC,OAAA,KAAkC;IAKlCT,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAyB,UAAA,SAAAF,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAAAG,OAAA,KAAkC;;;;;IAkBtEX,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA0B,SAAA,sBAAyC;IACzC1B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIxCH,EAAA,CAAAC,cAAA,cAAuD;IAC9BD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAsE;IAA5BD,EAAA,CAAAe,UAAA,mBAAAY,4DAAA;MAAA3B,EAAA,CAAAiB,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAQ,MAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACnE9B,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHNH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAC,KAAA,CAAW;;;;;IA2DFhC,EAAA,CAAAC,cAAA,mBAEgC;IAC9BD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACXH,EAAA,CAAAC,cAAA,mBAE8B;IAC5BD,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAUbH,EAAA,CAAAC,cAAA,WAAgE;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF5EH,EAAA,CAAAC,cAAA,YAAqD;IACnDD,EAAA,CAAAE,MAAA,GACA;;IAAAF,EAAA,CAAAY,UAAA,IAAAqB,4DAAA,mBAA0E;IAC5EjC,EAAA,CAAAG,YAAA,EAAI;;;;IAFFH,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAkC,kBAAA,MAAAlC,EAAA,CAAAmC,WAAA,OAAAC,QAAA,CAAAC,WAAA,eACA;IAAOrC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,CAAAC,MAAA,OAAuD;;;;;IAK9DtC,EAAA,CAAAC,cAAA,eAE6C;IAC3CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFDH,EAAA,CAAAuC,WAAA,qBAAAC,SAAA,CAAAC,KAAA,CAAsC;IAC1CzC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAM,SAAA,CAAAE,IAAA,MACF;;;;;IACA1C,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,OAAAE,QAAA,CAAAO,MAAA,CAAAL,MAAA,UACF;;;;;IARFtC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAY,UAAA,IAAAgC,8DAAA,mBAIO;;IACP5C,EAAA,CAAAY,UAAA,IAAAiC,8DAAA,mBAEO;IACT7C,EAAA,CAAAG,YAAA,EAAM;;;;IAPoBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAAmC,WAAA,OAAAC,QAAA,CAAAO,MAAA,QAA0B;IAIvB3C,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAO,MAAA,IAAAP,QAAA,CAAAO,MAAA,CAAAL,MAAA,KAA2C;;;;;IAYlEtC,EAAA,CAAA0B,SAAA,cAEuC;;;;;IADlC1B,EAAA,CAAAyB,UAAA,QAAAqB,OAAA,CAAAC,gBAAA,CAAAC,YAAA,GAAAhD,EAAA,CAAAiD,aAAA,CAAkC,QAAAH,OAAA,CAAAI,eAAA,CAAAF,YAAA;;;;;IAEvChD,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAJ,YAAA,OACF;;;;;IARFhD,EAAA,CAAAC,cAAA,cAE8C;IAC5CD,EAAA,CAAAY,UAAA,IAAAyC,mEAAA,kBAEuC;IACvCrD,EAAA,CAAAY,UAAA,IAAA0C,oEAAA,mBAEO;IACTtD,EAAA,CAAAG,YAAA,EAAM;;;;;IAPDH,EAAA,CAAAyB,UAAA,eAAA8B,OAAA,CAAAL,eAAA,CAAAF,YAAA,EAAwC;IACrChD,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAyB,UAAA,SAAA8B,OAAA,CAAAC,gBAAA,CAAAR,YAAA,EAAgC;IAG/BhD,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAyB,UAAA,UAAA8B,OAAA,CAAAC,gBAAA,CAAAR,YAAA,EAAiC;;;;;IAI1ChD,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,OAAAE,QAAA,CAAAqB,UAAA,CAAAnB,MAAA,UACF;;;;;IAbFtC,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAY,UAAA,IAAA8C,6DAAA,kBASM;;IACN1D,EAAA,CAAAY,UAAA,IAAA+C,8DAAA,mBAEO;IACT3D,EAAA,CAAAG,YAAA,EAAM;;;;IAZsBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAAmC,WAAA,OAAAC,QAAA,CAAAqB,UAAA,QAA8B;IAS1BzD,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAqB,UAAA,IAAArB,QAAA,CAAAqB,UAAA,CAAAnB,MAAA,KAAmD;;;;;IAQjFtC,EAAA,CAAAC,cAAA,eAEuC;IAC3BD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHDH,EAAA,CAAA4D,WAAA,YAAAxB,QAAA,CAAAyB,SAAA,CAAgC;IAEpC7D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAlC,EAAA,CAAA8D,WAAA,OAAA1B,QAAA,CAAA2B,OAAA,gBACF;;;;;IAGA/D,EAAA,CAAAC,cAAA,eAA0D;IAC9CD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAE,QAAA,CAAA4B,cAAA,OACF;;;;;IAGAhE,EAAA,CAAAC,cAAA,eAA+E;IACnED,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAE,QAAA,CAAA6B,QAAA,CAAA3B,MAAA,MACF;;;;;IAGAtC,EAAA,CAAAC,cAAA,eAA+E;IACnED,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkC,kBAAA,MAAAE,QAAA,CAAA8B,QAAA,CAAA5B,MAAA,MACF;;;;;IAKJtC,EAAA,CAAAC,cAAA,cAE6C;IACjCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzDH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,EAAA+B,QAAA,CAAA+B,aAAA,kBAAA/B,QAAA,CAAA+B,aAAA,CAAAC,WAAA,kBAAAhC,QAAA,CAAA+B,aAAA,CAAAC,WAAA,CAAA9B,MAAA,OAAkD;;;;;IAK5DtC,EAAA,CAAAC,cAAA,cAA+C;IAEvCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAD5CH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA+B,QAAA,CAAAiC,KAAA,CAAgB;IACjBrE,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAsE,kBAAA,KAAAlC,QAAA,CAAAmC,QAAA,cAAAnC,QAAA,CAAAoC,QAAA,KAAyC;;;;;;IArHlDxE,EAAA,CAAAC,cAAA,cAIgC;IAA3BD,EAAA,CAAAe,UAAA,mBAAA0D,sEAAA;MAAA,MAAAC,WAAA,GAAA1E,EAAA,CAAAiB,aAAA,CAAA0D,IAAA;MAAA,MAAAvC,QAAA,GAAAsC,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwD,OAAA,CAAAC,UAAA,CAAA1C,QAAA,CAAgB;IAAA,EAAC;IAG7BpC,EAAA,CAAA0B,SAAA,cAEM;IAGN1B,EAAA,CAAAC,cAAA,cAA0B;IAIYD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzFH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGzDH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAY,UAAA,KAAAmE,4DAAA,uBAIW;IACX/E,EAAA,CAAAY,UAAA,KAAAoE,4DAAA,uBAIW;IACbhF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5CH,EAAA,CAAAY,UAAA,KAAAqE,qDAAA,gBAGI;IAGJjF,EAAA,CAAAY,UAAA,KAAAsE,uDAAA,kBASM;IAGNlF,EAAA,CAAAC,cAAA,eAAyB;IAEvBD,EAAA,CAAAY,UAAA,KAAAuE,uDAAA,kBAcM;IAGNnF,EAAA,CAAAC,cAAA,eAAuB;IAErBD,EAAA,CAAAY,UAAA,KAAAwE,wDAAA,mBAKO;IAGPpF,EAAA,CAAAY,UAAA,KAAAyE,wDAAA,mBAGO;IAGPrF,EAAA,CAAAY,UAAA,KAAA0E,wDAAA,mBAGO;IAGPtF,EAAA,CAAAY,UAAA,KAAA2E,wDAAA,mBAGO;IACTvF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAY,UAAA,KAAA4E,uDAAA,kBAKM;IACRxF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAY,UAAA,KAAA6E,uDAAA,kBAKM;IACRzF,EAAA,CAAAG,YAAA,EAAM;;;;;IArHDH,EAAA,CAAAyB,UAAA,gBAAAW,QAAA,CAAoB;IAKlBpC,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAuC,WAAA,qBAAAmD,OAAA,CAAAC,gBAAA,CAAAvD,QAAA,CAAAoC,QAAA,EAA0D;IAQzBxE,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAqF,OAAA,CAAAE,eAAA,CAAAxD,QAAA,CAAAmC,QAAA,YAA8C;IACxDvE,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAkC,kBAAA,MAAAE,QAAA,CAAAyD,GAAA,kBAAAzD,QAAA,CAAAyD,GAAA,CAAAC,KAAA,SAA0B;IAIrC9F,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAyB,SAAA,CAAoB;IAKpB7D,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAA2D,SAAA,CAAoB;IASZ/F,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA+B,QAAA,CAAAiC,KAAA,CAAgB;IAGVrE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAC,WAAA,CAAsB;IAMzBrC,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAO,MAAA,IAAAP,QAAA,CAAAO,MAAA,CAAAL,MAAA,KAA2C;IActCtC,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAAqB,UAAA,IAAArB,QAAA,CAAAqB,UAAA,CAAAnB,MAAA,KAAmD;IAoBvEtC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAA2B,OAAA,CAAkB;IAOM/D,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAA4B,cAAA,CAAyB;IAM1BhE,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAA6B,QAAA,IAAA7B,QAAA,CAAA6B,QAAA,CAAA3B,MAAA,KAA+C;IAM/CtC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAyB,UAAA,SAAAW,QAAA,CAAA8B,QAAA,IAAA9B,QAAA,CAAA8B,QAAA,CAAA5B,MAAA,KAA+C;IAS3EtC,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAyB,UAAA,UAAAW,QAAA,CAAA+B,aAAA,kBAAA/B,QAAA,CAAA+B,aAAA,CAAAC,WAAA,OAAAhC,QAAA,CAAA+B,aAAA,kBAAA/B,QAAA,CAAA+B,aAAA,CAAAC,WAAA,kBAAAhC,QAAA,CAAA+B,aAAA,CAAAC,WAAA,CAAA9B,MAAA,YAA2F;;;;;;IAiBrGtC,EAAA,CAAAC,cAAA,cAA2D;IAC/CD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1CH,EAAA,CAAAC,cAAA,iBAEgD;IAAxCD,EAAA,CAAAe,UAAA,mBAAAiF,yEAAA;MAAAhG,EAAA,CAAAiB,aAAA,CAAAgF,IAAA;MAAA,MAAAC,UAAA,GAAAlG,EAAA,CAAAoB,aAAA,GAAAwD,SAAA;MAAA,MAAAuB,OAAA,GAAAnG,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA8E,OAAA,CAAAC,kBAAA,CAAAF,UAAA,CAAAG,EAAA,CAA6B;IAAA,EAAC;IAC7CrG,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAPCH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA6F,UAAA,CAAAI,IAAA,CAAiB;IACxBtG,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAkG,OAAA,CAAAC,kBAAA,CAAAN,UAAA,CAAAG,EAAA,EAAmC;;;;;;IAzJ5CrG,EAAA,CAAAC,cAAA,cAE6C;IAK7BD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGnEH,EAAA,CAAAC,cAAA,iBAGuC;IAD/BD,EAAA,CAAAe,UAAA,mBAAA0F,kEAAA;MAAA,MAAA/B,WAAA,GAAA1E,EAAA,CAAAiB,aAAA,CAAAyF,IAAA;MAAA,MAAAR,UAAA,GAAAxB,WAAA,CAAAE,SAAA;MAAA,MAAA+B,OAAA,GAAA3G,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsF,OAAA,CAAAP,kBAAA,CAAAF,UAAA,CAAAG,EAAA,CAA6B;IAAA,EAAC;IAE7CrG,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAK5BH,EAAA,CAAAC,cAAA,eAI+C;IAA1CD,EAAA,CAAAe,UAAA,gCAAA6F,6EAAAC,MAAA;MAAA7G,EAAA,CAAAiB,aAAA,CAAAyF,IAAA;MAAA,MAAAI,OAAA,GAAA9G,EAAA,CAAAoB,aAAA;MAAA,OAAsBpB,EAAA,CAAAqB,WAAA,CAAAyF,OAAA,CAAAC,UAAA,CAAAF,MAAA,CAAkB;IAAA,EAAC;IAG5C7G,EAAA,CAAAY,UAAA,KAAAoG,gDAAA,oBAwHM;IAGNhH,EAAA,CAAAY,UAAA,KAAAqG,gDAAA,kBASM;IACRjH,EAAA,CAAAG,YAAA,EAAM;;;;;IA/JHH,EAAA,CAAAuC,WAAA,qBAAA2D,UAAA,CAAAzD,KAAA,CAAuC;IAGfzC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAuC,WAAA,qBAAA2D,UAAA,CAAAzD,KAAA,CAAuC;IAEpDzC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA6F,UAAA,CAAAI,IAAA,CAAiB;IACrBtG,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA6F,UAAA,CAAA7B,KAAA,CAAkB;IACCrE,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAkC,kBAAA,MAAAgF,OAAA,CAAAC,cAAA,CAAAjB,UAAA,CAAAG,EAAA,OAAiC;IAczDrG,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAyB,UAAA,OAAAyE,UAAA,CAAAG,EAAA,CAAgB,oBAAAa,OAAA,CAAAE,cAAA,CAAAlB,UAAA,CAAAG,EAAA;IAMGrG,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAyB,UAAA,YAAAyF,OAAA,CAAAE,cAAA,CAAAlB,UAAA,CAAAG,EAAA,EAA4B;IA0HvBrG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAyB,UAAA,SAAAyF,OAAA,CAAAG,aAAA,CAAAnB,UAAA,CAAAG,EAAA,EAA8B;;;;;IA1J/DrG,EAAA,CAAAC,cAAA,cACsB;IAEpBD,EAAA,CAAAY,UAAA,IAAA0G,yCAAA,oBAkKM;IACRtH,EAAA,CAAAG,YAAA,EAAM;;;;IAlKoBH,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAyB,UAAA,YAAA8F,MAAA,CAAAC,OAAA,CAAU;;;ADhCtC,OAAM,MAAOC,oBAAoB;EAoB/BC,YACUC,WAAwB,EACxBC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IArBT,KAAAC,OAAO,GAAkB,EAAE;IAC1B,KAAAC,YAAY,GAAG,IAAIlI,YAAY,EAAQ;IACvC,KAAAmI,WAAW,GAAG,IAAInI,YAAY,EAAQ;IAEhD,KAAAU,UAAU,GAAuB,IAAI;IACrC,KAAA0H,OAAO,GAAG,KAAK;IACf,KAAAjG,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAwF,OAAO,GAAG,CACR;MAAEnB,EAAE,EAAE,SAAS;MAAEhC,KAAK,EAAE,SAAS;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAa,CAAE,EAC1E;MAAED,EAAE,EAAE,MAAM;MAAEhC,KAAK,EAAE,SAAS;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAY,CAAE,EACtE;MAAED,EAAE,EAAE,aAAa;MAAEhC,KAAK,EAAE,UAAU;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAa,CAAE,EAC/E;MAAED,EAAE,EAAE,QAAQ;MAAEhC,KAAK,EAAE,UAAU;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAa,CAAE,EAC1E;MAAED,EAAE,EAAE,SAAS;MAAEhC,KAAK,EAAE,OAAO;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAY,CAAE,EACvE;MAAED,EAAE,EAAE,MAAM;MAAEhC,KAAK,EAAE,SAAS;MAAE5B,KAAK,EAAE,SAAS;MAAE6D,IAAI,EAAE;IAAc,CAAE,CACzE;EAME;EAEH4B,QAAQA,CAAA;IACN,IAAI,CAACpG,eAAe,EAAE;EACxB;EAEA;EACAA,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACqG,MAAM,EAAE;IAElB,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjG,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC2F,WAAW,CAACS,cAAc,CAAC,IAAI,CAACD,MAAM,EAAE,IAAI,CAACL,OAAO,CAAC,CAACO,SAAS,CAAC;MACnEC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAAChI,UAAU,GAAGgI,IAAI;QACtB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,6CAA6C;QAC1D,IAAI,CAACiG,OAAO,GAAG,KAAK;QACpBO,OAAO,CAACxG,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAAC6F,QAAQ,CAACY,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC/E;KACD,CAAC;EACJ;EAEA;EACA3B,UAAUA,CAAC4B,KAA0B;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,IAAI,CAACN,IAAY;IACpC,MAAMO,SAAS,GAAGH,KAAK,CAACI,SAAS,CAAC1C,EAAE;IACpC,MAAM2C,SAAS,GAAGL,KAAK,CAACM,iBAAiB,CAAC5C,EAAE;IAE5C,IAAIsC,KAAK,CAACM,iBAAiB,KAAKN,KAAK,CAACI,SAAS,EAAE;MAC/C;MACAjJ,eAAe,CAAC6I,KAAK,CAACI,SAAS,CAACR,IAAI,EAAEI,KAAK,CAACO,aAAa,EAAEP,KAAK,CAACQ,YAAY,CAAC;MAC9E,IAAI,CAACC,kBAAkB,CAACR,IAAI,EAAED,KAAK,CAACQ,YAAY,CAAC;KAClD,MAAM;MACL;MACApJ,iBAAiB,CACf4I,KAAK,CAACM,iBAAiB,CAACV,IAAI,EAC5BI,KAAK,CAACI,SAAS,CAACR,IAAI,EACpBI,KAAK,CAACO,aAAa,EACnBP,KAAK,CAACQ,YAAY,CACnB;MACD,IAAI,CAACE,gBAAgB,CAACT,IAAI,EAAEE,SAAS,EAAEH,KAAK,CAACQ,YAAY,EAAEH,SAAS,CAAC;;EAEzE;EAEA;EACQK,gBAAgBA,CAACT,IAAU,EAAEE,SAAiB,EAAEQ,WAAmB,EAAEN,SAAiB;IAC5F,MAAMO,WAAW,GAAoB;MACnCT,SAAS;MACTQ,WAAW;MACXN;KACD;IAED,IAAI,CAACrB,WAAW,CAAC6B,QAAQ,CAACZ,IAAI,CAAC/C,GAAI,EAAE0D,WAAW,CAAC,CAAClB,SAAS,CAAC;MAC1DC,IAAI,EAAGmB,QAAQ,IAAI;QACjB,IAAI,CAAC5B,QAAQ,CAACY,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E;QACA,IAAI,CAACgB,WAAW,EAAE;MACpB,CAAC;MACD1H,KAAK,EAAGA,KAAK,IAAI;QACfwG,OAAO,CAACxG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC6F,QAAQ,CAACY,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E;QACA,IAAI,CAAC5G,eAAe,EAAE;MACxB;KACD,CAAC;EACJ;EAEA;EACQsH,kBAAkBA,CAACR,IAAU,EAAEU,WAAmB;IACxD,MAAMC,WAAW,GAAoB;MACnCT,SAAS,EAAEF,IAAI,CAACe,MAAM;MACtBL,WAAW;MACXN,SAAS,EAAEJ,IAAI,CAACe;KACjB;IAED,IAAI,CAAChC,WAAW,CAAC6B,QAAQ,CAACZ,IAAI,CAAC/C,GAAI,EAAE0D,WAAW,CAAC,CAAClB,SAAS,CAAC;MAC1DrG,KAAK,EAAGA,KAAK,IAAI;QACfwG,OAAO,CAACxG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACF,eAAe,EAAE,CAAC,CAAC;MAC1B;KACD,CAAC;EACJ;EAEA;EACAsE,kBAAkBA,CAACuD,MAAc;IAC/B,MAAMC,QAAQ,GAAsB;MAClCvF,KAAK,EAAE,gBAAgB;MACvBhC,WAAW,EAAE,EAAE;MACfsH,MAAM;MACNnF,QAAQ,EAAE,QAAQ;MAClBD,QAAQ,EAAE;KACX;IAED,IAAI,CAACoD,WAAW,CAACvB,kBAAkB,CAAC,IAAI,CAAC+B,MAAM,EAAEyB,QAAQ,CAAC,CAACvB,SAAS,CAAC;MACnEC,IAAI,EAAGmB,QAAQ,IAAI;QACjB,IAAI,CAACzB,WAAW,CAAC6B,IAAI,CAACJ,QAAQ,CAACb,IAAI,CAAC;QACpC,IAAI,CAAC9G,eAAe,EAAE,CAAC,CAAC;QACxB,IAAI,CAAC+F,QAAQ,CAACY,IAAI,CAAC,yBAAyB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC7E,CAAC;MACD1G,KAAK,EAAGA,KAAK,IAAI;QACfwG,OAAO,CAACxG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAC6F,QAAQ,CAACY,IAAI,CAAC,4BAA4B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAChF;KACD,CAAC;EACJ;EAEA;EACA5D,UAAUA,CAAC8D,IAAU;IACnB,IAAI,CAACb,YAAY,CAAC8B,IAAI,CAACjB,IAAI,CAAC;EAC9B;EAEA;EACAxB,cAAcA,CAAC0C,QAAgB;IAC7B,IAAI,CAAC,IAAI,CAACvJ,UAAU,EAAE,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACA,UAAU,CAACwJ,WAAW,CAACD,QAAoD,CAAC,IAAI,EAAE;EAChG;EAEA;EACAnE,gBAAgBA,CAACnB,QAAgB;IAC/B,MAAMwF,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE;KACb;IACD,OAAOA,MAAM,CAACxF,QAA+B,CAAC,IAAI,SAAS;EAC7D;EAEA;EACAoB,eAAeA,CAACrB,QAAgB;IAC9B,MAAM0F,KAAK,GAAG;MACZ,SAAS,EAAE,cAAc;MACzB,KAAK,EAAE,YAAY;MACnB,aAAa,EAAE,aAAa;MAC5B,MAAM,EAAE,YAAY;MACpB,MAAM,EAAE,MAAM;MACd,OAAO,EAAE;KACV;IACD,OAAOA,KAAK,CAAC1F,QAA8B,CAAC,IAAI,YAAY;EAC9D;EAEA;EACQmF,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACnJ,UAAU,EAAE;MACnB;MACA,MAAM2J,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7J,UAAU,CAACwJ,WAAW,CAAC,CAACM,IAAI,EAAE;MAC/D,IAAI,CAAC9J,UAAU,CAACC,KAAK,CAACgB,KAAK,GAAG0I,KAAK,CAAC5H,MAAM;MAC1C,IAAI,CAAC/B,UAAU,CAACC,KAAK,CAACC,OAAO,GAAGyJ,KAAK,CAACI,MAAM,CAAC1B,IAAI,IAAIA,IAAI,CAAC/E,SAAS,CAAC,CAACvB,MAAM;MAC3E,IAAI,CAAC/B,UAAU,CAACC,KAAK,CAACG,OAAO,GAAGuJ,KAAK,CAACI,MAAM,CAAC1B,IAAI,IAAIA,IAAI,CAAC7C,SAAS,CAAC,CAACzD,MAAM;MAE3E;MACA,MAAMiI,aAAa,GAAiD,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC;MAC/IA,aAAa,CAACC,OAAO,CAACb,MAAM,IAAG;QAC7B,MAAMc,WAAW,GAAG,IAAI,CAAClK,UAAW,CAACwJ,WAAW,CAACJ,MAAM,CAAC;QACxD,IAAI,CAACpJ,UAAW,CAACC,KAAK,CAACkK,QAAQ,CAACf,MAAM,CAAC,GAAGc,WAAW,GAAGA,WAAW,CAACnI,MAAM,GAAG,CAAC;MAChF,CAAC,CAAC;;EAEN;EAEA;EACAqI,YAAYA,CAACC,UAAyB;IACpC,IAAI,CAAC9C,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAG8C;IAAU,CAAE;IACjD,IAAI,CAAC9I,eAAe,EAAE;EACxB;EAEA;EACA+I,YAAYA,CAAA;IACV,IAAI,CAAC/C,OAAO,GAAG,EAAE;IACjB,IAAI,CAAChG,eAAe,EAAE;EACxB;EAEA;EACAR,OAAOA,CAAA;IACL,IAAI,CAACQ,eAAe,EAAE;EACxB;EAEA;EACAqF,cAAcA,CAAC2C,QAAgB;IAC7B,OAAO,IAAI,CAAC1C,cAAc,CAAC0C,QAAQ,CAAC,CAACxH,MAAM;EAC7C;EAEA;EACA+E,aAAaA,CAACyC,QAAgB;IAC5B,OAAO,IAAI,CAAC1C,cAAc,CAAC0C,QAAQ,CAAC,CAACxH,MAAM,KAAK,CAAC;EACnD;EAEA;EACAkE,kBAAkBA,CAACsD,QAAgB;IACjC,MAAMgB,KAAK,GAAG;MACZ,SAAS,EAAE,yBAAyB;MACpC,MAAM,EAAE,sBAAsB;MAC9B,aAAa,EAAE,uBAAuB;MACtC,QAAQ,EAAE,0BAA0B;MACpC,SAAS,EAAE,sBAAsB;MACjC,MAAM,EAAE;KACT;IACD,OAAOA,KAAK,CAAChB,QAA8B,CAAC,IAAI,cAAc;EAChE;EAEA;EACA5G,eAAeA,CAAC6H,QAAuB;IACrC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ;;IAEjB,OAAOA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACG,KAAK,IAAI,aAAa;EAClF;EAEA;EACAnI,gBAAgBA,CAACgI,QAAuB;IACtC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAO,IAAI;;IAEb,OAAOA,QAAQ,CAACI,YAAY,IAAI,IAAI;EACtC;EAEA;EACA3H,gBAAgBA,CAACuH,QAAuB;IACtC,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ,CAACI,YAAY;EAChE;EAEA;EACA/H,mBAAmBA,CAAC2H,QAAuB;IACzC,MAAMrI,IAAI,GAAG,IAAI,CAACQ,eAAe,CAAC6H,QAAQ,CAAC;IAC3C,OAAOrI,IAAI,CAAC0I,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;EACrC;;;uBA/PW5D,oBAAoB,EAAAzH,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBnE,oBAAoB;MAAAoE,SAAA;MAAAC,MAAA;QAAA3D,MAAA;QAAAL,OAAA;MAAA;MAAAiE,OAAA;QAAAhE,YAAA;QAAAC,WAAA;MAAA;MAAAgE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBjCrM,EAAA,CAAAC,cAAA,aAAoC;UAElCD,EAAA,CAAAY,UAAA,IAAA2L,mCAAA,kBA2BM;UAGNvM,EAAA,CAAAY,UAAA,IAAA4L,mCAAA,iBAGM;UAGNxM,EAAA,CAAAY,UAAA,IAAA6L,mCAAA,iBAMM;UAGNzM,EAAA,CAAAY,UAAA,IAAA8L,mCAAA,iBAsKM;UACR1M,EAAA,CAAAG,YAAA,EAAM;;;UApNwBH,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAyB,UAAA,SAAA6K,GAAA,CAAA/L,UAAA,CAAgB;UA8BZP,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAyB,UAAA,SAAA6K,GAAA,CAAArE,OAAA,CAAa;UAMfjI,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAyB,UAAA,SAAA6K,GAAA,CAAAtK,KAAA,KAAAsK,GAAA,CAAArE,OAAA,CAAuB;UAS1BjI,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAyB,UAAA,SAAA6K,GAAA,CAAA/L,UAAA,KAAA+L,GAAA,CAAArE,OAAA,KAAAqE,GAAA,CAAAtK,KAAA,CAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}