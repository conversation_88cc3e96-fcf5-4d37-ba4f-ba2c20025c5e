{"ast": null, "code": "/**\n * Given a Path and a key, return a new Path containing the new key.\n */\nexport function addPath(prev, key, typename) {\n  return {\n    prev,\n    key,\n    typename\n  };\n}\n/**\n * Given a Path, return an Array of the path keys.\n */\n\nexport function pathToArray(path) {\n  const flattened = [];\n  let curr = path;\n  while (curr) {\n    flattened.push(curr.key);\n    curr = curr.prev;\n  }\n  return flattened.reverse();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}