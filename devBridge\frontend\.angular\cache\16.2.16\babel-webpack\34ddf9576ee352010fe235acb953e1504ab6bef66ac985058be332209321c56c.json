{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isEnumType, isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\nimport { GraphQLID } from '../type/scalars.mjs';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\n\nexport function astFromValue(value, type) {\n  if (isNonNullType(type)) {\n    const astValue = astFromValue(value, type.ofType);\n    if ((astValue === null || astValue === void 0 ? void 0 : astValue.kind) === Kind.NULL) {\n      return null;\n    }\n    return astValue;\n  } // only explicit null, not undefined, NaN\n\n  if (value === null) {\n    return {\n      kind: Kind.NULL\n    };\n  } // undefined\n\n  if (value === undefined) {\n    return null;\n  } // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n  // the value is not an array, convert the value using the list's item type.\n\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (isIterableObject(value)) {\n      const valuesNodes = [];\n      for (const item of value) {\n        const itemNode = astFromValue(item, itemType);\n        if (itemNode != null) {\n          valuesNodes.push(itemNode);\n        }\n      }\n      return {\n        kind: Kind.LIST,\n        values: valuesNodes\n      };\n    }\n    return astFromValue(value, itemType);\n  } // Populate the fields of the input object by creating ASTs from each value\n  // in the JavaScript object according to the fields in the input type.\n\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(value)) {\n      return null;\n    }\n    const fieldNodes = [];\n    for (const field of Object.values(type.getFields())) {\n      const fieldValue = astFromValue(value[field.name], field.type);\n      if (fieldValue) {\n        fieldNodes.push({\n          kind: Kind.OBJECT_FIELD,\n          name: {\n            kind: Kind.NAME,\n            value: field.name\n          },\n          value: fieldValue\n        });\n      }\n    }\n    return {\n      kind: Kind.OBJECT,\n      fields: fieldNodes\n    };\n  }\n  if (isLeafType(type)) {\n    // Since value is an internally represented value, it must be serialized\n    // to an externally represented value before converting into an AST.\n    const serialized = type.serialize(value);\n    if (serialized == null) {\n      return null;\n    } // Others serialize based on their corresponding JavaScript scalar types.\n\n    if (typeof serialized === 'boolean') {\n      return {\n        kind: Kind.BOOLEAN,\n        value: serialized\n      };\n    } // JavaScript numbers can be Int or Float values.\n\n    if (typeof serialized === 'number' && Number.isFinite(serialized)) {\n      const stringNum = String(serialized);\n      return integerStringRegExp.test(stringNum) ? {\n        kind: Kind.INT,\n        value: stringNum\n      } : {\n        kind: Kind.FLOAT,\n        value: stringNum\n      };\n    }\n    if (typeof serialized === 'string') {\n      // Enum types use Enum literals.\n      if (isEnumType(type)) {\n        return {\n          kind: Kind.ENUM,\n          value: serialized\n        };\n      } // ID types can use Int literals.\n\n      if (type === GraphQLID && integerStringRegExp.test(serialized)) {\n        return {\n          kind: Kind.INT,\n          value: serialized\n        };\n      }\n      return {\n        kind: Kind.STRING,\n        value: serialized\n      };\n    }\n    throw new TypeError(`Cannot convert value to AST: ${inspect(serialized)}.`);\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\n\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}