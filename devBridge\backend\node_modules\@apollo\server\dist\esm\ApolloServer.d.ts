import type { GatewayExecutor } from '@apollo/server-gateway-interface';
import { type KeyValueCache } from '@apollo/utils.keyvaluecache';
import type { Logger } from '@apollo/utils.logger';
import type { WithRequired } from '@apollo/utils.withrequired';
import { type Resolvable } from './utils/resolvable.js';
import { type DocumentNode, type FormattedExecutionResult, type GraphQLFieldResolver, type GraphQLFormattedError, type GraphQLSchema, type ParseOptions, type TypedQueryDocumentNode, type ValidationRule } from 'graphql';
import type { ExecuteOperationOptions, VariableValues } from './externalTypes/graphql.js';
import type { ApolloConfig, ApolloServerOptions, ApolloServerPlugin, BaseContext, ContextThunk, DocumentStore, GraphQLRequest, GraphQLResponse, HTTPGraphQLHead, HTTPGraphQLRequest, HTTPGraphQLResponse, LandingPage, PersistedQueryOptions } from './externalTypes/index.js';
import type { GraphQLExperimentalIncrementalExecutionResults } from './incrementalDeliveryPolyfill.js';
import { SchemaManager } from './utils/schemaManager.js';
export type SchemaDerivedData = {
    schema: GraphQLSchema;
    documentStore: DocumentStore | null;
    documentStoreKeyPrefix: string;
};
type RunningServerState = {
    schemaManager: SchemaManager;
    landingPage: LandingPage | null;
};
type ServerState = {
    phase: 'initialized';
    schemaManager: SchemaManager;
} | {
    phase: 'starting';
    barrier: Resolvable<void>;
    schemaManager: SchemaManager;
    startedInBackground: boolean;
} | {
    phase: 'failed to start';
    error: Error;
} | ({
    phase: 'started';
    drainServers: (() => Promise<void>) | null;
    toDispose: (() => Promise<void>)[];
    toDisposeLast: (() => Promise<void>)[];
} & RunningServerState) | ({
    phase: 'draining';
    barrier: Resolvable<void>;
} & RunningServerState) | {
    phase: 'stopping';
    barrier: Resolvable<void>;
} | {
    phase: 'stopped';
    stopError: Error | null;
};
export interface ApolloServerInternals<TContext extends BaseContext> {
    state: ServerState;
    gatewayExecutor: GatewayExecutor | null;
    dangerouslyDisableValidation?: boolean;
    formatError?: (formattedError: GraphQLFormattedError, error: unknown) => GraphQLFormattedError;
    includeStacktraceInErrorResponses: boolean;
    persistedQueries?: WithRequired<PersistedQueryOptions, 'cache'>;
    nodeEnv: string;
    allowBatchedHttpRequests: boolean;
    apolloConfig: ApolloConfig;
    plugins: ApolloServerPlugin<TContext>[];
    parseOptions: ParseOptions;
    stopOnTerminationSignals: boolean | undefined;
    csrfPreventionRequestHeaders: string[] | null;
    rootValue?: ((parsedQuery: DocumentNode) => unknown) | unknown;
    validationRules: Array<ValidationRule>;
    hideSchemaDetailsFromClientErrors: boolean;
    fieldResolver?: GraphQLFieldResolver<any, TContext>;
    status400ForVariableCoercionErrors?: boolean;
    __testing_incrementalExecutionResults?: GraphQLExperimentalIncrementalExecutionResults;
    stringifyResult: (value: FormattedExecutionResult) => string | Promise<string>;
}
export declare class ApolloServer<in out TContext extends BaseContext = BaseContext> {
    private internals;
    readonly cache: KeyValueCache<string>;
    readonly logger: Logger;
    constructor(config: ApolloServerOptions<TContext>);
    start(): Promise<void>;
    startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests(): void;
    private _start;
    private maybeRegisterTerminationSignalHandlers;
    private _ensureStarted;
    assertStarted(expressionForError: string): void;
    private logStartupError;
    private static constructSchema;
    private static generateSchemaDerivedData;
    stop(): Promise<void>;
    private addDefaultPlugins;
    addPlugin(plugin: ApolloServerPlugin<TContext>): void;
    executeHTTPGraphQLRequest({ httpGraphQLRequest, context, }: {
        httpGraphQLRequest: HTTPGraphQLRequest;
        context: ContextThunk<TContext>;
    }): Promise<HTTPGraphQLResponse>;
    private errorResponse;
    private prefersHTML;
    executeOperation<TData = Record<string, unknown>, TVariables extends VariableValues = VariableValues>(this: ApolloServer<BaseContext>, request: Omit<GraphQLRequest<TVariables>, 'query'> & {
        query?: string | DocumentNode | TypedQueryDocumentNode<TData, TVariables>;
    }): Promise<GraphQLResponse<TData>>;
    executeOperation<TData = Record<string, unknown>, TVariables extends VariableValues = VariableValues>(request: Omit<GraphQLRequest<TVariables>, 'query'> & {
        query?: string | DocumentNode | TypedQueryDocumentNode<TData, TVariables>;
    }, options?: ExecuteOperationOptions<TContext>): Promise<GraphQLResponse<TData>>;
}
export declare function internalExecuteOperation<TContext extends BaseContext>({ server, graphQLRequest, internals, schemaDerivedData, sharedResponseHTTPGraphQLHead, }: {
    server: ApolloServer<TContext>;
    graphQLRequest: GraphQLRequest;
    internals: ApolloServerInternals<TContext>;
    schemaDerivedData: SchemaDerivedData;
    sharedResponseHTTPGraphQLHead: HTTPGraphQLHead | null;
}, options: ExecuteOperationOptions<TContext>): Promise<GraphQLResponse>;
export type ImplicitlyInstallablePlugin<TContext extends BaseContext> = ApolloServerPlugin<TContext> & {
    __internal_installed_implicitly__: boolean;
};
export declare function isImplicitlyInstallablePlugin<TContext extends BaseContext>(p: ApolloServerPlugin<TContext>): p is ImplicitlyInstallablePlugin<TContext>;
export declare const MEDIA_TYPES: {
    APPLICATION_JSON: string;
    APPLICATION_JSON_GRAPHQL_CALLBACK: string;
    APPLICATION_GRAPHQL_RESPONSE_JSON: string;
    MULTIPART_MIXED_NO_DEFER_SPEC: string;
    MULTIPART_MIXED_EXPERIMENTAL: string;
    TEXT_HTML: string;
};
export declare function chooseContentTypeForSingleResultResponse(head: HTTPGraphQLHead): string | null;
export {};
//# sourceMappingURL=ApolloServer.d.ts.map