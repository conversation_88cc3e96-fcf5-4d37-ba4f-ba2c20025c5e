{"ast": null, "code": "/**\n * Build a string describing the path.\n */\nexport function printPathArray(path) {\n  return path.map(key => typeof key === 'number' ? '[' + key.toString() + ']' : '.' + key).join('');\n}", "map": {"version": 3, "names": ["printPathArray", "path", "map", "key", "toString", "join"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/jsutils/printPathArray.mjs"], "sourcesContent": ["/**\n * Build a string describing the path.\n */\nexport function printPathArray(path) {\n  return path\n    .map((key) =>\n      typeof key === 'number' ? '[' + key.toString() + ']' : '.' + key,\n    )\n    .join('');\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOA,IAAI,CACRC,GAAG,CAAEC,GAAG,IACP,OAAOA,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAGA,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGD,GAC/D,CAAC,CACAE,IAAI,CAAC,EAAE,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}