{"version": 3, "file": "ApolloServer.d.ts", "sourceRoot": "", "sources": ["../../src/ApolloServer.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAExE,OAAO,EAGL,KAAK,aAAa,EACnB,MAAM,6BAA6B,CAAC;AACrC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE/D,OAAmB,EAAE,KAAK,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAKL,KAAK,YAAY,EACjB,KAAK,wBAAwB,EAC7B,KAAK,oBAAoB,EACzB,KAAK,qBAAqB,EAC1B,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,sBAAsB,EAE3B,KAAK,cAAc,EACpB,MAAM,SAAS,CAAC;AAejB,OAAO,KAAK,EACV,uBAAuB,EACvB,cAAc,EACf,MAAM,4BAA4B,CAAC;AACpC,OAAO,KAAK,EACV,YAAY,EACZ,mBAAmB,EACnB,kBAAkB,EAClB,WAAW,EACX,YAAY,EACZ,aAAa,EACb,cAAc,EAEd,eAAe,EAGf,eAAe,EACf,kBAAkB,EAClB,mBAAmB,EACnB,WAAW,EACX,qBAAqB,EACtB,MAAM,0BAA0B,CAAC;AAElC,OAAO,KAAK,EAAE,8CAA8C,EAAE,MAAM,kCAAkC,CAAC;AAYvG,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAqBzD,MAAM,MAAM,iBAAiB,GAAG;IAC9B,MAAM,EAAE,aAAa,CAAC;IAItB,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC;IAMpC,sBAAsB,EAAE,MAAM,CAAC;CAChC,CAAC;AAEF,KAAK,kBAAkB,GAAG;IACxB,aAAa,EAAE,aAAa,CAAC;IAC7B,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;CACjC,CAAC;AAEF,KAAK,WAAW,GACZ;IACE,KAAK,EAAE,aAAa,CAAC;IACrB,aAAa,EAAE,aAAa,CAAC;CAC9B,GACD;IACE,KAAK,EAAE,UAAU,CAAC;IAClB,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1B,aAAa,EAAE,aAAa,CAAC;IAM7B,mBAAmB,EAAE,OAAO,CAAC;CAC9B,GACD;IACE,KAAK,EAAE,iBAAiB,CAAC;IACzB,KAAK,EAAE,KAAK,CAAC;CACd,GACD,CAAC;IACC,KAAK,EAAE,SAAS,CAAC;IACjB,YAAY,EAAE,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;IAC3C,SAAS,EAAE,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;IACnC,aAAa,EAAE,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;CACxC,GAAG,kBAAkB,CAAC,GACvB,CAAC;IACC,KAAK,EAAE,UAAU,CAAC;IAClB,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;CAC3B,GAAG,kBAAkB,CAAC,GACvB;IACE,KAAK,EAAE,UAAU,CAAC;IAClB,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;CAC3B,GACD;IACE,KAAK,EAAE,SAAS,CAAC;IACjB,SAAS,EAAE,KAAK,GAAG,IAAI,CAAC;CACzB,CAAC;AAEN,MAAM,WAAW,qBAAqB,CAAC,QAAQ,SAAS,WAAW;IACjE,KAAK,EAAE,WAAW,CAAC;IACnB,eAAe,EAAE,eAAe,GAAG,IAAI,CAAC;IACxC,4BAA4B,CAAC,EAAE,OAAO,CAAC;IACvC,WAAW,CAAC,EAAE,CACZ,cAAc,EAAE,qBAAqB,EACrC,KAAK,EAAE,OAAO,KACX,qBAAqB,CAAC;IAC3B,iCAAiC,EAAE,OAAO,CAAC;IAC3C,gBAAgB,CAAC,EAAE,YAAY,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAChE,OAAO,EAAE,MAAM,CAAC;IAChB,wBAAwB,EAAE,OAAO,CAAC;IAClC,YAAY,EAAE,YAAY,CAAC;IAC3B,OAAO,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;IACxC,YAAY,EAAE,YAAY,CAAC;IAI3B,wBAAwB,EAAE,OAAO,GAAG,SAAS,CAAC;IAC9C,4BAA4B,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAE9C,SAAS,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,YAAY,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;IAC/D,eAAe,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;IACvC,iCAAiC,EAAE,OAAO,CAAC;IAC3C,aAAa,CAAC,EAAE,oBAAoB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAGpD,kCAAkC,CAAC,EAAE,OAAO,CAAC;IAC7C,qCAAqC,CAAC,EAAE,8CAA8C,CAAC;IACvF,eAAe,EAAE,CACf,KAAK,EAAE,wBAAwB,KAC5B,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC/B;AA6BD,qBAAa,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,SAAS,WAAW,GAAG,WAAW;IACzE,OAAO,CAAC,SAAS,CAAkC;IAEnD,SAAgB,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAC7C,SAAgB,MAAM,EAAE,MAAM,CAAC;gBAEnB,MAAM,EAAE,mBAAmB,CAAC,QAAQ,CAAC;IAwJpC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B,oEAAoE,IAAI,IAAI;YAIrE,MAAM;IAsJpB,OAAO,CAAC,sCAAsC;YAsEhC,cAAc;IAgErB,aAAa,CAAC,kBAAkB,EAAE,MAAM;IAwB/C,OAAO,CAAC,eAAe;IAQvB,OAAO,CAAC,MAAM,CAAC,eAAe;IAsB9B,OAAO,CAAC,MAAM,CAAC,yBAAyB;IAoC3B,IAAI;YAoFH,iBAAiB;IAkLxB,SAAS,CAAC,MAAM,EAAE,kBAAkB,CAAC,QAAQ,CAAC;IAOxC,yBAAyB,CAAC,EACrC,kBAAkB,EAClB,OAAO,GACR,EAAE;QACD,kBAAkB,EAAE,kBAAkB,CAAC;QACvC,OAAO,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;KACjC,GAAG,OAAO,CAAC,mBAAmB,CAAC;YAwGlB,aAAa;IAuC3B,OAAO,CAAC,WAAW;IAwCN,gBAAgB,CAC3B,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC/B,UAAU,SAAS,cAAc,GAAG,cAAc,EAElD,IAAI,EAAE,YAAY,CAAC,WAAW,CAAC,EAC/B,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG;QACnD,KAAK,CAAC,EAAE,MAAM,GAAG,YAAY,GAAG,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KAC3E,GACA,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACrB,gBAAgB,CAC3B,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC/B,UAAU,SAAS,cAAc,GAAG,cAAc,EAElD,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG;QACnD,KAAK,CAAC,EAAE,MAAM,GAAG,YAAY,GAAG,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KAC3E,EACD,OAAO,CAAC,EAAE,uBAAuB,CAAC,QAAQ,CAAC,GAC1C,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;CAkDnC;AAID,wBAAsB,wBAAwB,CAAC,QAAQ,SAAS,WAAW,EACzE,EACE,MAAM,EACN,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,6BAA6B,GAC9B,EAAE;IACD,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC/B,cAAc,EAAE,cAAc,CAAC;IAC/B,SAAS,EAAE,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAC3C,iBAAiB,EAAE,iBAAiB,CAAC;IACrC,6BAA6B,EAAE,eAAe,GAAG,IAAI,CAAC;CACvD,EACD,OAAO,EAAE,uBAAuB,CAAC,QAAQ,CAAC,GACzC,OAAO,CAAC,eAAe,CAAC,CAuD1B;AAQD,MAAM,MAAM,2BAA2B,CAAC,QAAQ,SAAS,WAAW,IAClE,kBAAkB,CAAC,QAAQ,CAAC,GAAG;IAC7B,iCAAiC,EAAE,OAAO,CAAC;CAC5C,CAAC;AAEJ,wBAAgB,6BAA6B,CAAC,QAAQ,SAAS,WAAW,EACxE,CAAC,EAAE,kBAAkB,CAAC,QAAQ,CAAC,GAC9B,CAAC,IAAI,2BAA2B,CAAC,QAAQ,CAAC,CAE5C;AAED,eAAO,MAAM,WAAW;;;;;;;CAWvB,CAAC;AAEF,wBAAgB,wCAAwC,CACtD,IAAI,EAAE,eAAe,GACpB,MAAM,GAAG,IAAI,CAqBf"}