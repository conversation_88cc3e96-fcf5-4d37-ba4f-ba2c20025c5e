{"version": 3, "file": "PrefixingKeyValueCache.js", "sourceRoot": "", "sources": ["../src/PrefixingKeyValueCache.ts"], "names": [], "mappings": ";;;;AAEA,MAAM,wCAAwC,GAAG,MAAM,CACrD,oCAAoC,CACrC,CAAC;AAYF,MAAa,sBAAsB;IAIjC,YAAoB,OAAyB,EAAE,MAAc;QAAzC,YAAO,GAAP,OAAO,CAAkB;QAC3C,IAAI,sBAAsB,CAAC,kCAAkC,CAAC,OAAO,CAAC,EAAE;YACtE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YAKjB,IAAI,CAAC,wCAAwC,CAAC,GAAG,IAAI,CAAC;SACvD;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;IACH,CAAC;IAED,GAAG,CAAC,GAAW;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,GAAG,CAAC,GAAW,EAAE,KAAQ,EAAE,OAAiC;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IACD,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IAChD,CAAC;IAKD,MAAM,CAAC,kCAAkC,CACvC,CAAmB;QAEnB,OAAO,wCAAwC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,+CAA+C,CACpD,CAAmB;QAEnB,OAAO,IAAI,uCAAuC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;CACF;AAzCD,wDAyCC;AAID,MAAM,uCAAuC;IAK3C,YAAoB,OAAyB;QAAzB,YAAO,GAAP,OAAO,CAAkB;QAF7C,QAA0C,GAAG,IAAI,CAAC;IAEF,CAAC;IAEjD,GAAG,CAAC,GAAW;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IACD,GAAG,CAAC,GAAW,EAAE,KAAQ,EAAE,OAAiC;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IACD,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;CACF;KAbE,wCAAwC"}