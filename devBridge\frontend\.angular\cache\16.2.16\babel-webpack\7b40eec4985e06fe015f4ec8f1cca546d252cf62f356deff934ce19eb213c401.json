{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link endOfISOWeekYear} function options.\n */\n\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of an ISO week-numbering year\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\nexport function endOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuaryOfNextYear = constructFrom(options?.in || date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const _date = startOfISOWeek(fourthOfJanuaryOfNextYear, options);\n  _date.setMilliseconds(_date.getMilliseconds() - 1);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeekYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}