# @smithy/signature-v4

[![NPM version](https://img.shields.io/npm/v/@smithy/signature-v4/latest.svg)](https://www.npmjs.com/package/@smithy/signature-v4)
[![NPM downloads](https://img.shields.io/npm/dm/@smithy/signature-v4.svg)](https://www.npmjs.com/package/@smithy/signature-v4)

This package contains an implementation of the [AWS Signature Version 4](https://docs.aws.amazon.com/AmazonS3/latest/API/sig-v4-authenticating-requests.html)
authentication scheme.

It is internal to Smithy-TypeScript generated clients, and not generally intended for standalone usage outside this context.

For custom usage, inspect the interface of the SignatureV4 class.
