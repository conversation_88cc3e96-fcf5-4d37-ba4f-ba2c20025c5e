{"ast": null, "code": "/**\n * Returns a number indicating whether a reference string comes before, or after,\n * or is the same as the given string in natural sort order.\n *\n * See: https://en.wikipedia.org/wiki/Natural_sort_order\n *\n */\nexport function naturalCompare(aStr, bStr) {\n  let aIndex = 0;\n  let bIndex = 0;\n  while (aIndex < aStr.length && bIndex < bStr.length) {\n    let aChar = aStr.charCodeAt(aIndex);\n    let bChar = bStr.charCodeAt(bIndex);\n    if (isDigit(aChar) && isDigit(bChar)) {\n      let aNum = 0;\n      do {\n        ++aIndex;\n        aNum = aNum * 10 + aChar - DIGIT_0;\n        aChar = aStr.charCodeAt(aIndex);\n      } while (isDigit(aChar) && aNum > 0);\n      let bNum = 0;\n      do {\n        ++bIndex;\n        bNum = bNum * 10 + bChar - DIGIT_0;\n        bChar = bStr.charCodeAt(bIndex);\n      } while (isDigit(bChar) && bNum > 0);\n      if (aNum < bNum) {\n        return -1;\n      }\n      if (aNum > bNum) {\n        return 1;\n      }\n    } else {\n      if (aChar < bChar) {\n        return -1;\n      }\n      if (aChar > bChar) {\n        return 1;\n      }\n      ++aIndex;\n      ++bIndex;\n    }\n  }\n  return aStr.length - bStr.length;\n}\nconst DIGIT_0 = 48;\nconst DIGIT_9 = 57;\nfunction isDigit(code) {\n  return !isNaN(code) && DIGIT_0 <= code && code <= DIGIT_9;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}