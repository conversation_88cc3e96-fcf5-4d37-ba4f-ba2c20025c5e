{"ast": null, "code": "import { Slot } from \"optimism\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { canUseWeakMap, canUseWeakSet } from \"../utilities/index.js\";\nexport var MapImpl = canUseWeakMap ? WeakMap : Map;\nexport var SetImpl = canUseWeakSet ? WeakSet : Set;\n// Contextual slot that allows us to disable accessor warnings on fields when in\n// migrate mode.\n/** @internal */\nexport var disableWarningsSlot = new Slot();\nvar issuedWarning = false;\nexport function warnOnImproperCacheImplementation() {\n  if (!issuedWarning) {\n    issuedWarning = true;\n    globalThis.__DEV__ !== false && invariant.warn(52);\n  }\n}\n//# sourceMappingURL=utils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}