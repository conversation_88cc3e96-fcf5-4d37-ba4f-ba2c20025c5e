const { Team } = require('../models/Team');
const User = require('../models/User');
const TeamHistory = require('../models/TeamHistory');
const { logger } = require('../utils/logger');

// ==================== STATISTIQUES ====================

// Obtenir les statistiques d'une équipe
exports.getTeamStats = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;

    const team = await Team.findById(teamId)
      .populate('admin', 'fullName email')
      .populate('coAdmins', 'fullName email')
      .populate('moderators', 'fullName email')
      .populate('members', 'fullName email');

    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.isMember(userId) && !team.isPublic) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès aux statistiques de cette équipe" 
      });
    }

    // Statistiques de base
    const basicStats = {
      totalMembers: team.memberCount,
      maxMembers: team.maxMembers,
      availableSlots: team.availableSlots,
      completionRate: team.getCompletionRate(),
      isFullTeam: team.isFullTeam,
      status: team.status,
      createdAt: team.createdAt,
      lastActivity: team.stats.lastActivity
    };

    // Répartition des rôles
    const roleDistribution = {
      admin: 1,
      coAdmins: team.coAdmins ? team.coAdmins.length : 0,
      moderators: team.moderators ? team.moderators.length : 0,
      members: team.members ? team.members.length : 0
    };

    // Statistiques d'activité (30 derniers jours)
    const activityStats = await TeamHistory.getActivityStats(teamId, 30);

    // Statistiques des projets et tâches
    const projectStats = {
      totalProjects: team.stats.totalProjects || 0,
      completedTasks: team.stats.completedTasks || 0,
      totalTasks: team.stats.totalTasks || 0,
      pendingTasks: (team.stats.totalTasks || 0) - (team.stats.completedTasks || 0)
    };

    res.json({
      teamId: team._id,
      teamName: team.name,
      basicStats,
      roleDistribution,
      projectStats,
      activityStats,
      generatedAt: new Date()
    });
  } catch (error) {
    logger.error('Erreur récupération statistiques:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des statistiques", 
      error: error.message 
    });
  }
};

// Obtenir les analytics avancées d'une équipe
exports.getTeamAnalytics = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { period = 30 } = req.query; // Période en jours

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions (admin/co-admin seulement)
    if (!team.hasAdminRights(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès aux analytics de cette équipe" 
      });
    }

    const periodDays = parseInt(period);
    const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);

    // Analytics d'activité
    const activityAnalytics = await TeamHistory.aggregate([
      {
        $match: {
          team: mongoose.Types.ObjectId(teamId),
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            date: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            category: '$category'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          activities: {
            $push: {
              category: '$_id.category',
              count: '$count'
            }
          },
          totalActivity: { $sum: '$count' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Analytics des membres
    const memberAnalytics = await TeamHistory.aggregate([
      {
        $match: {
          team: mongoose.Types.ObjectId(teamId),
          category: 'member',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      }
    ]);

    // Top contributeurs
    const topContributors = await TeamHistory.aggregate([
      {
        $match: {
          team: mongoose.Types.ObjectId(teamId),
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$user',
          activityCount: { $sum: 1 },
          categories: { $addToSet: '$category' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          user: {
            _id: '$user._id',
            fullName: '$user.fullName',
            email: '$user.email',
            profileImage: '$user.profileImage'
          },
          activityCount: 1,
          categories: 1
        }
      },
      { $sort: { activityCount: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      teamId: team._id,
      teamName: team.name,
      period: periodDays,
      analytics: {
        activity: activityAnalytics,
        members: memberAnalytics,
        topContributors
      },
      generatedAt: new Date()
    });
  } catch (error) {
    logger.error('Erreur récupération analytics:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des analytics", 
      error: error.message 
    });
  }
};

// Obtenir l'activité récente d'une équipe
exports.getTeamActivity = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { limit = 20, category, action } = req.query;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès à l'activité de cette équipe" 
      });
    }

    const activity = await TeamHistory.getTeamHistory(teamId, {
      limit: parseInt(limit),
      category,
      action
    });

    res.json({
      teamId: team._id,
      teamName: team.name,
      activity,
      count: activity.length
    });
  } catch (error) {
    logger.error('Erreur récupération activité:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération de l'activité", 
      error: error.message 
    });
  }
};

// ==================== TEMPLATES ET DUPLICATION ====================

// Dupliquer une équipe
exports.duplicateTeam = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { name, description, includeMembers = false } = req.body;

    const originalTeam = await Team.findById(teamId);
    if (!originalTeam) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!originalTeam.hasAdminRights(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour dupliquer cette équipe" 
      });
    }

    // Vérifier que le nouveau nom n'existe pas
    const existingTeam = await Team.findOne({ name });
    if (existingTeam) {
      return res.status(400).json({ 
        message: "Une équipe avec ce nom existe déjà" 
      });
    }

    // Créer la nouvelle équipe
    const newTeamData = {
      name,
      description: description || originalTeam.description,
      admin: userId,
      members: [userId],
      maxMembers: originalTeam.maxMembers,
      tags: [...originalTeam.tags],
      isPublic: originalTeam.isPublic,
      allowJoinRequests: originalTeam.allowJoinRequests,
      requireApproval: originalTeam.requireApproval,
      settings: { ...originalTeam.settings },
      originalTeam: teamId
    };

    // Inclure les membres si demandé
    if (includeMembers) {
      newTeamData.members = [...originalTeam.members];
      newTeamData.coAdmins = [...(originalTeam.coAdmins || [])];
      newTeamData.moderators = [...(originalTeam.moderators || [])];
    }

    const newTeam = new Team(newTeamData);
    await newTeam.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId, 
      userId, 
      'duplicated', 
      `Équipe dupliquée vers "${name}"`,
      { 
        newData: { newTeamId: newTeam._id, newTeamName: name },
        category: 'team'
      }
    );

    await TeamHistory.logAction(
      newTeam._id, 
      userId, 
      'created', 
      `Équipe créée par duplication de "${originalTeam.name}"`,
      { 
        newData: { originalTeamId: teamId, originalTeamName: originalTeam.name },
        category: 'team'
      }
    );

    await newTeam.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Équipe ${originalTeam.name} dupliquée vers ${name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Équipe dupliquée avec succès",
      originalTeam: {
        _id: originalTeam._id,
        name: originalTeam.name
      },
      newTeam
    });
  } catch (error) {
    logger.error('Erreur duplication équipe:', error);
    res.status(500).json({ 
      message: "Erreur lors de la duplication de l'équipe", 
      error: error.message 
    });
  }
};

// Créer un template à partir d'une équipe
exports.createTemplate = async (req, res) => {
  try {
    const teamId = req.params.id;
    const userId = req.user.id;
    const { templateName, templateCategory = 'general', description } = req.body;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.hasAdminRights(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour créer un template" 
      });
    }

    // Créer le template
    const templateData = {
      name: templateName,
      description: description || team.description,
      admin: userId,
      members: [userId],
      maxMembers: team.maxMembers,
      tags: [...team.tags],
      isPublic: true,
      isTemplate: true,
      templateCategory,
      settings: { ...team.settings },
      originalTeam: teamId
    };

    const template = new Team(templateData);
    await template.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId, 
      userId, 
      'template_created', 
      `Template "${templateName}" créé`,
      { 
        newData: { templateId: template._id, templateName, templateCategory },
        category: 'team'
      }
    );

    logger.info(`Template ${templateName} créé à partir de l'équipe ${team.name} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Template créé avec succès",
      template
    });
  } catch (error) {
    logger.error('Erreur création template:', error);
    res.status(500).json({ 
      message: "Erreur lors de la création du template", 
      error: error.message 
    });
  }
};

module.exports = {
  getTeamStats: exports.getTeamStats,
  getTeamAnalytics: exports.getTeamAnalytics,
  getTeamActivity: exports.getTeamActivity,
  duplicateTeam: exports.duplicateTeam,
  createTemplate: exports.createTemplate
};
