/**
 * @internal
 *
 * Serializes a number, turning non-numeric values into strings.
 *
 * @param value - The number to serialize.
 * @returns A number, or a string if the given number was non-numeric.
 */
export declare const serializeFloat: (value: number) => string | number;
/**
 * @internal
 * @param date - to be serialized.
 * @returns https://smithy.io/2.0/spec/protocol-traits.html#timestampformat-trait date-time format.
 */
export declare const serializeDateTime: (date: Date) => string;
