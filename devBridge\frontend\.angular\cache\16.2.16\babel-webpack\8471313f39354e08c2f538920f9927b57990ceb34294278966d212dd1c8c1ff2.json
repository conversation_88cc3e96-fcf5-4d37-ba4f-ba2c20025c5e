{"ast": null, "code": "import { Kind } from \"graphql\";\nimport { getFragmentMaskMode, maybeDeep<PERSON>reeze, resultKeyNameFromField } from \"../utilities/index.js\";\nimport { disableWarningsSlot } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nexport function maskDefinition(data, selectionSet, context) {\n  return disableWarningsSlot.withValue(true, function () {\n    var masked = maskSelectionSet(data, selectionSet, context, false);\n    if (Object.isFrozen(data)) {\n      maybeDeepFreeze(masked);\n    }\n    return masked;\n  });\n}\nfunction getMutableTarget(data, mutableTargets) {\n  if (mutableTargets.has(data)) {\n    return mutableTargets.get(data);\n  }\n  var mutableTarget = Array.isArray(data) ? [] : Object.create(null);\n  mutableTargets.set(data, mutableTarget);\n  return mutableTarget;\n}\nfunction maskSelectionSet(data, selectionSet, context, migration, path) {\n  var _a;\n  var knownChanged = context.knownChanged;\n  var memo = getMutableTarget(data, context.mutableTargets);\n  if (Array.isArray(data)) {\n    for (var _i = 0, _b = Array.from(data.entries()); _i < _b.length; _i++) {\n      var _c = _b[_i],\n        index = _c[0],\n        item = _c[1];\n      if (item === null) {\n        memo[index] = null;\n        continue;\n      }\n      var masked = maskSelectionSet(item, selectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \"[\").concat(index, \"]\") : void 0);\n      if (knownChanged.has(masked)) {\n        knownChanged.add(memo);\n      }\n      memo[index] = masked;\n    }\n    return knownChanged.has(memo) ? memo : data;\n  }\n  for (var _d = 0, _e = selectionSet.selections; _d < _e.length; _d++) {\n    var selection = _e[_d];\n    var value = void 0;\n    // we later want to add acessor warnings to the final result\n    // so we need a new object to add the accessor warning to\n    if (migration) {\n      knownChanged.add(memo);\n    }\n    if (selection.kind === Kind.FIELD) {\n      var keyName = resultKeyNameFromField(selection);\n      var childSelectionSet = selection.selectionSet;\n      value = memo[keyName] || data[keyName];\n      if (value === void 0) {\n        continue;\n      }\n      if (childSelectionSet && value !== null) {\n        var masked = maskSelectionSet(data[keyName], childSelectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \".\").concat(keyName) : void 0);\n        if (knownChanged.has(masked)) {\n          value = masked;\n        }\n      }\n      if (!(globalThis.__DEV__ !== false)) {\n        memo[keyName] = value;\n      }\n      if (globalThis.__DEV__ !== false) {\n        if (migration && keyName !== \"__typename\" &&\n        // either the field is not present in the memo object\n        // or it has a `get` descriptor, not a `value` descriptor\n        // => it is a warning accessor and we can overwrite it\n        // with another accessor\n        !((_a = Object.getOwnPropertyDescriptor(memo, keyName)) === null || _a === void 0 ? void 0 : _a.value)) {\n          Object.defineProperty(memo, keyName, getAccessorWarningDescriptor(keyName, value, path || \"\", context.operationName, context.operationType));\n        } else {\n          delete memo[keyName];\n          memo[keyName] = value;\n        }\n      }\n    }\n    if (selection.kind === Kind.INLINE_FRAGMENT && (!selection.typeCondition || context.cache.fragmentMatches(selection, data.__typename))) {\n      value = maskSelectionSet(data, selection.selectionSet, context, migration, path);\n    }\n    if (selection.kind === Kind.FRAGMENT_SPREAD) {\n      var fragmentName = selection.name.value;\n      var fragment = context.fragmentMap[fragmentName] || (context.fragmentMap[fragmentName] = context.cache.lookupFragment(fragmentName));\n      invariant(fragment, 47, fragmentName);\n      var mode = getFragmentMaskMode(selection);\n      if (mode !== \"mask\") {\n        value = maskSelectionSet(data, fragment.selectionSet, context, mode === \"migrate\", path);\n      }\n    }\n    if (knownChanged.has(value)) {\n      knownChanged.add(memo);\n    }\n  }\n  if (\"__typename\" in data && !(\"__typename\" in memo)) {\n    memo.__typename = data.__typename;\n  }\n  // This check prevents cases where masked fields may accidentally be\n  // returned as part of this object when the fragment also selects\n  // additional fields from the same child selection.\n  if (Object.keys(memo).length !== Object.keys(data).length) {\n    knownChanged.add(memo);\n  }\n  return knownChanged.has(memo) ? memo : data;\n}\nfunction getAccessorWarningDescriptor(fieldName, value, path, operationName, operationType) {\n  var getValue = function () {\n    if (disableWarningsSlot.getValue()) {\n      return value;\n    }\n    globalThis.__DEV__ !== false && invariant.warn(48, operationName ? \"\".concat(operationType, \" '\").concat(operationName, \"'\") : \"anonymous \".concat(operationType), \"\".concat(path, \".\").concat(fieldName).replace(/^\\./, \"\"));\n    getValue = function () {\n      return value;\n    };\n    return value;\n  };\n  return {\n    get: function () {\n      return getValue();\n    },\n    set: function (newValue) {\n      getValue = function () {\n        return newValue;\n      };\n    },\n    enumerable: true,\n    configurable: true\n  };\n}", "map": {"version": 3, "names": ["Kind", "getFragmentMaskMode", "maybeDeepFreeze", "resultKeyNameFromField", "disableWarningsSlot", "invariant", "maskDefinition", "data", "selectionSet", "context", "with<PERSON><PERSON><PERSON>", "masked", "maskSelectionSet", "Object", "isFrozen", "getMutableTarget", "mutableTargets", "has", "get", "mutableTarget", "Array", "isArray", "create", "set", "migration", "path", "_a", "knownChanged", "memo", "_i", "_b", "from", "entries", "length", "_c", "index", "item", "globalThis", "__DEV__", "concat", "add", "_d", "_e", "selections", "selection", "value", "kind", "FIELD", "keyName", "childSelectionSet", "getOwnPropertyDescriptor", "defineProperty", "getAccessorWarningDescriptor", "operationName", "operationType", "INLINE_FRAGMENT", "typeCondition", "cache", "fragmentMatches", "__typename", "FRAGMENT_SPREAD", "fragmentName", "name", "fragment", "fragmentMap", "lookupFragment", "mode", "keys", "fieldName", "getValue", "warn", "replace", "newValue", "enumerable", "configurable"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@apollo/client/masking/maskDefinition.js"], "sourcesContent": ["import { Kind } from \"graphql\";\nimport { getFragmentMaskMode, maybeDeep<PERSON>reeze, resultKeyNameFromField, } from \"../utilities/index.js\";\nimport { disableWarningsSlot } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nexport function maskDefinition(data, selectionSet, context) {\n    return disableWarningsSlot.withValue(true, function () {\n        var masked = maskSelectionSet(data, selectionSet, context, false);\n        if (Object.isFrozen(data)) {\n            maybeDeepFreeze(masked);\n        }\n        return masked;\n    });\n}\nfunction getMutableTarget(data, mutableTargets) {\n    if (mutableTargets.has(data)) {\n        return mutableTargets.get(data);\n    }\n    var mutableTarget = Array.isArray(data) ? [] : Object.create(null);\n    mutableTargets.set(data, mutableTarget);\n    return mutableTarget;\n}\nfunction maskSelectionSet(data, selectionSet, context, migration, path) {\n    var _a;\n    var knownChanged = context.knownChanged;\n    var memo = getMutableTarget(data, context.mutableTargets);\n    if (Array.isArray(data)) {\n        for (var _i = 0, _b = Array.from(data.entries()); _i < _b.length; _i++) {\n            var _c = _b[_i], index = _c[0], item = _c[1];\n            if (item === null) {\n                memo[index] = null;\n                continue;\n            }\n            var masked = maskSelectionSet(item, selectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \"[\").concat(index, \"]\") : void 0);\n            if (knownChanged.has(masked)) {\n                knownChanged.add(memo);\n            }\n            memo[index] = masked;\n        }\n        return knownChanged.has(memo) ? memo : data;\n    }\n    for (var _d = 0, _e = selectionSet.selections; _d < _e.length; _d++) {\n        var selection = _e[_d];\n        var value = void 0;\n        // we later want to add acessor warnings to the final result\n        // so we need a new object to add the accessor warning to\n        if (migration) {\n            knownChanged.add(memo);\n        }\n        if (selection.kind === Kind.FIELD) {\n            var keyName = resultKeyNameFromField(selection);\n            var childSelectionSet = selection.selectionSet;\n            value = memo[keyName] || data[keyName];\n            if (value === void 0) {\n                continue;\n            }\n            if (childSelectionSet && value !== null) {\n                var masked = maskSelectionSet(data[keyName], childSelectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \".\").concat(keyName) : void 0);\n                if (knownChanged.has(masked)) {\n                    value = masked;\n                }\n            }\n            if (!(globalThis.__DEV__ !== false)) {\n                memo[keyName] = value;\n            }\n            if (globalThis.__DEV__ !== false) {\n                if (migration &&\n                    keyName !== \"__typename\" &&\n                    // either the field is not present in the memo object\n                    // or it has a `get` descriptor, not a `value` descriptor\n                    // => it is a warning accessor and we can overwrite it\n                    // with another accessor\n                    !((_a = Object.getOwnPropertyDescriptor(memo, keyName)) === null || _a === void 0 ? void 0 : _a.value)) {\n                    Object.defineProperty(memo, keyName, getAccessorWarningDescriptor(keyName, value, path || \"\", context.operationName, context.operationType));\n                }\n                else {\n                    delete memo[keyName];\n                    memo[keyName] = value;\n                }\n            }\n        }\n        if (selection.kind === Kind.INLINE_FRAGMENT &&\n            (!selection.typeCondition ||\n                context.cache.fragmentMatches(selection, data.__typename))) {\n            value = maskSelectionSet(data, selection.selectionSet, context, migration, path);\n        }\n        if (selection.kind === Kind.FRAGMENT_SPREAD) {\n            var fragmentName = selection.name.value;\n            var fragment = context.fragmentMap[fragmentName] ||\n                (context.fragmentMap[fragmentName] =\n                    context.cache.lookupFragment(fragmentName));\n            invariant(fragment, 47, fragmentName);\n            var mode = getFragmentMaskMode(selection);\n            if (mode !== \"mask\") {\n                value = maskSelectionSet(data, fragment.selectionSet, context, mode === \"migrate\", path);\n            }\n        }\n        if (knownChanged.has(value)) {\n            knownChanged.add(memo);\n        }\n    }\n    if (\"__typename\" in data && !(\"__typename\" in memo)) {\n        memo.__typename = data.__typename;\n    }\n    // This check prevents cases where masked fields may accidentally be\n    // returned as part of this object when the fragment also selects\n    // additional fields from the same child selection.\n    if (Object.keys(memo).length !== Object.keys(data).length) {\n        knownChanged.add(memo);\n    }\n    return knownChanged.has(memo) ? memo : data;\n}\nfunction getAccessorWarningDescriptor(fieldName, value, path, operationName, operationType) {\n    var getValue = function () {\n        if (disableWarningsSlot.getValue()) {\n            return value;\n        }\n        globalThis.__DEV__ !== false && invariant.warn(48, operationName ?\n            \"\".concat(operationType, \" '\").concat(operationName, \"'\")\n            : \"anonymous \".concat(operationType), \"\".concat(path, \".\").concat(fieldName).replace(/^\\./, \"\"));\n        getValue = function () { return value; };\n        return value;\n    };\n    return {\n        get: function () {\n            return getValue();\n        },\n        set: function (newValue) {\n            getValue = function () { return newValue; };\n        },\n        enumerable: true,\n        configurable: true,\n    };\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,SAAS;AAC9B,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,sBAAsB,QAAS,uBAAuB;AACrG,SAASC,mBAAmB,QAAQ,YAAY;AAChD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAE;EACxD,OAAOL,mBAAmB,CAACM,SAAS,CAAC,IAAI,EAAE,YAAY;IACnD,IAAIC,MAAM,GAAGC,gBAAgB,CAACL,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAE,KAAK,CAAC;IACjE,IAAII,MAAM,CAACC,QAAQ,CAACP,IAAI,CAAC,EAAE;MACvBL,eAAe,CAACS,MAAM,CAAC;IAC3B;IACA,OAAOA,MAAM;EACjB,CAAC,CAAC;AACN;AACA,SAASI,gBAAgBA,CAACR,IAAI,EAAES,cAAc,EAAE;EAC5C,IAAIA,cAAc,CAACC,GAAG,CAACV,IAAI,CAAC,EAAE;IAC1B,OAAOS,cAAc,CAACE,GAAG,CAACX,IAAI,CAAC;EACnC;EACA,IAAIY,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACd,IAAI,CAAC,GAAG,EAAE,GAAGM,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC;EAClEN,cAAc,CAACO,GAAG,CAAChB,IAAI,EAAEY,aAAa,CAAC;EACvC,OAAOA,aAAa;AACxB;AACA,SAASP,gBAAgBA,CAACL,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAEe,SAAS,EAAEC,IAAI,EAAE;EACpE,IAAIC,EAAE;EACN,IAAIC,YAAY,GAAGlB,OAAO,CAACkB,YAAY;EACvC,IAAIC,IAAI,GAAGb,gBAAgB,CAACR,IAAI,EAAEE,OAAO,CAACO,cAAc,CAAC;EACzD,IAAII,KAAK,CAACC,OAAO,CAACd,IAAI,CAAC,EAAE;IACrB,KAAK,IAAIsB,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGV,KAAK,CAACW,IAAI,CAACxB,IAAI,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAEH,EAAE,GAAGC,EAAE,CAACG,MAAM,EAAEJ,EAAE,EAAE,EAAE;MACpE,IAAIK,EAAE,GAAGJ,EAAE,CAACD,EAAE,CAAC;QAAEM,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;QAAEE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;MAC5C,IAAIE,IAAI,KAAK,IAAI,EAAE;QACfR,IAAI,CAACO,KAAK,CAAC,GAAG,IAAI;QAClB;MACJ;MACA,IAAIxB,MAAM,GAAGC,gBAAgB,CAACwB,IAAI,EAAE5B,YAAY,EAAEC,OAAO,EAAEe,SAAS,EAAEa,UAAU,CAACC,OAAO,KAAK,KAAK,GAAG,EAAE,CAACC,MAAM,CAACd,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAACc,MAAM,CAACJ,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;MAC5J,IAAIR,YAAY,CAACV,GAAG,CAACN,MAAM,CAAC,EAAE;QAC1BgB,YAAY,CAACa,GAAG,CAACZ,IAAI,CAAC;MAC1B;MACAA,IAAI,CAACO,KAAK,CAAC,GAAGxB,MAAM;IACxB;IACA,OAAOgB,YAAY,CAACV,GAAG,CAACW,IAAI,CAAC,GAAGA,IAAI,GAAGrB,IAAI;EAC/C;EACA,KAAK,IAAIkC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGlC,YAAY,CAACmC,UAAU,EAAEF,EAAE,GAAGC,EAAE,CAACT,MAAM,EAAEQ,EAAE,EAAE,EAAE;IACjE,IAAIG,SAAS,GAAGF,EAAE,CAACD,EAAE,CAAC;IACtB,IAAII,KAAK,GAAG,KAAK,CAAC;IAClB;IACA;IACA,IAAIrB,SAAS,EAAE;MACXG,YAAY,CAACa,GAAG,CAACZ,IAAI,CAAC;IAC1B;IACA,IAAIgB,SAAS,CAACE,IAAI,KAAK9C,IAAI,CAAC+C,KAAK,EAAE;MAC/B,IAAIC,OAAO,GAAG7C,sBAAsB,CAACyC,SAAS,CAAC;MAC/C,IAAIK,iBAAiB,GAAGL,SAAS,CAACpC,YAAY;MAC9CqC,KAAK,GAAGjB,IAAI,CAACoB,OAAO,CAAC,IAAIzC,IAAI,CAACyC,OAAO,CAAC;MACtC,IAAIH,KAAK,KAAK,KAAK,CAAC,EAAE;QAClB;MACJ;MACA,IAAII,iBAAiB,IAAIJ,KAAK,KAAK,IAAI,EAAE;QACrC,IAAIlC,MAAM,GAAGC,gBAAgB,CAACL,IAAI,CAACyC,OAAO,CAAC,EAAEC,iBAAiB,EAAExC,OAAO,EAAEe,SAAS,EAAEa,UAAU,CAACC,OAAO,KAAK,KAAK,GAAG,EAAE,CAACC,MAAM,CAACd,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAACc,MAAM,CAACS,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;QACvK,IAAIrB,YAAY,CAACV,GAAG,CAACN,MAAM,CAAC,EAAE;UAC1BkC,KAAK,GAAGlC,MAAM;QAClB;MACJ;MACA,IAAI,EAAE0B,UAAU,CAACC,OAAO,KAAK,KAAK,CAAC,EAAE;QACjCV,IAAI,CAACoB,OAAO,CAAC,GAAGH,KAAK;MACzB;MACA,IAAIR,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;QAC9B,IAAId,SAAS,IACTwB,OAAO,KAAK,YAAY;QACxB;QACA;QACA;QACA;QACA,EAAE,CAACtB,EAAE,GAAGb,MAAM,CAACqC,wBAAwB,CAACtB,IAAI,EAAEoB,OAAO,CAAC,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,KAAK,CAAC,EAAE;UACxGhC,MAAM,CAACsC,cAAc,CAACvB,IAAI,EAAEoB,OAAO,EAAEI,4BAA4B,CAACJ,OAAO,EAAEH,KAAK,EAAEpB,IAAI,IAAI,EAAE,EAAEhB,OAAO,CAAC4C,aAAa,EAAE5C,OAAO,CAAC6C,aAAa,CAAC,CAAC;QAChJ,CAAC,MACI;UACD,OAAO1B,IAAI,CAACoB,OAAO,CAAC;UACpBpB,IAAI,CAACoB,OAAO,CAAC,GAAGH,KAAK;QACzB;MACJ;IACJ;IACA,IAAID,SAAS,CAACE,IAAI,KAAK9C,IAAI,CAACuD,eAAe,KACtC,CAACX,SAAS,CAACY,aAAa,IACrB/C,OAAO,CAACgD,KAAK,CAACC,eAAe,CAACd,SAAS,EAAErC,IAAI,CAACoD,UAAU,CAAC,CAAC,EAAE;MAChEd,KAAK,GAAGjC,gBAAgB,CAACL,IAAI,EAAEqC,SAAS,CAACpC,YAAY,EAAEC,OAAO,EAAEe,SAAS,EAAEC,IAAI,CAAC;IACpF;IACA,IAAImB,SAAS,CAACE,IAAI,KAAK9C,IAAI,CAAC4D,eAAe,EAAE;MACzC,IAAIC,YAAY,GAAGjB,SAAS,CAACkB,IAAI,CAACjB,KAAK;MACvC,IAAIkB,QAAQ,GAAGtD,OAAO,CAACuD,WAAW,CAACH,YAAY,CAAC,KAC3CpD,OAAO,CAACuD,WAAW,CAACH,YAAY,CAAC,GAC9BpD,OAAO,CAACgD,KAAK,CAACQ,cAAc,CAACJ,YAAY,CAAC,CAAC;MACnDxD,SAAS,CAAC0D,QAAQ,EAAE,EAAE,EAAEF,YAAY,CAAC;MACrC,IAAIK,IAAI,GAAGjE,mBAAmB,CAAC2C,SAAS,CAAC;MACzC,IAAIsB,IAAI,KAAK,MAAM,EAAE;QACjBrB,KAAK,GAAGjC,gBAAgB,CAACL,IAAI,EAAEwD,QAAQ,CAACvD,YAAY,EAAEC,OAAO,EAAEyD,IAAI,KAAK,SAAS,EAAEzC,IAAI,CAAC;MAC5F;IACJ;IACA,IAAIE,YAAY,CAACV,GAAG,CAAC4B,KAAK,CAAC,EAAE;MACzBlB,YAAY,CAACa,GAAG,CAACZ,IAAI,CAAC;IAC1B;EACJ;EACA,IAAI,YAAY,IAAIrB,IAAI,IAAI,EAAE,YAAY,IAAIqB,IAAI,CAAC,EAAE;IACjDA,IAAI,CAAC+B,UAAU,GAAGpD,IAAI,CAACoD,UAAU;EACrC;EACA;EACA;EACA;EACA,IAAI9C,MAAM,CAACsD,IAAI,CAACvC,IAAI,CAAC,CAACK,MAAM,KAAKpB,MAAM,CAACsD,IAAI,CAAC5D,IAAI,CAAC,CAAC0B,MAAM,EAAE;IACvDN,YAAY,CAACa,GAAG,CAACZ,IAAI,CAAC;EAC1B;EACA,OAAOD,YAAY,CAACV,GAAG,CAACW,IAAI,CAAC,GAAGA,IAAI,GAAGrB,IAAI;AAC/C;AACA,SAAS6C,4BAA4BA,CAACgB,SAAS,EAAEvB,KAAK,EAAEpB,IAAI,EAAE4B,aAAa,EAAEC,aAAa,EAAE;EACxF,IAAIe,QAAQ,GAAG,SAAAA,CAAA,EAAY;IACvB,IAAIjE,mBAAmB,CAACiE,QAAQ,CAAC,CAAC,EAAE;MAChC,OAAOxB,KAAK;IAChB;IACAR,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIjC,SAAS,CAACiE,IAAI,CAAC,EAAE,EAAEjB,aAAa,GAC5D,EAAE,CAACd,MAAM,CAACe,aAAa,EAAE,IAAI,CAAC,CAACf,MAAM,CAACc,aAAa,EAAE,GAAG,CAAC,GACvD,YAAY,CAACd,MAAM,CAACe,aAAa,CAAC,EAAE,EAAE,CAACf,MAAM,CAACd,IAAI,EAAE,GAAG,CAAC,CAACc,MAAM,CAAC6B,SAAS,CAAC,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACpGF,QAAQ,GAAG,SAAAA,CAAA,EAAY;MAAE,OAAOxB,KAAK;IAAE,CAAC;IACxC,OAAOA,KAAK;EAChB,CAAC;EACD,OAAO;IACH3B,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAOmD,QAAQ,CAAC,CAAC;IACrB,CAAC;IACD9C,GAAG,EAAE,SAAAA,CAAUiD,QAAQ,EAAE;MACrBH,QAAQ,GAAG,SAAAA,CAAA,EAAY;QAAE,OAAOG,QAAQ;MAAE,CAAC;IAC/C,CAAC;IACDC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE;EAClB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}