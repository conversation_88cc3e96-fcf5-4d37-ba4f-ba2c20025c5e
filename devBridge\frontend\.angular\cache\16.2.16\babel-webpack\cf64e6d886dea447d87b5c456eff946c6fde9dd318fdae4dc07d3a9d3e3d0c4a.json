{"ast": null, "code": "import { syntaxError } from '../error/syntaxError.mjs';\nimport { Location, OperationTypeNode } from './ast.mjs';\nimport { DirectiveLocation } from './directiveLocation.mjs';\nimport { Kind } from './kinds.mjs';\nimport { isPunctuatorTokenKind, Lexer } from './lexer.mjs';\nimport { isSource, Source } from './source.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nexport function parse(source, options) {\n  const parser = new Parser(source, options);\n  return parser.parseDocument();\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nexport function parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nexport function parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nexport function parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nexport class Parser {\n  constructor(source, options = {}) {\n    const sourceObj = isSource(source) ? source : new Source(source);\n    this._lexer = new Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(TokenKind.NAME);\n    return this.node(token, {\n      kind: Kind.NAME,\n      value: token.value\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: Kind.DOCUMENT,\n      definitions: this.many(TokenKind.SOF, this.parseDefinition, TokenKind.EOF)\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription ? this._lexer.lookahead() : this._lexer.token;\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n        case 'type':\n          return this.parseObjectTypeDefinition();\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n        case 'union':\n          return this.parseUnionTypeDefinition();\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n      if (hasDescription) {\n        throw syntaxError(this._lexer.source, this._lexer.token.start, 'Unexpected description, descriptions are supported only on type definitions.');\n      }\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n        case 'fragment':\n          return this.parseFragmentDefinition();\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n    if (this.peek(TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: Kind.OPERATION_DEFINITION,\n        operation: OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet()\n      });\n    }\n    const operation = this.parseOperationType();\n    let name;\n    if (this.peek(TokenKind.NAME)) {\n      name = this.parseName();\n    }\n    return this.node(start, {\n      kind: Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(TokenKind.NAME);\n    switch (operationToken.value) {\n      case 'query':\n        return OperationTypeNode.QUERY;\n      case 'mutation':\n        return OperationTypeNode.MUTATION;\n      case 'subscription':\n        return OperationTypeNode.SUBSCRIPTION;\n    }\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseVariableDefinition, TokenKind.PAREN_R);\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(TokenKind.EQUALS) ? this.parseConstValueLiteral() : undefined,\n      directives: this.parseConstDirectives()\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: Kind.VARIABLE,\n      name: this.parseName()\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: Kind.SELECTION_SET,\n      selections: this.many(TokenKind.BRACE_L, this.parseSelection, TokenKind.BRACE_R)\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(TokenKind.SPREAD) ? this.parseFragment() : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n    if (this.expectOptionalToken(TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n    return this.node(start, {\n      kind: Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(TokenKind.BRACE_L) ? this.parseSelectionSet() : undefined\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(TokenKind.PAREN_L, item, TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst)\n    });\n  }\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n    if (!hasTypeCondition && this.peek(TokenKind.NAME)) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false)\n      });\n    }\n    return this.node(start, {\n      kind: Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet()\n      });\n    }\n    return this.node(start, {\n      kind: Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet()\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n    switch (token.kind) {\n      case TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n      case TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n      case TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.INT,\n          value: token.value\n        });\n      case TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: Kind.FLOAT,\n          value: token.value\n        });\n      case TokenKind.STRING:\n      case TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n      case TokenKind.NAME:\n        this.advanceLexer();\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: true\n            });\n          case 'false':\n            return this.node(token, {\n              kind: Kind.BOOLEAN,\n              value: false\n            });\n          case 'null':\n            return this.node(token, {\n              kind: Kind.NULL\n            });\n          default:\n            return this.node(token, {\n              kind: Kind.ENUM,\n              value: token.value\n            });\n        }\n      case TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(TokenKind.DOLLAR);\n          if (this._lexer.token.kind === TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw syntaxError(this._lexer.source, token.start, `Unexpected variable \"$${varName}\" in constant value.`);\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n        return this.parseVariable();\n      default:\n        throw this.unexpected();\n    }\n  }\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: Kind.STRING,\n      value: token.value,\n      block: token.kind === TokenKind.BLOCK_STRING\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n    return this.node(this._lexer.token, {\n      kind: Kind.LIST,\n      values: this.any(TokenKind.BRACKET_L, item, TokenKind.BRACKET_R)\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n    return this.node(this._lexer.token, {\n      kind: Kind.OBJECT,\n      fields: this.any(TokenKind.BRACE_L, item, TokenKind.BRACE_R)\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return this.node(start, {\n      kind: Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst)\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n    while (this.peek(TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n    return directives;\n  }\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(TokenKind.AT);\n    return this.node(start, {\n      kind: Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst)\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n    if (this.expectOptionalToken(TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: Kind.LIST_TYPE,\n        type: innerType\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n    if (this.expectOptionalToken(TokenKind.BANG)) {\n      return this.node(start, {\n        kind: Kind.NON_NULL_TYPE,\n        type\n      });\n    }\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: Kind.NAMED_TYPE,\n      name: this.parseName()\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(TokenKind.STRING) || this.peek(TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n    return this.node(start, {\n      kind: Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements') ? this.delimitedMany(TokenKind.AMP, this.parseNamedType) : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseFieldDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseInputValueDef, TokenKind.PAREN_R);\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n    if (this.expectOptionalToken(TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(TokenKind.EQUALS) ? this.delimitedMany(TokenKind.PIPE, this.parseNamedType) : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseEnumValueDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (this._lexer.token.value === 'true' || this._lexer.token.value === 'false' || this._lexer.token.value === 'null') {\n      throw syntaxError(this._lexer.source, this._lexer.token.start, `${getTokenDesc(this._lexer.token)} is reserved and cannot be used for an enum value.`);\n    }\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseInputValueDef, TokenKind.BRACE_R);\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n        case 'type':\n          return this.parseObjectTypeExtension();\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n        case 'union':\n          return this.parseUnionTypeExtension();\n        case 'enum':\n          return this.parseEnumTypeExtension();\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n    return this.node(start, {\n      kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    if (Object.prototype.hasOwnProperty.call(DirectiveLocation, name.value)) {\n      return name;\n    }\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new Location(startToken, this._lexer.lastToken, this._lexer.source);\n    }\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n    throw syntaxError(this._lexer.source, token.start, `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`);\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw syntaxError(this._lexer.source, token.start, `Expected \"${value}\", found ${getTokenDesc(token)}.`);\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token = atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return syntaxError(this._lexer.source, token.start, `Unexpected ${getTokenDesc(token)}.`);\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n      return nodes;\n    }\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n    return nodes;\n  }\n  advanceLexer() {\n    const {\n      maxTokens\n    } = this._options;\n    const token = this._lexer.advance();\n    if (maxTokens !== undefined && token.kind !== TokenKind.EOF) {\n      ++this._tokenCounter;\n      if (this._tokenCounter > maxTokens) {\n        throw syntaxError(this._lexer.source, token.start, `Document contains more that ${maxTokens} tokens. Parsing aborted.`);\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return isPunctuatorTokenKind(kind) ? `\"${kind}\"` : kind;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}