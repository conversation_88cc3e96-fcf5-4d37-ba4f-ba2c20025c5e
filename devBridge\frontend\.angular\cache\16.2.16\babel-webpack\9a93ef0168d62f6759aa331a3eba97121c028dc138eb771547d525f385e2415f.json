{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\n\n/**\n * The {@link differenceInCalendarISOWeekYears} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of calendar ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of calendar ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO week-numbering years\n *\n * @example\n * // How many calendar ISO week-numbering years are 1 January 2010 and 1 January 2012?\n * const result = differenceInCalendarISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 2\n */\nexport function differenceInCalendarISOWeekYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return getISOWeekYear(laterDate_, options) - getISOWeekYear(earlierDate_, options);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeekYears;", "map": {"version": 3, "names": ["normalizeDates", "getISOWeekYear", "differenceInCalendarISOWeekYears", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/differenceInCalendarISOWeekYears.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\n\n/**\n * The {@link differenceInCalendarISOWeekYears} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of calendar ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of calendar ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO week-numbering years\n *\n * @example\n * // How many calendar ISO week-numbering years are 1 January 2010 and 1 January 2012?\n * const result = differenceInCalendarISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 2\n */\nexport function differenceInCalendarISOWeekYears(\n  laterDate,\n  earlierDate,\n  options,\n) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    getISOWeekYear(laterDate_, options) - getISOWeekYear(earlierDate_, options)\n  );\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeekYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,qBAAqB;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gCAAgCA,CAC9CC,SAAS,EACTC,WAAW,EACXC,OAAO,EACP;EACA,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGP,cAAc,CAC/CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OACEH,cAAc,CAACK,UAAU,EAAED,OAAO,CAAC,GAAGJ,cAAc,CAACM,YAAY,EAAEF,OAAO,CAAC;AAE/E;;AAEA;AACA,eAAeH,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}