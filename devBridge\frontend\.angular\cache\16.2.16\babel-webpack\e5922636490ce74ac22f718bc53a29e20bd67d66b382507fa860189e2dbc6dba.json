{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\n/**\n * Provided a collection of ASTs, presumably each from different files,\n * concatenate the ASTs together into batched AST, useful for validating many\n * GraphQL source files which together represent one conceptual application.\n */\n\nexport function concatAST(documents) {\n  const definitions = [];\n  for (const doc of documents) {\n    definitions.push(...doc.definitions);\n  }\n  return {\n    kind: Kind.DOCUMENT,\n    definitions\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}