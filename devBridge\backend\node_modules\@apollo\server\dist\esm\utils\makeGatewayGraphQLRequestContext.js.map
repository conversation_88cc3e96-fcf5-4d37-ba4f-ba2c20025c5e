{"version": 3, "file": "makeGatewayGraphQLRequestContext.js", "sourceRoot": "", "sources": ["../../../src/utils/makeGatewayGraphQLRequestContext.ts"], "names": [], "mappings": "AAoFA,MAAM,UAAU,gCAAgC,CAC9C,iBAAmE,EACnE,MAA8B,EAC9B,SAA0C;IAE1C,MAAM,OAAO,GAA0B,EAAE,CAAC;IAC1C,IAAI,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC;IAClD,CAAC;IACD,IAAI,eAAe,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjD,OAAO,CAAC,aAAa,GAAG,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC;IAClE,CAAC;IACD,IAAI,WAAW,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;QAC7C,OAAO,CAAC,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC;IAC1D,CAAC;IACD,IAAI,YAAY,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;QAC9C,OAAO,CAAC,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC;IAC5D,CAAC;IACD,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/C,MAAM,YAAY,GAChB,OAAO,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3D,OAAO,CAAC,IAAI,GAAG;YACb,MAAM,EAAE,OAAO,CAAC,MAAM;YAGtB,GAAG,EAAE,+BAA+B,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GACzD,OAAO,CAAC,MACV,EAAE;YACF,OAAO,EAAE,IAAI,0BAA0B,CAAC,OAAO,CAAC,OAAO,CAAC;SACzD,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAA2B;QACvC,IAAI,EAAE;YACJ,OAAO,EAAE,IAAI,0BAA0B,CACrC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CACxC;YACD,IAAI,MAAM;gBACR,OAAO,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAChD,CAAC;YACD,IAAI,MAAM,CAAC,SAAS;gBAClB,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACrD,CAAC;SACF;KAEF,CAAC;IAEF,OAAO;QACL,OAAO;QACP,QAAQ;QACR,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,MAAM,EAAE,iBAAiB,CAAC,MAAM;QAMhC,UAAU,EACR,gDAAqE;QACvE,OAAO,EAAE,iBAAiB,CAAC,YAAY;QACvC,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,SAAS,EAAE,iBAAiB,CAAC,SAAS;QACtC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;QACpC,MAAM,EAAE,iBAAiB,CAAC,MAAM;QAChC,aAAa,EAAE,iBAAiB,CAAC,aAAa;QAC9C,SAAS,EAAE,iBAAiB,CAAC,SAAS;QACtC,MAAM,EAAE,iBAAiB,CAAC,MAAM;QAChC,OAAO,EAAE,iBAAiB,CAAC,OAAO;QAClC,KAAK,EAAE,SAAS,CAAC,iCAAiC;QAClD,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB;QACxD,gBAAgB,EAAE,iBAAiB,CAAC,gBAAgB;KACrD,CAAC;AACJ,CAAC;AAMD,MAAM,0BAA0B;IAC9B,YAAoB,GAAc;QAAd,QAAG,GAAH,GAAG,CAAW;IAAG,CAAC;IACtC,MAAM,CAAC,IAAY,EAAE,KAAa;QAChC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,MAAM,CAAC,IAAY;QACjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,GAAG,CAAC,IAAY;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACpC,CAAC;IACD,GAAG,CAAC,IAAY;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IACD,GAAG,CAAC,IAAY,EAAE,KAAa;QAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IACD,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;IACD,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;CACF"}