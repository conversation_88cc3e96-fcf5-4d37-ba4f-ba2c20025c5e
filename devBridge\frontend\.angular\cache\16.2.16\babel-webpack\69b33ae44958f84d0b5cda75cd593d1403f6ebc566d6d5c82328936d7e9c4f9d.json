{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TaskService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}tasks`;\n    console.log('Task API URL:', this.apiUrl);\n  }\n  // Récupérer toutes les tâches\n  getTasks() {\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Tasks received:', data)), catchError(this.handleError));\n  }\n  // Récupérer les tâches d'une équipe spécifique\n  getTasksByTeam(teamId) {\n    return this.http.get(`${this.apiUrl}/team/${teamId}`).pipe(tap(data => console.log(`Tasks for team ${teamId} received:`, data)), catchError(this.handleError));\n  }\n  // Récupérer une tâche par son ID\n  getTask(id) {\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task received:', data)), catchError(this.handleError));\n  }\n  // Créer une nouvelle tâche\n  createTask(task) {\n    return this.http.post(this.apiUrl, task).pipe(tap(data => console.log('Task created:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour une tâche existante\n  updateTask(id, task) {\n    return this.http.put(`${this.apiUrl}/${id}`, task).pipe(tap(data => console.log('Task updated:', data)), catchError(this.handleError));\n  }\n  // Supprimer une tâche\n  deleteTask(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task deleted:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour le statut d'une tâche\n  updateTaskStatus(id, status) {\n    return this.http.patch(`${this.apiUrl}/${id}/status`, {\n      status\n    }).pipe(tap(data => console.log('Task status updated:', data)), catchError(this.handleError));\n  }\n  // ==================== KANBAN ====================\n  // Obtenir le tableau Kanban d'une équipe\n  getKanbanBoard(teamId, filters) {\n    let params = new HttpParams();\n    if (filters) {\n      if (filters.includeArchived !== undefined) {\n        params = params.set('includeArchived', filters.includeArchived.toString());\n      }\n      if (filters.assignedTo && filters.assignedTo.length > 0) {\n        params = params.set('assignedTo', filters.assignedTo.join(','));\n      }\n      if (filters.labels && filters.labels.length > 0) {\n        params = params.set('labels', filters.labels.join(','));\n      }\n      if (filters.category) {\n        params = params.set('category', filters.category);\n      }\n    }\n    return this.http.get(`${this.apiUrl}/kanban/${teamId}`, {\n      params\n    }).pipe(tap(data => console.log('Kanban board received:', data)), catchError(this.handleError));\n  }\n  // Créer une tâche dans une colonne spécifique\n  createTaskInColumn(teamId, task) {\n    return this.http.post(`${this.apiUrl}/kanban/${teamId}`, task).pipe(tap(data => console.log('Task created in column:', data)), catchError(this.handleError));\n  }\n  // Déplacer une tâche (drag & drop)\n  moveTask(taskId, moveRequest) {\n    return this.http.patch(`${this.apiUrl}/${taskId}/move`, moveRequest).pipe(tap(data => console.log('Task moved:', data)), catchError(this.handleError));\n  }\n  // Archiver/désarchiver une tâche\n  toggleArchiveTask(taskId) {\n    return this.http.patch(`${this.apiUrl}/${taskId}/archive`, {}).pipe(tap(data => console.log('Task archive toggled:', data)), catchError(this.handleError));\n  }\n  // Obtenir les statistiques des tâches\n  getTaskStatistics(teamId, period = 30) {\n    const params = new HttpParams().set('period', period.toString());\n    return this.http.get(`${this.apiUrl}/stats/${teamId}`, {\n      params\n    }).pipe(tap(data => console.log('Task statistics received:', data)), catchError(this.handleError));\n  }\n  // ==================== FONCTIONNALITÉS AVANCÉES ====================\n  // Ajouter un commentaire\n  addComment(taskId, comment) {\n    return this.http.post(`${this.apiUrl}/${taskId}/comments`, comment).pipe(tap(data => console.log('Comment added:', data)), catchError(this.handleError));\n  }\n  // Gérer les labels\n  manageLabel(taskId, label) {\n    return this.http.post(`${this.apiUrl}/${taskId}/labels`, label).pipe(tap(data => console.log('Label managed:', data)), catchError(this.handleError));\n  }\n  // Assigner des utilisateurs\n  assignUsers(taskId, assignment) {\n    return this.http.patch(`${this.apiUrl}/${taskId}/assign`, assignment).pipe(tap(data => console.log('Users assigned:', data)), catchError(this.handleError));\n  }\n  // Obtenir les sous-tâches\n  getSubtasks(taskId) {\n    return this.http.get(`${this.apiUrl}/${taskId}/subtasks`).pipe(tap(data => console.log('Subtasks received:', data)), catchError(this.handleError));\n  }\n  // Enregistrer une vue\n  recordView(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/view`, {}).pipe(tap(data => console.log('View recorded:', data)), catchError(this.handleError));\n  }\n  // ==================== INTÉGRATION IA ====================\n  // Analyser une tâche avec l'IA\n  analyzeTaskWithAI(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/ai/analyze`, {}).pipe(tap(data => console.log('AI analysis completed:', data)), catchError(this.handleError));\n  }\n  // Générer des sous-tâches avec l'IA\n  generateSubtasksWithAI(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/ai/subtasks`, {}).pipe(tap(data => console.log('AI subtasks generated:', data)), catchError(this.handleError));\n  }\n  // Estimer l'effort avec l'IA\n  estimateEffortWithAI(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/ai/estimate`, {}).pipe(tap(data => console.log('AI effort estimated:', data)), catchError(this.handleError));\n  }\n  // Générer une description avec l'IA\n  generateDescriptionWithAI(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/ai/description`, {}).pipe(tap(data => console.log('AI description generated:', data)), catchError(this.handleError));\n  }\n  // Appliquer une suggestion IA\n  applySuggestion(taskId, suggestionIndex) {\n    return this.http.post(`${this.apiUrl}/${taskId}/ai/suggestions/${suggestionIndex}/apply`, {}).pipe(tap(data => console.log('AI suggestion applied:', data)), catchError(this.handleError));\n  }\n  // ==================== MÉTHODES UTILITAIRES ====================\n  // Créer une tâche avancée\n  createAdvancedTask(task) {\n    return this.http.post(this.apiUrl, task).pipe(tap(data => console.log('Advanced task created:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour une tâche avancée\n  updateAdvancedTask(id, task) {\n    return this.http.put(`${this.apiUrl}/${id}`, task).pipe(tap(data => console.log('Advanced task updated:', data)), catchError(this.handleError));\n  }\n  // Dupliquer une tâche\n  duplicateTask(taskId) {\n    return this.http.post(`${this.apiUrl}/${taskId}/duplicate`, {}).pipe(tap(data => console.log('Task duplicated:', data)), catchError(this.handleError));\n  }\n  // Obtenir les tâches avec filtres avancés\n  getTasksWithFilters(teamId, filters) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      if (filters[key] !== undefined && filters[key] !== null) {\n        if (Array.isArray(filters[key])) {\n          params = params.set(key, filters[key].join(','));\n        } else {\n          params = params.set(key, filters[key].toString());\n        }\n      }\n    });\n    return this.http.get(`${this.apiUrl}/team/${teamId}`, {\n      params\n    }).pipe(tap(data => console.log('Filtered tasks received:', data)), catchError(this.handleError));\n  }\n  // Rechercher des tâches\n  searchTasks(teamId, query) {\n    const params = new HttpParams().set('search', query);\n    return this.http.get(`${this.apiUrl}/team/${teamId}`, {\n      params\n    }).pipe(tap(data => console.log('Search results:', data)), catchError(this.handleError));\n  }\n  // Obtenir les tâches assignées à un utilisateur\n  getUserTasks(userId, status) {\n    let params = new HttpParams().set('assignedTo', userId);\n    if (status) {\n      params = params.set('status', status);\n    }\n    return this.http.get(`${this.apiUrl}/user/${userId}`, {\n      params\n    }).pipe(tap(data => console.log('User tasks received:', data)), catchError(this.handleError));\n  }\n  // Obtenir les tâches en retard\n  getOverdueTasks(teamId) {\n    const params = new HttpParams().set('overdue', 'true');\n    return this.http.get(`${this.apiUrl}/team/${teamId}`, {\n      params\n    }).pipe(tap(data => console.log('Overdue tasks received:', data)), catchError(this.handleError));\n  }\n  // Gérer les erreurs HTTP\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Error: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\n    }\n    console.error(errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function TaskService_Factory(t) {\n      return new (t || TaskService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TaskService,\n      factory: TaskService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "throwError", "catchError", "tap", "environment", "TaskService", "constructor", "http", "apiUrl", "urlBackend", "console", "log", "getTasks", "get", "pipe", "data", "handleError", "getTasksByTeam", "teamId", "getTask", "id", "createTask", "task", "post", "updateTask", "put", "deleteTask", "delete", "updateTaskStatus", "status", "patch", "getKanbanBoard", "filters", "params", "includeArchived", "undefined", "set", "toString", "assignedTo", "length", "join", "labels", "category", "createTaskInColumn", "moveTask", "taskId", "moveRequest", "toggleArchiveTask", "getTaskStatistics", "period", "addComment", "comment", "manageLabel", "label", "assignUsers", "assignment", "getSubtasks", "recordView", "analyzeTaskWithAI", "generateSubtasksWithAI", "estimateEffortWithAI", "generateDescriptionWithAI", "applySuggestion", "suggestionIndex", "createAdvancedTask", "updateAdvancedTask", "duplicateTask", "getTasksWithFilters", "Object", "keys", "for<PERSON>ach", "key", "Array", "isArray", "searchTasks", "query", "getUserTasks", "userId", "getOverdueTasks", "error", "errorMessage", "ErrorEvent", "message", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\services\\task.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\nimport {\r\n  Task,\r\n  KanbanBoard,\r\n  KanbanFilters,\r\n  MoveTaskRequest,\r\n  CreateTaskRequest,\r\n  UpdateTaskRequest,\r\n  AddCommentRequest,\r\n  AddLabelRequest,\r\n  AssignTaskRequest,\r\n  TaskStatistics,\r\n  AIAnalysisResponse,\r\n  GenerateSubtasksResponse,\r\n  EstimateEffortResponse,\r\n  GenerateDescriptionResponse\r\n} from '../models/task.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TaskService {\r\n  private apiUrl = `${environment.urlBackend}tasks`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('Task API URL:', this.apiUrl);\r\n  }\r\n\r\n  // Récupérer toutes les tâches\r\n  getTasks(): Observable<Task[]> {\r\n    return this.http.get<Task[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer les tâches d'une équipe spécifique\r\n  getTasksByTeam(teamId: string): Observable<Task[]> {\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`).pipe(\r\n      tap((data) => console.log(`Tasks for team ${teamId} received:`, data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer une tâche par son ID\r\n  getTask(id: string): Observable<Task> {\r\n    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer une nouvelle tâche\r\n  createTask(task: Task): Observable<Task> {\r\n    return this.http.post<Task>(this.apiUrl, task).pipe(\r\n      tap((data) => console.log('Task created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour une tâche existante\r\n  updateTask(id: string, task: Task): Observable<Task> {\r\n    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(\r\n      tap((data) => console.log('Task updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer une tâche\r\n  deleteTask(id: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task deleted:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour le statut d'une tâche\r\n  updateTaskStatus(\r\n    id: string,\r\n    status: 'todo' | 'in-progress' | 'done'\r\n  ): Observable<Task> {\r\n    return this.http\r\n      .patch<Task>(`${this.apiUrl}/${id}/status`, { status })\r\n      .pipe(\r\n        tap((data) => console.log('Task status updated:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // ==================== KANBAN ====================\r\n\r\n  // Obtenir le tableau Kanban d'une équipe\r\n  getKanbanBoard(teamId: string, filters?: KanbanFilters): Observable<KanbanBoard> {\r\n    let params = new HttpParams();\r\n\r\n    if (filters) {\r\n      if (filters.includeArchived !== undefined) {\r\n        params = params.set('includeArchived', filters.includeArchived.toString());\r\n      }\r\n      if (filters.assignedTo && filters.assignedTo.length > 0) {\r\n        params = params.set('assignedTo', filters.assignedTo.join(','));\r\n      }\r\n      if (filters.labels && filters.labels.length > 0) {\r\n        params = params.set('labels', filters.labels.join(','));\r\n      }\r\n      if (filters.category) {\r\n        params = params.set('category', filters.category);\r\n      }\r\n    }\r\n\r\n    return this.http.get<KanbanBoard>(`${this.apiUrl}/kanban/${teamId}`, { params }).pipe(\r\n      tap((data) => console.log('Kanban board received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer une tâche dans une colonne spécifique\r\n  createTaskInColumn(teamId: string, task: CreateTaskRequest): Observable<{ message: string; task: Task }> {\r\n    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/kanban/${teamId}`, task).pipe(\r\n      tap((data) => console.log('Task created in column:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Déplacer une tâche (drag & drop)\r\n  moveTask(taskId: string, moveRequest: MoveTaskRequest): Observable<{ message: string; task: Task }> {\r\n    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/move`, moveRequest).pipe(\r\n      tap((data) => console.log('Task moved:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Archiver/désarchiver une tâche\r\n  toggleArchiveTask(taskId: string): Observable<{ message: string; task: Task }> {\r\n    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/archive`, {}).pipe(\r\n      tap((data) => console.log('Task archive toggled:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Obtenir les statistiques des tâches\r\n  getTaskStatistics(teamId: string, period: number = 30): Observable<TaskStatistics> {\r\n    const params = new HttpParams().set('period', period.toString());\r\n\r\n    return this.http.get<TaskStatistics>(`${this.apiUrl}/stats/${teamId}`, { params }).pipe(\r\n      tap((data) => console.log('Task statistics received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // ==================== FONCTIONNALITÉS AVANCÉES ====================\r\n\r\n  // Ajouter un commentaire\r\n  addComment(taskId: string, comment: AddCommentRequest): Observable<{ message: string; task: Task }> {\r\n    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/comments`, comment).pipe(\r\n      tap((data) => console.log('Comment added:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Gérer les labels\r\n  manageLabel(taskId: string, label: AddLabelRequest): Observable<{ message: string; task: Task }> {\r\n    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/labels`, label).pipe(\r\n      tap((data) => console.log('Label managed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Assigner des utilisateurs\r\n  assignUsers(taskId: string, assignment: AssignTaskRequest): Observable<{ message: string; task: Task }> {\r\n    return this.http.patch<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/assign`, assignment).pipe(\r\n      tap((data) => console.log('Users assigned:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Obtenir les sous-tâches\r\n  getSubtasks(taskId: string): Observable<Task[]> {\r\n    return this.http.get<Task[]>(`${this.apiUrl}/${taskId}/subtasks`).pipe(\r\n      tap((data) => console.log('Subtasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Enregistrer une vue\r\n  recordView(taskId: string): Observable<{ message: string }> {\r\n    return this.http.post<{ message: string }>(`${this.apiUrl}/${taskId}/view`, {}).pipe(\r\n      tap((data) => console.log('View recorded:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // ==================== INTÉGRATION IA ====================\r\n\r\n  // Analyser une tâche avec l'IA\r\n  analyzeTaskWithAI(taskId: string): Observable<AIAnalysisResponse> {\r\n    return this.http.post<AIAnalysisResponse>(`${this.apiUrl}/${taskId}/ai/analyze`, {}).pipe(\r\n      tap((data) => console.log('AI analysis completed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Générer des sous-tâches avec l'IA\r\n  generateSubtasksWithAI(taskId: string): Observable<GenerateSubtasksResponse> {\r\n    return this.http.post<GenerateSubtasksResponse>(`${this.apiUrl}/${taskId}/ai/subtasks`, {}).pipe(\r\n      tap((data) => console.log('AI subtasks generated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Estimer l'effort avec l'IA\r\n  estimateEffortWithAI(taskId: string): Observable<EstimateEffortResponse> {\r\n    return this.http.post<EstimateEffortResponse>(`${this.apiUrl}/${taskId}/ai/estimate`, {}).pipe(\r\n      tap((data) => console.log('AI effort estimated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Générer une description avec l'IA\r\n  generateDescriptionWithAI(taskId: string): Observable<GenerateDescriptionResponse> {\r\n    return this.http.post<GenerateDescriptionResponse>(`${this.apiUrl}/${taskId}/ai/description`, {}).pipe(\r\n      tap((data) => console.log('AI description generated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Appliquer une suggestion IA\r\n  applySuggestion(taskId: string, suggestionIndex: number): Observable<{ message: string; task: Task; suggestion: any }> {\r\n    return this.http.post<{ message: string; task: Task; suggestion: any }>(`${this.apiUrl}/${taskId}/ai/suggestions/${suggestionIndex}/apply`, {}).pipe(\r\n      tap((data) => console.log('AI suggestion applied:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // ==================== MÉTHODES UTILITAIRES ====================\r\n\r\n  // Créer une tâche avancée\r\n  createAdvancedTask(task: CreateTaskRequest): Observable<Task> {\r\n    return this.http.post<Task>(this.apiUrl, task).pipe(\r\n      tap((data) => console.log('Advanced task created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour une tâche avancée\r\n  updateAdvancedTask(id: string, task: UpdateTaskRequest): Observable<Task> {\r\n    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(\r\n      tap((data) => console.log('Advanced task updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Dupliquer une tâche\r\n  duplicateTask(taskId: string): Observable<{ message: string; task: Task }> {\r\n    return this.http.post<{ message: string; task: Task }>(`${this.apiUrl}/${taskId}/duplicate`, {}).pipe(\r\n      tap((data) => console.log('Task duplicated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Obtenir les tâches avec filtres avancés\r\n  getTasksWithFilters(teamId: string, filters: any): Observable<Task[]> {\r\n    let params = new HttpParams();\r\n\r\n    Object.keys(filters).forEach(key => {\r\n      if (filters[key] !== undefined && filters[key] !== null) {\r\n        if (Array.isArray(filters[key])) {\r\n          params = params.set(key, filters[key].join(','));\r\n        } else {\r\n          params = params.set(key, filters[key].toString());\r\n        }\r\n      }\r\n    });\r\n\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(\r\n      tap((data) => console.log('Filtered tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Rechercher des tâches\r\n  searchTasks(teamId: string, query: string): Observable<Task[]> {\r\n    const params = new HttpParams().set('search', query);\r\n\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(\r\n      tap((data) => console.log('Search results:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Obtenir les tâches assignées à un utilisateur\r\n  getUserTasks(userId: string, status?: string): Observable<Task[]> {\r\n    let params = new HttpParams().set('assignedTo', userId);\r\n    if (status) {\r\n      params = params.set('status', status);\r\n    }\r\n\r\n    return this.http.get<Task[]>(`${this.apiUrl}/user/${userId}`, { params }).pipe(\r\n      tap((data) => console.log('User tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Obtenir les tâches en retard\r\n  getOverdueTasks(teamId: string): Observable<Task[]> {\r\n    const params = new HttpParams().set('overdue', 'true');\r\n\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`, { params }).pipe(\r\n      tap((data) => console.log('Overdue tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Gérer les erreurs HTTP\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Error: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\r\n    }\r\n    console.error(errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAwCA,UAAU,QAAQ,sBAAsB;AAChF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;AAqB5D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,OAAO;IAG/CC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACH,MAAM,CAAC;EAC3C;EAEA;EACAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAS,IAAI,CAACL,MAAM,CAAC,CAACM,IAAI,CAC5CX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC,CAAC,EACnDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAI,CAACX,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASU,MAAM,EAAE,CAAC,CAACJ,IAAI,CAChEX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,kBAAkBO,MAAM,YAAY,EAAEH,IAAI,CAAC,CAAC,EACtEb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAG,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACb,IAAI,CAACM,GAAG,CAAO,GAAG,IAAI,CAACL,MAAM,IAAIY,EAAE,EAAE,CAAC,CAACN,IAAI,CACrDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAO,IAAI,CAACf,MAAM,EAAEc,IAAI,CAAC,CAACR,IAAI,CACjDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAQ,UAAUA,CAACJ,EAAU,EAAEE,IAAU;IAC/B,OAAO,IAAI,CAACf,IAAI,CAACkB,GAAG,CAAO,GAAG,IAAI,CAACjB,MAAM,IAAIY,EAAE,EAAE,EAAEE,IAAI,CAAC,CAACR,IAAI,CAC3DX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAU,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAACb,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAACnB,MAAM,IAAIY,EAAE,EAAE,CAAC,CAACN,IAAI,CAClDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAY,gBAAgBA,CACdR,EAAU,EACVS,MAAuC;IAEvC,OAAO,IAAI,CAACtB,IAAI,CACbuB,KAAK,CAAO,GAAG,IAAI,CAACtB,MAAM,IAAIY,EAAE,SAAS,EAAE;MAAES;IAAM,CAAE,CAAC,CACtDf,IAAI,CACHX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,IAAI,CAAC,CAAC,EACxDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACL;EAEA;EAEA;EACAe,cAAcA,CAACb,MAAc,EAAEc,OAAuB;IACpD,IAAIC,MAAM,GAAG,IAAIjC,UAAU,EAAE;IAE7B,IAAIgC,OAAO,EAAE;MACX,IAAIA,OAAO,CAACE,eAAe,KAAKC,SAAS,EAAE;QACzCF,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,iBAAiB,EAAEJ,OAAO,CAACE,eAAe,CAACG,QAAQ,EAAE,CAAC;;MAE5E,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACvDN,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,YAAY,EAAEJ,OAAO,CAACM,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;;MAEjE,IAAIR,OAAO,CAACS,MAAM,IAAIT,OAAO,CAACS,MAAM,CAACF,MAAM,GAAG,CAAC,EAAE;QAC/CN,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,QAAQ,EAAEJ,OAAO,CAACS,MAAM,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;;MAEzD,IAAIR,OAAO,CAACU,QAAQ,EAAE;QACpBT,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,UAAU,EAAEJ,OAAO,CAACU,QAAQ,CAAC;;;IAIrD,OAAO,IAAI,CAACnC,IAAI,CAACM,GAAG,CAAc,GAAG,IAAI,CAACL,MAAM,WAAWU,MAAM,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAACnB,IAAI,CACnFX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA2B,kBAAkBA,CAACzB,MAAc,EAAEI,IAAuB;IACxD,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAkC,GAAG,IAAI,CAACf,MAAM,WAAWU,MAAM,EAAE,EAAEI,IAAI,CAAC,CAACR,IAAI,CAClGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EAC3Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA4B,QAAQA,CAACC,MAAc,EAAEC,WAA4B;IACnD,OAAO,IAAI,CAACvC,IAAI,CAACuB,KAAK,CAAkC,GAAG,IAAI,CAACtB,MAAM,IAAIqC,MAAM,OAAO,EAAEC,WAAW,CAAC,CAAChC,IAAI,CACxGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,IAAI,CAAC,CAAC,EAC/Cb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA+B,iBAAiBA,CAACF,MAAc;IAC9B,OAAO,IAAI,CAACtC,IAAI,CAACuB,KAAK,CAAkC,GAAG,IAAI,CAACtB,MAAM,IAAIqC,MAAM,UAAU,EAAE,EAAE,CAAC,CAAC/B,IAAI,CAClGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,IAAI,CAAC,CAAC,EACzDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAgC,iBAAiBA,CAAC9B,MAAc,EAAE+B,MAAA,GAAiB,EAAE;IACnD,MAAMhB,MAAM,GAAG,IAAIjC,UAAU,EAAE,CAACoC,GAAG,CAAC,QAAQ,EAAEa,MAAM,CAACZ,QAAQ,EAAE,CAAC;IAEhE,OAAO,IAAI,CAAC9B,IAAI,CAACM,GAAG,CAAiB,GAAG,IAAI,CAACL,MAAM,UAAUU,MAAM,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAACnB,IAAI,CACrFX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,IAAI,CAAC,CAAC,EAC7Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;EACAkC,UAAUA,CAACL,MAAc,EAAEM,OAA0B;IACnD,OAAO,IAAI,CAAC5C,IAAI,CAACgB,IAAI,CAAkC,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,WAAW,EAAEM,OAAO,CAAC,CAACrC,IAAI,CACvGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAoC,WAAWA,CAACP,MAAc,EAAEQ,KAAsB;IAChD,OAAO,IAAI,CAAC9C,IAAI,CAACgB,IAAI,CAAkC,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,SAAS,EAAEQ,KAAK,CAAC,CAACvC,IAAI,CACnGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAsC,WAAWA,CAACT,MAAc,EAAEU,UAA6B;IACvD,OAAO,IAAI,CAAChD,IAAI,CAACuB,KAAK,CAAkC,GAAG,IAAI,CAACtB,MAAM,IAAIqC,MAAM,SAAS,EAAEU,UAAU,CAAC,CAACzC,IAAI,CACzGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC,CAAC,EACnDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAwC,WAAWA,CAACX,MAAc;IACxB,OAAO,IAAI,CAACtC,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,IAAIqC,MAAM,WAAW,CAAC,CAAC/B,IAAI,CACpEX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,IAAI,CAAC,CAAC,EACtDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAyC,UAAUA,CAACZ,MAAc;IACvB,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAAsB,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,OAAO,EAAE,EAAE,CAAC,CAAC/B,IAAI,CAClFX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;EACA0C,iBAAiBA,CAACb,MAAc;IAC9B,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAAqB,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,aAAa,EAAE,EAAE,CAAC,CAAC/B,IAAI,CACvFX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA2C,sBAAsBA,CAACd,MAAc;IACnC,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAA2B,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,cAAc,EAAE,EAAE,CAAC,CAAC/B,IAAI,CAC9FX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA4C,oBAAoBA,CAACf,MAAc;IACjC,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAAyB,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,cAAc,EAAE,EAAE,CAAC,CAAC/B,IAAI,CAC5FX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,IAAI,CAAC,CAAC,EACxDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA6C,yBAAyBA,CAAChB,MAAc;IACtC,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAA8B,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,iBAAiB,EAAE,EAAE,CAAC,CAAC/B,IAAI,CACpGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,IAAI,CAAC,CAAC,EAC7Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA8C,eAAeA,CAACjB,MAAc,EAAEkB,eAAuB;IACrD,OAAO,IAAI,CAACxD,IAAI,CAACgB,IAAI,CAAmD,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,mBAAmBkB,eAAe,QAAQ,EAAE,EAAE,CAAC,CAACjD,IAAI,CAClJX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;EACAgD,kBAAkBA,CAAC1C,IAAuB;IACxC,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAO,IAAI,CAACf,MAAM,EAAEc,IAAI,CAAC,CAACR,IAAI,CACjDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAiD,kBAAkBA,CAAC7C,EAAU,EAAEE,IAAuB;IACpD,OAAO,IAAI,CAACf,IAAI,CAACkB,GAAG,CAAO,GAAG,IAAI,CAACjB,MAAM,IAAIY,EAAE,EAAE,EAAEE,IAAI,CAAC,CAACR,IAAI,CAC3DX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,IAAI,CAAC,CAAC,EAC1Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAkD,aAAaA,CAACrB,MAAc;IAC1B,OAAO,IAAI,CAACtC,IAAI,CAACgB,IAAI,CAAkC,GAAG,IAAI,CAACf,MAAM,IAAIqC,MAAM,YAAY,EAAE,EAAE,CAAC,CAAC/B,IAAI,CACnGX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAAC,CAAC,EACpDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmD,mBAAmBA,CAACjD,MAAc,EAAEc,OAAY;IAC9C,IAAIC,MAAM,GAAG,IAAIjC,UAAU,EAAE;IAE7BoE,MAAM,CAACC,IAAI,CAACrC,OAAO,CAAC,CAACsC,OAAO,CAACC,GAAG,IAAG;MACjC,IAAIvC,OAAO,CAACuC,GAAG,CAAC,KAAKpC,SAAS,IAAIH,OAAO,CAACuC,GAAG,CAAC,KAAK,IAAI,EAAE;QACvD,IAAIC,KAAK,CAACC,OAAO,CAACzC,OAAO,CAACuC,GAAG,CAAC,CAAC,EAAE;UAC/BtC,MAAM,GAAGA,MAAM,CAACG,GAAG,CAACmC,GAAG,EAAEvC,OAAO,CAACuC,GAAG,CAAC,CAAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;SACjD,MAAM;UACLP,MAAM,GAAGA,MAAM,CAACG,GAAG,CAACmC,GAAG,EAAEvC,OAAO,CAACuC,GAAG,CAAC,CAAClC,QAAQ,EAAE,CAAC;;;IAGvD,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC9B,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASU,MAAM,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAACnB,IAAI,CAC5EX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,IAAI,CAAC,CAAC,EAC5Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA0D,WAAWA,CAACxD,MAAc,EAAEyD,KAAa;IACvC,MAAM1C,MAAM,GAAG,IAAIjC,UAAU,EAAE,CAACoC,GAAG,CAAC,QAAQ,EAAEuC,KAAK,CAAC;IAEpD,OAAO,IAAI,CAACpE,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASU,MAAM,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAACnB,IAAI,CAC5EX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC,CAAC,EACnDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA4D,YAAYA,CAACC,MAAc,EAAEhD,MAAe;IAC1C,IAAII,MAAM,GAAG,IAAIjC,UAAU,EAAE,CAACoC,GAAG,CAAC,YAAY,EAAEyC,MAAM,CAAC;IACvD,IAAIhD,MAAM,EAAE;MACVI,MAAM,GAAGA,MAAM,CAACG,GAAG,CAAC,QAAQ,EAAEP,MAAM,CAAC;;IAGvC,OAAO,IAAI,CAACtB,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASqE,MAAM,EAAE,EAAE;MAAE5C;IAAM,CAAE,CAAC,CAACnB,IAAI,CAC5EX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,IAAI,CAAC,CAAC,EACxDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACA8D,eAAeA,CAAC5D,MAAc;IAC5B,MAAMe,MAAM,GAAG,IAAIjC,UAAU,EAAE,CAACoC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;IAEtD,OAAO,IAAI,CAAC7B,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASU,MAAM,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAACnB,IAAI,CAC5EX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EAC3Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACQA,WAAWA,CAAC+D,KAAwB;IAC1C,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAC/C,MAAM;MACL;MACAF,YAAY,GAAG,eAAeD,KAAK,CAAClD,MAAM,cAAckD,KAAK,CAACG,OAAO,EAAE;;IAEzExE,OAAO,CAACqE,KAAK,CAACC,YAAY,CAAC;IAC3B,OAAO/E,UAAU,CAAC,MAAM,IAAIkF,KAAK,CAACH,YAAY,CAAC,CAAC;EAClD;;;uBAhTW3E,WAAW,EAAA+E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXlF,WAAW;MAAAmF,OAAA,EAAXnF,WAAW,CAAAoF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}