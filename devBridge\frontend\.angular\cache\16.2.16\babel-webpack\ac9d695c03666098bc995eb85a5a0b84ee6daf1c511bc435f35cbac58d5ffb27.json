{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isAsyncIterable } from '../jsutils/isAsyncIterable.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { collectFields } from './collectFields.mjs';\nimport { assertValidExecutionArguments, buildExecutionContext, buildResolveInfo, execute, getFieldDef } from './execute.mjs';\nimport { mapAsyncIterator } from './mapAsyncIterator.mjs';\nimport { getArgumentValues } from './values.mjs';\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification.\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of ExecutionResults representing the response stream.\n *\n * Accepts either an object with named arguments, or individual arguments.\n */\n\nexport function subscribe(_x) {\n  return _subscribe.apply(this, arguments);\n}\nfunction _subscribe() {\n  _subscribe = _asyncToGenerator(function* (args) {\n    // Temporary for v15 to v16 migration. Remove in v17\n    arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n    const resultOrStream = yield createSourceEventStream(args);\n    if (!isAsyncIterable(resultOrStream)) {\n      return resultOrStream;\n    } // For each payload yielded from a subscription, map it over the normal\n    // GraphQL `execute` function, with `payload` as the rootValue.\n    // This implements the \"MapSourceToResponseEvent\" algorithm described in\n    // the GraphQL specification. The `execute` function provides the\n    // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n    // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n    const mapSourceToResponse = payload => execute({\n      ...args,\n      rootValue: payload\n    }); // Map every source value to a ExecutionResult value as described above.\n\n    return mapAsyncIterator(resultOrStream, mapSourceToResponse);\n  });\n  return _subscribe.apply(this, arguments);\n}\nfunction toNormalizedArgs(args) {\n  const firstArg = args[0];\n  if (firstArg && 'document' in firstArg) {\n    return firstArg;\n  }\n  return {\n    schema: firstArg,\n    // FIXME: when underlying TS bug fixed, see https://github.com/microsoft/TypeScript/issues/31613\n    document: args[1],\n    rootValue: args[2],\n    contextValue: args[3],\n    variableValues: args[4],\n    operationName: args[5],\n    subscribeFieldResolver: args[6]\n  };\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport function createSourceEventStream() {\n  return _createSourceEventStream.apply(this, arguments);\n}\nfunction _createSourceEventStream() {\n  _createSourceEventStream = _asyncToGenerator(function* (...rawArgs) {\n    const args = toNormalizedArgs(rawArgs);\n    const {\n      schema,\n      document,\n      variableValues\n    } = args; // If arguments are missing or incorrectly typed, this is an internal\n    // developer mistake which should throw an early error.\n\n    assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n    // a \"Response\" with only errors is returned.\n\n    const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n    if (!('schema' in exeContext)) {\n      return {\n        errors: exeContext\n      };\n    }\n    try {\n      const eventStream = yield executeSubscription(exeContext); // Assert field returned an event stream, otherwise yield an error.\n\n      if (!isAsyncIterable(eventStream)) {\n        throw new Error('Subscription field must return Async Iterable. ' + `Received: ${inspect(eventStream)}.`);\n      }\n      return eventStream;\n    } catch (error) {\n      // If it GraphQLError, report it as an ExecutionResult, containing only errors and no data.\n      // Otherwise treat the error as a system-class error and re-throw it.\n      if (error instanceof GraphQLError) {\n        return {\n          errors: [error]\n        };\n      }\n      throw error;\n    }\n  });\n  return _createSourceEventStream.apply(this, arguments);\n}\nfunction executeSubscription(_x2) {\n  return _executeSubscription.apply(this, arguments);\n}\nfunction _executeSubscription() {\n  _executeSubscription = _asyncToGenerator(function* (exeContext) {\n    const {\n      schema,\n      fragments,\n      operation,\n      variableValues,\n      rootValue\n    } = exeContext;\n    const rootType = schema.getSubscriptionType();\n    if (rootType == null) {\n      throw new GraphQLError('Schema is not configured to execute subscription operation.', {\n        nodes: operation\n      });\n    }\n    const rootFields = collectFields(schema, fragments, variableValues, rootType, operation.selectionSet);\n    const [responseName, fieldNodes] = [...rootFields.entries()][0];\n    const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n    if (!fieldDef) {\n      const fieldName = fieldNodes[0].name.value;\n      throw new GraphQLError(`The subscription field \"${fieldName}\" is not defined.`, {\n        nodes: fieldNodes\n      });\n    }\n    const path = addPath(undefined, responseName, rootType.name);\n    const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, rootType, path);\n    try {\n      var _fieldDef$subscribe;\n\n      // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n      // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n      // Build a JS object of arguments from the field.arguments AST, using the\n      // variables scope to fulfill any variable references.\n      const args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n      // is provided to every resolve function within an execution. It is commonly\n      // used to represent an authenticated user, or request-specific caches.\n\n      const contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n      // AsyncIterable yielding raw payloads.\n\n      const resolveFn = (_fieldDef$subscribe = fieldDef.subscribe) !== null && _fieldDef$subscribe !== void 0 ? _fieldDef$subscribe : exeContext.subscribeFieldResolver;\n      const eventStream = yield resolveFn(rootValue, args, contextValue, info);\n      if (eventStream instanceof Error) {\n        throw eventStream;\n      }\n      return eventStream;\n    } catch (error) {\n      throw locatedError(error, fieldNodes, pathToArray(path));\n    }\n  });\n  return _executeSubscription.apply(this, arguments);\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "isAsyncIterable", "addPath", "pathToArray", "GraphQLError", "locatedError", "collectFields", "assertValidExecutionArguments", "buildExecutionContext", "buildResolveInfo", "execute", "getFieldDef", "mapAsyncIterator", "getArgumentValues", "subscribe", "_x", "_subscribe", "apply", "arguments", "_asyncToGenerator", "args", "length", "resultOrStream", "createSourceEventStream", "mapSourceToResponse", "payload", "rootValue", "toNormalizedArgs", "firstArg", "schema", "document", "contextValue", "variableValues", "operationName", "subscribeFieldResolver", "_createSourceEventStream", "rawArgs", "exeContext", "errors", "eventStream", "executeSubscription", "Error", "error", "_x2", "_executeSubscription", "fragments", "operation", "rootType", "getSubscriptionType", "nodes", "rootFields", "selectionSet", "responseName", "fieldNodes", "entries", "fieldDef", "fieldName", "name", "value", "path", "undefined", "info", "_fieldDef$subscribe", "resolveFn"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/execution/subscribe.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isAsyncIterable } from '../jsutils/isAsyncIterable.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { collectFields } from './collectFields.mjs';\nimport {\n  assertValidExecutionArguments,\n  buildExecutionContext,\n  buildResolveInfo,\n  execute,\n  getFieldDef,\n} from './execute.mjs';\nimport { mapAsyncIterator } from './mapAsyncIterator.mjs';\nimport { getArgumentValues } from './values.mjs';\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification.\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of ExecutionResults representing the response stream.\n *\n * Accepts either an object with named arguments, or individual arguments.\n */\n\nexport async function subscribe(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 ||\n    devAssert(\n      false,\n      'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.',\n    );\n  const resultOrStream = await createSourceEventStream(args);\n\n  if (!isAsyncIterable(resultOrStream)) {\n    return resultOrStream;\n  } // For each payload yielded from a subscription, map it over the normal\n  // GraphQL `execute` function, with `payload` as the rootValue.\n  // This implements the \"MapSourceToResponseEvent\" algorithm described in\n  // the GraphQL specification. The `execute` function provides the\n  // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n  // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n  const mapSourceToResponse = (payload) =>\n    execute({ ...args, rootValue: payload }); // Map every source value to a ExecutionResult value as described above.\n\n  return mapAsyncIterator(resultOrStream, mapSourceToResponse);\n}\n\nfunction toNormalizedArgs(args) {\n  const firstArg = args[0];\n\n  if (firstArg && 'document' in firstArg) {\n    return firstArg;\n  }\n\n  return {\n    schema: firstArg,\n    // FIXME: when underlying TS bug fixed, see https://github.com/microsoft/TypeScript/issues/31613\n    document: args[1],\n    rootValue: args[2],\n    contextValue: args[3],\n    variableValues: args[4],\n    operationName: args[5],\n    subscribeFieldResolver: args[6],\n  };\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport async function createSourceEventStream(...rawArgs) {\n  const args = toNormalizedArgs(rawArgs);\n  const { schema, document, variableValues } = args; // If arguments are missing or incorrectly typed, this is an internal\n  // developer mistake which should throw an early error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext,\n    };\n  }\n\n  try {\n    const eventStream = await executeSubscription(exeContext); // Assert field returned an event stream, otherwise yield an error.\n\n    if (!isAsyncIterable(eventStream)) {\n      throw new Error(\n        'Subscription field must return Async Iterable. ' +\n          `Received: ${inspect(eventStream)}.`,\n      );\n    }\n\n    return eventStream;\n  } catch (error) {\n    // If it GraphQLError, report it as an ExecutionResult, containing only errors and no data.\n    // Otherwise treat the error as a system-class error and re-throw it.\n    if (error instanceof GraphQLError) {\n      return {\n        errors: [error],\n      };\n    }\n\n    throw error;\n  }\n}\n\nasync function executeSubscription(exeContext) {\n  const { schema, fragments, operation, variableValues, rootValue } =\n    exeContext;\n  const rootType = schema.getSubscriptionType();\n\n  if (rootType == null) {\n    throw new GraphQLError(\n      'Schema is not configured to execute subscription operation.',\n      {\n        nodes: operation,\n      },\n    );\n  }\n\n  const rootFields = collectFields(\n    schema,\n    fragments,\n    variableValues,\n    rootType,\n    operation.selectionSet,\n  );\n  const [responseName, fieldNodes] = [...rootFields.entries()][0];\n  const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n\n  if (!fieldDef) {\n    const fieldName = fieldNodes[0].name.value;\n    throw new GraphQLError(\n      `The subscription field \"${fieldName}\" is not defined.`,\n      {\n        nodes: fieldNodes,\n      },\n    );\n  }\n\n  const path = addPath(undefined, responseName, rootType.name);\n  const info = buildResolveInfo(\n    exeContext,\n    fieldDef,\n    fieldNodes,\n    rootType,\n    path,\n  );\n\n  try {\n    var _fieldDef$subscribe;\n\n    // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n    // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    const args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n    // AsyncIterable yielding raw payloads.\n\n    const resolveFn =\n      (_fieldDef$subscribe = fieldDef.subscribe) !== null &&\n      _fieldDef$subscribe !== void 0\n        ? _fieldDef$subscribe\n        : exeContext.subscribeFieldResolver;\n    const eventStream = await resolveFn(rootValue, args, contextValue, info);\n\n    if (eventStream instanceof Error) {\n      throw eventStream;\n    }\n\n    return eventStream;\n  } catch (error) {\n    throw locatedError(error, fieldNodes, pathToArray(path));\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SACEC,6BAA6B,EAC7BC,qBAAqB,EACrBC,gBAAgB,EAChBC,OAAO,EACPC,WAAW,QACN,eAAe;AACtB,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,iBAAiB,QAAQ,cAAc;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAsBC,SAASA,CAAAC,EAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAsB9B,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAtBM,WAAyBC,IAAI,EAAE;IACpC;IACAF,SAAS,CAACG,MAAM,GAAG,CAAC,IAClBtB,SAAS,CACP,KAAK,EACL,qGACF,CAAC;IACH,MAAMuB,cAAc,SAASC,uBAAuB,CAACH,IAAI,CAAC;IAE1D,IAAI,CAACnB,eAAe,CAACqB,cAAc,CAAC,EAAE;MACpC,OAAOA,cAAc;IACvB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;;IAEA,MAAME,mBAAmB,GAAIC,OAAO,IAClCf,OAAO,CAAC;MAAE,GAAGU,IAAI;MAAEM,SAAS,EAAED;IAAQ,CAAC,CAAC,CAAC,CAAC;;IAE5C,OAAOb,gBAAgB,CAACU,cAAc,EAAEE,mBAAmB,CAAC;EAC9D,CAAC;EAAA,OAAAR,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASS,gBAAgBA,CAACP,IAAI,EAAE;EAC9B,MAAMQ,QAAQ,GAAGR,IAAI,CAAC,CAAC,CAAC;EAExB,IAAIQ,QAAQ,IAAI,UAAU,IAAIA,QAAQ,EAAE;IACtC,OAAOA,QAAQ;EACjB;EAEA,OAAO;IACLC,MAAM,EAAED,QAAQ;IAChB;IACAE,QAAQ,EAAEV,IAAI,CAAC,CAAC,CAAC;IACjBM,SAAS,EAAEN,IAAI,CAAC,CAAC,CAAC;IAClBW,YAAY,EAAEX,IAAI,CAAC,CAAC,CAAC;IACrBY,cAAc,EAAEZ,IAAI,CAAC,CAAC,CAAC;IACvBa,aAAa,EAAEb,IAAI,CAAC,CAAC,CAAC;IACtBc,sBAAsB,EAAEd,IAAI,CAAC,CAAC;EAChC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAsBG,uBAAuBA,CAAA;EAAA,OAAAY,wBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAsC5C,SAAAiB,yBAAA;EAAAA,wBAAA,GAAAhB,iBAAA,CAtCM,WAAuC,GAAGiB,OAAO,EAAE;IACxD,MAAMhB,IAAI,GAAGO,gBAAgB,CAACS,OAAO,CAAC;IACtC,MAAM;MAAEP,MAAM;MAAEC,QAAQ;MAAEE;IAAe,CAAC,GAAGZ,IAAI,CAAC,CAAC;IACnD;;IAEAb,6BAA6B,CAACsB,MAAM,EAAEC,QAAQ,EAAEE,cAAc,CAAC,CAAC,CAAC;IACjE;;IAEA,MAAMK,UAAU,GAAG7B,qBAAqB,CAACY,IAAI,CAAC,CAAC,CAAC;;IAEhD,IAAI,EAAE,QAAQ,IAAIiB,UAAU,CAAC,EAAE;MAC7B,OAAO;QACLC,MAAM,EAAED;MACV,CAAC;IACH;IAEA,IAAI;MACF,MAAME,WAAW,SAASC,mBAAmB,CAACH,UAAU,CAAC,CAAC,CAAC;;MAE3D,IAAI,CAACpC,eAAe,CAACsC,WAAW,CAAC,EAAE;QACjC,MAAM,IAAIE,KAAK,CACb,iDAAiD,GAC9C,aAAYzC,OAAO,CAACuC,WAAW,CAAE,GACtC,CAAC;MACH;MAEA,OAAOA,WAAW;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd;MACA;MACA,IAAIA,KAAK,YAAYtC,YAAY,EAAE;QACjC,OAAO;UACLkC,MAAM,EAAE,CAACI,KAAK;QAChB,CAAC;MACH;MAEA,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,OAAAP,wBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEcsB,mBAAmBA,CAAAG,GAAA;EAAA,OAAAC,oBAAA,CAAA3B,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA0B,qBAAA;EAAAA,oBAAA,GAAAzB,iBAAA,CAAlC,WAAmCkB,UAAU,EAAE;IAC7C,MAAM;MAAER,MAAM;MAAEgB,SAAS;MAAEC,SAAS;MAAEd,cAAc;MAAEN;IAAU,CAAC,GAC/DW,UAAU;IACZ,MAAMU,QAAQ,GAAGlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC;IAE7C,IAAID,QAAQ,IAAI,IAAI,EAAE;MACpB,MAAM,IAAI3C,YAAY,CACpB,6DAA6D,EAC7D;QACE6C,KAAK,EAAEH;MACT,CACF,CAAC;IACH;IAEA,MAAMI,UAAU,GAAG5C,aAAa,CAC9BuB,MAAM,EACNgB,SAAS,EACTb,cAAc,EACde,QAAQ,EACRD,SAAS,CAACK,YACZ,CAAC;IACD,MAAM,CAACC,YAAY,EAAEC,UAAU,CAAC,GAAG,CAAC,GAAGH,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAMC,QAAQ,GAAG5C,WAAW,CAACkB,MAAM,EAAEkB,QAAQ,EAAEM,UAAU,CAAC,CAAC,CAAC,CAAC;IAE7D,IAAI,CAACE,QAAQ,EAAE;MACb,MAAMC,SAAS,GAAGH,UAAU,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK;MAC1C,MAAM,IAAItD,YAAY,CACnB,2BAA0BoD,SAAU,mBAAkB,EACvD;QACEP,KAAK,EAAEI;MACT,CACF,CAAC;IACH;IAEA,MAAMM,IAAI,GAAGzD,OAAO,CAAC0D,SAAS,EAAER,YAAY,EAAEL,QAAQ,CAACU,IAAI,CAAC;IAC5D,MAAMI,IAAI,GAAGpD,gBAAgB,CAC3B4B,UAAU,EACVkB,QAAQ,EACRF,UAAU,EACVN,QAAQ,EACRY,IACF,CAAC;IAED,IAAI;MACF,IAAIG,mBAAmB;;MAEvB;MACA;MACA;MACA;MACA,MAAM1C,IAAI,GAAGP,iBAAiB,CAAC0C,QAAQ,EAAEF,UAAU,CAAC,CAAC,CAAC,EAAErB,cAAc,CAAC,CAAC,CAAC;MACzE;MACA;;MAEA,MAAMD,YAAY,GAAGM,UAAU,CAACN,YAAY,CAAC,CAAC;MAC9C;;MAEA,MAAMgC,SAAS,GACb,CAACD,mBAAmB,GAAGP,QAAQ,CAACzC,SAAS,MAAM,IAAI,IACnDgD,mBAAmB,KAAK,KAAK,CAAC,GAC1BA,mBAAmB,GACnBzB,UAAU,CAACH,sBAAsB;MACvC,MAAMK,WAAW,SAASwB,SAAS,CAACrC,SAAS,EAAEN,IAAI,EAAEW,YAAY,EAAE8B,IAAI,CAAC;MAExE,IAAItB,WAAW,YAAYE,KAAK,EAAE;QAChC,MAAMF,WAAW;MACnB;MAEA,OAAOA,WAAW;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,MAAMrC,YAAY,CAACqC,KAAK,EAAEW,UAAU,EAAElD,WAAW,CAACwD,IAAI,CAAC,CAAC;IAC1D;EACF,CAAC;EAAA,OAAAf,oBAAA,CAAA3B,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}