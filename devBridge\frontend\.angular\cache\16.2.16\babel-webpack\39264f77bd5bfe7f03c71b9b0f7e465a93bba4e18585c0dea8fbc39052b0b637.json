{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { Kind } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { isField, resultKeyNameFromField, isReference, makeReference, shouldInclude, addTypenameToDocument, getDefaultValues, getMainDefinition, getQueryDefinition, getFragmentFromSelection, maybeDeepFreeze, mergeDeepArray, DeepMerger, isNonNullObject, canUseWeakMap, compact, canonicalStringify, cacheSizes } from \"../../utilities/index.js\";\nimport { maybeDependOnExistenceOfEntity, supportsResultCaching } from \"./entityStore.js\";\nimport { isArray, extractFragmentContext, getTypenameFromStoreObject, shouldCanonizeResults } from \"./helpers.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { ObjectCanon } from \"./object-canon.js\";\nfunction execSelectionSetKeyArgs(options) {\n  return [options.selectionSet, options.objectOrReference, options.context,\n  // We split out this property so we can pass different values\n  // independently without modifying options.context itself.\n  options.context.canonizeResults];\n}\nvar StoreReader = /** @class */function () {\n  function StoreReader(config) {\n    var _this = this;\n    this.knownResults = new (canUseWeakMap ? WeakMap : Map)();\n    this.config = compact(config, {\n      addTypename: config.addTypename !== false,\n      canonizeResults: shouldCanonizeResults(config)\n    });\n    this.canon = config.canon || new ObjectCanon();\n    // memoized functions in this class will be \"garbage-collected\"\n    // by recreating the whole `StoreReader` in\n    // `InMemoryCache.resetResultsCache`\n    // (triggered from `InMemoryCache.gc` with `resetResultCache: true`)\n    this.executeSelectionSet = wrap(function (options) {\n      var _a;\n      var canonizeResults = options.context.canonizeResults;\n      var peekArgs = execSelectionSetKeyArgs(options);\n      // Negate this boolean option so we can find out if we've already read\n      // this result using the other boolean value.\n      peekArgs[3] = !canonizeResults;\n      var other = (_a = _this.executeSelectionSet).peek.apply(_a, peekArgs);\n      if (other) {\n        if (canonizeResults) {\n          return __assign(__assign({}, other), {\n            // If we previously read this result without canonizing it, we can\n            // reuse that result simply by canonizing it now.\n            result: _this.canon.admit(other.result)\n          });\n        }\n        // If we previously read this result with canonization enabled, we can\n        // return that canonized result as-is.\n        return other;\n      }\n      maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n      // Finally, if we didn't find any useful previous results, run the real\n      // execSelectionSetImpl method with the given options.\n      return _this.execSelectionSetImpl(options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.executeSelectionSet\"] || 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n      keyArgs: execSelectionSetKeyArgs,\n      // Note that the parameters of makeCacheKey are determined by the\n      // array returned by keyArgs.\n      makeCacheKey: function (selectionSet, parent, context, canonizeResults) {\n        if (supportsResultCaching(context.store)) {\n          return context.store.makeCacheKey(selectionSet, isReference(parent) ? parent.__ref : parent, context.varString, canonizeResults);\n        }\n      }\n    });\n    this.executeSubSelectedArray = wrap(function (options) {\n      maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n      return _this.execSubSelectedArrayImpl(options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.executeSubSelectedArray\"] || 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n      makeCacheKey: function (_a) {\n        var field = _a.field,\n          array = _a.array,\n          context = _a.context;\n        if (supportsResultCaching(context.store)) {\n          return context.store.makeCacheKey(field, array, context.varString);\n        }\n      }\n    });\n  }\n  StoreReader.prototype.resetCanon = function () {\n    this.canon = new ObjectCanon();\n  };\n  /**\n   * Given a store and a query, return as much of the result as possible and\n   * identify if any data was missing from the store.\n   */\n  StoreReader.prototype.diffQueryAgainstStore = function (_a) {\n    var store = _a.store,\n      query = _a.query,\n      _b = _a.rootId,\n      rootId = _b === void 0 ? \"ROOT_QUERY\" : _b,\n      variables = _a.variables,\n      _c = _a.returnPartialData,\n      returnPartialData = _c === void 0 ? true : _c,\n      _d = _a.canonizeResults,\n      canonizeResults = _d === void 0 ? this.config.canonizeResults : _d;\n    var policies = this.config.cache.policies;\n    variables = __assign(__assign({}, getDefaultValues(getQueryDefinition(query))), variables);\n    var rootRef = makeReference(rootId);\n    var execResult = this.executeSelectionSet({\n      selectionSet: getMainDefinition(query).selectionSet,\n      objectOrReference: rootRef,\n      enclosingRef: rootRef,\n      context: __assign({\n        store: store,\n        query: query,\n        policies: policies,\n        variables: variables,\n        varString: canonicalStringify(variables),\n        canonizeResults: canonizeResults\n      }, extractFragmentContext(query, this.config.fragments))\n    });\n    var missing;\n    if (execResult.missing) {\n      // For backwards compatibility we still report an array of\n      // MissingFieldError objects, even though there will only ever be at most\n      // one of them, now that all missing field error messages are grouped\n      // together in the execResult.missing tree.\n      missing = [new MissingFieldError(firstMissing(execResult.missing), execResult.missing, query, variables)];\n      if (!returnPartialData) {\n        throw missing[0];\n      }\n    }\n    return {\n      result: execResult.result,\n      complete: !missing,\n      missing: missing\n    };\n  };\n  StoreReader.prototype.isFresh = function (result, parent, selectionSet, context) {\n    if (supportsResultCaching(context.store) && this.knownResults.get(result) === selectionSet) {\n      var latest = this.executeSelectionSet.peek(selectionSet, parent, context,\n      // If result is canonical, then it could only have been previously\n      // cached by the canonizing version of executeSelectionSet, so we can\n      // avoid checking both possibilities here.\n      this.canon.isKnown(result));\n      if (latest && result === latest.result) {\n        return true;\n      }\n    }\n    return false;\n  };\n  // Uncached version of executeSelectionSet.\n  StoreReader.prototype.execSelectionSetImpl = function (_a) {\n    var _this = this;\n    var selectionSet = _a.selectionSet,\n      objectOrReference = _a.objectOrReference,\n      enclosingRef = _a.enclosingRef,\n      context = _a.context;\n    if (isReference(objectOrReference) && !context.policies.rootTypenamesById[objectOrReference.__ref] && !context.store.has(objectOrReference.__ref)) {\n      return {\n        result: this.canon.empty,\n        missing: \"Dangling reference to missing \".concat(objectOrReference.__ref, \" object\")\n      };\n    }\n    var variables = context.variables,\n      policies = context.policies,\n      store = context.store;\n    var typename = store.getFieldValue(objectOrReference, \"__typename\");\n    var objectsToMerge = [];\n    var missing;\n    var missingMerger = new DeepMerger();\n    if (this.config.addTypename && typeof typename === \"string\" && !policies.rootIdsByTypename[typename]) {\n      // Ensure we always include a default value for the __typename\n      // field, if we have one, and this.config.addTypename is true. Note\n      // that this field can be overridden by other merged objects.\n      objectsToMerge.push({\n        __typename: typename\n      });\n    }\n    function handleMissing(result, resultName) {\n      var _a;\n      if (result.missing) {\n        missing = missingMerger.merge(missing, (_a = {}, _a[resultName] = result.missing, _a));\n      }\n      return result.result;\n    }\n    var workSet = new Set(selectionSet.selections);\n    workSet.forEach(function (selection) {\n      var _a, _b;\n      // Omit fields with directives @skip(if: <truthy value>) or\n      // @include(if: <falsy value>).\n      if (!shouldInclude(selection, variables)) return;\n      if (isField(selection)) {\n        var fieldValue = policies.readField({\n          fieldName: selection.name.value,\n          field: selection,\n          variables: context.variables,\n          from: objectOrReference\n        }, context);\n        var resultName = resultKeyNameFromField(selection);\n        if (fieldValue === void 0) {\n          if (!addTypenameToDocument.added(selection)) {\n            missing = missingMerger.merge(missing, (_a = {}, _a[resultName] = \"Can't find field '\".concat(selection.name.value, \"' on \").concat(isReference(objectOrReference) ? objectOrReference.__ref + \" object\" : \"object \" + JSON.stringify(objectOrReference, null, 2)), _a));\n          }\n        } else if (isArray(fieldValue)) {\n          if (fieldValue.length > 0) {\n            fieldValue = handleMissing(_this.executeSubSelectedArray({\n              field: selection,\n              array: fieldValue,\n              enclosingRef: enclosingRef,\n              context: context\n            }), resultName);\n          }\n        } else if (!selection.selectionSet) {\n          // If the field does not have a selection set, then we handle it\n          // as a scalar value. To keep this.canon from canonicalizing\n          // this value, we use this.canon.pass to wrap fieldValue in a\n          // Pass object that this.canon.admit will later unwrap as-is.\n          if (context.canonizeResults) {\n            fieldValue = _this.canon.pass(fieldValue);\n          }\n        } else if (fieldValue != null) {\n          // In this case, because we know the field has a selection set,\n          // it must be trying to query a GraphQLObjectType, which is why\n          // fieldValue must be != null.\n          fieldValue = handleMissing(_this.executeSelectionSet({\n            selectionSet: selection.selectionSet,\n            objectOrReference: fieldValue,\n            enclosingRef: isReference(fieldValue) ? fieldValue : enclosingRef,\n            context: context\n          }), resultName);\n        }\n        if (fieldValue !== void 0) {\n          objectsToMerge.push((_b = {}, _b[resultName] = fieldValue, _b));\n        }\n      } else {\n        var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n        if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n          throw newInvariantError(10, selection.name.value);\n        }\n        if (fragment && policies.fragmentMatches(fragment, typename)) {\n          fragment.selectionSet.selections.forEach(workSet.add, workSet);\n        }\n      }\n    });\n    var result = mergeDeepArray(objectsToMerge);\n    var finalResult = {\n      result: result,\n      missing: missing\n    };\n    var frozen = context.canonizeResults ? this.canon.admit(finalResult)\n    // Since this.canon is normally responsible for freezing results (only in\n    // development), freeze them manually if canonization is disabled.\n    : maybeDeepFreeze(finalResult);\n    // Store this result with its selection set so that we can quickly\n    // recognize it again in the StoreReader#isFresh method.\n    if (frozen.result) {\n      this.knownResults.set(frozen.result, selectionSet);\n    }\n    return frozen;\n  };\n  // Uncached version of executeSubSelectedArray.\n  StoreReader.prototype.execSubSelectedArrayImpl = function (_a) {\n    var _this = this;\n    var field = _a.field,\n      array = _a.array,\n      enclosingRef = _a.enclosingRef,\n      context = _a.context;\n    var missing;\n    var missingMerger = new DeepMerger();\n    function handleMissing(childResult, i) {\n      var _a;\n      if (childResult.missing) {\n        missing = missingMerger.merge(missing, (_a = {}, _a[i] = childResult.missing, _a));\n      }\n      return childResult.result;\n    }\n    if (field.selectionSet) {\n      array = array.filter(context.store.canRead);\n    }\n    array = array.map(function (item, i) {\n      // null value in array\n      if (item === null) {\n        return null;\n      }\n      // This is a nested array, recurse\n      if (isArray(item)) {\n        return handleMissing(_this.executeSubSelectedArray({\n          field: field,\n          array: item,\n          enclosingRef: enclosingRef,\n          context: context\n        }), i);\n      }\n      // This is an object, run the selection set on it\n      if (field.selectionSet) {\n        return handleMissing(_this.executeSelectionSet({\n          selectionSet: field.selectionSet,\n          objectOrReference: item,\n          enclosingRef: isReference(item) ? item : enclosingRef,\n          context: context\n        }), i);\n      }\n      if (globalThis.__DEV__ !== false) {\n        assertSelectionSetForIdValue(context.store, field, item);\n      }\n      return item;\n    });\n    return {\n      result: context.canonizeResults ? this.canon.admit(array) : array,\n      missing: missing\n    };\n  };\n  return StoreReader;\n}();\nexport { StoreReader };\nfunction firstMissing(tree) {\n  try {\n    JSON.stringify(tree, function (_, value) {\n      if (typeof value === \"string\") throw value;\n      return value;\n    });\n  } catch (result) {\n    return result;\n  }\n}\nfunction assertSelectionSetForIdValue(store, field, fieldValue) {\n  if (!field.selectionSet) {\n    var workSet_1 = new Set([fieldValue]);\n    workSet_1.forEach(function (value) {\n      if (isNonNullObject(value)) {\n        invariant(!isReference(value), 11, getTypenameFromStoreObject(store, value), field.name.value);\n        Object.values(value).forEach(workSet_1.add, workSet_1);\n      }\n    });\n  }\n}\n//# sourceMappingURL=readFromStore.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}