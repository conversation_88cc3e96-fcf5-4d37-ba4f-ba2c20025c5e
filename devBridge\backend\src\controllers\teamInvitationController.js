const { Team } = require('../models/Team');
const User = require('../models/User');
const TeamInvitation = require('../models/TeamInvitation');
const JoinRequest = require('../models/JoinRequest');
const TeamHistory = require('../models/TeamHistory');
const { logger } = require('../utils/logger');
const { sendEmail } = require('../utils/sendEmail');

// ==================== INVITATIONS ====================

// Envoyer une invitation
exports.sendInvitation = async (req, res) => {
  try {
    const { email, userId, role = 'member', message } = req.body;
    const teamId = req.params.id;
    const inviterId = req.user.id;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.hasAdminRights(inviterId) && 
        !(team.settings.allowMemberInvite && team.isMember(inviterId))) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour inviter des membres" 
      });
    }

    let invitee = null;
    let invitationType = 'email';

    // Si un userId est fourni, vérifier l'utilisateur
    if (userId) {
      invitee = await User.findById(userId);
      if (!invitee) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }
      
      if (team.isMember(userId)) {
        return res.status(400).json({ 
          message: "Cet utilisateur est déjà membre de l'équipe" 
        });
      }
      
      invitationType = 'direct';
    } else if (!email) {
      return res.status(400).json({ 
        message: "Email ou ID utilisateur requis" 
      });
    }

    // Vérifier s'il y a déjà une invitation en attente
    const existingInvitation = await TeamInvitation.findOne({
      team: teamId,
      $or: [
        { invitee: userId },
        { email: email }
      ],
      status: 'pending'
    });

    if (existingInvitation) {
      return res.status(400).json({ 
        message: "Une invitation est déjà en attente pour cet utilisateur" 
      });
    }

    // Créer l'invitation
    const invitation = new TeamInvitation({
      team: teamId,
      inviter: inviterId,
      invitee: userId || null,
      email: email || (invitee ? invitee.email : null),
      type: invitationType,
      role: role,
      message: message || '',
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'web'
      }
    });

    await invitation.save();
    await invitation.populate([
      { path: 'team', select: 'name description' },
      { path: 'inviter', select: 'fullName email' }
    ]);

    // Envoyer l'email d'invitation
    if (email) {
      const inviteLink = `${process.env.FRONTEND_URL}/teams/invite/${invitation.token}`;
      
      try {
        await sendEmail({
          to: email,
          subject: `Invitation à rejoindre l'équipe ${team.name}`,
          template: 'team-invitation',
          data: {
            teamName: team.name,
            inviterName: req.user.fullName,
            message: message || '',
            inviteLink: inviteLink,
            expiresAt: invitation.expiresAt
          }
        });
      } catch (emailError) {
        logger.error('Erreur envoi email invitation:', emailError);
        // Ne pas faire échouer l'invitation si l'email échoue
      }
    }

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      teamId, 
      inviterId, 
      'invite_sent', 
      `Invitation envoyée à ${email || invitee.fullName}`,
      { 
        affectedUser: userId,
        newData: { email, role, type: invitationType },
        category: 'invitation'
      }
    );

    logger.info(`Invitation envoyée pour l'équipe ${team.name} à ${email || invitee.fullName}`);

    res.status(201).json({
      message: "Invitation envoyée avec succès",
      invitation
    });
  } catch (error) {
    logger.error('Erreur envoi invitation:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'envoi de l'invitation", 
      error: error.message 
    });
  }
};

// Obtenir les invitations d'une équipe
exports.getTeamInvitations = async (req, res) => {
  try {
    const teamId = req.params.id;
    const { status = 'pending' } = req.query;

    const team = await Team.findById(teamId);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions
    if (!team.hasModeratorRights(req.user.id)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour voir les invitations" 
      });
    }

    const invitations = await TeamInvitation.find({
      team: teamId,
      ...(status !== 'all' && { status })
    })
    .populate('inviter', 'fullName email profileImage')
    .populate('invitee', 'fullName email profileImage')
    .sort({ createdAt: -1 });

    res.json({
      invitations,
      count: invitations.length
    });
  } catch (error) {
    logger.error('Erreur récupération invitations:', error);
    res.status(500).json({ 
      message: "Erreur lors de la récupération des invitations", 
      error: error.message 
    });
  }
};

// Accepter une invitation
exports.acceptInvitation = async (req, res) => {
  try {
    const { token } = req.params;
    const userId = req.user.id;

    const invitation = await TeamInvitation.findByToken(token);
    if (!invitation) {
      return res.status(404).json({ 
        message: "Invitation non trouvée ou expirée" 
      });
    }

    // Vérifier que l'utilisateur peut accepter cette invitation
    if (invitation.invitee && invitation.invitee.toString() !== userId) {
      return res.status(403).json({ 
        message: "Cette invitation ne vous est pas destinée" 
      });
    }

    if (invitation.email && invitation.email !== req.user.email) {
      return res.status(403).json({ 
        message: "Cette invitation ne correspond pas à votre email" 
      });
    }

    const team = await Team.findById(invitation.team);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    if (team.isMember(userId)) {
      return res.status(400).json({ 
        message: "Vous êtes déjà membre de cette équipe" 
      });
    }

    if (team.isFullTeam) {
      return res.status(400).json({ 
        message: "L'équipe a atteint sa capacité maximale" 
      });
    }

    // Accepter l'invitation
    await invitation.accept();

    // Ajouter l'utilisateur à l'équipe selon son rôle
    if (invitation.role === 'moderator') {
      team.addMember(userId);
      team.addModerator(userId);
    } else if (invitation.role === 'co-admin') {
      team.addMember(userId);
      team.addCoAdmin(userId);
    } else {
      team.addMember(userId);
    }

    await team.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      team._id, 
      userId, 
      'invite_accepted', 
      `${req.user.fullName} a accepté l'invitation`,
      { 
        affectedUser: userId,
        newData: { role: invitation.role },
        category: 'invitation'
      }
    );

    await team.populate([
      { path: 'admin', select: 'fullName email profileImage' },
      { path: 'coAdmins', select: 'fullName email profileImage' },
      { path: 'moderators', select: 'fullName email profileImage' },
      { path: 'members', select: 'fullName email profileImage' }
    ]);

    logger.info(`Invitation acceptée pour l'équipe ${team.name} par ${req.user.fullName}`);

    res.json({
      message: "Invitation acceptée avec succès",
      team,
      role: invitation.role
    });
  } catch (error) {
    logger.error('Erreur acceptation invitation:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'acceptation de l'invitation", 
      error: error.message 
    });
  }
};

// Décliner une invitation
exports.declineInvitation = async (req, res) => {
  try {
    const { token } = req.params;
    const userId = req.user.id;

    const invitation = await TeamInvitation.findByToken(token);
    if (!invitation) {
      return res.status(404).json({ 
        message: "Invitation non trouvée ou expirée" 
      });
    }

    // Vérifier que l'utilisateur peut décliner cette invitation
    if (invitation.invitee && invitation.invitee.toString() !== userId) {
      return res.status(403).json({ 
        message: "Cette invitation ne vous est pas destinée" 
      });
    }

    if (invitation.email && invitation.email !== req.user.email) {
      return res.status(403).json({ 
        message: "Cette invitation ne correspond pas à votre email" 
      });
    }

    await invitation.decline();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      invitation.team, 
      userId, 
      'invite_declined', 
      `${req.user.fullName} a décliné l'invitation`,
      { 
        affectedUser: userId,
        category: 'invitation'
      }
    );

    logger.info(`Invitation déclinée pour l'équipe ${invitation.team} par ${req.user.fullName}`);

    res.json({
      message: "Invitation déclinée"
    });
  } catch (error) {
    logger.error('Erreur déclinaison invitation:', error);
    res.status(500).json({ 
      message: "Erreur lors du déclin de l'invitation", 
      error: error.message 
    });
  }
};

// Annuler une invitation
exports.cancelInvitation = async (req, res) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.id;

    const invitation = await TeamInvitation.findById(invitationId);
    if (!invitation) {
      return res.status(404).json({ message: "Invitation non trouvée" });
    }

    const team = await Team.findById(invitation.team);
    if (!team) {
      return res.status(404).json({ message: "Équipe non trouvée" });
    }

    // Vérifier les permissions (inviteur ou admin)
    if (invitation.inviter.toString() !== userId && !team.hasAdminRights(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour annuler cette invitation" 
      });
    }

    await invitation.cancel();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      team._id, 
      userId, 
      'invite_cancelled', 
      `Invitation annulée`,
      { 
        affectedUser: invitation.invitee,
        category: 'invitation'
      }
    );

    logger.info(`Invitation annulée pour l'équipe ${team.name}`);

    res.json({
      message: "Invitation annulée avec succès"
    });
  } catch (error) {
    logger.error('Erreur annulation invitation:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'annulation de l'invitation", 
      error: error.message 
    });
  }
};

module.exports = {
  sendInvitation: exports.sendInvitation,
  getTeamInvitations: exports.getTeamInvitations,
  acceptInvitation: exports.acceptInvitation,
  declineInvitation: exports.declineInvitation,
  cancelInvitation: exports.cancelInvitation
};
