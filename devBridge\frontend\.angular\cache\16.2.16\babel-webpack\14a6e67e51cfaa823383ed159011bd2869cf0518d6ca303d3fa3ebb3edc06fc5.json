{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\n// CDK\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from \"@angular/core\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static {\n      this.ɵfac = function SharedModule_Factory(t) {\n        return new (t || SharedModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SharedModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, ReactiveFormsModule, FormsModule,\n        // Angular Material\n        MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n        // CDK\n        DragDropModule, OverlayModule,\n        // Angular Material (pour réutilisation)\n        MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n        // CDK\n        DragDropModule, OverlayModule,\n        // Forms\n        ReactiveFormsModule, FormsModule]\n      });\n    }\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}