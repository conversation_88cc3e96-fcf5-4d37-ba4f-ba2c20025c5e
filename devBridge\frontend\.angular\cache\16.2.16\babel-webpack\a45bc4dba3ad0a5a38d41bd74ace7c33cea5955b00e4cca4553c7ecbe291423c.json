{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique fragment names\n *\n * A GraphQL document is only valid if all defined fragments have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Name-Uniqueness\n */\nexport function UniqueFragmentNamesRule(context) {\n  const knownFragmentNames = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n    FragmentDefinition(node) {\n      const fragmentName = node.name.value;\n      if (knownFragmentNames[fragmentName]) {\n        context.reportError(new GraphQLError(`There can be only one fragment named \"${fragmentName}\".`, {\n          nodes: [knownFragmentNames[fragmentName], node.name]\n        }));\n      } else {\n        knownFragmentNames[fragmentName] = node.name;\n      }\n      return false;\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "UniqueFragmentNamesRule", "context", "knownFragmentNames", "Object", "create", "OperationDefinition", "FragmentDefinition", "node", "fragmentName", "name", "value", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueFragmentNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique fragment names\n *\n * A GraphQL document is only valid if all defined fragments have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Name-Uniqueness\n */\nexport function UniqueFragmentNamesRule(context) {\n  const knownFragmentNames = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n\n    FragmentDefinition(node) {\n      const fragmentName = node.name.value;\n\n      if (knownFragmentNames[fragmentName]) {\n        context.reportError(\n          new GraphQLError(\n            `There can be only one fragment named \"${fragmentName}\".`,\n            {\n              nodes: [knownFragmentNames[fragmentName], node.name],\n            },\n          ),\n        );\n      } else {\n        knownFragmentNames[fragmentName] = node.name;\n      }\n\n      return false;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9C,OAAO;IACLC,mBAAmB,EAAEA,CAAA,KAAM,KAAK;IAEhCC,kBAAkBA,CAACC,IAAI,EAAE;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;MAEpC,IAAIR,kBAAkB,CAACM,YAAY,CAAC,EAAE;QACpCP,OAAO,CAACU,WAAW,CACjB,IAAIZ,YAAY,CACb,yCAAwCS,YAAa,IAAG,EACzD;UACEI,KAAK,EAAE,CAACV,kBAAkB,CAACM,YAAY,CAAC,EAAED,IAAI,CAACE,IAAI;QACrD,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLP,kBAAkB,CAACM,YAAY,CAAC,GAAGD,IAAI,CAACE,IAAI;MAC9C;MAEA,OAAO,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}