{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class MonthParser extends Parser {\n  incompatibleTokens = [\"Y\", \"R\", \"q\", \"Q\", \"L\", \"w\", \"I\", \"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n  priority = 110;\n  parse(dateString, token, match) {\n    const valueCallback = value => value - 1;\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"M\":\n        return mapValue(parseNumericPattern(numericPatterns.month, dateString), valueCallback);\n      // 01, 02, ..., 12\n      case \"MM\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"month\"\n        }), valueCallback);\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return match.month(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.month(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n\n      // J, F, ..., D\n      case \"MMMMM\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return match.month(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.month(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.month(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}