.ai-assistant-container {
  padding: 1rem;
  height: 100%;
  overflow-y: auto;
  
  .ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
    
    .ai-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      mat-icon {
        color: #8b5cf6;
        font-size: 1.5rem;
      }
      
      h3 {
        margin: 0;
        color: #1e293b;
        font-weight: 600;
      }
    }
    
    .ai-status {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.875rem;
      background: #f1f5f9;
      color: #64748b;
      
      &.enabled {
        background: #dcfce7;
        color: #16a34a;
        
        mat-icon {
          color: #16a34a;
        }
      }
      
      mat-icon {
        font-size: 1rem;
      }
    }
  }
  
  .task-info {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #8b5cf6;
    
    h4 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
      font-weight: 600;
    }
    
    .task-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        color: #64748b;
        
        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }
  
  .ai-actions {
    margin-bottom: 1.5rem;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #1e293b;
      font-weight: 600;
    }
    
    .action-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
      
      button {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        min-height: 80px;
        position: relative;
        
        mat-icon {
          font-size: 1.5rem;
        }
        
        span {
          font-size: 0.875rem;
          font-weight: 500;
        }
        
        mat-spinner {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
  
  .ai-suggestions {
    margin-bottom: 1.5rem;
    
    .suggestions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      
      h4 {
        margin: 0;
        color: #1e293b;
        font-weight: 600;
      }
    }
    
    .suggestions-list {
      .suggestion-item {
        margin-bottom: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.2s ease;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.applied {
          background: #f0fdf4;
          border-color: #16a34a;
          
          .suggestion-header {
            background: #dcfce7;
          }
        }
        
        .suggestion-header {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1rem;
          background: #f8fafc;
          
          .suggestion-icon {
            flex-shrink: 0;
            
            mat-icon {
              font-size: 1.5rem;
            }
          }
          
          .suggestion-content {
            flex: 1;
            
            h5 {
              margin: 0 0 0.5rem 0;
              color: #1e293b;
              font-weight: 600;
              font-size: 0.875rem;
            }
            
            p {
              margin: 0;
              color: #64748b;
              font-size: 0.875rem;
              line-height: 1.4;
            }
          }
          
          .suggestion-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            
            .confidence-badge {
              padding: 0.25rem 0.5rem;
              border-radius: 12px;
              font-size: 0.75rem;
              font-weight: 600;
              color: white;
            }
          }
        }
        
        .suggestion-details {
          .details-content {
            padding: 1rem;
            
            p {
              margin: 0 0 1rem 0;
              color: #64748b;
              font-size: 0.875rem;
              line-height: 1.5;
            }
            
            .suggestion-metadata {
              display: flex;
              flex-direction: column;
              gap: 0.5rem;
              
              .meta-item {
                font-size: 0.75rem;
                color: #64748b;
                
                strong {
                  color: #1e293b;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .ai-stats {
    margin-bottom: 1.5rem;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #1e293b;
      font-weight: 600;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
      margin-bottom: 1rem;
      
      .stat-item {
        padding: 0.75rem;
        background: #f8fafc;
        border-radius: 6px;
        text-align: center;
        
        .stat-label {
          display: block;
          font-size: 0.75rem;
          color: #64748b;
          margin-bottom: 0.25rem;
        }
        
        .stat-value {
          display: block;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
        }
      }
    }
    
    .reanalysis-recommendation {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem;
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 6px;
      font-size: 0.875rem;
      
      mat-icon {
        color: #f59e0b;
      }
      
      span {
        flex: 1;
        color: #92400e;
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: #64748b;
    
    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
    
    h4 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
    }
    
    p {
      margin: 0 0 1.5rem 0;
      font-size: 0.875rem;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .ai-assistant-container {
    .ai-actions .action-grid {
      grid-template-columns: 1fr;
    }
    
    .ai-stats .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .suggestion-header {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.75rem !important;
      
      .suggestion-actions {
        align-self: flex-end;
      }
    }
  }
}
