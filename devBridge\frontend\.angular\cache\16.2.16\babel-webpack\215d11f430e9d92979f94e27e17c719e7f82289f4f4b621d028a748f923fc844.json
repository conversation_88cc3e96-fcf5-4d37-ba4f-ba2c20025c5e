{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 32)(2, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r9.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 24);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 29);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 30);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 41);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 38);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      ctx_r25.selectedNotifications.clear();\n      ctx_r25.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r25.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 44);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 51);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 90);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      ctx_r42.getNotificationAttachments(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      ctx_r45.joinConversation(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.markAsRead(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64)(2, \"div\", 65)(3, \"label\", 32)(4, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.toggleSelection(notification_r33.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 66);\n    i0.ɵɵelement(7, \"img\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"div\", 69)(10, \"div\", 70)(11, \"div\", 71)(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 73);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 74)(18, \"span\", 75);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 76);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 79);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 80);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 2, 0, \"button\", 81);\n    i0.ɵɵelementStart(26, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      ctx_r53.openNotificationDetails(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 84);\n    i0.ɵɵelementStart(29, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      ctx_r54.deleteNotification(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r33.isRead)(\"futuristic-notification-read\", notification_r33.isRead)(\"futuristic-notification-selected\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r33.senderId == null ? null : notification_r33.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r33.senderId == null ? null : notification_r33.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r33.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r33.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.type === \"NEW_MESSAGE\" || notification_r33.type === \"GROUP_INVITE\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"div\", 97);\n    i0.ɵɵelementStart(2, \"p\", 98);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60, 61);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const _r30 = i0.ɵɵreference(1);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onScroll(_r30));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 62);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"img\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const attachment_r58 = i0.ɵɵnextContext().$implicit;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r58.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r60.getFileIcon(attachment_r58.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r61.formatFileSize(attachment_r58.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 103);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 104);\n    i0.ɵɵelementStart(3, \"div\", 105)(4, \"div\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"span\", 108);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 110)(11, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelement(12, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.downloadAttachment(attachment_r58));\n    });\n    i0.ɵɵelement(14, \"i\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = ctx.$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r58.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r57.getFileTypeLabel(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r58.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nexport class NotificationListComponent {\n  constructor(messageService, themeService, router) {\n    this.messageService = messageService;\n    this.themeService = themeService;\n    this.router = router;\n    this.loading = true;\n    this.loadingMore = false;\n    this.hasMoreNotifications = true;\n    this.error = null;\n    this.showOnlyUnread = false;\n    this.isSoundMuted = false;\n    // Propriétés pour la sélection multiple\n    this.selectedNotifications = new Set();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n    // Propriétés pour le modal des pièces jointes\n    this.showAttachmentsModal = false;\n    this.loadingAttachments = false;\n    this.currentAttachments = [];\n    // Propriétés pour le modal des détails de notification\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.destroy$ = new Subject();\n    this.scrollPosition$ = new BehaviorSubject(0);\n    this.notifications$ = this.messageService.notifications$;\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n    this.unreadCount$ = this.messageService.notificationCount$;\n    this.isDarkMode$ = this.themeService.darkMode$;\n    // Vérifier l'état du son\n    this.isSoundMuted = this.messageService.isMuted();\n  }\n  /**\n   * Rejoint une conversation ou un groupe à partir d'une notification\n   * @param notification Notification contenant les informations de la conversation ou du groupe\n   */\n  joinConversation(notification) {\n    console.log('Rejoindre la conversation:', notification);\n    // Marquer la notification comme lue\n    this.markAsRead(notification.id);\n    // Extraire les informations pertinentes de la notification\n    const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n    const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n    // Déterminer où rediriger l'utilisateur\n    if (conversationId) {\n      // Rediriger vers la conversation existante\n      console.log('Redirection vers la conversation existante:', conversationId);\n      // Utiliser le format exact de l'URL fournie avec l'ID\n      window.location.href = `/messages/conversations/chat/${conversationId}`;\n    } else if (groupId) {\n      // Rediriger vers le groupe\n      console.log('Redirection vers le groupe:', groupId);\n      window.location.href = `/messages/group/${groupId}`;\n    } else if (notification.senderId && notification.senderId.id) {\n      // Si aucun ID de conversation n'est trouvé, mais qu'il y a un expéditeur,\n      // utiliser getOrCreateConversation pour obtenir ou créer une conversation\n      console.log(\"Création/récupération d'une conversation avec l'utilisateur:\", notification.senderId.id);\n      // Afficher un indicateur de chargement si nécessaire\n      // this.loading = true;\n      this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n        next: conversation => {\n          console.log('Conversation obtenue:', conversation);\n          // this.loading = false;\n          if (conversation && conversation.id) {\n            // Rediriger vers la conversation nouvellement créée ou récupérée\n            // Utiliser le format exact de l'URL fournie avec l'ID\n            window.location.href = `/messages/conversations/chat/${conversation.id}`;\n          } else {\n            console.error('Conversation invalide reçue:', conversation);\n            window.location.href = '/messages';\n          }\n        },\n        error: error => {\n          console.error('Erreur lors de la création/récupération de la conversation:', error);\n          // this.loading = false;\n          // En cas d'erreur, rediriger vers la liste des messages\n          window.location.href = '/messages';\n        }\n      });\n    } else {\n      // Si aucune information n'est trouvée, rediriger vers la liste des messages\n      console.log('Redirection vers la liste des messages');\n      window.location.href = '/messages';\n    }\n  }\n  onScroll(target) {\n    if (!target) return;\n    const scrollPosition = target.scrollTop;\n    const scrollHeight = target.scrollHeight;\n    const clientHeight = target.clientHeight;\n    // Si on est proche du bas (à 200px du bas)\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\n      this.scrollPosition$.next(scrollPosition);\n    }\n  }\n  ngOnInit() {\n    // Charger la préférence de son depuis le localStorage\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n    if (savedMutePreference !== null) {\n      this.isSoundMuted = savedMutePreference === 'true';\n      console.log(`Préférence de son chargée: ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n      this.messageService.setMuted(this.isSoundMuted);\n    }\n    this.loadNotifications();\n    this.setupSubscriptions();\n    this.setupInfiniteScroll();\n    this.filterDeletedNotifications();\n  }\n  /**\n   * Filtre les notifications supprimées lors du chargement initial\n   */\n  filterDeletedNotifications() {\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    if (deletedNotificationIds.size > 0) {\n      console.log(`Filtrage de ${deletedNotificationIds.size} notifications supprimées`);\n      // Filtrer les notifications pour exclure celles qui ont été supprimées\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        // Mettre à jour l'interface utilisateur\n        this.messageService.notifications.next(filteredNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(filteredNotifications);\n      });\n    }\n  }\n  setupInfiniteScroll() {\n    // Configurer le chargement des anciennes notifications lors du défilement\n    this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n    // Attendre 200ms après le dernier événement de défilement\n    distinctUntilChanged(),\n    // Ne déclencher que si la position de défilement a changé\n    filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n    ).subscribe(() => {\n      this.loadMoreNotifications();\n    });\n  }\n  loadNotifications() {\n    console.log('NotificationListComponent: Loading notifications');\n    this.loading = true;\n    this.loadingMore = false;\n    this.error = null;\n    this.hasMoreNotifications = true;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n    this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n      // Filtrer les notifications supprimées\n      if (deletedNotificationIds.size > 0) {\n        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        console.log('NotificationListComponent: Notifications loaded successfully', notifications);\n        // Mettre à jour l'interface utilisateur avec les notifications filtrées\n        this.messageService.notifications.next(notifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = notifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        this.loading = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        console.error('NotificationListComponent: Error loading notifications', err);\n        this.error = err;\n        this.loading = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  loadMoreNotifications() {\n    if (this.loadingMore || !this.hasMoreNotifications) return;\n    console.log('NotificationListComponent: Loading more notifications');\n    this.loadingMore = true;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n    this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n      // Filtrer les notifications supprimées\n      if (deletedNotificationIds.size > 0) {\n        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        console.log(`Filtré ${notifications.length - filteredNotifications.length} notifications supprimées`);\n        return filteredNotifications;\n      }\n      return notifications;\n    })).subscribe({\n      next: notifications => {\n        console.log('NotificationListComponent: More notifications loaded successfully', notifications);\n        // Mettre à jour l'interface utilisateur avec les notifications filtrées\n        this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n          const allNotifications = [...existingNotifications, ...notifications];\n          this.messageService.notifications.next(allNotifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = allNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          // Mettre à jour le cache de notifications dans le service\n          this.updateNotificationCache(allNotifications);\n        });\n        this.loadingMore = false;\n        this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n      },\n      error: err => {\n        console.error('NotificationListComponent: Error loading more notifications', err);\n        this.loadingMore = false;\n        this.hasMoreNotifications = false;\n      }\n    });\n  }\n  setupSubscriptions() {\n    this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.error('Notification stream error:', error);\n      return of(null);\n    })).subscribe();\n    this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n      console.error('Notifications read stream error:', error);\n      return of(null);\n    })).subscribe();\n  }\n  markAsRead(notificationId) {\n    console.log('Marking notification as read:', notificationId);\n    if (!notificationId) {\n      console.error('Invalid notification ID:', notificationId);\n      this.error = new Error('Invalid notification ID');\n      return;\n    }\n    // Afficher des informations de débogage sur la notification\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const notification = notifications.find(n => n.id === notificationId);\n      if (notification) {\n        console.log('Found notification to mark as read:', {\n          id: notification.id,\n          type: notification.type,\n          isRead: notification.isRead\n        });\n        // Mettre à jour localement la notification\n        const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Appeler le service pour marquer la notification comme lue\n        this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            console.log('Mark as read result:', result);\n            if (result && result.success) {\n              console.log('Notification marked as read successfully');\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            console.error('Error marking notification as read:', err);\n            console.error('Error details:', {\n              message: err.message,\n              stack: err.stack,\n              notificationId\n            });\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n            // this.error = err;\n          }\n        });\n      } else {\n        console.warn('Notification not found in local cache:', notificationId);\n        // Forcer le rechargement des notifications\n        this.loadNotifications();\n      }\n    });\n  }\n  // Méthode pour mettre à jour le cache de notifications dans le service\n  updateNotificationCache(notifications) {\n    // Mettre à jour le cache de notifications dans le service\n    const notificationCache = this.messageService.notificationCache;\n    if (notificationCache) {\n      notifications.forEach(notification => {\n        notificationCache.set(notification.id, notification);\n      });\n      // Forcer la mise à jour du compteur\n      const unreadCount = notifications.filter(n => !n.isRead).length;\n      this.messageService.notificationCount.next(unreadCount);\n      console.log('Notification cache updated, new unread count:', unreadCount);\n    }\n  }\n  markAllAsRead() {\n    console.log('Marking all notifications as read');\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      console.log('All notifications:', notifications);\n      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n      console.log('Unread notification IDs to mark as read:', unreadIds);\n      if (unreadIds.length === 0) {\n        console.log('No unread notifications to mark as read');\n        return;\n      }\n      // Vérifier que tous les IDs sont valides\n      const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n      if (validIds.length !== unreadIds.length) {\n        console.error('Some notification IDs are invalid:', unreadIds);\n        this.error = new Error('Invalid notification IDs');\n        return;\n      }\n      console.log('Marking all notifications as read:', validIds);\n      // Mettre à jour localement toutes les notifications\n      const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n        ...n,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : n);\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.messageService.notifications.next(updatedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n      this.messageService.notificationCount.next(unreadCount);\n      // Mettre à jour le cache de notifications dans le service\n      this.updateNotificationCache(updatedNotifications);\n      // Appeler le service pour marquer toutes les notifications comme lues\n      this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Mark all as read result:', result);\n          if (result && result.success) {\n            console.log('All notifications marked as read successfully');\n            // Si l'erreur était liée à cette opération, la réinitialiser\n            if (this.error && this.error.message.includes('mark')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          console.error('Error marking all notifications as read:', err);\n          console.error('Error details:', {\n            message: err.message,\n            stack: err.stack\n          });\n          // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n          // this.error = err;\n        }\n      });\n    });\n  }\n\n  hasNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n  }\n  hasUnreadNotifications() {\n    return this.unreadCount$.pipe(map(count => count > 0));\n  }\n  /**\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\n   */\n  toggleUnreadFilter() {\n    this.showOnlyUnread = !this.showOnlyUnread;\n    console.log(`Filtre des notifications non lues ${this.showOnlyUnread ? 'activé' : 'désactivé'}`);\n    if (this.showOnlyUnread) {\n      // Utiliser la méthode du service pour obtenir uniquement les notifications non lues\n      this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n    } else {\n      // Afficher toutes les notifications\n      this.filteredNotifications$ = this.notifications$;\n    }\n  }\n  /**\n   * Active/désactive le son des notifications\n   */\n  toggleSound() {\n    this.isSoundMuted = !this.isSoundMuted;\n    console.log(`Son des notifications ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n    // Utiliser la méthode du service pour activer/désactiver le son\n    this.messageService.setMuted(this.isSoundMuted);\n    // Tester le son si activé\n    if (!this.isSoundMuted) {\n      console.log('Test du son de notification...');\n      // Jouer le son après un court délai pour s'assurer que le navigateur est prêt\n      setTimeout(() => {\n        // Jouer le son deux fois pour s'assurer qu'il est audible\n        this.messageService.playNotificationSound();\n        // Jouer une deuxième fois après 1 seconde\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n        }, 1000);\n      }, 100);\n    }\n    // Sauvegarder la préférence dans le localStorage\n    localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n  }\n  /**\n   * Récupère les pièces jointes d'une notification et ouvre le modal\n   * @param notificationId ID de la notification\n   */\n  getNotificationAttachments(notificationId) {\n    if (!notificationId) {\n      console.error('ID de notification invalide');\n      return;\n    }\n    console.log(`Récupération des pièces jointes pour la notification ${notificationId}`);\n    // Réinitialiser les pièces jointes et afficher le modal\n    this.currentAttachments = [];\n    this.loadingAttachments = true;\n    this.showAttachmentsModal = true;\n    // Vérifier d'abord si la notification existe dans le cache local\n    let notification;\n    // Utiliser pipe(take(1)) pour obtenir la valeur actuelle de l'Observable\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      notification = notifications.find(n => n.id === notificationId);\n    });\n    if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n      console.log('Pièces jointes trouvées dans le cache local:', notification.message.attachments);\n      this.loadingAttachments = false;\n      // Conversion des pièces jointes au format attendu\n      this.currentAttachments = notification.message.attachments.map(attachment => ({\n        id: '',\n        url: attachment.url || '',\n        type: this.convertAttachmentType(attachment.type),\n        name: attachment.name || '',\n        size: attachment.size || 0,\n        duration: 0 // NotificationAttachment n'a pas de durée\n      }));\n\n      return;\n    }\n    // Si aucune pièce jointe n'est trouvée localement, essayer de les récupérer du serveur\n    this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: attachments => {\n        console.log(`${attachments.length} pièces jointes récupérées du serveur`, attachments);\n        this.loadingAttachments = false;\n        this.currentAttachments = attachments;\n      },\n      error: err => {\n        console.error('Erreur lors de la récupération des pièces jointes', err);\n        this.loadingAttachments = false;\n      }\n    });\n  }\n  /**\n   * Ferme le modal des pièces jointes\n   */\n  closeAttachmentsModal() {\n    this.showAttachmentsModal = false;\n  }\n  /**\n   * Ouvre le modal des détails de notification\n   * @param notification Notification à afficher\n   */\n  openNotificationDetails(notification) {\n    console.log('Ouverture des détails de la notification:', notification);\n    this.currentNotification = notification;\n    this.showNotificationDetailsModal = true;\n    // Marquer la notification comme lue\n    if (!notification.isRead) {\n      this.markAsRead(notification.id);\n    }\n  }\n  /**\n   * Ferme le modal des détails de notification\n   */\n  closeNotificationDetailsModal() {\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n  }\n  /**\n   * Vérifie si le type de fichier est une image\n   * @param type Type MIME du fichier\n   * @returns true si c'est une image, false sinon\n   */\n  isImage(type) {\n    return type?.startsWith('image/') || false;\n  }\n  /**\n   * Obtient l'icône FontAwesome correspondant au type de fichier\n   * @param type Type MIME du fichier\n   * @returns Classe CSS de l'icône\n   */\n  getFileIcon(type) {\n    if (!type) return 'fas fa-file';\n    if (type.startsWith('image/')) return 'fas fa-file-image';\n    if (type.startsWith('video/')) return 'fas fa-file-video';\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\n    if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n    if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n    if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n    return 'fas fa-file';\n  }\n  /**\n   * Obtient le libellé du type de fichier\n   * @param type Type MIME du fichier\n   * @returns Libellé du type de fichier\n   */\n  getFileTypeLabel(type) {\n    if (!type) return 'Fichier';\n    if (type.startsWith('image/')) return 'Image';\n    if (type.startsWith('video/')) return 'Vidéo';\n    if (type.startsWith('audio/')) return 'Audio';\n    if (type.startsWith('text/')) return 'Texte';\n    if (type.includes('pdf')) return 'PDF';\n    if (type.includes('word') || type.includes('document')) return 'Document';\n    if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n    if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n    return 'Fichier';\n  }\n  /**\n   * Formate la taille du fichier en unités lisibles\n   * @param size Taille en octets\n   * @returns Taille formatée (ex: \"1.5 MB\")\n   */\n  formatFileSize(size) {\n    if (!size) return '';\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let i = 0;\n    let formattedSize = size;\n    while (formattedSize >= 1024 && i < units.length - 1) {\n      formattedSize /= 1024;\n      i++;\n    }\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\n  }\n  /**\n   * Ouvre une pièce jointe dans un nouvel onglet\n   * @param url URL de la pièce jointe\n   */\n  openAttachment(url) {\n    if (!url) return;\n    window.open(url, '_blank');\n  }\n  /**\n   * Télécharge une pièce jointe\n   * @param attachment Pièce jointe à télécharger\n   */\n  downloadAttachment(attachment) {\n    if (!attachment?.url) return;\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'attachment';\n    link.target = '_blank';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  acceptFriendRequest(notification) {\n    this.markAsRead(notification.id);\n  }\n  /**\n   * Supprime une notification et la stocke dans le localStorage\n   * @param notificationId ID de la notification à supprimer\n   */\n  deleteNotification(notificationId) {\n    console.log('Suppression de la notification:', notificationId);\n    if (!notificationId) {\n      console.error('ID de notification invalide');\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    // Ajouter l'ID de la notification à supprimer\n    deletedNotificationIds.add(notificationId);\n    // Sauvegarder les IDs dans le localStorage\n    this.saveDeletedNotificationIds(deletedNotificationIds);\n    // Appeler le service pour supprimer la notification\n    this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        console.log('Résultat de la suppression:', result);\n        if (result && result.success) {\n          console.log('Notification supprimée avec succès');\n          // Si l'erreur était liée à cette opération, la réinitialiser\n          if (this.error && this.error.message.includes('suppression')) {\n            this.error = null;\n          }\n        }\n      },\n      error: err => {\n        console.error('Erreur lors de la suppression de la notification:', err);\n        // Même en cas d'erreur, conserver l'ID dans le localStorage\n        this.error = err;\n      }\n    });\n  }\n  /**\n   * Supprime toutes les notifications et les stocke dans le localStorage\n   */\n  deleteAllNotifications() {\n    console.log('Suppression de toutes les notifications');\n    // Récupérer toutes les notifications actuelles\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      // Ajouter tous les IDs des notifications actuelles\n      notifications.forEach(notification => {\n        deletedNotificationIds.add(notification.id);\n      });\n      // Sauvegarder les IDs dans le localStorage\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      // Appeler le service pour supprimer toutes les notifications\n      this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression de toutes les notifications:', result);\n          if (result && result.success) {\n            console.log(`${result.count} notifications supprimées avec succès`);\n            // Si l'erreur était liée à cette opération, la réinitialiser\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression de toutes les notifications:', err);\n          // Même en cas d'erreur, conserver les IDs dans le localStorage\n          this.error = err;\n        }\n      });\n    });\n  }\n  getErrorMessage() {\n    return this.error?.message || 'Unknown error occurred';\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n      if (deletedIdsJson) {\n        return new Set(JSON.parse(deletedIdsJson));\n      }\n      return new Set();\n    } catch (error) {\n      console.error('Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  /**\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\n   * @param deletedIds Set contenant les IDs des notifications supprimées\n   */\n  saveDeletedNotificationIds(deletedIds) {\n    try {\n      localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n      console.log(`${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`);\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde des IDs de notifications supprimées:', error);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Sélectionne ou désélectionne une notification\n   * @param notificationId ID de la notification\n   * @param event Événement de la case à cocher\n   */\n  toggleSelection(notificationId, event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n    // Mettre à jour l'état de sélection globale\n    this.updateSelectionState();\n    // Afficher ou masquer la barre de sélection\n    this.showSelectionBar = this.selectedNotifications.size > 0;\n  }\n  /**\n   * Sélectionne ou désélectionne toutes les notifications\n   * @param event Événement de la case à cocher\n   */\n  toggleSelectAll(event) {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n    this.allSelected = !this.allSelected;\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      if (this.allSelected) {\n        // Sélectionner toutes les notifications\n        notifications.forEach(notification => {\n          this.selectedNotifications.add(notification.id);\n        });\n      } else {\n        // Désélectionner toutes les notifications\n        this.selectedNotifications.clear();\n      }\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    });\n  }\n  /**\n   * Met à jour l'état de sélection globale\n   */\n  updateSelectionState() {\n    this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n      this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n    });\n  }\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications() {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log('Suppression des notifications sélectionnées:', selectedIds);\n    // Supprimer localement les notifications sélectionnées\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.messageService.notifications.next(updatedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n      this.messageService.notificationCount.next(unreadCount);\n      // Mettre à jour le cache de notifications dans le service\n      this.updateNotificationCache(updatedNotifications);\n      // Réinitialiser la sélection\n      this.selectedNotifications.clear();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n    });\n    // Appeler le service pour supprimer les notifications sélectionnées\n    this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        console.log('Résultat de la suppression multiple:', result);\n        if (result && result.success) {\n          console.log(`${result.count} notifications supprimées avec succès`);\n        }\n      },\n      error: err => {\n        console.error('Erreur lors de la suppression multiple des notifications:', err);\n      }\n    });\n  }\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead() {\n    if (this.selectedNotifications.size === 0) {\n      return;\n    }\n    const selectedIds = Array.from(this.selectedNotifications);\n    console.log('Marquage des notifications sélectionnées comme lues:', selectedIds);\n    // Marquer localement les notifications sélectionnées comme lues\n    this.notifications$.pipe(take(1)).subscribe(notifications => {\n      const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n        ...notification,\n        isRead: true,\n        readAt: new Date().toISOString()\n      } : notification);\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.messageService.notifications.next(updatedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n      this.messageService.notificationCount.next(unreadCount);\n      // Mettre à jour le cache de notifications dans le service\n      this.updateNotificationCache(updatedNotifications);\n      // Réinitialiser la sélection\n      this.selectedNotifications.clear();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n    });\n    // Appeler le service pour marquer les notifications comme lues\n    this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n      next: result => {\n        console.log('Résultat du marquage comme lu:', result);\n        if (result && result.success) {\n          console.log('Notifications marquées comme lues avec succès');\n        }\n      },\n      error: err => {\n        console.error('Erreur lors du marquage des notifications comme lues:', err);\n      }\n    });\n  }\n  /**\n   * Vérifie si une notification est sélectionnée\n   * @param notificationId ID de la notification\n   * @returns true si la notification est sélectionnée, false sinon\n   */\n  isSelected(notificationId) {\n    return this.selectedNotifications.has(notificationId);\n  }\n  /**\n   * Convertit un type de pièce jointe de notification en type de message\n   * @param type Type de pièce jointe\n   * @returns Type de message correspondant\n   */\n  convertAttachmentType(type) {\n    switch (type) {\n      case 'IMAGE':\n        return MessageType.IMAGE;\n      case 'FILE':\n        return MessageType.FILE;\n      case 'AUDIO':\n        return MessageType.AUDIO;\n      case 'VIDEO':\n        return MessageType.VIDEO;\n      case 'image':\n        return MessageType.IMAGE_LOWER;\n      case 'file':\n        return MessageType.FILE_LOWER;\n      case 'audio':\n        return MessageType.AUDIO_LOWER;\n      case 'video':\n        return MessageType.VIDEO_LOWER;\n      default:\n        return MessageType.FILE;\n      // Type par défaut\n    }\n  }\n\n  static {\n    this.ɵfac = function NotificationListComponent_Factory(t) {\n      return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationListComponent,\n      selectors: [[\"app-notification-list\"]],\n      viewQuery: function NotificationListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n        }\n      },\n      hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n            return ctx.onScroll($event.target);\n          });\n        }\n      },\n      decls: 28,\n      vars: 19,\n      consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer comme lu\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"click\"], [1, \"fas\", \"fa-comments\"], [\"title\", \"Marquer comme lu\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"]],\n      template: function NotificationListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Notifications \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n          i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n          i0.ɵɵelement(20, \"i\", 16);\n          i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n            return ctx.closeAttachmentsModal();\n          });\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n          i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 13, ctx.isDarkMode$));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 15, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 17, ctx.hasNotifications()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe],\n      styles: [\"\\n\\n\\n.futuristic-notifications-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  min-height: 100vh;\\n  background: #f8fafc;\\n  color: #64748b;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\\n  background: #0f172a;\\n  color: #cbd5e1;\\n}\\n\\n.futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 1px 3px rgba(0,0,0,0.1);\\n  border: 1px solid #e2e8f0;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\\n  background: #1e293b;\\n  border-color: #334155;\\n}\\n\\n.futuristic-notifications-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\\n  border-color: #334155;\\n}\\n\\n.futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  color: #f9fafb;\\n}\\n\\n.futuristic-action-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: none;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%] {\\n  background: #374151;\\n  color: #d1d5db;\\n}\\n\\n.futuristic-primary-button[_ngcontent-%COMP%] {\\n  background: #3b82f6;\\n  color: white;\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.futuristic-primary-button[_ngcontent-%COMP%]:hover {\\n  background: #2563eb;\\n}\\n\\n.futuristic-danger-button[_ngcontent-%COMP%] {\\n  background: #ef4444;\\n  color: white;\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.futuristic-danger-button[_ngcontent-%COMP%]:hover {\\n  background: #dc2626;\\n}\\n\\n.futuristic-notifications-list[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n\\n.futuristic-notification-card[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  margin-bottom: 0.5rem;\\n  border-radius: 0.5rem;\\n  border: 1px solid #e2e8f0;\\n  background: white;\\n  transition: all 0.2s;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\\n  background: #334155;\\n  border-color: #475569;\\n}\\n\\n.futuristic-notification-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\\n  transform: translateY(-1px);\\n}\\n\\n.futuristic-notification-read[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 2rem;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border: 2px solid #e5e7eb;\\n  border-top: 2px solid #3b82f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: #6b7280;\\n  text-align: center;\\n}\\n\\n.futuristic-empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #64748b;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%] {\\n  color: #94a3b8;\\n}\\n\\n.futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #9ca3af;\\n  margin-bottom: 1rem;\\n}\\n\\n.futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\\n  color: #d1d5db;\\n}\\n\\n.futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  margin-bottom: 1rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%] {\\n  background: #10b981;\\n  color: white;\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.futuristic-check-button[_ngcontent-%COMP%]:hover {\\n  background: #059669;\\n}\\n\\n.futuristic-error-message[_ngcontent-%COMP%] {\\n  background: #fef2f2;\\n  border: 1px solid #fecaca;\\n  border-radius: 0.5rem;\\n  padding: 1rem;\\n  margin: 1rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%] {\\n  background: #7f1d1d;\\n  border-color: #991b1b;\\n}\\n\\n.futuristic-error-title[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%] {\\n  color: #fca5a5;\\n}\\n\\n.futuristic-error-text[_ngcontent-%COMP%] {\\n  color: #7f1d1d;\\n  font-size: 0.875rem;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-error-text[_ngcontent-%COMP%] {\\n  color: #fca5a5;\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%] {\\n  background: #dc2626;\\n  color: white;\\n  padding: 0.25rem 0.75rem;\\n  border: none;\\n  border-radius: 0.25rem;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  margin-top: 0.5rem;\\n}\\n\\n.futuristic-retry-button[_ngcontent-%COMP%]:hover {\\n  background: #b91c1c;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvbi1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaURBQWlEOztBQUVqRDtFQUNFLGFBQWE7RUFDYixpQkFBaUI7RUFDakIsbUJBQW1CO0VBQ25CLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixzQkFBc0I7RUFDdEIscUNBQXFDO0VBQ3JDLHlCQUF5QjtFQUN6QixpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGVBQWU7RUFDZixZQUFZO0VBQ1oscUJBQXFCO0VBQ3JCLGVBQWU7RUFDZixvQkFBb0I7RUFDcEIsbUJBQW1CO0VBQ25CLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osb0JBQW9CO0VBQ3BCLFlBQVk7RUFDWixxQkFBcUI7RUFDckIsZUFBZTtFQUNmLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osb0JBQW9CO0VBQ3BCLFlBQVk7RUFDWixxQkFBcUI7RUFDckIsZUFBZTtFQUNmLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixnQkFBZ0I7RUFDaEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHFCQUFxQjtFQUNyQixxQkFBcUI7RUFDckIseUJBQXlCO0VBQ3pCLGlCQUFpQjtFQUNqQixvQkFBb0I7RUFDcEIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UscUNBQXFDO0VBQ3JDLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLGFBQWE7QUFDZjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1oseUJBQXlCO0VBQ3pCLDZCQUE2QjtFQUM3QixrQkFBa0I7RUFDbEIsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0UsS0FBSyx1QkFBdUIsRUFBRTtFQUM5QixPQUFPLHlCQUF5QixFQUFFO0FBQ3BDOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsYUFBYTtFQUNiLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLGNBQWM7RUFDZCxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsWUFBWTtFQUNaLG9CQUFvQjtFQUNwQixZQUFZO0VBQ1oscUJBQXFCO0VBQ3JCLGVBQWU7RUFDZixvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIseUJBQXlCO0VBQ3pCLHFCQUFxQjtFQUNyQixhQUFhO0VBQ2IsWUFBWTtBQUNkOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxnQkFBZ0I7RUFDaEIscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWix3QkFBd0I7RUFDeEIsWUFBWTtFQUNaLHNCQUFzQjtFQUN0QixlQUFlO0VBQ2YsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQiIsImZpbGUiOiJub3RpZmljYXRpb24tbGlzdC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogQ1NTIG1pbmltYWwgZXQgcHJvcHJlIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgKi9cblxuLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1jb250YWluZXIge1xuICBwYWRkaW5nOiAxcmVtO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgYmFja2dyb3VuZDogI2Y4ZmFmYztcbiAgY29sb3I6ICM2NDc0OGI7XG59XG5cbi5kYXJrIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtY29udGFpbmVyIHtcbiAgYmFja2dyb3VuZDogIzBmMTcyYTtcbiAgY29sb3I6ICNjYmQ1ZTE7XG59XG5cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtY2FyZCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xuICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLDAsMCwwLjEpO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG59XG5cbi5kYXJrIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtY2FyZCB7XG4gIGJhY2tncm91bmQ6ICMxZTI5M2I7XG4gIGJvcmRlci1jb2xvcjogIzMzNDE1NTtcbn1cblxuLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDFyZW07XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4uZGFyayAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWhlYWRlciB7XG4gIGJvcmRlci1jb2xvcjogIzMzNDE1NTtcbn1cblxuLmZ1dHVyaXN0aWMtdGl0bGUge1xuICBmb250LXNpemU6IDEuMjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMWYyOTM3O1xufVxuXG4uZGFyayAuZnV0dXJpc3RpYy10aXRsZSB7XG4gIGNvbG9yOiAjZjlmYWZiO1xufVxuXG4uZnV0dXJpc3RpYy1hY3Rpb24tYnV0dG9uIHtcbiAgcGFkZGluZzogMC41cmVtO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycztcbiAgYmFja2dyb3VuZDogI2YzZjRmNjtcbiAgY29sb3I6ICMzNzQxNTE7XG59XG5cbi5kYXJrIC5mdXR1cmlzdGljLWFjdGlvbi1idXR0b24ge1xuICBiYWNrZ3JvdW5kOiAjMzc0MTUxO1xuICBjb2xvcjogI2QxZDVkYjtcbn1cblxuLmZ1dHVyaXN0aWMtcHJpbWFyeS1idXR0b24ge1xuICBiYWNrZ3JvdW5kOiAjM2I4MmY2O1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycztcbn1cblxuLmZ1dHVyaXN0aWMtcHJpbWFyeS1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjMjU2M2ViO1xufVxuXG4uZnV0dXJpc3RpYy1kYW5nZXItYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogI2VmNDQ0NDtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnM7XG59XG5cbi5mdXR1cmlzdGljLWRhbmdlci1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjZGMyNjI2O1xufVxuXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWxpc3Qge1xuICBwYWRkaW5nOiAxcmVtO1xuICBtYXgtaGVpZ2h0OiA3MHZoO1xuICBvdmVyZmxvdy15OiBhdXRvO1xufVxuXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZCB7XG4gIHBhZGRpbmc6IDFyZW07XG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnM7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMXJlbTtcbn1cblxuLmRhcmsgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLWNhcmQge1xuICBiYWNrZ3JvdW5kOiAjMzM0MTU1O1xuICBib3JkZXItY29sb3I6ICM0NzU1Njk7XG59XG5cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1jYXJkOmhvdmVyIHtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwwLDAsMC4xKTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xufVxuXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tcmVhZCB7XG4gIG9wYWNpdHk6IDAuNztcbn1cblxuLmZ1dHVyaXN0aWMtbG9hZGluZy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMnJlbTtcbn1cblxuLmZ1dHVyaXN0aWMtbG9hZGluZy1jaXJjbGUge1xuICB3aWR0aDogMnJlbTtcbiAgaGVpZ2h0OiAycmVtO1xuICBib3JkZXI6IDJweCBzb2xpZCAjZTVlN2ViO1xuICBib3JkZXItdG9wOiAycHggc29saWQgIzNiODJmNjtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xufVxuXG5Aa2V5ZnJhbWVzIHNwaW4ge1xuICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XG4gIDEwMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XG59XG5cbi5mdXR1cmlzdGljLWxvYWRpbmctdGV4dCB7XG4gIG1hcmdpbi10b3A6IDFyZW07XG4gIGNvbG9yOiAjNmI3MjgwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5mdXR1cmlzdGljLWVtcHR5LXN0YXRlIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nOiAycmVtO1xuICBjb2xvcjogIzY0NzQ4Yjtcbn1cblxuLmRhcmsgLmZ1dHVyaXN0aWMtZW1wdHktc3RhdGUge1xuICBjb2xvcjogIzk0YTNiODtcbn1cblxuLmZ1dHVyaXN0aWMtZW1wdHktaWNvbiB7XG4gIGZvbnQtc2l6ZTogM3JlbTtcbiAgY29sb3I6ICM5Y2EzYWY7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5cbi5mdXR1cmlzdGljLWVtcHR5LXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzM3NDE1MTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xufVxuXG4uZGFyayAuZnV0dXJpc3RpYy1lbXB0eS10aXRsZSB7XG4gIGNvbG9yOiAjZDFkNWRiO1xufVxuXG4uZnV0dXJpc3RpYy1lbXB0eS10ZXh0IHtcbiAgY29sb3I6ICM2YjcyODA7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5cbi5kYXJrIC5mdXR1cmlzdGljLWVtcHR5LXRleHQge1xuICBjb2xvcjogIzljYTNhZjtcbn1cblxuLmZ1dHVyaXN0aWMtY2hlY2stYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogIzEwYjk4MTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnM7XG59XG5cbi5mdXR1cmlzdGljLWNoZWNrLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICMwNTk2Njk7XG59XG5cbi5mdXR1cmlzdGljLWVycm9yLW1lc3NhZ2Uge1xuICBiYWNrZ3JvdW5kOiAjZmVmMmYyO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZmVjYWNhO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIHBhZGRpbmc6IDFyZW07XG4gIG1hcmdpbjogMXJlbTtcbn1cblxuLmRhcmsgLmZ1dHVyaXN0aWMtZXJyb3ItbWVzc2FnZSB7XG4gIGJhY2tncm91bmQ6ICM3ZjFkMWQ7XG4gIGJvcmRlci1jb2xvcjogIzk5MWIxYjtcbn1cblxuLmZ1dHVyaXN0aWMtZXJyb3ItdGl0bGUge1xuICBjb2xvcjogI2RjMjYyNjtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xufVxuXG4uZGFyayAuZnV0dXJpc3RpYy1lcnJvci10aXRsZSB7XG4gIGNvbG9yOiAjZmNhNWE1O1xufVxuXG4uZnV0dXJpc3RpYy1lcnJvci10ZXh0IHtcbiAgY29sb3I6ICM3ZjFkMWQ7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG59XG5cbi5kYXJrIC5mdXR1cmlzdGljLWVycm9yLXRleHQge1xuICBjb2xvcjogI2ZjYTVhNTtcbn1cblxuLmZ1dHVyaXN0aWMtcmV0cnktYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogI2RjMjYyNjtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XG4gIGJvcmRlcjogbm9uZTtcbiAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBtYXJnaW4tdG9wOiAwLjVyZW07XG59XG5cbi5mdXR1cmlzdGljLXJldHJ5LWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICNiOTFjMWM7XG59XG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "of", "BehaviorSubject", "MessageType", "catchError", "map", "takeUntil", "take", "debounceTime", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationListComponent_div_8_div_3_Template_input_click_2_listener", "$event", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r9", "allSelected", "NotificationListComponent_div_8_button_9_Template_button_click_0_listener", "_r15", "ctx_r14", "markAllAsRead", "ɵɵtext", "NotificationListComponent_div_8_button_11_Template_button_click_0_listener", "_r17", "ctx_r16", "deleteAllNotifications", "NotificationListComponent_div_8_Template_button_click_1_listener", "_r19", "ctx_r18", "loadNotifications", "ɵɵtemplate", "NotificationListComponent_div_8_div_3_Template", "NotificationListComponent_div_8_Template_button_click_5_listener", "ctx_r20", "toggleUn<PERSON><PERSON><PERSON>er", "NotificationListComponent_div_8_Template_button_click_7_listener", "ctx_r21", "toggleSound", "NotificationListComponent_div_8_button_9_Template", "NotificationListComponent_div_8_button_11_Template", "ɵɵpipeBind1", "ctx_r0", "hasNotifications", "ɵɵclassProp", "showOnlyUnread", "isSoundMuted", "ɵɵpropertyInterpolate", "unreadCount$", "NotificationListComponent_div_9_Template_button_click_3_listener", "_r23", "ctx_r22", "markSelectedAsRead", "NotificationListComponent_div_9_Template_button_click_6_listener", "ctx_r24", "deleteSelectedNotifications", "NotificationListComponent_div_9_Template_button_click_9_listener", "ctx_r25", "selectedNotifications", "clear", "showSelectionBar", "ɵɵtextInterpolate1", "ctx_r1", "size", "NotificationListComponent_div_11_Template_button_click_8_listener", "_r27", "ctx_r26", "ɵɵtextInterpolate", "ctx_r3", "getErrorMessage", "NotificationListComponent_div_12_Template_button_click_7_listener", "_r29", "ctx_r28", "notification_r33", "message", "content", "attachments", "length", "NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener", "_r44", "$implicit", "ctx_r42", "getNotificationAttachments", "id", "stopPropagation", "NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener", "_r47", "ctx_r45", "joinConversation", "NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener", "_r50", "ctx_r48", "mark<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener", "restoredCtx", "_r52", "ctx_r51", "toggleSelection", "NotificationListComponent_div_14_ng_container_2_div_20_Template", "NotificationListComponent_div_14_ng_container_2_div_21_Template", "NotificationListComponent_div_14_ng_container_2_div_22_Template", "NotificationListComponent_div_14_ng_container_2_button_24_Template", "NotificationListComponent_div_14_ng_container_2_button_25_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener", "ctx_r53", "openNotificationDetails", "NotificationListComponent_div_14_ng_container_2_button_28_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener", "ctx_r54", "deleteNotification", "ɵɵelementContainerEnd", "isRead", "ctx_r31", "isSelected", "senderId", "image", "ɵɵsanitizeUrl", "username", "ɵɵpipeBind2", "timestamp", "type", "NotificationListComponent_div_14_Template_div_scroll_0_listener", "_r56", "_r30", "ɵɵreference", "ctx_r55", "onScroll", "NotificationListComponent_div_14_ng_container_2_Template", "NotificationListComponent_div_14_div_4_Template", "ctx_r5", "filteredNotifications$", "loadingMore", "NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener", "_r64", "attachment_r58", "ctx_r62", "openAttachment", "url", "ɵɵclassMap", "ctx_r60", "getFileIcon", "ctx_r61", "formatFileSize", "NotificationListComponent_div_27_div_1_div_1_Template", "NotificationListComponent_div_27_div_1_div_2_Template", "NotificationListComponent_div_27_div_1_span_9_Template", "NotificationListComponent_div_27_div_1_Template_button_click_11_listener", "_r69", "ctx_r68", "NotificationListComponent_div_27_div_1_Template_button_click_13_listener", "ctx_r70", "downloadAttachment", "ctx_r57", "isImage", "name", "getFileTypeLabel", "NotificationListComponent_div_27_div_1_Template", "ctx_r8", "currentAttachments", "NotificationListComponent", "constructor", "messageService", "themeService", "router", "loading", "hasMoreNotifications", "error", "Set", "showAttachmentsModal", "loadingAttachments", "showNotificationDetailsModal", "currentNotification", "destroy$", "scrollPosition$", "notifications$", "notificationCount$", "isDarkMode$", "darkMode$", "isMuted", "notification", "console", "log", "conversationId", "metadata", "relatedEntity", "includes", "groupId", "window", "location", "href", "getOrCreateConversation", "subscribe", "next", "conversation", "target", "scrollPosition", "scrollTop", "scrollHeight", "clientHeight", "ngOnInit", "savedMutePreference", "localStorage", "getItem", "setMuted", "setupSubscriptions", "setupInfiniteScroll", "filterDeletedNotifications", "deletedNotificationIds", "getDeletedNotificationIds", "pipe", "notifications", "filteredNotifications", "has", "unreadCount", "n", "notificationCount", "updateNotificationCache", "loadMoreNotifications", "getNotifications", "err", "existingNotifications", "allNotifications", "subscribeToNewNotifications", "subscribeToNotificationsRead", "notificationId", "Error", "find", "updatedNotifications", "readAt", "Date", "toISOString", "result", "success", "stack", "warn", "notificationCache", "for<PERSON>ach", "set", "unreadIds", "validIds", "trim", "hasUnreadNotifications", "count", "getUnreadNotifications", "setTimeout", "playNotificationSound", "setItem", "toString", "attachment", "convertAttachmentType", "duration", "closeAttachmentsModal", "closeNotificationDetailsModal", "startsWith", "units", "i", "formattedSize", "toFixed", "open", "link", "document", "createElement", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "acceptFriendRequest", "add", "saveDeletedNotificationIds", "deletedIdsJson", "JSON", "parse", "deletedIds", "stringify", "Array", "from", "ngOnDestroy", "complete", "event", "delete", "updateSelectionState", "selectedIds", "deleteMultipleNotifications", "IMAGE", "FILE", "AUDIO", "VIDEO", "IMAGE_LOWER", "FILE_LOWER", "AUDIO_LOWER", "VIDEO_LOWER", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ThemeService", "i3", "Router", "selectors", "viewQuery", "NotificationListComponent_Query", "rf", "ctx", "NotificationListComponent_div_8_Template", "NotificationListComponent_div_9_Template", "NotificationListComponent_div_10_Template", "NotificationListComponent_div_11_Template", "NotificationListComponent_div_12_Template", "NotificationListComponent_div_14_Template", "NotificationListComponent_Template_div_click_16_listener", "NotificationListComponent_Template_div_click_17_listener", "NotificationListComponent_Template_button_click_22_listener", "NotificationListComponent_div_25_Template", "NotificationListComponent_div_26_Template", "NotificationListComponent_div_27_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.ts", "C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notification-list\\notification-list.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnIni<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HostL<PERSON>ener,\r\n  ElementRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/services/message.service';\r\nimport { Observable, Subject, of, throwError, BehaviorSubject } from 'rxjs';\r\nimport {\r\n  Notification,\r\n  Attachment,\r\n  NotificationAttachment,\r\n  AttachmentType,\r\n  MessageType,\r\n} from 'src/app/models/message.model';\r\nimport {\r\n  catchError,\r\n  map,\r\n  takeUntil,\r\n  switchMap,\r\n  take,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  filter,\r\n} from 'rxjs/operators';\r\nimport { ThemeService } from '@app/services/theme.service';\r\n@Component({\r\n  selector: 'app-notification-list',\r\n  templateUrl: './notification-list.component.html',\r\n  styleUrls: ['./notification-list.component.css'],\r\n})\r\nexport class NotificationListComponent implements OnInit, OnDestroy {\r\n  @ViewChild('notificationContainer', { static: false })\r\n  notificationContainer!: ElementRef;\r\n\r\n  notifications$: Observable<Notification[]>;\r\n  filteredNotifications$: Observable<Notification[]>;\r\n  unreadCount$: Observable<number>;\r\n  isDarkMode$: Observable<boolean>;\r\n  loading = true;\r\n  loadingMore = false;\r\n  hasMoreNotifications = true;\r\n  error: Error | null = null;\r\n  showOnlyUnread = false;\r\n  isSoundMuted = false;\r\n\r\n  // Propriétés pour la sélection multiple\r\n  selectedNotifications: Set<string> = new Set<string>();\r\n  allSelected = false;\r\n  showSelectionBar = false;\r\n\r\n  // Propriétés pour le modal des pièces jointes\r\n  showAttachmentsModal = false;\r\n  loadingAttachments = false;\r\n  currentAttachments: Attachment[] = [];\r\n\r\n  // Propriétés pour le modal des détails de notification\r\n  showNotificationDetailsModal = false;\r\n  currentNotification: Notification | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  private scrollPosition$ = new BehaviorSubject<number>(0);\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private themeService: ThemeService,\r\n    private router: Router\r\n  ) {\r\n    this.notifications$ = this.messageService.notifications$;\r\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\r\n    this.unreadCount$ = this.messageService.notificationCount$;\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n\r\n    // Vérifier l'état du son\r\n    this.isSoundMuted = this.messageService.isMuted();\r\n  }\r\n\r\n  /**\r\n   * Rejoint une conversation ou un groupe à partir d'une notification\r\n   * @param notification Notification contenant les informations de la conversation ou du groupe\r\n   */\r\n  joinConversation(notification: Notification): void {\r\n    console.log('Rejoindre la conversation:', notification);\r\n\r\n    // Marquer la notification comme lue\r\n    this.markAsRead(notification.id);\r\n\r\n    // Extraire les informations pertinentes de la notification\r\n    const conversationId =\r\n      notification.conversationId ||\r\n      (notification.metadata && notification.metadata['conversationId']) ||\r\n      (notification.relatedEntity &&\r\n      notification.relatedEntity.includes('conversation')\r\n        ? notification.relatedEntity\r\n        : null);\r\n\r\n    const groupId =\r\n      notification.groupId ||\r\n      (notification.metadata && notification.metadata['groupId']) ||\r\n      (notification.relatedEntity &&\r\n      notification.relatedEntity.includes('group')\r\n        ? notification.relatedEntity\r\n        : null);\r\n\r\n    // Déterminer où rediriger l'utilisateur\r\n    if (conversationId) {\r\n      // Rediriger vers la conversation existante\r\n      console.log(\r\n        'Redirection vers la conversation existante:',\r\n        conversationId\r\n      );\r\n      // Utiliser le format exact de l'URL fournie avec l'ID\r\n      window.location.href = `/messages/conversations/chat/${conversationId}`;\r\n    } else if (groupId) {\r\n      // Rediriger vers le groupe\r\n      console.log('Redirection vers le groupe:', groupId);\r\n      window.location.href = `/messages/group/${groupId}`;\r\n    } else if (notification.senderId && notification.senderId.id) {\r\n      // Si aucun ID de conversation n'est trouvé, mais qu'il y a un expéditeur,\r\n      // utiliser getOrCreateConversation pour obtenir ou créer une conversation\r\n      console.log(\r\n        \"Création/récupération d'une conversation avec l'utilisateur:\",\r\n        notification.senderId.id\r\n      );\r\n\r\n      // Afficher un indicateur de chargement si nécessaire\r\n      // this.loading = true;\r\n\r\n      this.messageService\r\n        .getOrCreateConversation(notification.senderId.id)\r\n        .subscribe({\r\n          next: (conversation) => {\r\n            console.log('Conversation obtenue:', conversation);\r\n            // this.loading = false;\r\n\r\n            if (conversation && conversation.id) {\r\n              // Rediriger vers la conversation nouvellement créée ou récupérée\r\n              // Utiliser le format exact de l'URL fournie avec l'ID\r\n              window.location.href = `/messages/conversations/chat/${conversation.id}`;\r\n            } else {\r\n              console.error('Conversation invalide reçue:', conversation);\r\n              window.location.href = '/messages';\r\n            }\r\n          },\r\n          error: (error) => {\r\n            console.error(\r\n              'Erreur lors de la création/récupération de la conversation:',\r\n              error\r\n            );\r\n            // this.loading = false;\r\n\r\n            // En cas d'erreur, rediriger vers la liste des messages\r\n            window.location.href = '/messages';\r\n          },\r\n        });\r\n    } else {\r\n      // Si aucune information n'est trouvée, rediriger vers la liste des messages\r\n      console.log('Redirection vers la liste des messages');\r\n      window.location.href = '/messages';\r\n    }\r\n  }\r\n\r\n  @HostListener('scroll', ['$event.target'])\r\n  onScroll(target: HTMLElement): void {\r\n    if (!target) return;\r\n\r\n    const scrollPosition = target.scrollTop;\r\n    const scrollHeight = target.scrollHeight;\r\n    const clientHeight = target.clientHeight;\r\n\r\n    // Si on est proche du bas (à 200px du bas)\r\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\r\n      this.scrollPosition$.next(scrollPosition);\r\n    }\r\n  }\r\n  ngOnInit(): void {\r\n    // Charger la préférence de son depuis le localStorage\r\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\r\n    if (savedMutePreference !== null) {\r\n      this.isSoundMuted = savedMutePreference === 'true';\r\n      console.log(\r\n        `Préférence de son chargée: ${\r\n          this.isSoundMuted ? 'désactivé' : 'activé'\r\n        }`\r\n      );\r\n      this.messageService.setMuted(this.isSoundMuted);\r\n    }\r\n\r\n    this.loadNotifications();\r\n    this.setupSubscriptions();\r\n    this.setupInfiniteScroll();\r\n    this.filterDeletedNotifications();\r\n  }\r\n\r\n  /**\r\n   * Filtre les notifications supprimées lors du chargement initial\r\n   */\r\n  private filterDeletedNotifications(): void {\r\n    // Récupérer les IDs des notifications supprimées du localStorage\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n    if (deletedNotificationIds.size > 0) {\r\n      console.log(\r\n        `Filtrage de ${deletedNotificationIds.size} notifications supprimées`\r\n      );\r\n\r\n      // Filtrer les notifications pour exclure celles qui ont été supprimées\r\n      this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n        const filteredNotifications = notifications.filter(\r\n          (notification) => !deletedNotificationIds.has(notification.id)\r\n        );\r\n\r\n        // Mettre à jour l'interface utilisateur\r\n        (this.messageService as any).notifications.next(filteredNotifications);\r\n\r\n        // Mettre à jour le compteur de notifications non lues\r\n        const unreadCount = filteredNotifications.filter(\r\n          (n) => !n.isRead\r\n        ).length;\r\n        (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n        // Mettre à jour le cache de notifications dans le service\r\n        this.updateNotificationCache(filteredNotifications);\r\n      });\r\n    }\r\n  }\r\n\r\n  setupInfiniteScroll(): void {\r\n    // Configurer le chargement des anciennes notifications lors du défilement\r\n    this.scrollPosition$\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        debounceTime(200), // Attendre 200ms après le dernier événement de défilement\r\n        distinctUntilChanged(), // Ne déclencher que si la position de défilement a changé\r\n        filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\r\n      )\r\n      .subscribe(() => {\r\n        this.loadMoreNotifications();\r\n      });\r\n  }\r\n  loadNotifications(): void {\r\n    console.log('NotificationListComponent: Loading notifications');\r\n    this.loading = true;\r\n    this.loadingMore = false;\r\n    this.error = null;\r\n    this.hasMoreNotifications = true;\r\n\r\n    // Récupérer les IDs des notifications supprimées du localStorage\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n    console.log(\r\n      `${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`\r\n    );\r\n\r\n    this.messageService\r\n      .getNotifications(true)\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map((notifications) => {\r\n          // Filtrer les notifications supprimées\r\n          if (deletedNotificationIds.size > 0) {\r\n            return notifications.filter(\r\n              (notification) => !deletedNotificationIds.has(notification.id)\r\n            );\r\n          }\r\n          return notifications;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (notifications) => {\r\n          console.log(\r\n            'NotificationListComponent: Notifications loaded successfully',\r\n            notifications\r\n          );\r\n\r\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\r\n          (this.messageService as any).notifications.next(notifications);\r\n\r\n          // Mettre à jour le compteur de notifications non lues\r\n          const unreadCount = notifications.filter((n) => !n.isRead).length;\r\n          (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n          this.loading = false;\r\n          this.hasMoreNotifications =\r\n            this.messageService.hasMoreNotifications();\r\n        },\r\n        error: (err: Error) => {\r\n          console.error(\r\n            'NotificationListComponent: Error loading notifications',\r\n            err\r\n          );\r\n          this.error = err;\r\n          this.loading = false;\r\n          this.hasMoreNotifications = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  loadMoreNotifications(): void {\r\n    if (this.loadingMore || !this.hasMoreNotifications) return;\r\n\r\n    console.log('NotificationListComponent: Loading more notifications');\r\n    this.loadingMore = true;\r\n\r\n    // Récupérer les IDs des notifications supprimées du localStorage\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n    console.log(\r\n      `${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`\r\n    );\r\n\r\n    this.messageService\r\n      .loadMoreNotifications()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map((notifications) => {\r\n          // Filtrer les notifications supprimées\r\n          if (deletedNotificationIds.size > 0) {\r\n            const filteredNotifications = notifications.filter(\r\n              (notification) => !deletedNotificationIds.has(notification.id)\r\n            );\r\n            console.log(\r\n              `Filtré ${\r\n                notifications.length - filteredNotifications.length\r\n              } notifications supprimées`\r\n            );\r\n            return filteredNotifications;\r\n          }\r\n          return notifications;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (notifications) => {\r\n          console.log(\r\n            'NotificationListComponent: More notifications loaded successfully',\r\n            notifications\r\n          );\r\n\r\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\r\n          this.notifications$\r\n            .pipe(take(1))\r\n            .subscribe((existingNotifications) => {\r\n              const allNotifications = [\r\n                ...existingNotifications,\r\n                ...notifications,\r\n              ];\r\n              (this.messageService as any).notifications.next(allNotifications);\r\n\r\n              // Mettre à jour le compteur de notifications non lues\r\n              const unreadCount = allNotifications.filter(\r\n                (n) => !n.isRead\r\n              ).length;\r\n              (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n              // Mettre à jour le cache de notifications dans le service\r\n              this.updateNotificationCache(allNotifications);\r\n            });\r\n\r\n          this.loadingMore = false;\r\n          this.hasMoreNotifications =\r\n            this.messageService.hasMoreNotifications();\r\n        },\r\n        error: (err: Error) => {\r\n          console.error(\r\n            'NotificationListComponent: Error loading more notifications',\r\n            err\r\n          );\r\n          this.loadingMore = false;\r\n          this.hasMoreNotifications = false;\r\n        },\r\n      });\r\n  }\r\n  setupSubscriptions(): void {\r\n    this.messageService\r\n      .subscribeToNewNotifications()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        catchError((error) => {\r\n          console.error('Notification stream error:', error);\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.messageService\r\n      .subscribeToNotificationsRead()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        catchError((error) => {\r\n          console.error('Notifications read stream error:', error);\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe();\r\n  }\r\n  markAsRead(notificationId: string): void {\r\n    console.log('Marking notification as read:', notificationId);\r\n\r\n    if (!notificationId) {\r\n      console.error('Invalid notification ID:', notificationId);\r\n      this.error = new Error('Invalid notification ID');\r\n      return;\r\n    }\r\n\r\n    // Afficher des informations de débogage sur la notification\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const notification = notifications.find((n) => n.id === notificationId);\r\n      if (notification) {\r\n        console.log('Found notification to mark as read:', {\r\n          id: notification.id,\r\n          type: notification.type,\r\n          isRead: notification.isRead,\r\n        });\r\n\r\n        // Mettre à jour localement la notification\r\n        const updatedNotifications = notifications.map((n) =>\r\n          n.id === notificationId\r\n            ? { ...n, isRead: true, readAt: new Date().toISOString() }\r\n            : n\r\n        );\r\n\r\n        // Mettre à jour l'interface utilisateur immédiatement\r\n        (this.messageService as any).notifications.next(updatedNotifications);\r\n\r\n        // Mettre à jour le compteur de notifications non lues\r\n        const unreadCount = updatedNotifications.filter(\r\n          (n) => !n.isRead\r\n        ).length;\r\n        (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n        // Mettre à jour le cache de notifications dans le service\r\n        this.updateNotificationCache(updatedNotifications);\r\n\r\n        // Appeler le service pour marquer la notification comme lue\r\n        this.messageService\r\n          .markAsRead([notificationId])\r\n          .pipe(takeUntil(this.destroy$))\r\n          .subscribe({\r\n            next: (result) => {\r\n              console.log('Mark as read result:', result);\r\n              if (result && result.success) {\r\n                console.log('Notification marked as read successfully');\r\n                // Si l'erreur était liée à cette opération, la réinitialiser\r\n                if (this.error && this.error.message.includes('mark')) {\r\n                  this.error = null;\r\n                }\r\n              }\r\n            },\r\n            error: (err) => {\r\n              console.error('Error marking notification as read:', err);\r\n              console.error('Error details:', {\r\n                message: err.message,\r\n                stack: err.stack,\r\n                notificationId,\r\n              });\r\n              // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\r\n              // this.error = err;\r\n            },\r\n          });\r\n      } else {\r\n        console.warn('Notification not found in local cache:', notificationId);\r\n\r\n        // Forcer le rechargement des notifications\r\n        this.loadNotifications();\r\n      }\r\n    });\r\n  }\r\n\r\n  // Méthode pour mettre à jour le cache de notifications dans le service\r\n  private updateNotificationCache(notifications: any[]): void {\r\n    // Mettre à jour le cache de notifications dans le service\r\n    const notificationCache = (this.messageService as any).notificationCache;\r\n    if (notificationCache) {\r\n      notifications.forEach((notification) => {\r\n        notificationCache.set(notification.id, notification);\r\n      });\r\n\r\n      // Forcer la mise à jour du compteur\r\n      const unreadCount = notifications.filter((n) => !n.isRead).length;\r\n      (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n      console.log('Notification cache updated, new unread count:', unreadCount);\r\n    }\r\n  }\r\n\r\n  markAllAsRead(): void {\r\n    console.log('Marking all notifications as read');\r\n\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      console.log('All notifications:', notifications);\r\n      const unreadIds = notifications.filter((n) => !n.isRead).map((n) => n.id);\r\n\r\n      console.log('Unread notification IDs to mark as read:', unreadIds);\r\n\r\n      if (unreadIds.length === 0) {\r\n        console.log('No unread notifications to mark as read');\r\n        return;\r\n      }\r\n\r\n      // Vérifier que tous les IDs sont valides\r\n      const validIds = unreadIds.filter(\r\n        (id) => id && typeof id === 'string' && id.trim() !== ''\r\n      );\r\n\r\n      if (validIds.length !== unreadIds.length) {\r\n        console.error('Some notification IDs are invalid:', unreadIds);\r\n        this.error = new Error('Invalid notification IDs');\r\n        return;\r\n      }\r\n\r\n      console.log('Marking all notifications as read:', validIds);\r\n\r\n      // Mettre à jour localement toutes les notifications\r\n      const updatedNotifications = notifications.map((n) =>\r\n        validIds.includes(n.id)\r\n          ? { ...n, isRead: true, readAt: new Date().toISOString() }\r\n          : n\r\n      );\r\n\r\n      // Mettre à jour l'interface utilisateur immédiatement\r\n      (this.messageService as any).notifications.next(updatedNotifications);\r\n\r\n      // Mettre à jour le compteur de notifications non lues\r\n      const unreadCount = updatedNotifications.filter((n) => !n.isRead).length;\r\n      (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n      // Mettre à jour le cache de notifications dans le service\r\n      this.updateNotificationCache(updatedNotifications);\r\n\r\n      // Appeler le service pour marquer toutes les notifications comme lues\r\n      this.messageService\r\n        .markAsRead(validIds)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (result) => {\r\n            console.log('Mark all as read result:', result);\r\n            if (result && result.success) {\r\n              console.log('All notifications marked as read successfully');\r\n              // Si l'erreur était liée à cette opération, la réinitialiser\r\n              if (this.error && this.error.message.includes('mark')) {\r\n                this.error = null;\r\n              }\r\n            }\r\n          },\r\n          error: (err) => {\r\n            console.error('Error marking all notifications as read:', err);\r\n            console.error('Error details:', {\r\n              message: err.message,\r\n              stack: err.stack,\r\n            });\r\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\r\n            // this.error = err;\r\n          },\r\n        });\r\n    });\r\n  }\r\n\r\n  hasNotifications(): Observable<boolean> {\r\n    return this.notifications$.pipe(\r\n      map((notifications) => notifications?.length > 0)\r\n    );\r\n  }\r\n  hasUnreadNotifications(): Observable<boolean> {\r\n    return this.unreadCount$.pipe(map((count) => count > 0));\r\n  }\r\n\r\n  /**\r\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\r\n   */\r\n  toggleUnreadFilter(): void {\r\n    this.showOnlyUnread = !this.showOnlyUnread;\r\n    console.log(\r\n      `Filtre des notifications non lues ${\r\n        this.showOnlyUnread ? 'activé' : 'désactivé'\r\n      }`\r\n    );\r\n\r\n    if (this.showOnlyUnread) {\r\n      // Utiliser la méthode du service pour obtenir uniquement les notifications non lues\r\n      this.filteredNotifications$ =\r\n        this.messageService.getUnreadNotifications();\r\n    } else {\r\n      // Afficher toutes les notifications\r\n      this.filteredNotifications$ = this.notifications$;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Active/désactive le son des notifications\r\n   */\r\n  toggleSound(): void {\r\n    this.isSoundMuted = !this.isSoundMuted;\r\n    console.log(\r\n      `Son des notifications ${this.isSoundMuted ? 'désactivé' : 'activé'}`\r\n    );\r\n\r\n    // Utiliser la méthode du service pour activer/désactiver le son\r\n    this.messageService.setMuted(this.isSoundMuted);\r\n\r\n    // Tester le son si activé\r\n    if (!this.isSoundMuted) {\r\n      console.log('Test du son de notification...');\r\n\r\n      // Jouer le son après un court délai pour s'assurer que le navigateur est prêt\r\n      setTimeout(() => {\r\n        // Jouer le son deux fois pour s'assurer qu'il est audible\r\n        this.messageService.playNotificationSound();\r\n\r\n        // Jouer une deuxième fois après 1 seconde\r\n        setTimeout(() => {\r\n          this.messageService.playNotificationSound();\r\n        }, 1000);\r\n      }, 100);\r\n    }\r\n\r\n    // Sauvegarder la préférence dans le localStorage\r\n    localStorage.setItem(\r\n      'notificationSoundMuted',\r\n      this.isSoundMuted.toString()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupère les pièces jointes d'une notification et ouvre le modal\r\n   * @param notificationId ID de la notification\r\n   */\r\n  getNotificationAttachments(notificationId: string): void {\r\n    if (!notificationId) {\r\n      console.error('ID de notification invalide');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      `Récupération des pièces jointes pour la notification ${notificationId}`\r\n    );\r\n\r\n    // Réinitialiser les pièces jointes et afficher le modal\r\n    this.currentAttachments = [];\r\n    this.loadingAttachments = true;\r\n    this.showAttachmentsModal = true;\r\n\r\n    // Vérifier d'abord si la notification existe dans le cache local\r\n    let notification: Notification | undefined;\r\n\r\n    // Utiliser pipe(take(1)) pour obtenir la valeur actuelle de l'Observable\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      notification = notifications.find(\r\n        (n: Notification) => n.id === notificationId\r\n      );\r\n    });\r\n\r\n    if (\r\n      notification &&\r\n      notification.message &&\r\n      notification.message.attachments &&\r\n      notification.message.attachments.length > 0\r\n    ) {\r\n      console.log(\r\n        'Pièces jointes trouvées dans le cache local:',\r\n        notification.message.attachments\r\n      );\r\n      this.loadingAttachments = false;\r\n      // Conversion des pièces jointes au format attendu\r\n      this.currentAttachments = notification.message.attachments.map(\r\n        (attachment: NotificationAttachment) =>\r\n          ({\r\n            id: '', // NotificationAttachment n'a pas d'ID\r\n            url: attachment.url || '',\r\n            type: this.convertAttachmentType(attachment.type),\r\n            name: attachment.name || '',\r\n            size: attachment.size || 0,\r\n            duration: 0, // NotificationAttachment n'a pas de durée\r\n          } as Attachment)\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Si aucune pièce jointe n'est trouvée localement, essayer de les récupérer du serveur\r\n    this.messageService\r\n      .getNotificationAttachments(notificationId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (attachments) => {\r\n          console.log(\r\n            `${attachments.length} pièces jointes récupérées du serveur`,\r\n            attachments\r\n          );\r\n          this.loadingAttachments = false;\r\n          this.currentAttachments = attachments;\r\n        },\r\n        error: (err) => {\r\n          console.error(\r\n            'Erreur lors de la récupération des pièces jointes',\r\n            err\r\n          );\r\n          this.loadingAttachments = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Ferme le modal des pièces jointes\r\n   */\r\n  closeAttachmentsModal(): void {\r\n    this.showAttachmentsModal = false;\r\n  }\r\n\r\n  /**\r\n   * Ouvre le modal des détails de notification\r\n   * @param notification Notification à afficher\r\n   */\r\n  openNotificationDetails(notification: Notification): void {\r\n    console.log('Ouverture des détails de la notification:', notification);\r\n    this.currentNotification = notification;\r\n    this.showNotificationDetailsModal = true;\r\n\r\n    // Marquer la notification comme lue\r\n    if (!notification.isRead) {\r\n      this.markAsRead(notification.id);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Ferme le modal des détails de notification\r\n   */\r\n  closeNotificationDetailsModal(): void {\r\n    this.showNotificationDetailsModal = false;\r\n    this.currentNotification = null;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si le type de fichier est une image\r\n   * @param type Type MIME du fichier\r\n   * @returns true si c'est une image, false sinon\r\n   */\r\n  isImage(type: string): boolean {\r\n    return type?.startsWith('image/') || false;\r\n  }\r\n\r\n  /**\r\n   * Obtient l'icône FontAwesome correspondant au type de fichier\r\n   * @param type Type MIME du fichier\r\n   * @returns Classe CSS de l'icône\r\n   */\r\n  getFileIcon(type: string): string {\r\n    if (!type) return 'fas fa-file';\r\n\r\n    if (type.startsWith('image/')) return 'fas fa-file-image';\r\n    if (type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\r\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (type.includes('word') || type.includes('document'))\r\n      return 'fas fa-file-word';\r\n    if (type.includes('excel') || type.includes('sheet'))\r\n      return 'fas fa-file-excel';\r\n    if (type.includes('powerpoint') || type.includes('presentation'))\r\n      return 'fas fa-file-powerpoint';\r\n    if (type.includes('zip') || type.includes('compressed'))\r\n      return 'fas fa-file-archive';\r\n\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  /**\r\n   * Obtient le libellé du type de fichier\r\n   * @param type Type MIME du fichier\r\n   * @returns Libellé du type de fichier\r\n   */\r\n  getFileTypeLabel(type: string): string {\r\n    if (!type) return 'Fichier';\r\n\r\n    if (type.startsWith('image/')) return 'Image';\r\n    if (type.startsWith('video/')) return 'Vidéo';\r\n    if (type.startsWith('audio/')) return 'Audio';\r\n    if (type.startsWith('text/')) return 'Texte';\r\n    if (type.includes('pdf')) return 'PDF';\r\n    if (type.includes('word') || type.includes('document')) return 'Document';\r\n    if (type.includes('excel') || type.includes('sheet'))\r\n      return 'Feuille de calcul';\r\n    if (type.includes('powerpoint') || type.includes('presentation'))\r\n      return 'Présentation';\r\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\r\n\r\n    return 'Fichier';\r\n  }\r\n\r\n  /**\r\n   * Formate la taille du fichier en unités lisibles\r\n   * @param size Taille en octets\r\n   * @returns Taille formatée (ex: \"1.5 MB\")\r\n   */\r\n  formatFileSize(size: number): string {\r\n    if (!size) return '';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n    let i = 0;\r\n    let formattedSize = size;\r\n\r\n    while (formattedSize >= 1024 && i < units.length - 1) {\r\n      formattedSize /= 1024;\r\n      i++;\r\n    }\r\n\r\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\r\n  }\r\n\r\n  /**\r\n   * Ouvre une pièce jointe dans un nouvel onglet\r\n   * @param url URL de la pièce jointe\r\n   */\r\n  openAttachment(url: string): void {\r\n    if (!url) return;\r\n    window.open(url, '_blank');\r\n  }\r\n\r\n  /**\r\n   * Télécharge une pièce jointe\r\n   * @param attachment Pièce jointe à télécharger\r\n   */\r\n  downloadAttachment(attachment: Attachment): void {\r\n    if (!attachment?.url) return;\r\n\r\n    const link = document.createElement('a');\r\n    link.href = attachment.url;\r\n    link.download = attachment.name || 'attachment';\r\n    link.target = '_blank';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  acceptFriendRequest(notification: Notification): void {\r\n    this.markAsRead(notification.id);\r\n  }\r\n\r\n  /**\r\n   * Supprime une notification et la stocke dans le localStorage\r\n   * @param notificationId ID de la notification à supprimer\r\n   */\r\n  deleteNotification(notificationId: string): void {\r\n    console.log('Suppression de la notification:', notificationId);\r\n\r\n    if (!notificationId) {\r\n      console.error('ID de notification invalide');\r\n      this.error = new Error('ID de notification invalide');\r\n      return;\r\n    }\r\n\r\n    // Récupérer les IDs des notifications supprimées du localStorage\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n    // Ajouter l'ID de la notification à supprimer\r\n    deletedNotificationIds.add(notificationId);\r\n\r\n    // Sauvegarder les IDs dans le localStorage\r\n    this.saveDeletedNotificationIds(deletedNotificationIds);\r\n\r\n    // Appeler le service pour supprimer la notification\r\n    this.messageService\r\n      .deleteNotification(notificationId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          console.log('Résultat de la suppression:', result);\r\n          if (result && result.success) {\r\n            console.log('Notification supprimée avec succès');\r\n            // Si l'erreur était liée à cette opération, la réinitialiser\r\n            if (this.error && this.error.message.includes('suppression')) {\r\n              this.error = null;\r\n            }\r\n          }\r\n        },\r\n        error: (err) => {\r\n          console.error(\r\n            'Erreur lors de la suppression de la notification:',\r\n            err\r\n          );\r\n          // Même en cas d'erreur, conserver l'ID dans le localStorage\r\n          this.error = err;\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Supprime toutes les notifications et les stocke dans le localStorage\r\n   */\r\n  deleteAllNotifications(): void {\r\n    console.log('Suppression de toutes les notifications');\r\n\r\n    // Récupérer toutes les notifications actuelles\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      // Récupérer les IDs des notifications supprimées du localStorage\r\n      const deletedNotificationIds = this.getDeletedNotificationIds();\r\n\r\n      // Ajouter tous les IDs des notifications actuelles\r\n      notifications.forEach((notification) => {\r\n        deletedNotificationIds.add(notification.id);\r\n      });\r\n\r\n      // Sauvegarder les IDs dans le localStorage\r\n      this.saveDeletedNotificationIds(deletedNotificationIds);\r\n\r\n      // Appeler le service pour supprimer toutes les notifications\r\n      this.messageService\r\n        .deleteAllNotifications()\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (result) => {\r\n            console.log(\r\n              'Résultat de la suppression de toutes les notifications:',\r\n              result\r\n            );\r\n            if (result && result.success) {\r\n              console.log(\r\n                `${result.count} notifications supprimées avec succès`\r\n              );\r\n              // Si l'erreur était liée à cette opération, la réinitialiser\r\n              if (this.error && this.error.message.includes('suppression')) {\r\n                this.error = null;\r\n              }\r\n            }\r\n          },\r\n          error: (err) => {\r\n            console.error(\r\n              'Erreur lors de la suppression de toutes les notifications:',\r\n              err\r\n            );\r\n            // Même en cas d'erreur, conserver les IDs dans le localStorage\r\n            this.error = err;\r\n          },\r\n        });\r\n    });\r\n  }\r\n\r\n  getErrorMessage(): string {\r\n    return this.error?.message || 'Unknown error occurred';\r\n  }\r\n\r\n  /**\r\n   * Récupère les IDs des notifications supprimées du localStorage\r\n   * @returns Set contenant les IDs des notifications supprimées\r\n   */\r\n  private getDeletedNotificationIds(): Set<string> {\r\n    try {\r\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\r\n      if (deletedIdsJson) {\r\n        return new Set<string>(JSON.parse(deletedIdsJson));\r\n      }\r\n      return new Set<string>();\r\n    } catch (error) {\r\n      console.error(\r\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\r\n        error\r\n      );\r\n      return new Set<string>();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\r\n   * @param deletedIds Set contenant les IDs des notifications supprimées\r\n   */\r\n  private saveDeletedNotificationIds(deletedIds: Set<string>): void {\r\n    try {\r\n      localStorage.setItem(\r\n        'deletedNotificationIds',\r\n        JSON.stringify(Array.from(deletedIds))\r\n      );\r\n      console.log(\r\n        `${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`\r\n      );\r\n    } catch (error) {\r\n      console.error(\r\n        'Erreur lors de la sauvegarde des IDs de notifications supprimées:',\r\n        error\r\n      );\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  /**\r\n   * Sélectionne ou désélectionne une notification\r\n   * @param notificationId ID de la notification\r\n   * @param event Événement de la case à cocher\r\n   */\r\n  toggleSelection(notificationId: string, event: Event): void {\r\n    event.stopPropagation(); // Empêcher la propagation de l'événement\r\n\r\n    if (this.selectedNotifications.has(notificationId)) {\r\n      this.selectedNotifications.delete(notificationId);\r\n    } else {\r\n      this.selectedNotifications.add(notificationId);\r\n    }\r\n\r\n    // Mettre à jour l'état de sélection globale\r\n    this.updateSelectionState();\r\n\r\n    // Afficher ou masquer la barre de sélection\r\n    this.showSelectionBar = this.selectedNotifications.size > 0;\r\n  }\r\n\r\n  /**\r\n   * Sélectionne ou désélectionne toutes les notifications\r\n   * @param event Événement de la case à cocher\r\n   */\r\n  toggleSelectAll(event: Event): void {\r\n    event.stopPropagation(); // Empêcher la propagation de l'événement\r\n\r\n    this.allSelected = !this.allSelected;\r\n\r\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\r\n      if (this.allSelected) {\r\n        // Sélectionner toutes les notifications\r\n        notifications.forEach((notification) => {\r\n          this.selectedNotifications.add(notification.id);\r\n        });\r\n      } else {\r\n        // Désélectionner toutes les notifications\r\n        this.selectedNotifications.clear();\r\n      }\r\n\r\n      // Afficher ou masquer la barre de sélection\r\n      this.showSelectionBar = this.selectedNotifications.size > 0;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Met à jour l'état de sélection globale\r\n   */\r\n  private updateSelectionState(): void {\r\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\r\n      this.allSelected =\r\n        notifications.length > 0 &&\r\n        this.selectedNotifications.size === notifications.length;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Supprime les notifications sélectionnées\r\n   */\r\n  deleteSelectedNotifications(): void {\r\n    if (this.selectedNotifications.size === 0) {\r\n      return;\r\n    }\r\n\r\n    const selectedIds = Array.from(this.selectedNotifications);\r\n    console.log('Suppression des notifications sélectionnées:', selectedIds);\r\n\r\n    // Supprimer localement les notifications sélectionnées\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const updatedNotifications = notifications.filter(\r\n        (notification) => !this.selectedNotifications.has(notification.id)\r\n      );\r\n\r\n      // Mettre à jour l'interface utilisateur immédiatement\r\n      (this.messageService as any).notifications.next(updatedNotifications);\r\n\r\n      // Mettre à jour le compteur de notifications non lues\r\n      const unreadCount = updatedNotifications.filter((n) => !n.isRead).length;\r\n      (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n      // Mettre à jour le cache de notifications dans le service\r\n      this.updateNotificationCache(updatedNotifications);\r\n\r\n      // Réinitialiser la sélection\r\n      this.selectedNotifications.clear();\r\n      this.allSelected = false;\r\n      this.showSelectionBar = false;\r\n    });\r\n\r\n    // Appeler le service pour supprimer les notifications sélectionnées\r\n    this.messageService\r\n      .deleteMultipleNotifications(selectedIds)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          console.log('Résultat de la suppression multiple:', result);\r\n          if (result && result.success) {\r\n            console.log(`${result.count} notifications supprimées avec succès`);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          console.error(\r\n            'Erreur lors de la suppression multiple des notifications:',\r\n            err\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Marque les notifications sélectionnées comme lues\r\n   */\r\n  markSelectedAsRead(): void {\r\n    if (this.selectedNotifications.size === 0) {\r\n      return;\r\n    }\r\n\r\n    const selectedIds = Array.from(this.selectedNotifications);\r\n    console.log(\r\n      'Marquage des notifications sélectionnées comme lues:',\r\n      selectedIds\r\n    );\r\n\r\n    // Marquer localement les notifications sélectionnées comme lues\r\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\r\n      const updatedNotifications = notifications.map((notification) =>\r\n        this.selectedNotifications.has(notification.id)\r\n          ? { ...notification, isRead: true, readAt: new Date().toISOString() }\r\n          : notification\r\n      );\r\n\r\n      // Mettre à jour l'interface utilisateur immédiatement\r\n      (this.messageService as any).notifications.next(updatedNotifications);\r\n\r\n      // Mettre à jour le compteur de notifications non lues\r\n      const unreadCount = updatedNotifications.filter((n) => !n.isRead).length;\r\n      (this.messageService as any).notificationCount.next(unreadCount);\r\n\r\n      // Mettre à jour le cache de notifications dans le service\r\n      this.updateNotificationCache(updatedNotifications);\r\n\r\n      // Réinitialiser la sélection\r\n      this.selectedNotifications.clear();\r\n      this.allSelected = false;\r\n      this.showSelectionBar = false;\r\n    });\r\n\r\n    // Appeler le service pour marquer les notifications comme lues\r\n    this.messageService\r\n      .markAsRead(selectedIds)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (result) => {\r\n          console.log('Résultat du marquage comme lu:', result);\r\n          if (result && result.success) {\r\n            console.log('Notifications marquées comme lues avec succès');\r\n          }\r\n        },\r\n        error: (err) => {\r\n          console.error(\r\n            'Erreur lors du marquage des notifications comme lues:',\r\n            err\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Vérifie si une notification est sélectionnée\r\n   * @param notificationId ID de la notification\r\n   * @returns true si la notification est sélectionnée, false sinon\r\n   */\r\n  isSelected(notificationId: string): boolean {\r\n    return this.selectedNotifications.has(notificationId);\r\n  }\r\n\r\n  /**\r\n   * Convertit un type de pièce jointe de notification en type de message\r\n   * @param type Type de pièce jointe\r\n   * @returns Type de message correspondant\r\n   */\r\n  private convertAttachmentType(type: AttachmentType): MessageType {\r\n    switch (type) {\r\n      case 'IMAGE':\r\n        return MessageType.IMAGE;\r\n      case 'FILE':\r\n        return MessageType.FILE;\r\n      case 'AUDIO':\r\n        return MessageType.AUDIO;\r\n      case 'VIDEO':\r\n        return MessageType.VIDEO;\r\n      case 'image':\r\n        return MessageType.IMAGE_LOWER;\r\n      case 'file':\r\n        return MessageType.FILE_LOWER;\r\n      case 'audio':\r\n        return MessageType.AUDIO_LOWER;\r\n      case 'video':\r\n        return MessageType.VIDEO_LOWER;\r\n      default:\r\n        return MessageType.FILE; // Type par défaut\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  class=\"futuristic-notifications-container main-grid-container\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"background-elements background-grid\">\r\n    <!-- Grid pattern and scan line will be added via CSS -->\r\n  </div>\r\n\r\n  <div class=\"futuristic-notifications-card content-card relative z-10\">\r\n    <div class=\"futuristic-notifications-header\">\r\n      <h2 class=\"futuristic-title\">\r\n        <i class=\"fas fa-bell mr-2\"></i>\r\n        Notifications\r\n      </h2>\r\n\r\n      <!-- Barre d'actions normale -->\r\n      <div class=\"flex space-x-2\" *ngIf=\"!showSelectionBar\">\r\n        <!-- Bouton de rafraîchissement -->\r\n        <button\r\n          (click)=\"loadNotifications()\"\r\n          class=\"futuristic-action-button\"\r\n          title=\"Rafraîchir\"\r\n        >\r\n          <i class=\"fas fa-sync-alt\"></i>\r\n        </button>\r\n\r\n        <!-- Case à cocher \"Tout sélectionner\" (déplacée après le bouton de rafraîchissement) -->\r\n        <div *ngIf=\"hasNotifications() | async\" class=\"select-all-checkbox\">\r\n          <label class=\"futuristic-checkbox\">\r\n            <input\r\n              type=\"checkbox\"\r\n              [checked]=\"allSelected\"\r\n              (click)=\"toggleSelectAll($event)\"\r\n            />\r\n            <span class=\"checkmark\"></span>\r\n          </label>\r\n        </div>\r\n\r\n        <!-- Bouton de filtrage des notifications non lues -->\r\n        <button\r\n          (click)=\"toggleUnreadFilter()\"\r\n          class=\"futuristic-action-button\"\r\n          [class.active]=\"showOnlyUnread\"\r\n          title=\"Filtrer les non lues\"\r\n        >\r\n          <i class=\"fas fa-filter\"></i>\r\n        </button>\r\n\r\n        <!-- Bouton pour activer/désactiver le son -->\r\n        <button\r\n          (click)=\"toggleSound()\"\r\n          class=\"futuristic-action-button\"\r\n          [class.active]=\"!isSoundMuted\"\r\n          title=\"{{ isSoundMuted ? 'Activer le son' : 'Désactiver le son' }}\"\r\n        >\r\n          <i\r\n            class=\"fas\"\r\n            [ngClass]=\"isSoundMuted ? 'fa-volume-mute' : 'fa-volume-up'\"\r\n          ></i>\r\n        </button>\r\n\r\n        <!-- Bouton pour marquer toutes les notifications comme lues -->\r\n        <button\r\n          *ngIf=\"(unreadCount$ | async) || 0\"\r\n          (click)=\"markAllAsRead()\"\r\n          class=\"futuristic-primary-button\"\r\n        >\r\n          <i class=\"fas fa-check-double mr-1\"></i> Tout marquer comme lu\r\n        </button>\r\n\r\n        <!-- Bouton pour supprimer toutes les notifications -->\r\n        <button\r\n          *ngIf=\"hasNotifications() | async\"\r\n          (click)=\"deleteAllNotifications()\"\r\n          class=\"futuristic-danger-button\"\r\n          title=\"Supprimer toutes les notifications\"\r\n        >\r\n          <i class=\"fas fa-trash-alt mr-1\"></i> Tout supprimer\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Barre d'actions pour les notifications sélectionnées -->\r\n      <div class=\"flex space-x-2 selection-actions\" *ngIf=\"showSelectionBar\">\r\n        <span class=\"selection-count\"\r\n          >{{ selectedNotifications.size }} sélectionné(s)</span\r\n        >\r\n\r\n        <!-- Bouton pour marquer les notifications sélectionnées comme lues -->\r\n        <button\r\n          (click)=\"markSelectedAsRead()\"\r\n          class=\"futuristic-primary-button\"\r\n        >\r\n          <i class=\"fas fa-check mr-1\"></i> Marquer comme lu\r\n        </button>\r\n\r\n        <!-- Bouton pour supprimer les notifications sélectionnées -->\r\n        <button\r\n          (click)=\"deleteSelectedNotifications()\"\r\n          class=\"futuristic-danger-button\"\r\n        >\r\n          <i class=\"fas fa-trash-alt mr-1\"></i> Supprimer\r\n        </button>\r\n\r\n        <!-- Bouton pour annuler la sélection -->\r\n        <button\r\n          (click)=\"\r\n            selectedNotifications.clear();\r\n            showSelectionBar = false;\r\n            allSelected = false\r\n          \"\r\n          class=\"futuristic-cancel-button\"\r\n        >\r\n          <i class=\"fas fa-times mr-1\"></i> Annuler\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État de chargement futuriste -->\r\n    <div *ngIf=\"loading\" class=\"futuristic-loading-container\">\r\n      <div class=\"futuristic-loading-circle\"></div>\r\n      <p class=\"futuristic-loading-text\">Chargement des notifications...</p>\r\n    </div>\r\n\r\n    <!-- État d'erreur futuriste -->\r\n    <div *ngIf=\"error\" class=\"futuristic-error-message\">\r\n      <div class=\"flex items-center\">\r\n        <i class=\"fas fa-exclamation-triangle futuristic-error-icon\"></i>\r\n        <div>\r\n          <h3 class=\"futuristic-error-title\">Erreur de chargement</h3>\r\n          <p class=\"futuristic-error-text\">{{ getErrorMessage() }}</p>\r\n        </div>\r\n        <button\r\n          (click)=\"loadNotifications()\"\r\n          class=\"futuristic-retry-button ml-auto\"\r\n        >\r\n          Réessayer\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État vide futuriste -->\r\n    <div\r\n      *ngIf=\"!loading && !(hasNotifications() | async)\"\r\n      class=\"futuristic-empty-state\"\r\n    >\r\n      <div class=\"futuristic-empty-icon\">\r\n        <i class=\"fas fa-bell-slash\"></i>\r\n      </div>\r\n      <h3 class=\"futuristic-empty-title\">Aucune notification</h3>\r\n      <p class=\"futuristic-empty-text\">Vous êtes à jour !</p>\r\n      <button (click)=\"loadNotifications()\" class=\"futuristic-check-button\">\r\n        Vérifier les nouvelles notifications\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Liste des notifications futuriste -->\r\n    <div\r\n      *ngIf=\"!loading && (hasNotifications() | async)\"\r\n      class=\"futuristic-notifications-list\"\r\n      #notificationContainer\r\n      (scroll)=\"onScroll(notificationContainer)\"\r\n    >\r\n      <ng-container *ngFor=\"let notification of filteredNotifications$ | async\">\r\n        <div\r\n          [class.futuristic-notification-unread]=\"!notification.isRead\"\r\n          [class.futuristic-notification-read]=\"notification.isRead\"\r\n          [class.futuristic-notification-selected]=\"isSelected(notification.id)\"\r\n          class=\"futuristic-notification-card\"\r\n        >\r\n          <!-- Case à cocher pour la sélection (déplacée en haut à gauche) -->\r\n          <div class=\"notification-checkbox\">\r\n            <label class=\"futuristic-checkbox\">\r\n              <input\r\n                type=\"checkbox\"\r\n                [checked]=\"isSelected(notification.id)\"\r\n                (click)=\"toggleSelection(notification.id, $event)\"\r\n              />\r\n              <span class=\"checkmark\"></span>\r\n            </label>\r\n          </div>\r\n\r\n          <!-- Avatar de l'expéditeur simplifié -->\r\n          <div class=\"notification-avatar\">\r\n            <img\r\n              [src]=\"\r\n                notification.senderId?.image ||\r\n                'assets/images/default-avatar.png'\r\n              \"\r\n              alt=\"Avatar\"\r\n              onerror=\"this.src='assets/images/default-avatar.png'\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Contenu principal de la notification -->\r\n          <div class=\"notification-main-content\">\r\n            <!-- Contenu de notification simplifié -->\r\n            <div class=\"notification-content\">\r\n              <div class=\"notification-header\">\r\n                <div class=\"notification-header-top\">\r\n                  <span class=\"notification-sender\">{{\r\n                    notification.senderId?.username || \"Système\"\r\n                  }}</span>\r\n\r\n                  <!-- Heure de la notification (placée à droite du nom d'utilisateur) -->\r\n                  <div class=\"notification-time\">\r\n                    {{ notification.timestamp | date : \"shortTime\" }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Contenu du message (déplacé après l'en-tête) -->\r\n              <div class=\"notification-text-container\">\r\n                <span class=\"notification-text\">{{\r\n                  notification.content\r\n                }}</span>\r\n              </div>\r\n\r\n              <!-- Aperçu du message simplifié -->\r\n              <div\r\n                *ngIf=\"notification.message?.content\"\r\n                class=\"notification-message-preview\"\r\n              >\r\n                {{ notification.message?.content }}\r\n              </div>\r\n\r\n              <!-- Indicateur de pièces jointes -->\r\n              <div\r\n                *ngIf=\"notification.message?.attachments?.length\"\r\n                class=\"notification-attachments-indicator\"\r\n              >\r\n                <i class=\"fas fa-paperclip\"></i>\r\n                {{ notification.message?.attachments?.length }} pièce(s)\r\n                jointe(s)\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Indicateur de non-lu (petit point bleu) -->\r\n            <div *ngIf=\"!notification.isRead\" class=\"unread-indicator\"></div>\r\n          </div>\r\n\r\n          <!-- Actions de notification -->\r\n          <div class=\"notification-actions\">\r\n            <!-- Bouton pour afficher les pièces jointes -->\r\n            <button\r\n              *ngIf=\"notification.message?.attachments?.length\"\r\n              (click)=\"\r\n                getNotificationAttachments(notification.id);\r\n                $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-attachment-button\"\r\n              title=\"Voir les pièces jointes\"\r\n            >\r\n              <i class=\"fas fa-paperclip\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour rejoindre la conversation -->\r\n            <button\r\n              *ngIf=\"\r\n                notification.type === 'NEW_MESSAGE' ||\r\n                notification.type === 'GROUP_INVITE'\r\n              \"\r\n              (click)=\"joinConversation(notification); $event.stopPropagation()\"\r\n              class=\"notification-action-button notification-join-button\"\r\n              title=\"Rejoindre la conversation\"\r\n            >\r\n              <i class=\"fas fa-comments\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour voir les détails de la notification -->\r\n            <button\r\n              (click)=\"\r\n                openNotificationDetails(notification); $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-details-button\"\r\n              title=\"Voir les détails\"\r\n            >\r\n              <i class=\"fas fa-info-circle\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton marquer comme lu -->\r\n            <button\r\n              *ngIf=\"!notification.isRead\"\r\n              (click)=\"markAsRead(notification.id); $event.stopPropagation()\"\r\n              class=\"notification-action-button notification-read-button\"\r\n              title=\"Marquer comme lu\"\r\n            >\r\n              <i class=\"fas fa-check\"></i>\r\n            </button>\r\n\r\n            <!-- Bouton pour supprimer la notification -->\r\n            <button\r\n              (click)=\"\r\n                deleteNotification(notification.id); $event.stopPropagation()\r\n              \"\r\n              class=\"notification-action-button notification-delete-button\"\r\n              title=\"Supprimer cette notification\"\r\n            >\r\n              <i class=\"fas fa-trash-alt\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <!-- Indicateur de chargement des anciennes notifications -->\r\n      <div *ngIf=\"loadingMore\" class=\"futuristic-loading-more\">\r\n        <div class=\"futuristic-loading-circle-small\"></div>\r\n        <p class=\"futuristic-loading-text-small\">\r\n          Chargement des notifications plus anciennes...\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Modal pour afficher les pièces jointes -->\r\n<div\r\n  class=\"futuristic-modal-overlay\"\r\n  [style.display]=\"showAttachmentsModal ? 'flex' : 'none'\"\r\n  (click)=\"closeAttachmentsModal()\"\r\n>\r\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"futuristic-modal-header\">\r\n      <h3 class=\"futuristic-modal-title\">\r\n        <i class=\"fas fa-paperclip mr-2\"></i>\r\n        Pièces jointes\r\n      </h3>\r\n      <button class=\"futuristic-modal-close\" (click)=\"closeAttachmentsModal()\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n    <div class=\"futuristic-modal-body\">\r\n      <div *ngIf=\"loadingAttachments\" class=\"futuristic-loading-container\">\r\n        <div class=\"futuristic-loading-circle\"></div>\r\n        <p class=\"futuristic-loading-text\">Chargement des pièces jointes...</p>\r\n      </div>\r\n\r\n      <div\r\n        *ngIf=\"!loadingAttachments && currentAttachments.length === 0\"\r\n        class=\"futuristic-empty-state\"\r\n      >\r\n        <div class=\"futuristic-empty-icon\">\r\n          <i class=\"fas fa-file-alt\"></i>\r\n        </div>\r\n        <h3 class=\"futuristic-empty-title\">Aucune pièce jointe</h3>\r\n        <p class=\"futuristic-empty-text\">\r\n          Aucune pièce jointe n'a été trouvée pour cette notification.\r\n        </p>\r\n      </div>\r\n\r\n      <div\r\n        *ngIf=\"!loadingAttachments && currentAttachments.length > 0\"\r\n        class=\"futuristic-attachments-list\"\r\n      >\r\n        <div\r\n          *ngFor=\"let attachment of currentAttachments\"\r\n          class=\"futuristic-attachment-item\"\r\n        >\r\n          <!-- Image -->\r\n          <div\r\n            *ngIf=\"isImage(attachment.type)\"\r\n            class=\"futuristic-attachment-preview\"\r\n          >\r\n            <img\r\n              [src]=\"attachment.url\"\r\n              alt=\"Image\"\r\n              (click)=\"openAttachment(attachment.url)\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Document -->\r\n          <div\r\n            *ngIf=\"!isImage(attachment.type)\"\r\n            class=\"futuristic-attachment-icon\"\r\n          >\r\n            <i [class]=\"getFileIcon(attachment.type)\"></i>\r\n          </div>\r\n\r\n          <div class=\"futuristic-attachment-info\">\r\n            <div class=\"futuristic-attachment-name\">\r\n              {{ attachment.name || \"Pièce jointe\" }}\r\n            </div>\r\n            <div class=\"futuristic-attachment-meta\">\r\n              <span class=\"futuristic-attachment-type\">{{\r\n                getFileTypeLabel(attachment.type)\r\n              }}</span>\r\n              <span\r\n                *ngIf=\"attachment.size\"\r\n                class=\"futuristic-attachment-size\"\r\n                >{{ formatFileSize(attachment.size) }}</span\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"futuristic-attachment-actions\">\r\n            <button\r\n              class=\"futuristic-attachment-button\"\r\n              (click)=\"openAttachment(attachment.url)\"\r\n              title=\"Ouvrir\"\r\n            >\r\n              <i class=\"fas fa-external-link-alt\"></i>\r\n            </button>\r\n            <button\r\n              class=\"futuristic-attachment-button\"\r\n              (click)=\"downloadAttachment(attachment)\"\r\n              title=\"Télécharger\"\r\n            >\r\n              <i class=\"fas fa-download\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAUA,SAAqBA,OAAO,EAAEC,EAAE,EAAcC,eAAe,QAAQ,MAAM;AAC3E,SAKEC,WAAW,QACN,8BAA8B;AACrC,SACEC,UAAU,EACVC,GAAG,EACHC,SAAS,EAETC,IAAI,EACJC,YAAY,EACZC,oBAAoB,EACpBC,MAAM,QACD,gBAAgB;;;;;;;;;;ICCfC,EAAA,CAAAC,cAAA,cAAoE;IAK9DD,EAAA,CAAAE,UAAA,mBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAHnCJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;;;;IAJJX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAAC,WAAA,CAAuB;;;;;;IA+B7BhB,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAe,0EAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAU,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBpB,EAAA,CAAAY,SAAA,YAAwC;IAACZ,EAAA,CAAAqB,MAAA,8BAC3C;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAAoB,2EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAe,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAIlCzB,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,uBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;;IA9DXX,EAAA,CAAAC,cAAA,cAAsD;IAGlDD,EAAA,CAAAE,UAAA,mBAAAwB,iEAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAmB,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAI7B7B,EAAA,CAAAY,SAAA,YAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAC,8CAAA,kBASM;;IAGN/B,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAA8B,iEAAA;MAAAhC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAM,OAAA,GAAAjC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAK9BlC,EAAA,CAAAY,SAAA,YAA6B;IAC/BZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAKC;IAJCD,EAAA,CAAAE,UAAA,mBAAAiC,iEAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAS,OAAA,GAAApC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2B,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBrC,EAAA,CAAAY,SAAA,YAGK;IACPZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,IAAAQ,iDAAA,qBAMS;;IAGTtC,EAAA,CAAA8B,UAAA,KAAAS,kDAAA,qBAOS;;IACXvC,EAAA,CAAAW,YAAA,EAAM;;;;IApDEX,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,OAAAC,MAAA,CAAAC,gBAAA,IAAgC;IAepC1C,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA2C,WAAA,WAAAF,MAAA,CAAAG,cAAA,CAA+B;IAU/B5C,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA2C,WAAA,YAAAF,MAAA,CAAAI,YAAA,CAA8B;IAC9B7C,EAAA,CAAA8C,qBAAA,UAAAL,MAAA,CAAAI,YAAA,+CAAmE;IAIjE7C,EAAA,CAAAa,SAAA,GAA4D;IAA5Db,EAAA,CAAAc,UAAA,YAAA2B,MAAA,CAAAI,YAAA,qCAA4D;IAM7D7C,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAM,YAAA,OAAiC;IASjC/C,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAd,EAAA,CAAAwC,WAAA,SAAAC,MAAA,CAAAC,gBAAA,IAAgC;;;;;;IAUrC1C,EAAA,CAAAC,cAAA,cAAuE;IAElED,EAAA,CAAAqB,MAAA,GAA+C;IAAArB,EAAA,CAAAW,YAAA,EACjD;IAGDX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA8C,iEAAA;MAAAhD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyC,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BnD,EAAA,CAAAY,SAAA,YAAiC;IAACZ,EAAA,CAAAqB,MAAA,yBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAAkD,iEAAA;MAAApD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4C,OAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAGvCtD,EAAA,CAAAY,SAAA,YAAqC;IAACZ,EAAA,CAAAqB,MAAA,kBACxC;IAAArB,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAOC;IANCD,EAAA,CAAAE,UAAA,mBAAAqD,iEAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAQ,aAAA;MACegD,OAAA,CAAAC,qBAAA,CAAAC,KAAA,EACf;MAAAF,OAAA,CAAAG,gBAAA,GACN,KAAK;MAAA,OAAA3D,EAAA,CAAAS,WAAA,CAAA+C,OAAA,CAAAxC,WAAA,GACL,KAAK;IAAA,EAAE;IAGDhB,EAAA,CAAAY,SAAA,aAAiC;IAACZ,EAAA,CAAAqB,MAAA,iBACpC;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IA7BNX,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAA4D,kBAAA,KAAAC,MAAA,CAAAJ,qBAAA,CAAAK,IAAA,8BAA+C;;;;;IAkCtD9D,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,sCAA+B;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAIxEX,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAY,SAAA,YAAiE;IACjEZ,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAqB,MAAA,2BAAoB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC5DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IAE9DX,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6D,kEAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAwD,OAAA,CAAApC,iBAAA,EAAmB;IAAA,EAAC;IAG7B7B,EAAA,CAAAqB,MAAA,uBACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;IAP0BX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAkE,iBAAA,CAAAC,MAAA,CAAAC,eAAA,GAAuB;;;;;;IAY9DpE,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,YAAiC;IACnCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,0BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAqB,MAAA,mCAAkB;IAAArB,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,iBAAsE;IAA9DD,EAAA,CAAAE,UAAA,mBAAAmE,kEAAA;MAAArE,EAAA,CAAAK,aAAA,CAAAiE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8D,OAAA,CAAA1C,iBAAA,EAAmB;IAAA,EAAC;IACnC7B,EAAA,CAAAqB,MAAA,kDACF;IAAArB,EAAA,CAAAW,YAAA,EAAS;;;;;IAkEDX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,MACF;;;;;IAGA1E,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAY,SAAA,YAAgC;IAChCZ,EAAA,CAAAqB,MAAA,GAEF;IAAArB,EAAA,CAAAW,YAAA,EAAM;;;;IAFJX,EAAA,CAAAa,SAAA,GAEF;IAFEb,EAAA,CAAA4D,kBAAA,MAAAY,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,8BAEF;;;;;IAIF5E,EAAA,CAAAY,SAAA,cAAiE;;;;;;IAMjEZ,EAAA,CAAAC,cAAA,iBAQC;IANCD,EAAA,CAAAE,UAAA,mBAAA2E,2FAAAzE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAN,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAQ,aAAA;MACmBwE,OAAA,CAAAC,0BAAA,CAAAT,gBAAA,CAAAU,EAAA,CACnB;MAAA,OAAkBlF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAClB;IAAA,EAAC;IAIDnF,EAAA,CAAAY,SAAA,YAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAGTX,EAAA,CAAAC,cAAA,iBAQC;IAHCD,EAAA,CAAAE,UAAA,mBAAAkF,2FAAAhF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgF,IAAA;MAAA,MAAAb,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAO,OAAA,GAAAtF,EAAA,CAAAQ,aAAA;MAAS8E,OAAA,CAAAC,gBAAA,CAAAf,gBAAA,CAA8B;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAIlEnF,EAAA,CAAAY,SAAA,YAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAcTX,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAAsF,2FAAApF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoF,IAAA;MAAA,MAAAjB,gBAAA,GAAAxE,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAW,OAAA,GAAA1F,EAAA,CAAAQ,aAAA;MAASkF,OAAA,CAAAC,UAAA,CAAAnB,gBAAA,CAAAU,EAAA,CAA2B;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAI/DnF,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IA7HfX,EAAA,CAAA4F,uBAAA,GAA0E;IACxE5F,EAAA,CAAAC,cAAA,cAKC;IAOOD,EAAA,CAAAE,UAAA,mBAAA2F,gFAAAzF,MAAA;MAAA,MAAA0F,WAAA,GAAA9F,EAAA,CAAAK,aAAA,CAAA0F,IAAA;MAAA,MAAAvB,gBAAA,GAAAsB,WAAA,CAAAf,SAAA;MAAA,MAAAiB,OAAA,GAAAhG,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAuF,OAAA,CAAAC,eAAA,CAAAzB,gBAAA,CAAAU,EAAA,EAAA9E,MAAA,CAAwC;IAAA,EAAC;IAHpDJ,EAAA,CAAAW,YAAA,EAIE;IACFX,EAAA,CAAAY,SAAA,eAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAQ;IAIVX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAY,SAAA,cAOE;IACJZ,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAAuC;IAKGD,EAAA,CAAAqB,MAAA,IAEhC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAqB,MAAA,IACF;;IAAArB,EAAA,CAAAW,YAAA,EAAM;IAKVX,EAAA,CAAAC,cAAA,eAAyC;IACPD,EAAA,CAAAqB,MAAA,IAE9B;IAAArB,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAA8B,UAAA,KAAAoE,+DAAA,kBAKM;IAGNlG,EAAA,CAAA8B,UAAA,KAAAqE,+DAAA,kBAOM;IACRnG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA8B,UAAA,KAAAsE,+DAAA,kBAAiE;IACnEpG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAAkC;IAEhCD,EAAA,CAAA8B,UAAA,KAAAuE,kEAAA,qBAUS;IAGTrG,EAAA,CAAA8B,UAAA,KAAAwE,kEAAA,qBAUS;IAGTtG,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAqG,kFAAAnG,MAAA;MAAA,MAAA0F,WAAA,GAAA9F,EAAA,CAAAK,aAAA,CAAA0F,IAAA;MAAA,MAAAvB,gBAAA,GAAAsB,WAAA,CAAAf,SAAA;MAAA,MAAAyB,OAAA,GAAAxG,EAAA,CAAAQ,aAAA;MACmBgG,OAAA,CAAAC,uBAAA,CAAAjC,gBAAA,CAAqC;MAAA,OAAExE,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EACzD;IAAA;IAIDnF,EAAA,CAAAY,SAAA,aAAkC;IACpCZ,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAA8B,UAAA,KAAA4E,kEAAA,qBAOS;IAGT1G,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAE,UAAA,mBAAAyG,kFAAAvG,MAAA;MAAA,MAAA0F,WAAA,GAAA9F,EAAA,CAAAK,aAAA,CAAA0F,IAAA;MAAA,MAAAvB,gBAAA,GAAAsB,WAAA,CAAAf,SAAA;MAAA,MAAA6B,OAAA,GAAA5G,EAAA,CAAAQ,aAAA;MACmBoG,OAAA,CAAAC,kBAAA,CAAArC,gBAAA,CAAAU,EAAA,CAAmC;MAAA,OAAElF,EAAA,CAAAS,WAAA,CAAAL,MAAA,CAAA+E,eAAA,EACvD;IAAA;IAIDnF,EAAA,CAAAY,SAAA,aAAgC;IAClCZ,EAAA,CAAAW,YAAA,EAAS;IAGfX,EAAA,CAAA8G,qBAAA,EAAe;;;;;IAzIX9G,EAAA,CAAAa,SAAA,GAA6D;IAA7Db,EAAA,CAAA2C,WAAA,oCAAA6B,gBAAA,CAAAuC,MAAA,CAA6D,iCAAAvC,gBAAA,CAAAuC,MAAA,sCAAAC,OAAA,CAAAC,UAAA,CAAAzC,gBAAA,CAAAU,EAAA;IAUvDlF,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,UAAA,YAAAkG,OAAA,CAAAC,UAAA,CAAAzC,gBAAA,CAAAU,EAAA,EAAuC;IAUzClF,EAAA,CAAAa,SAAA,GAGC;IAHDb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAA0C,QAAA,kBAAA1C,gBAAA,CAAA0C,QAAA,CAAAC,KAAA,yCAAAnH,EAAA,CAAAoH,aAAA,CAGC;IAYqCpH,EAAA,CAAAa,SAAA,GAEhC;IAFgCb,EAAA,CAAAkE,iBAAA,EAAAM,gBAAA,CAAA0C,QAAA,kBAAA1C,gBAAA,CAAA0C,QAAA,CAAAG,QAAA,oBAEhC;IAIArH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAAsH,WAAA,SAAA9C,gBAAA,CAAA+C,SAAA,oBACF;IAM8BvH,EAAA,CAAAa,SAAA,GAE9B;IAF8Bb,EAAA,CAAAkE,iBAAA,CAAAM,gBAAA,CAAAE,OAAA,CAE9B;IAKD1E,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,CAAmC;IAQnC1E,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAU9C5E,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAAuC,MAAA,CAA0B;IAO7B/G,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAa/C5E,EAAA,CAAAa,SAAA,GAGD;IAHCb,EAAA,CAAAc,UAAA,SAAA0D,gBAAA,CAAAgD,IAAA,sBAAAhD,gBAAA,CAAAgD,IAAA,oBAGD;IAqBCxH,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,UAAA,UAAA0D,gBAAA,CAAAuC,MAAA,CAA0B;;;;;IAuBnC/G,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAY,SAAA,cAAmD;IACnDZ,EAAA,CAAAC,cAAA,YAAyC;IACvCD,EAAA,CAAAqB,MAAA,uDACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAxJRX,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAE,UAAA,oBAAAuH,gEAAA;MAAAzH,EAAA,CAAAK,aAAA,CAAAqH,IAAA;MAAA,MAAAC,IAAA,GAAA3H,EAAA,CAAA4H,WAAA;MAAA,MAAAC,OAAA,GAAA7H,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAAoH,OAAA,CAAAC,QAAA,CAAAH,IAAA,CAA+B;IAAA,EAAC;IAE1C3H,EAAA,CAAA8B,UAAA,IAAAiG,wDAAA,6BA2Ie;;IAGf/H,EAAA,CAAA8B,UAAA,IAAAkG,+CAAA,kBAKM;IACRhI,EAAA,CAAAW,YAAA,EAAM;;;;IApJmCX,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAwC,WAAA,OAAAyF,MAAA,CAAAC,sBAAA,EAAiC;IA8IlElI,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,SAAAmH,MAAA,CAAAE,WAAA,CAAiB;;;;;IA2BvBnI,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAqB,MAAA,4CAAgC;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;IAGzEX,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,SAAA,YAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAqB,MAAA,+BAAmB;IAAArB,EAAA,CAAAW,YAAA,EAAK;IAC3DX,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAqB,MAAA,yFACF;IAAArB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAYFX,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,UAAA,mBAAAkI,2EAAA;MAAApI,EAAA,CAAAK,aAAA,CAAAgI,IAAA;MAAA,MAAAC,cAAA,GAAAtI,EAAA,CAAAQ,aAAA,GAAAuE,SAAA;MAAA,MAAAwD,OAAA,GAAAvI,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8H,OAAA,CAAAC,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAH1CzI,EAAA,CAAAW,YAAA,EAIE;;;;IAHAX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,QAAAwH,cAAA,CAAAG,GAAA,EAAAzI,EAAA,CAAAoH,aAAA,CAAsB;;;;;IAO1BpH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,SAAA,QAA8C;IAChDZ,EAAA,CAAAW,YAAA,EAAM;;;;;IADDX,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAA0I,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAN,cAAA,CAAAd,IAAA,EAAsC;;;;;IAWvCxH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAqB,MAAA,GAAqC;IAAArB,EAAA,CAAAW,YAAA,EACvC;;;;;IADEX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkE,iBAAA,CAAA2E,OAAA,CAAAC,cAAA,CAAAR,cAAA,CAAAxE,IAAA,EAAqC;;;;;;IAnC9C9D,EAAA,CAAAC,cAAA,eAGC;IAECD,EAAA,CAAA8B,UAAA,IAAAiH,qDAAA,mBASM;IAGN/I,EAAA,CAAA8B,UAAA,IAAAkH,qDAAA,mBAKM;IAENhJ,EAAA,CAAAC,cAAA,eAAwC;IAEpCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwC;IACGD,EAAA,CAAAqB,MAAA,GAEvC;IAAArB,EAAA,CAAAW,YAAA,EAAO;IACTX,EAAA,CAAA8B,UAAA,IAAAmH,sDAAA,oBAIC;IACHjJ,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,gBAA2C;IAGvCD,EAAA,CAAAE,UAAA,mBAAAgJ,yEAAA;MAAA,MAAApD,WAAA,GAAA9F,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAf,SAAA;MAAA,MAAAqE,OAAA,GAAApJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA2I,OAAA,CAAAZ,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAGxCzI,EAAA,CAAAY,SAAA,cAAwC;IAC1CZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAmJ,yEAAA;MAAA,MAAAvD,WAAA,GAAA9F,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAf,SAAA;MAAA,MAAAuE,OAAA,GAAAtJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA6I,OAAA,CAAAC,kBAAA,CAAAjB,cAAA,CAA8B;IAAA,EAAC;IAGxCtI,EAAA,CAAAY,SAAA,cAA+B;IACjCZ,EAAA,CAAAW,YAAA,EAAS;;;;;IAhDRX,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,SAAA0I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA8B;IAY9BxH,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,UAAA0I,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA+B;IAQ9BxH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA4D,kBAAA,MAAA0E,cAAA,CAAAoB,IAAA,6BACF;IAE2C1J,EAAA,CAAAa,SAAA,GAEvC;IAFuCb,EAAA,CAAAkE,iBAAA,CAAAsF,OAAA,CAAAG,gBAAA,CAAArB,cAAA,CAAAd,IAAA,EAEvC;IAECxH,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAAwH,cAAA,CAAAxE,IAAA,CAAqB;;;;;IArChC9D,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAA8B,UAAA,IAAA8H,+CAAA,oBAwDM;IACR5J,EAAA,CAAAW,YAAA,EAAM;;;;IAxDqBX,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAc,UAAA,YAAA+I,MAAA,CAAAC,kBAAA,CAAqB;;;ADjUtD,OAAM,MAAOC,yBAAyB;EAgCpCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAjC,WAAW,GAAG,KAAK;IACnB,KAAAkC,oBAAoB,GAAG,IAAI;IAC3B,KAAAC,KAAK,GAAiB,IAAI;IAC1B,KAAA1H,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAY,qBAAqB,GAAgB,IAAI8G,GAAG,EAAU;IACtD,KAAAvJ,WAAW,GAAG,KAAK;IACnB,KAAA2C,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAA6G,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAX,kBAAkB,GAAiB,EAAE;IAErC;IACA,KAAAY,4BAA4B,GAAG,KAAK;IACpC,KAAAC,mBAAmB,GAAwB,IAAI;IAEvC,KAAAC,QAAQ,GAAG,IAAIvL,OAAO,EAAQ;IAC9B,KAAAwL,eAAe,GAAG,IAAItL,eAAe,CAAS,CAAC,CAAC;IAOtD,IAAI,CAACuL,cAAc,GAAG,IAAI,CAACb,cAAc,CAACa,cAAc;IACxD,IAAI,CAAC5C,sBAAsB,GAAG,IAAI,CAAC4C,cAAc,CAAC,CAAC;IACnD,IAAI,CAAC/H,YAAY,GAAG,IAAI,CAACkH,cAAc,CAACc,kBAAkB;IAC1D,IAAI,CAACC,WAAW,GAAG,IAAI,CAACd,YAAY,CAACe,SAAS;IAE9C;IACA,IAAI,CAACpI,YAAY,GAAG,IAAI,CAACoH,cAAc,CAACiB,OAAO,EAAE;EACnD;EAEA;;;;EAIA3F,gBAAgBA,CAAC4F,YAA0B;IACzCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY,CAAC;IAEvD;IACA,IAAI,CAACxF,UAAU,CAACwF,YAAY,CAACjG,EAAE,CAAC;IAEhC;IACA,MAAMoG,cAAc,GAClBH,YAAY,CAACG,cAAc,IAC1BH,YAAY,CAACI,QAAQ,IAAIJ,YAAY,CAACI,QAAQ,CAAC,gBAAgB,CAAE,KACjEJ,YAAY,CAACK,aAAa,IAC3BL,YAAY,CAACK,aAAa,CAACC,QAAQ,CAAC,cAAc,CAAC,GAC/CN,YAAY,CAACK,aAAa,GAC1B,IAAI,CAAC;IAEX,MAAME,OAAO,GACXP,YAAY,CAACO,OAAO,IACnBP,YAAY,CAACI,QAAQ,IAAIJ,YAAY,CAACI,QAAQ,CAAC,SAAS,CAAE,KAC1DJ,YAAY,CAACK,aAAa,IAC3BL,YAAY,CAACK,aAAa,CAACC,QAAQ,CAAC,OAAO,CAAC,GACxCN,YAAY,CAACK,aAAa,GAC1B,IAAI,CAAC;IAEX;IACA,IAAIF,cAAc,EAAE;MAClB;MACAF,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7CC,cAAc,CACf;MACD;MACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gCAAgCP,cAAc,EAAE;KACxE,MAAM,IAAII,OAAO,EAAE;MAClB;MACAN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,OAAO,CAAC;MACnDC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,mBAAmBH,OAAO,EAAE;KACpD,MAAM,IAAIP,YAAY,CAACjE,QAAQ,IAAIiE,YAAY,CAACjE,QAAQ,CAAChC,EAAE,EAAE;MAC5D;MACA;MACAkG,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DF,YAAY,CAACjE,QAAQ,CAAChC,EAAE,CACzB;MAED;MACA;MAEA,IAAI,CAAC+E,cAAc,CAChB6B,uBAAuB,CAACX,YAAY,CAACjE,QAAQ,CAAChC,EAAE,CAAC,CACjD6G,SAAS,CAAC;QACTC,IAAI,EAAGC,YAAY,IAAI;UACrBb,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEY,YAAY,CAAC;UAClD;UAEA,IAAIA,YAAY,IAAIA,YAAY,CAAC/G,EAAE,EAAE;YACnC;YACA;YACAyG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gCAAgCI,YAAY,CAAC/G,EAAE,EAAE;WACzE,MAAM;YACLkG,OAAO,CAACd,KAAK,CAAC,8BAA8B,EAAE2B,YAAY,CAAC;YAC3DN,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;;QAEtC,CAAC;QACDvB,KAAK,EAAGA,KAAK,IAAI;UACfc,OAAO,CAACd,KAAK,CACX,6DAA6D,EAC7DA,KAAK,CACN;UACD;UAEA;UACAqB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;QACpC;OACD,CAAC;KACL,MAAM;MACL;MACAT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDM,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;;EAEtC;EAGA/D,QAAQA,CAACoE,MAAmB;IAC1B,IAAI,CAACA,MAAM,EAAE;IAEb,MAAMC,cAAc,GAAGD,MAAM,CAACE,SAAS;IACvC,MAAMC,YAAY,GAAGH,MAAM,CAACG,YAAY;IACxC,MAAMC,YAAY,GAAGJ,MAAM,CAACI,YAAY;IAExC;IACA,IAAID,YAAY,GAAGF,cAAc,GAAGG,YAAY,GAAG,GAAG,EAAE;MACtD,IAAI,CAACzB,eAAe,CAACmB,IAAI,CAACG,cAAc,CAAC;;EAE7C;EACAI,QAAQA,CAAA;IACN;IACA,MAAMC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC1E,IAAIF,mBAAmB,KAAK,IAAI,EAAE;MAChC,IAAI,CAAC3J,YAAY,GAAG2J,mBAAmB,KAAK,MAAM;MAClDpB,OAAO,CAACC,GAAG,CACT,8BACE,IAAI,CAACxI,YAAY,GAAG,WAAW,GAAG,QACpC,EAAE,CACH;MACD,IAAI,CAACoH,cAAc,CAAC0C,QAAQ,CAAC,IAAI,CAAC9J,YAAY,CAAC;;IAGjD,IAAI,CAAChB,iBAAiB,EAAE;IACxB,IAAI,CAAC+K,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;;;EAGQA,0BAA0BA,CAAA;IAChC;IACA,MAAMC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAID,sBAAsB,CAACjJ,IAAI,GAAG,CAAC,EAAE;MACnCsH,OAAO,CAACC,GAAG,CACT,eAAe0B,sBAAsB,CAACjJ,IAAI,2BAA2B,CACtE;MAED;MACA,IAAI,CAACgH,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;QAC5D,MAAMC,qBAAqB,GAAGD,aAAa,CAACnN,MAAM,CAC/CoL,YAAY,IAAK,CAAC4B,sBAAsB,CAACK,GAAG,CAACjC,YAAY,CAACjG,EAAE,CAAC,CAC/D;QAED;QACC,IAAI,CAAC+E,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACmB,qBAAqB,CAAC;QAEtE;QACA,MAAME,WAAW,GAAGF,qBAAqB,CAACpN,MAAM,CAC7CuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CACjB,CAACnC,MAAM;QACP,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAEhE;QACA,IAAI,CAACG,uBAAuB,CAACL,qBAAqB,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEAN,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAChC,eAAe,CACjBoC,IAAI,CACHtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,EACxB/K,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE;IAAE;IACxBC,MAAM,CAAC,MAAM,CAAC,IAAI,CAACoI,WAAW,IAAI,IAAI,CAACkC,oBAAoB,CAAC,CAAC;KAC9D,CACA0B,SAAS,CAAC,MAAK;MACd,IAAI,CAAC0B,qBAAqB,EAAE;IAC9B,CAAC,CAAC;EACN;EACA5L,iBAAiBA,CAAA;IACfuJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/D,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACmC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM0C,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D5B,OAAO,CAACC,GAAG,CACT,GAAG0B,sBAAsB,CAACjJ,IAAI,yDAAyD,CACxF;IAED,IAAI,CAACmG,cAAc,CAChByD,gBAAgB,CAAC,IAAI,CAAC,CACtBT,IAAI,CACHtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,EACxBlL,GAAG,CAAEwN,aAAa,IAAI;MACpB;MACA,IAAIH,sBAAsB,CAACjJ,IAAI,GAAG,CAAC,EAAE;QACnC,OAAOoJ,aAAa,CAACnN,MAAM,CACxBoL,YAAY,IAAK,CAAC4B,sBAAsB,CAACK,GAAG,CAACjC,YAAY,CAACjG,EAAE,CAAC,CAC/D;;MAEH,OAAOgI,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACtB9B,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9D6B,aAAa,CACd;QAED;QACC,IAAI,CAACjD,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACkB,aAAa,CAAC;QAE9D;QACA,MAAMG,WAAW,GAAGH,aAAa,CAACnN,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACnC,MAAM;QAChE,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAEhE,IAAI,CAACjD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,oBAAoB,GACvB,IAAI,CAACJ,cAAc,CAACI,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGqD,GAAU,IAAI;QACpBvC,OAAO,CAACd,KAAK,CACX,wDAAwD,EACxDqD,GAAG,CACJ;QACD,IAAI,CAACrD,KAAK,GAAGqD,GAAG;QAChB,IAAI,CAACvD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EAEAoD,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACtF,WAAW,IAAI,CAAC,IAAI,CAACkC,oBAAoB,EAAE;IAEpDe,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAAClD,WAAW,GAAG,IAAI;IAEvB;IACA,MAAM4E,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D5B,OAAO,CAACC,GAAG,CACT,GAAG0B,sBAAsB,CAACjJ,IAAI,yDAAyD,CACxF;IAED,IAAI,CAACmG,cAAc,CAChBwD,qBAAqB,EAAE,CACvBR,IAAI,CACHtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,EACxBlL,GAAG,CAAEwN,aAAa,IAAI;MACpB;MACA,IAAIH,sBAAsB,CAACjJ,IAAI,GAAG,CAAC,EAAE;QACnC,MAAMqJ,qBAAqB,GAAGD,aAAa,CAACnN,MAAM,CAC/CoL,YAAY,IAAK,CAAC4B,sBAAsB,CAACK,GAAG,CAACjC,YAAY,CAACjG,EAAE,CAAC,CAC/D;QACDkG,OAAO,CAACC,GAAG,CACT,UACE6B,aAAa,CAACtI,MAAM,GAAGuI,qBAAqB,CAACvI,MAC/C,2BAA2B,CAC5B;QACD,OAAOuI,qBAAqB;;MAE9B,OAAOD,aAAa;IACtB,CAAC,CAAC,CACH,CACAnB,SAAS,CAAC;MACTC,IAAI,EAAGkB,aAAa,IAAI;QACtB9B,OAAO,CAACC,GAAG,CACT,mEAAmE,EACnE6B,aAAa,CACd;QAED;QACA,IAAI,CAACpC,cAAc,CAChBmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CACbmM,SAAS,CAAE6B,qBAAqB,IAAI;UACnC,MAAMC,gBAAgB,GAAG,CACvB,GAAGD,qBAAqB,EACxB,GAAGV,aAAa,CACjB;UACA,IAAI,CAACjD,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAAC6B,gBAAgB,CAAC;UAEjE;UACA,MAAMR,WAAW,GAAGQ,gBAAgB,CAAC9N,MAAM,CACxCuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CACjB,CAACnC,MAAM;UACP,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;UAEhE;UACA,IAAI,CAACG,uBAAuB,CAACK,gBAAgB,CAAC;QAChD,CAAC,CAAC;QAEJ,IAAI,CAAC1F,WAAW,GAAG,KAAK;QACxB,IAAI,CAACkC,oBAAoB,GACvB,IAAI,CAACJ,cAAc,CAACI,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGqD,GAAU,IAAI;QACpBvC,OAAO,CAACd,KAAK,CACX,6DAA6D,EAC7DqD,GAAG,CACJ;QACD,IAAI,CAACxF,WAAW,GAAG,KAAK;QACxB,IAAI,CAACkC,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EACAuC,kBAAkBA,CAAA;IAChB,IAAI,CAAC3C,cAAc,CAChB6D,2BAA2B,EAAE,CAC7Bb,IAAI,CACHtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,EACxBnL,UAAU,CAAE6K,KAAK,IAAI;MACnBc,OAAO,CAACd,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOhL,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAyM,SAAS,EAAE;IACd,IAAI,CAAC9B,cAAc,CAChB8D,4BAA4B,EAAE,CAC9Bd,IAAI,CACHtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,EACxBnL,UAAU,CAAE6K,KAAK,IAAI;MACnBc,OAAO,CAACd,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAOhL,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAyM,SAAS,EAAE;EAChB;EACApG,UAAUA,CAACqI,cAAsB;IAC/B5C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2C,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB5C,OAAO,CAACd,KAAK,CAAC,0BAA0B,EAAE0D,cAAc,CAAC;MACzD,IAAI,CAAC1D,KAAK,GAAG,IAAI2D,KAAK,CAAC,yBAAyB,CAAC;MACjD;;IAGF;IACA,IAAI,CAACnD,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAM/B,YAAY,GAAG+B,aAAa,CAACgB,IAAI,CAAEZ,CAAC,IAAKA,CAAC,CAACpI,EAAE,KAAK8I,cAAc,CAAC;MACvE,IAAI7C,YAAY,EAAE;QAChBC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDnG,EAAE,EAAEiG,YAAY,CAACjG,EAAE;UACnBsC,IAAI,EAAE2D,YAAY,CAAC3D,IAAI;UACvBT,MAAM,EAAEoE,YAAY,CAACpE;SACtB,CAAC;QAEF;QACA,MAAMoH,oBAAoB,GAAGjB,aAAa,CAACxN,GAAG,CAAE4N,CAAC,IAC/CA,CAAC,CAACpI,EAAE,KAAK8I,cAAc,GACnB;UAAE,GAAGV,CAAC;UAAEvG,MAAM,EAAE,IAAI;UAAEqH,MAAM,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;QAAE,CAAE,GACxDhB,CAAC,CACN;QAED;QACC,IAAI,CAACrD,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACmC,oBAAoB,CAAC;QAErE;QACA,MAAMd,WAAW,GAAGc,oBAAoB,CAACpO,MAAM,CAC5CuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CACjB,CAACnC,MAAM;QACP,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;QAEhE;QACA,IAAI,CAACG,uBAAuB,CAACW,oBAAoB,CAAC;QAElD;QACA,IAAI,CAAClE,cAAc,CAChBtE,UAAU,CAAC,CAACqI,cAAc,CAAC,CAAC,CAC5Bf,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;UACTC,IAAI,EAAGuC,MAAM,IAAI;YACfnD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkD,MAAM,CAAC;YAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;cAC5BpD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;cACvD;cACA,IAAI,IAAI,CAACf,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC7F,OAAO,CAACgH,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACrD,IAAI,CAACnB,KAAK,GAAG,IAAI;;;UAGvB,CAAC;UACDA,KAAK,EAAGqD,GAAG,IAAI;YACbvC,OAAO,CAACd,KAAK,CAAC,qCAAqC,EAAEqD,GAAG,CAAC;YACzDvC,OAAO,CAACd,KAAK,CAAC,gBAAgB,EAAE;cAC9B7F,OAAO,EAAEkJ,GAAG,CAAClJ,OAAO;cACpBgK,KAAK,EAAEd,GAAG,CAACc,KAAK;cAChBT;aACD,CAAC;YACF;YACA;UACF;SACD,CAAC;OACL,MAAM;QACL5C,OAAO,CAACsD,IAAI,CAAC,wCAAwC,EAAEV,cAAc,CAAC;QAEtE;QACA,IAAI,CAACnM,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEA;EACQ2L,uBAAuBA,CAACN,aAAoB;IAClD;IACA,MAAMyB,iBAAiB,GAAI,IAAI,CAAC1E,cAAsB,CAAC0E,iBAAiB;IACxE,IAAIA,iBAAiB,EAAE;MACrBzB,aAAa,CAAC0B,OAAO,CAAEzD,YAAY,IAAI;QACrCwD,iBAAiB,CAACE,GAAG,CAAC1D,YAAY,CAACjG,EAAE,EAAEiG,YAAY,CAAC;MACtD,CAAC,CAAC;MAEF;MACA,MAAMkC,WAAW,GAAGH,aAAa,CAACnN,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACnC,MAAM;MAChE,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;MAEhEjC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEgC,WAAW,CAAC;;EAE7E;EAEAjM,aAAaA,CAAA;IACXgK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD,IAAI,CAACP,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6B,aAAa,CAAC;MAChD,MAAM4B,SAAS,GAAG5B,aAAa,CAACnN,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACrH,GAAG,CAAE4N,CAAC,IAAKA,CAAC,CAACpI,EAAE,CAAC;MAEzEkG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEyD,SAAS,CAAC;MAElE,IAAIA,SAAS,CAAClK,MAAM,KAAK,CAAC,EAAE;QAC1BwG,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD;;MAGF;MACA,MAAM0D,QAAQ,GAAGD,SAAS,CAAC/O,MAAM,CAC9BmF,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC8J,IAAI,EAAE,KAAK,EAAE,CACzD;MAED,IAAID,QAAQ,CAACnK,MAAM,KAAKkK,SAAS,CAAClK,MAAM,EAAE;QACxCwG,OAAO,CAACd,KAAK,CAAC,oCAAoC,EAAEwE,SAAS,CAAC;QAC9D,IAAI,CAACxE,KAAK,GAAG,IAAI2D,KAAK,CAAC,0BAA0B,CAAC;QAClD;;MAGF7C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0D,QAAQ,CAAC;MAE3D;MACA,MAAMZ,oBAAoB,GAAGjB,aAAa,CAACxN,GAAG,CAAE4N,CAAC,IAC/CyB,QAAQ,CAACtD,QAAQ,CAAC6B,CAAC,CAACpI,EAAE,CAAC,GACnB;QAAE,GAAGoI,CAAC;QAAEvG,MAAM,EAAE,IAAI;QAAEqH,MAAM,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACxDhB,CAAC,CACN;MAED;MACC,IAAI,CAACrD,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACmC,oBAAoB,CAAC;MAErE;MACA,MAAMd,WAAW,GAAGc,oBAAoB,CAACpO,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACnC,MAAM;MACvE,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;MAEhE;MACA,IAAI,CAACG,uBAAuB,CAACW,oBAAoB,CAAC;MAElD;MACA,IAAI,CAAClE,cAAc,CAChBtE,UAAU,CAACoJ,QAAQ,CAAC,CACpB9B,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;QACTC,IAAI,EAAGuC,MAAM,IAAI;UACfnD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkD,MAAM,CAAC;UAC/C,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5BpD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC5D;YACA,IAAI,IAAI,CAACf,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC7F,OAAO,CAACgH,QAAQ,CAAC,MAAM,CAAC,EAAE;cACrD,IAAI,CAACnB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGqD,GAAG,IAAI;UACbvC,OAAO,CAACd,KAAK,CAAC,0CAA0C,EAAEqD,GAAG,CAAC;UAC9DvC,OAAO,CAACd,KAAK,CAAC,gBAAgB,EAAE;YAC9B7F,OAAO,EAAEkJ,GAAG,CAAClJ,OAAO;YACpBgK,KAAK,EAAEd,GAAG,CAACc;WACZ,CAAC;UACF;UACA;QACF;OACD,CAAC;IACN,CAAC,CAAC;EACJ;;EAEA/L,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACoI,cAAc,CAACmC,IAAI,CAC7BvN,GAAG,CAAEwN,aAAa,IAAKA,aAAa,EAAEtI,MAAM,GAAG,CAAC,CAAC,CAClD;EACH;EACAqK,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAClM,YAAY,CAACkK,IAAI,CAACvN,GAAG,CAAEwP,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EAC1D;EAEA;;;EAGAhN,kBAAkBA,CAAA;IAChB,IAAI,CAACU,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1CwI,OAAO,CAACC,GAAG,CACT,qCACE,IAAI,CAACzI,cAAc,GAAG,QAAQ,GAAG,WACnC,EAAE,CACH;IAED,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB;MACA,IAAI,CAACsF,sBAAsB,GACzB,IAAI,CAAC+B,cAAc,CAACkF,sBAAsB,EAAE;KAC/C,MAAM;MACL;MACA,IAAI,CAACjH,sBAAsB,GAAG,IAAI,CAAC4C,cAAc;;EAErD;EAEA;;;EAGAzI,WAAWA,CAAA;IACT,IAAI,CAACQ,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCuI,OAAO,CAACC,GAAG,CACT,yBAAyB,IAAI,CAACxI,YAAY,GAAG,WAAW,GAAG,QAAQ,EAAE,CACtE;IAED;IACA,IAAI,CAACoH,cAAc,CAAC0C,QAAQ,CAAC,IAAI,CAAC9J,YAAY,CAAC;IAE/C;IACA,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;MACtBuI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA+D,UAAU,CAAC,MAAK;QACd;QACA,IAAI,CAACnF,cAAc,CAACoF,qBAAqB,EAAE;QAE3C;QACAD,UAAU,CAAC,MAAK;UACd,IAAI,CAACnF,cAAc,CAACoF,qBAAqB,EAAE;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;;IAGT;IACA5C,YAAY,CAAC6C,OAAO,CAClB,wBAAwB,EACxB,IAAI,CAACzM,YAAY,CAAC0M,QAAQ,EAAE,CAC7B;EACH;EAEA;;;;EAIAtK,0BAA0BA,CAAC+I,cAAsB;IAC/C,IAAI,CAACA,cAAc,EAAE;MACnB5C,OAAO,CAACd,KAAK,CAAC,6BAA6B,CAAC;MAC5C;;IAGFc,OAAO,CAACC,GAAG,CACT,wDAAwD2C,cAAc,EAAE,CACzE;IAED;IACA,IAAI,CAAClE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACW,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAIW,YAAsC;IAE1C;IACA,IAAI,CAACL,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D/B,YAAY,GAAG+B,aAAa,CAACgB,IAAI,CAC9BZ,CAAe,IAAKA,CAAC,CAACpI,EAAE,KAAK8I,cAAc,CAC7C;IACH,CAAC,CAAC;IAEF,IACE7C,YAAY,IACZA,YAAY,CAAC1G,OAAO,IACpB0G,YAAY,CAAC1G,OAAO,CAACE,WAAW,IAChCwG,YAAY,CAAC1G,OAAO,CAACE,WAAW,CAACC,MAAM,GAAG,CAAC,EAC3C;MACAwG,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9CF,YAAY,CAAC1G,OAAO,CAACE,WAAW,CACjC;MACD,IAAI,CAAC8F,kBAAkB,GAAG,KAAK;MAC/B;MACA,IAAI,CAACX,kBAAkB,GAAGqB,YAAY,CAAC1G,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC3D8P,UAAkC,KAChC;QACCtK,EAAE,EAAE,EAAE;QACNuD,GAAG,EAAE+G,UAAU,CAAC/G,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACiI,qBAAqB,CAACD,UAAU,CAAChI,IAAI,CAAC;QACjDkC,IAAI,EAAE8F,UAAU,CAAC9F,IAAI,IAAI,EAAE;QAC3B5F,IAAI,EAAE0L,UAAU,CAAC1L,IAAI,IAAI,CAAC;QAC1B4L,QAAQ,EAAE,CAAC,CAAE;OACC,EACnB;;MACD;;IAGF;IACA,IAAI,CAACzF,cAAc,CAChBhF,0BAA0B,CAAC+I,cAAc,CAAC,CAC1Cf,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;MACTC,IAAI,EAAGrH,WAAW,IAAI;QACpByG,OAAO,CAACC,GAAG,CACT,GAAG1G,WAAW,CAACC,MAAM,uCAAuC,EAC5DD,WAAW,CACZ;QACD,IAAI,CAAC8F,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACX,kBAAkB,GAAGnF,WAAW;MACvC,CAAC;MACD2F,KAAK,EAAGqD,GAAG,IAAI;QACbvC,OAAO,CAACd,KAAK,CACX,mDAAmD,EACnDqD,GAAG,CACJ;QACD,IAAI,CAAClD,kBAAkB,GAAG,KAAK;MACjC;KACD,CAAC;EACN;EAEA;;;EAGAkF,qBAAqBA,CAAA;IACnB,IAAI,CAACnF,oBAAoB,GAAG,KAAK;EACnC;EAEA;;;;EAIA/D,uBAAuBA,CAAC0E,YAA0B;IAChDC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,YAAY,CAAC;IACtE,IAAI,CAACR,mBAAmB,GAAGQ,YAAY;IACvC,IAAI,CAACT,4BAA4B,GAAG,IAAI;IAExC;IACA,IAAI,CAACS,YAAY,CAACpE,MAAM,EAAE;MACxB,IAAI,CAACpB,UAAU,CAACwF,YAAY,CAACjG,EAAE,CAAC;;EAEpC;EAEA;;;EAGA0K,6BAA6BA,CAAA;IAC3B,IAAI,CAAClF,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACjC;EAEA;;;;;EAKAlB,OAAOA,CAACjC,IAAY;IAClB,OAAOA,IAAI,EAAEqI,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;EAC5C;EAEA;;;;;EAKAjH,WAAWA,CAACpB,IAAY;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,aAAa;IAE/B,IAAIA,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAIrI,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAIrI,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAIrI,IAAI,CAACqI,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,iBAAiB;IACtD,IAAIrI,IAAI,CAACiE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAClD,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,MAAM,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,UAAU,CAAC,EACpD,OAAO,kBAAkB;IAC3B,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,OAAO,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,YAAY,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,wBAAwB;IACjC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,KAAK,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,YAAY,CAAC,EACrD,OAAO,qBAAqB;IAE9B,OAAO,aAAa;EACtB;EAEA;;;;;EAKA9B,gBAAgBA,CAACnC,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAE3B,IAAIA,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAIrI,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAIrI,IAAI,CAACqI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAIrI,IAAI,CAACqI,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC5C,IAAIrI,IAAI,CAACiE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;IACtC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,MAAM,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACzE,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,OAAO,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,YAAY,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,cAAc;IACvB,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,KAAK,CAAC,IAAIjE,IAAI,CAACiE,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,SAAS;IAEzE,OAAO,SAAS;EAClB;EAEA;;;;;EAKA3C,cAAcA,CAAChF,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAMgM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,aAAa,GAAGlM,IAAI;IAExB,OAAOkM,aAAa,IAAI,IAAI,IAAID,CAAC,GAAGD,KAAK,CAAClL,MAAM,GAAG,CAAC,EAAE;MACpDoL,aAAa,IAAI,IAAI;MACrBD,CAAC,EAAE;;IAGL,OAAO,GAAGC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,CAAC,CAAC,EAAE;EAClD;EAEA;;;;EAIAvH,cAAcA,CAACC,GAAW;IACxB,IAAI,CAACA,GAAG,EAAE;IACVkD,MAAM,CAACuE,IAAI,CAACzH,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEA;;;;EAIAc,kBAAkBA,CAACiG,UAAsB;IACvC,IAAI,CAACA,UAAU,EAAE/G,GAAG,EAAE;IAEtB,MAAM0H,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACtE,IAAI,GAAG2D,UAAU,CAAC/G,GAAG;IAC1B0H,IAAI,CAACG,QAAQ,GAAGd,UAAU,CAAC9F,IAAI,IAAI,YAAY;IAC/CyG,IAAI,CAACjE,MAAM,GAAG,QAAQ;IACtBkE,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;IAC/BA,IAAI,CAACM,KAAK,EAAE;IACZL,QAAQ,CAACG,IAAI,CAACG,WAAW,CAACP,IAAI,CAAC;EACjC;EAEAQ,mBAAmBA,CAACxF,YAA0B;IAC5C,IAAI,CAACxF,UAAU,CAACwF,YAAY,CAACjG,EAAE,CAAC;EAClC;EAEA;;;;EAIA2B,kBAAkBA,CAACmH,cAAsB;IACvC5C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2C,cAAc,CAAC;IAE9D,IAAI,CAACA,cAAc,EAAE;MACnB5C,OAAO,CAACd,KAAK,CAAC,6BAA6B,CAAC;MAC5C,IAAI,CAACA,KAAK,GAAG,IAAI2D,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF;IACA,MAAMlB,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D;IACAD,sBAAsB,CAAC6D,GAAG,CAAC5C,cAAc,CAAC;IAE1C;IACA,IAAI,CAAC6C,0BAA0B,CAAC9D,sBAAsB,CAAC;IAEvD;IACA,IAAI,CAAC9C,cAAc,CAChBpD,kBAAkB,CAACmH,cAAc,CAAC,CAClCf,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;MACTC,IAAI,EAAGuC,MAAM,IAAI;QACfnD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkD,MAAM,CAAC;QAClD,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5BpD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD;UACA,IAAI,IAAI,CAACf,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC7F,OAAO,CAACgH,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC5D,IAAI,CAACnB,KAAK,GAAG,IAAI;;;MAGvB,CAAC;MACDA,KAAK,EAAGqD,GAAG,IAAI;QACbvC,OAAO,CAACd,KAAK,CACX,mDAAmD,EACnDqD,GAAG,CACJ;QACD;QACA,IAAI,CAACrD,KAAK,GAAGqD,GAAG;MAClB;KACD,CAAC;EACN;EAEA;;;EAGAlM,sBAAsBA,CAAA;IACpB2J,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,IAAI,CAACP,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D;MACA,MAAMH,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;MAE/D;MACAE,aAAa,CAAC0B,OAAO,CAAEzD,YAAY,IAAI;QACrC4B,sBAAsB,CAAC6D,GAAG,CAACzF,YAAY,CAACjG,EAAE,CAAC;MAC7C,CAAC,CAAC;MAEF;MACA,IAAI,CAAC2L,0BAA0B,CAAC9D,sBAAsB,CAAC;MAEvD;MACA,IAAI,CAAC9C,cAAc,CAChBxI,sBAAsB,EAAE,CACxBwL,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;QACTC,IAAI,EAAGuC,MAAM,IAAI;UACfnD,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDkD,MAAM,CACP;UACD,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5BpD,OAAO,CAACC,GAAG,CACT,GAAGkD,MAAM,CAACW,KAAK,uCAAuC,CACvD;YACD;YACA,IAAI,IAAI,CAAC5E,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC7F,OAAO,CAACgH,QAAQ,CAAC,aAAa,CAAC,EAAE;cAC5D,IAAI,CAACnB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGqD,GAAG,IAAI;UACbvC,OAAO,CAACd,KAAK,CACX,4DAA4D,EAC5DqD,GAAG,CACJ;UACD;UACA,IAAI,CAACrD,KAAK,GAAGqD,GAAG;QAClB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEAvJ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACkG,KAAK,EAAE7F,OAAO,IAAI,wBAAwB;EACxD;EAEA;;;;EAIQuI,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAM8D,cAAc,GAAGrE,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MACrE,IAAIoE,cAAc,EAAE;QAClB,OAAO,IAAIvG,GAAG,CAASwG,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC;;MAEpD,OAAO,IAAIvG,GAAG,EAAU;KACzB,CAAC,OAAOD,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CACX,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIC,GAAG,EAAU;;EAE5B;EAEA;;;;EAIQsG,0BAA0BA,CAACI,UAAuB;IACxD,IAAI;MACFxE,YAAY,CAAC6C,OAAO,CAClB,wBAAwB,EACxByB,IAAI,CAACG,SAAS,CAACC,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC,CACvC;MACD7F,OAAO,CAACC,GAAG,CACT,GAAG4F,UAAU,CAACnN,IAAI,mEAAmE,CACtF;KACF,CAAC,OAAOwG,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CACX,mEAAmE,EACnEA,KAAK,CACN;;EAEL;EAEA+G,WAAWA,CAAA;IACT,IAAI,CAACzG,QAAQ,CAACoB,IAAI,EAAE;IACpB,IAAI,CAACpB,QAAQ,CAAC0G,QAAQ,EAAE;EAC1B;EAEA;;;;;EAKArL,eAAeA,CAAC+H,cAAsB,EAAEuD,KAAY;IAClDA,KAAK,CAACpM,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC1B,qBAAqB,CAAC2J,GAAG,CAACY,cAAc,CAAC,EAAE;MAClD,IAAI,CAACvK,qBAAqB,CAAC+N,MAAM,CAACxD,cAAc,CAAC;KAClD,MAAM;MACL,IAAI,CAACvK,qBAAqB,CAACmN,GAAG,CAAC5C,cAAc,CAAC;;IAGhD;IACA,IAAI,CAACyD,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;EAC7D;EAEA;;;;EAIApD,eAAeA,CAAC6Q,KAAY;IAC1BA,KAAK,CAACpM,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,CAACnE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACkH,sBAAsB,CAAC+E,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,IAAI,CAAClM,WAAW,EAAE;QACpB;QACAkM,aAAa,CAAC0B,OAAO,CAAEzD,YAAY,IAAI;UACrC,IAAI,CAAC1H,qBAAqB,CAACmN,GAAG,CAACzF,YAAY,CAACjG,EAAE,CAAC;QACjD,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACzB,qBAAqB,CAACC,KAAK,EAAE;;MAGpC;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEA;;;EAGQ2N,oBAAoBA,CAAA;IAC1B,IAAI,CAACvJ,sBAAsB,CAAC+E,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MACpE,IAAI,CAAClM,WAAW,GACdkM,aAAa,CAACtI,MAAM,GAAG,CAAC,IACxB,IAAI,CAACnB,qBAAqB,CAACK,IAAI,KAAKoJ,aAAa,CAACtI,MAAM;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGAtB,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACG,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;MACzC;;IAGF,MAAM4N,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3N,qBAAqB,CAAC;IAC1D2H,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEqG,WAAW,CAAC;IAExE;IACA,IAAI,CAAC5G,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMiB,oBAAoB,GAAGjB,aAAa,CAACnN,MAAM,CAC9CoL,YAAY,IAAK,CAAC,IAAI,CAAC1H,qBAAqB,CAAC2J,GAAG,CAACjC,YAAY,CAACjG,EAAE,CAAC,CACnE;MAED;MACC,IAAI,CAAC+E,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACmC,oBAAoB,CAAC;MAErE;MACA,MAAMd,WAAW,GAAGc,oBAAoB,CAACpO,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACnC,MAAM;MACvE,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;MAEhE;MACA,IAAI,CAACG,uBAAuB,CAACW,oBAAoB,CAAC;MAElD;MACA,IAAI,CAAC1K,qBAAqB,CAACC,KAAK,EAAE;MAClC,IAAI,CAAC1C,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC2C,gBAAgB,GAAG,KAAK;IAC/B,CAAC,CAAC;IAEF;IACA,IAAI,CAACsG,cAAc,CAChB0H,2BAA2B,CAACD,WAAW,CAAC,CACxCzE,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;MACTC,IAAI,EAAGuC,MAAM,IAAI;QACfnD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEkD,MAAM,CAAC;QAC3D,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5BpD,OAAO,CAACC,GAAG,CAAC,GAAGkD,MAAM,CAACW,KAAK,uCAAuC,CAAC;;MAEvE,CAAC;MACD5E,KAAK,EAAGqD,GAAG,IAAI;QACbvC,OAAO,CAACd,KAAK,CACX,2DAA2D,EAC3DqD,GAAG,CACJ;MACH;KACD,CAAC;EACN;EAEA;;;EAGAxK,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACM,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;MACzC;;IAGF,MAAM4N,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3N,qBAAqB,CAAC;IAC1D2H,OAAO,CAACC,GAAG,CACT,sDAAsD,EACtDqG,WAAW,CACZ;IAED;IACA,IAAI,CAAC5G,cAAc,CAACmC,IAAI,CAACrN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmM,SAAS,CAAEmB,aAAa,IAAI;MAC5D,MAAMiB,oBAAoB,GAAGjB,aAAa,CAACxN,GAAG,CAAEyL,YAAY,IAC1D,IAAI,CAAC1H,qBAAqB,CAAC2J,GAAG,CAACjC,YAAY,CAACjG,EAAE,CAAC,GAC3C;QAAE,GAAGiG,YAAY;QAAEpE,MAAM,EAAE,IAAI;QAAEqH,MAAM,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACnEnD,YAAY,CACjB;MAED;MACC,IAAI,CAAClB,cAAsB,CAACiD,aAAa,CAAClB,IAAI,CAACmC,oBAAoB,CAAC;MAErE;MACA,MAAMd,WAAW,GAAGc,oBAAoB,CAACpO,MAAM,CAAEuN,CAAC,IAAK,CAACA,CAAC,CAACvG,MAAM,CAAC,CAACnC,MAAM;MACvE,IAAI,CAACqF,cAAsB,CAACsD,iBAAiB,CAACvB,IAAI,CAACqB,WAAW,CAAC;MAEhE;MACA,IAAI,CAACG,uBAAuB,CAACW,oBAAoB,CAAC;MAElD;MACA,IAAI,CAAC1K,qBAAqB,CAACC,KAAK,EAAE;MAClC,IAAI,CAAC1C,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC2C,gBAAgB,GAAG,KAAK;IAC/B,CAAC,CAAC;IAEF;IACA,IAAI,CAACsG,cAAc,CAChBtE,UAAU,CAAC+L,WAAW,CAAC,CACvBzE,IAAI,CAACtN,SAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BmB,SAAS,CAAC;MACTC,IAAI,EAAGuC,MAAM,IAAI;QACfnD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkD,MAAM,CAAC;QACrD,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5BpD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAEhE,CAAC;MACDf,KAAK,EAAGqD,GAAG,IAAI;QACbvC,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDqD,GAAG,CACJ;MACH;KACD,CAAC;EACN;EAEA;;;;;EAKA1G,UAAUA,CAAC+G,cAAsB;IAC/B,OAAO,IAAI,CAACvK,qBAAqB,CAAC2J,GAAG,CAACY,cAAc,CAAC;EACvD;EAEA;;;;;EAKQyB,qBAAqBA,CAACjI,IAAoB;IAChD,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAOhI,WAAW,CAACoS,KAAK;MAC1B,KAAK,MAAM;QACT,OAAOpS,WAAW,CAACqS,IAAI;MACzB,KAAK,OAAO;QACV,OAAOrS,WAAW,CAACsS,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOtS,WAAW,CAACuS,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOvS,WAAW,CAACwS,WAAW;MAChC,KAAK,MAAM;QACT,OAAOxS,WAAW,CAACyS,UAAU;MAC/B,KAAK,OAAO;QACV,OAAOzS,WAAW,CAAC0S,WAAW;MAChC,KAAK,OAAO;QACV,OAAO1S,WAAW,CAAC2S,WAAW;MAChC;QACE,OAAO3S,WAAW,CAACqS,IAAI;MAAE;;EAE/B;;;;uBAloCW9H,yBAAyB,EAAA/J,EAAA,CAAAoS,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtS,EAAA,CAAAoS,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAxS,EAAA,CAAAoS,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzB3I,yBAAyB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;mBAAzBC,GAAA,CAAAjL,QAAA,CAAA1H,MAAA,CAAA8L,MAAA,CAAuB;UAAA;;;;;;;;UClCpClM,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAY,SAAA,aAEM;UAENZ,EAAA,CAAAC,cAAA,aAAsE;UAGhED,EAAA,CAAAY,SAAA,WAAgC;UAChCZ,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UAGLX,EAAA,CAAA8B,UAAA,IAAAkR,wCAAA,mBA+DM;UAGNhT,EAAA,CAAA8B,UAAA,IAAAmR,wCAAA,kBAgCM;UACRjT,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAA8B,UAAA,KAAAoR,yCAAA,iBAGM;UAGNlT,EAAA,CAAA8B,UAAA,KAAAqR,yCAAA,kBAcM;UAGNnT,EAAA,CAAA8B,UAAA,KAAAsR,yCAAA,kBAYM;;UAGNpT,EAAA,CAAA8B,UAAA,KAAAuR,yCAAA,kBA0JM;;UACRrT,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAE,UAAA,mBAAAoT,yDAAA;YAAA,OAASP,GAAA,CAAApD,qBAAA,EAAuB;UAAA,EAAC;UAEjC3P,EAAA,CAAAC,cAAA,eAA2E;UAAnCD,EAAA,CAAAE,UAAA,mBAAAqT,yDAAAnT,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAY,SAAA,aAAqC;UACrCZ,EAAA,CAAAqB,MAAA,6BACF;UAAArB,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,kBAAyE;UAAlCD,EAAA,CAAAE,UAAA,mBAAAsT,4DAAA;YAAA,OAAST,GAAA,CAAApD,qBAAA,EAAuB;UAAA,EAAC;UACtE3P,EAAA,CAAAY,SAAA,aAA4B;UAC9BZ,EAAA,CAAAW,YAAA,EAAS;UAEXX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAA8B,UAAA,KAAA2R,yCAAA,iBAGM;UAENzT,EAAA,CAAA8B,UAAA,KAAA4R,yCAAA,kBAWM;UAEN1T,EAAA,CAAA8B,UAAA,KAAA6R,yCAAA,kBA6DM;UACR3T,EAAA,CAAAW,YAAA,EAAM;;;UA1ZRX,EAAA,CAAA2C,WAAA,SAAA3C,EAAA,CAAAwC,WAAA,QAAAuQ,GAAA,CAAA/H,WAAA,EAAkC;UAeDhL,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAc,UAAA,UAAAiS,GAAA,CAAApP,gBAAA,CAAuB;UAkEL3D,EAAA,CAAAa,SAAA,GAAsB;UAAtBb,EAAA,CAAAc,UAAA,SAAAiS,GAAA,CAAApP,gBAAA,CAAsB;UAoCjE3D,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAc,UAAA,SAAAiS,GAAA,CAAA3I,OAAA,CAAa;UAMbpK,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAAc,UAAA,SAAAiS,GAAA,CAAAzI,KAAA,CAAW;UAkBdtK,EAAA,CAAAa,SAAA,GAA+C;UAA/Cb,EAAA,CAAAc,UAAA,UAAAiS,GAAA,CAAA3I,OAAA,KAAApK,EAAA,CAAAwC,WAAA,SAAAuQ,GAAA,CAAArQ,gBAAA,IAA+C;UAe/C1C,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAAc,UAAA,UAAAiS,GAAA,CAAA3I,OAAA,IAAApK,EAAA,CAAAwC,WAAA,SAAAuQ,GAAA,CAAArQ,gBAAA,IAA8C;UAgKnD1C,EAAA,CAAAa,SAAA,GAAwD;UAAxDb,EAAA,CAAA4T,WAAA,YAAAb,GAAA,CAAAvI,oBAAA,mBAAwD;UAc9CxK,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAAc,UAAA,SAAAiS,GAAA,CAAAtI,kBAAA,CAAwB;UAM3BzK,EAAA,CAAAa,SAAA,GAA4D;UAA5Db,EAAA,CAAAc,UAAA,UAAAiS,GAAA,CAAAtI,kBAAA,IAAAsI,GAAA,CAAAjJ,kBAAA,CAAAlF,MAAA,OAA4D;UAa5D5E,EAAA,CAAAa,SAAA,GAA0D;UAA1Db,EAAA,CAAAc,UAAA,UAAAiS,GAAA,CAAAtI,kBAAA,IAAAsI,GAAA,CAAAjJ,kBAAA,CAAAlF,MAAA,KAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}