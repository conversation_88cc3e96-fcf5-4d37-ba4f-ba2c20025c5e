import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// Shared Module
import { SharedModule } from '../../../shared/shared.module';

// Components
import { TasksComponent } from './tasks.component';

const routes = [
  {
    path: '',
    component: TasksComponent
  },
  {
    path: ':teamId',
    component: TasksComponent
  }
];

@NgModule({
  declarations: [
    TasksComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule
  ]
})
export class TasksModule { }
