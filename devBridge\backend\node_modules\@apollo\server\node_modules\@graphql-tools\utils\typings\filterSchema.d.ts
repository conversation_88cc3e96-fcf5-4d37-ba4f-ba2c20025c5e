import { GraphQLSchema } from 'graphql';
import { <PERSON><PERSON><PERSON><PERSON>, RootFieldFilter, TypeFilter, ArgumentFilter } from './Interfaces.js';
export declare function filterSchema({ schema, typeFilter, fieldFilter, rootFieldFilter, objectFieldFilter, interfaceFieldFilter, inputObjectFieldFilter, argumentFilter, }: {
    schema: GraphQLSchema;
    rootFieldFilter?: RootFieldFilter;
    typeFilter?: TypeFilter;
    fieldFilter?: FieldFilter;
    objectFieldFilter?: FieldFilter;
    interfaceFieldFilter?: FieldFilter;
    inputObjectFieldFilter?: FieldFilter;
    argumentFilter?: ArgumentFilter;
}): GraphQLSchema;
