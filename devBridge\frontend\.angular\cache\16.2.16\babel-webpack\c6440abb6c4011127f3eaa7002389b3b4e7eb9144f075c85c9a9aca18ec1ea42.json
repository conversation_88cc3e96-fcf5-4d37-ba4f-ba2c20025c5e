{"ast": null, "code": "import { invariant } from '../../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType, isInputObjectType } from '../../../type/definition.mjs';\n\n/**\n * No deprecated\n *\n * A GraphQL document is only valid if all selected fields and all used enum values have not been\n * deprecated.\n *\n * Note: This rule is optional and is not part of the Validation section of the GraphQL\n * Specification. The main purpose of this rule is detection of deprecated usages and not\n * necessarily to forbid their use when querying a service.\n */\nexport function NoDeprecatedCustomRule(context) {\n  return {\n    Field(node) {\n      const fieldDef = context.getFieldDef();\n      const deprecationReason = fieldDef === null || fieldDef === void 0 ? void 0 : fieldDef.deprecationReason;\n      if (fieldDef && deprecationReason != null) {\n        const parentType = context.getParentType();\n        parentType != null || invariant(false);\n        context.reportError(new GraphQLError(`The field ${parentType.name}.${fieldDef.name} is deprecated. ${deprecationReason}`, {\n          nodes: node\n        }));\n      }\n    },\n    Argument(node) {\n      const argDef = context.getArgument();\n      const deprecationReason = argDef === null || argDef === void 0 ? void 0 : argDef.deprecationReason;\n      if (argDef && deprecationReason != null) {\n        const directiveDef = context.getDirective();\n        if (directiveDef != null) {\n          context.reportError(new GraphQLError(`Directive \"@${directiveDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        } else {\n          const parentType = context.getParentType();\n          const fieldDef = context.getFieldDef();\n          parentType != null && fieldDef != null || invariant(false);\n          context.reportError(new GraphQLError(`Field \"${parentType.name}.${fieldDef.name}\" argument \"${argDef.name}\" is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    ObjectField(node) {\n      const inputObjectDef = getNamedType(context.getParentInputType());\n      if (isInputObjectType(inputObjectDef)) {\n        const inputFieldDef = inputObjectDef.getFields()[node.name.value];\n        const deprecationReason = inputFieldDef === null || inputFieldDef === void 0 ? void 0 : inputFieldDef.deprecationReason;\n        if (deprecationReason != null) {\n          context.reportError(new GraphQLError(`The input field ${inputObjectDef.name}.${inputFieldDef.name} is deprecated. ${deprecationReason}`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    EnumValue(node) {\n      const enumValueDef = context.getEnumValue();\n      const deprecationReason = enumValueDef === null || enumValueDef === void 0 ? void 0 : enumValueDef.deprecationReason;\n      if (enumValueDef && deprecationReason != null) {\n        const enumTypeDef = getNamedType(context.getInputType());\n        enumTypeDef != null || invariant(false);\n        context.reportError(new GraphQLError(`The enum value \"${enumTypeDef.name}.${enumValueDef.name}\" is deprecated. ${deprecationReason}`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}