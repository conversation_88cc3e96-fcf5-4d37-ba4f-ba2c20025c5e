{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { finalize, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction EquipeListComponent_option_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction EquipeListComponent_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r8.label, \" \");\n  }\n}\nfunction EquipeListComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"div\", 49)(3, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 51);\n    i0.ɵɵtext(5, \" Chargement des \\u00E9quipes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"h3\", 57);\n    i0.ɵɵtext(7, \" Erreur de chargement des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 58);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_78_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.loadEquipes());\n    });\n    i0.ɵɵelement(11, \"i\", 60);\n    i0.ɵɵtext(12, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 64);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 65);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_79_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(8, \"i\", 67);\n    i0.ɵɵtext(9, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 120);\n    i0.ɵɵelement(1, \"i\", 121);\n    i0.ɵɵtext(2, \" Compl\\u00E8te \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 125);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" #\", tag_r23, \" \");\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 126);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equipe_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", equipe_r14.tags.length - 3, \" \");\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_80_div_1_div_20_span_1_Template, 2, 1, \"span\", 123);\n    i0.ɵɵtemplate(2, EquipeListComponent_div_80_div_1_div_20_span_2_Template, 2, 1, \"span\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equipe_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", equipe_r14.tags.slice(0, 3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equipe_r14.tags.length > 3);\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_66_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r26.navigateToEditEquipe(equipe_r14._id));\n    });\n    i0.ɵɵelement(1, \"i\", 128);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.archiveTeam(equipe_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 130);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_68_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.activateTeam(equipe_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 132);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r35.deleteEquipe(equipe_r14._id));\n    });\n    i0.ɵɵelement(1, \"i\", 134);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"div\", 9)(3, \"div\", 71);\n    i0.ɵɵelementStart(4, \"div\", 72)(5, \"div\", 73)(6, \"div\", 56)(7, \"div\", 74)(8, \"h3\", 75);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"span\", 77);\n    i0.ɵɵelement(14, \"i\", 78);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, EquipeListComponent_div_80_div_1_span_16_Template, 3, 0, \"span\", 79);\n    i0.ɵɵelementStart(17, \"span\", 80);\n    i0.ɵɵelement(18, \"i\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, EquipeListComponent_div_80_div_1_div_20_Template, 3, 2, \"div\", 82);\n    i0.ɵɵelementStart(21, \"div\", 83);\n    i0.ɵɵelement(22, \"i\", 84);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 85);\n    i0.ɵɵelement(25, \"i\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"p\", 87);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"slice\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 88)(30, \"div\", 89)(31, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r38.navigateToTasks(equipe_r14._id));\n    });\n    i0.ɵɵelement(32, \"div\", 91);\n    i0.ɵɵelementStart(33, \"div\", 92)(34, \"div\", 93);\n    i0.ɵɵelement(35, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 95);\n    i0.ɵɵtext(37, \"G\\u00E9rer les T\\u00E2ches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 96);\n    i0.ɵɵelement(39, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"div\", 98)(41, \"div\", 99)(42, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 101);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 102);\n    i0.ɵɵelement(46, \"i\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 104);\n    i0.ɵɵelement(48, \"div\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 106)(50, \"div\", 107);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 108)(53, \"div\", 109);\n    i0.ɵɵelement(54, \"div\", 110);\n    i0.ɵɵelementStart(55, \"span\", 111);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 109);\n    i0.ɵɵelement(58, \"i\", 112);\n    i0.ɵɵelementStart(59, \"span\", 111);\n    i0.ɵɵtext(60, \"Admin\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(61, \"div\", 113)(62, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_Template_button_click_62_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r40.navigateToEquipeDetail(equipe_r14._id));\n    });\n    i0.ɵɵelement(63, \"i\", 115);\n    i0.ɵɵtext(64, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 33);\n    i0.ɵɵtemplate(66, EquipeListComponent_div_80_div_1_button_66_Template, 2, 0, \"button\", 116);\n    i0.ɵɵtemplate(67, EquipeListComponent_div_80_div_1_button_67_Template, 2, 0, \"button\", 117);\n    i0.ɵɵtemplate(68, EquipeListComponent_div_80_div_1_button_68_Template, 2, 0, \"button\", 118);\n    i0.ɵɵtemplate(69, EquipeListComponent_div_80_div_1_button_69_Template, 2, 0, \"button\", 119);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"px-2 py-1 rounded-full text-xs font-medium \" + ctx_r13.getStatusBadgeClass(equipe_r14.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getStatusText(equipe_r14.status), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r13.getMemberCount(equipe_r14), \"/\", equipe_r14.maxMembers || 10, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.isTeamFull(equipe_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(equipe_r14.isPublic ? \"bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400\" : \"bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(equipe_r14.isPublic ? \"fas fa-globe\" : \"fas fa-lock\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.isPublic ? \"Publique\" : \"Priv\\u00E9e\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equipe_r14.tags && equipe_r14.tags.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r13.getAdminDisplayName(equipe_r14.admin), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.description && equipe_r14.description.length > 80 ? i0.ɵɵpipeBind3(28, 25, equipe_r14.description, 0, 80) + \"...\" : equipe_r14.description || \"Aucune description disponible\", \" \");\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getTaskCount(equipe_r14._id || \"\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.getTaskStatus(equipe_r14._id || \"\").percentage, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r13.getTaskStatus(equipe_r14._id || \"\").completed, \" / \", ctx_r13.getTaskStatus(equipe_r14._id || \"\").total, \" t\\u00E2ches termin\\u00E9es \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.getTaskStatus(equipe_r14._id || \"\").percentage, \"% compl\\u00E9t\\u00E9\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14) && equipe_r14.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14) && equipe_r14.status === \"archived\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canDeleteTeam(equipe_r14));\n  }\n}\nfunction EquipeListComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_80_div_1_Template, 70, 29, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.equipes);\n  }\n}\nfunction EquipeListComponent_div_81_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_button_7_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const i_r43 = restoredCtx.index;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onPageChange(i_r43 + 1));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r43 = ctx.index;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gradient-to-r\", ctx_r41.currentPage === i_r43 + 1)(\"from-[#4f5fad]\", ctx_r41.currentPage === i_r43 + 1)(\"to-[#7826b5]\", ctx_r41.currentPage === i_r43 + 1)(\"dark:from-[#00f7ff]\", ctx_r41.currentPage === i_r43 + 1)(\"dark:to-[#4f5fad]\", ctx_r41.currentPage === i_r43 + 1)(\"text-white\", ctx_r41.currentPage === i_r43 + 1)(\"text-[#6d6870]\", ctx_r41.currentPage !== i_r43 + 1)(\"dark:text-[#a0a0a0]\", ctx_r41.currentPage !== i_r43 + 1)(\"hover:text-[#4f5fad]\", ctx_r41.currentPage !== i_r43 + 1)(\"dark:hover:text-[#00f7ff]\", ctx_r41.currentPage !== i_r43 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r43 + 1, \" \");\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipeListComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 136)(2, \"div\", 33)(3, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onPageChange(ctx_r46.currentPage - 1));\n    });\n    i0.ɵɵelement(4, \"i\", 138);\n    i0.ɵɵtext(5, \" Pr\\u00E9c\\u00E9dent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 139);\n    i0.ɵɵtemplate(7, EquipeListComponent_div_81_button_7_Template, 2, 21, \"button\", 140);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onPageChange(ctx_r48.currentPage + 1));\n    });\n    i0.ɵɵtext(9, \" Suivant \");\n    i0.ɵɵelement(10, \"i\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 142)(12, \"span\", 83);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(6, _c0).constructor(ctx_r6.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" Page \", ctx_r6.currentPage, \" sur \", ctx_r6.totalPages, \" (\", ctx_r6.totalItems, \" \\u00E9quipe(s) au total) \");\n  }\n}\nexport let EquipeListComponent = /*#__PURE__*/(() => {\n  class EquipeListComponent {\n    constructor(equipeService, router, notificationService, authService) {\n      this.equipeService = equipeService;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.authService = authService;\n      this.equipes = [];\n      this.loading = false;\n      this.error = null;\n      // Nouvelles propriétés pour les fonctionnalités avancées\n      this.searchControl = new FormControl('');\n      this.statusFilter = new FormControl('all');\n      this.publicFilter = new FormControl('all');\n      this.currentPage = 1;\n      this.itemsPerPage = 10;\n      this.totalPages = 0;\n      this.totalItems = 0;\n      // Options pour les filtres\n      this.statusOptions = [{\n        value: 'all',\n        label: 'Tous les statuts'\n      }, {\n        value: 'active',\n        label: 'Actives'\n      }, {\n        value: 'inactive',\n        label: 'Inactives'\n      }, {\n        value: 'archived',\n        label: 'Archivées'\n      }];\n      this.publicOptions = [{\n        value: 'all',\n        label: 'Toutes'\n      }, {\n        value: 'true',\n        label: 'Publiques'\n      }, {\n        value: 'false',\n        label: 'Privées'\n      }];\n      // Colonnes affichées\n      this.displayedColumns = ['name', 'admin', 'members', 'status', 'actions'];\n      this.currentUser = this.authService.getCurrentUser();\n    }\n    ngOnInit() {\n      this.setupSearchSubscription();\n      this.setupFilterSubscriptions();\n      this.loadEquipes();\n    }\n    setupSearchSubscription() {\n      this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(() => {\n        this.currentPage = 1;\n        this.loadEquipes();\n      });\n    }\n    setupFilterSubscriptions() {\n      this.statusFilter.valueChanges.subscribe(() => {\n        this.currentPage = 1;\n        this.loadEquipes();\n      });\n      this.publicFilter.valueChanges.subscribe(() => {\n        this.currentPage = 1;\n        this.loadEquipes();\n      });\n    }\n    loadEquipes() {\n      this.loading = true;\n      this.error = null;\n      const filters = {\n        page: this.currentPage,\n        limit: this.itemsPerPage\n      };\n      // Ajouter les filtres si ils ne sont pas \"all\"\n      const searchValue = this.searchControl.value?.trim();\n      if (searchValue) {\n        filters.search = searchValue;\n      }\n      const statusValue = this.statusFilter.value;\n      if (statusValue && statusValue !== 'all') {\n        filters.status = statusValue;\n      }\n      const publicValue = this.publicFilter.value;\n      if (publicValue && publicValue !== 'all') {\n        filters.isPublic = publicValue === 'true';\n      }\n      this.equipeService.getEquipesWithFilters(filters).pipe(finalize(() => this.loading = false)).subscribe({\n        next: response => {\n          console.log('Équipes chargées:', response);\n          this.equipes = response.teams;\n          this.totalPages = response.pagination.total;\n          this.totalItems = response.pagination.totalItems;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes:', error);\n          this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n          this.notificationService.showError('Erreur lors du chargement des équipes');\n          // Fallback vers l'ancienne méthode\n          this.loadEquipesLegacy();\n        }\n      });\n    }\n    loadEquipesLegacy() {\n      this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          console.log('Équipes chargées (legacy):', data);\n          this.equipes = data;\n          this.totalItems = data.length;\n          this.totalPages = Math.ceil(data.length / this.itemsPerPage);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes (legacy):', error);\n          this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n          this.notificationService.showError('Erreur lors du chargement des équipes');\n        }\n      });\n    }\n    navigateToAddEquipe() {\n      this.router.navigate(['/equipes/ajouter']);\n    }\n    navigateToEditEquipe(id) {\n      this.router.navigate(['/equipes/modifier', id]);\n    }\n    navigateToEquipeDetail(id) {\n      this.router.navigate(['/equipes/detail', id]);\n    }\n    deleteEquipe(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n        this.loading = true;\n        this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n          next: () => {\n            console.log('Équipe supprimée avec succès');\n            this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n            this.loadEquipes();\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression de l\\'équipe:', error);\n            this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n            this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n          }\n        });\n      }\n    }\n    navigateToTasks(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      // Naviguer vers la page des tâches de l'équipe (route admin)\n      this.router.navigate(['/admin/tasks', id]);\n    }\n    // Méthode pour obtenir le nombre de tâches d'une équipe (simulé)\n    getTaskCount(equipeId) {\n      // TODO: Implémenter l'appel API réel pour obtenir le nombre de tâches\n      // Pour l'instant, retourner un nombre aléatoire pour la démonstration\n      const counts = [0, 5, 9, 15, 7, 11, 18, 3];\n      const index = equipeId.length % counts.length;\n      return counts[index];\n    }\n    // Méthode pour obtenir le statut des tâches d'une équipe\n    getTaskStatus(equipeId) {\n      const total = this.getTaskCount(equipeId);\n      const completed = Math.floor(total * 0.7); // 70% des tâches sont complétées (admin a plus de contrôle)\n      const percentage = total > 0 ? Math.round(completed / total * 100) : 0;\n      return {\n        completed,\n        total,\n        percentage\n      };\n    }\n    // Nouvelles méthodes pour les fonctionnalités avancées\n    /**\n     * Gestion de la pagination\n     */\n    onPageChange(page) {\n      this.currentPage = page;\n      this.loadEquipes();\n    }\n    /**\n     * Changer le nombre d'éléments par page\n     */\n    onItemsPerPageChange(itemsPerPage) {\n      this.itemsPerPage = itemsPerPage;\n      this.currentPage = 1;\n      this.loadEquipes();\n    }\n    /**\n     * Réinitialiser tous les filtres\n     */\n    resetFilters() {\n      this.searchControl.setValue('');\n      this.statusFilter.setValue('all');\n      this.publicFilter.setValue('all');\n      this.currentPage = 1;\n      this.loadEquipes();\n    }\n    /**\n     * Archiver une équipe\n     */\n    archiveTeam(equipe) {\n      if (!equipe._id) return;\n      if (confirm(`Êtes-vous sûr de vouloir archiver l'équipe \"${equipe.name}\" ?`)) {\n        this.loading = true;\n        this.equipeService.archiveTeam(equipe._id).pipe(finalize(() => this.loading = false)).subscribe({\n          next: response => {\n            this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été archivée`);\n            this.loadEquipes();\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'archivage:', error);\n            this.notificationService.showError('Erreur lors de l\\'archivage de l\\'équipe');\n          }\n        });\n      }\n    }\n    /**\n     * Activer une équipe\n     */\n    activateTeam(equipe) {\n      if (!equipe._id) return;\n      this.loading = true;\n      this.equipeService.activateTeam(equipe._id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: response => {\n          this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été activée`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'activation:', error);\n          this.notificationService.showError('Erreur lors de l\\'activation de l\\'équipe');\n        }\n      });\n    }\n    /**\n     * Vérifier si l'utilisateur peut modifier une équipe\n     */\n    canEditTeam(equipe) {\n      if (!this.currentUser) return false;\n      // Admin système peut tout modifier\n      if (this.currentUser.role === 'admin') return true;\n      // Admin de l'équipe peut modifier\n      return this.equipeService.isTeamAdmin(equipe, this.currentUser.id);\n    }\n    /**\n     * Vérifier si l'utilisateur peut supprimer une équipe\n     */\n    canDeleteTeam(equipe) {\n      return this.canEditTeam(equipe);\n    }\n    /**\n     * Obtenir le nom d'affichage d'un admin\n     */\n    getAdminDisplayName(admin) {\n      if (!admin) return 'Non assigné';\n      if (typeof admin === 'string') return admin;\n      return admin.fullName || admin.username || admin.email || 'Utilisateur';\n    }\n    /**\n     * Obtenir le nombre de membres d'une équipe\n     */\n    getMemberCount(equipe) {\n      return equipe.memberCount || equipe.members?.length || 0;\n    }\n    /**\n     * Obtenir le badge de statut\n     */\n    getStatusBadgeClass(status) {\n      switch (status) {\n        case 'active':\n          return 'badge-success';\n        case 'inactive':\n          return 'badge-warning';\n        case 'archived':\n          return 'badge-secondary';\n        default:\n          return 'badge-primary';\n      }\n    }\n    /**\n     * Obtenir le texte du statut\n     */\n    getStatusText(status) {\n      switch (status) {\n        case 'active':\n          return 'Active';\n        case 'inactive':\n          return 'Inactive';\n        case 'archived':\n          return 'Archivée';\n        default:\n          return 'Inconnue';\n      }\n    }\n    /**\n     * Vérifier si une équipe est pleine\n     */\n    isTeamFull(equipe) {\n      return equipe.isFullTeam || false;\n    }\n    /**\n     * Obtenir les slots disponibles\n     */\n    getAvailableSlots(equipe) {\n      return equipe.availableSlots || 0;\n    }\n    static {\n      this.ɵfac = function EquipeListComponent_Factory(t) {\n        return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeListComponent,\n        selectors: [[\"app-equipe-list\"]],\n        decls: 82,\n        vars: 13,\n        consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"mt-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"font-medium\"], [1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [1, \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-4\", \"gap-4\"], [1, \"lg:col-span-2\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Nom d'\\u00E9quipe, description, tags...\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-10\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"text-[#2d3748]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]\", \"dark:placeholder-[#a0a0a0]\", \"transition-all\", 3, \"formControl\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-filter\", \"mr-1\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"text-[#2d3748]\", \"dark:text-[#e0e0e0]\", \"transition-all\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-4\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"px-2\", \"py-1\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded\", \"text-xs\", 3, \"value\", \"change\"], [\"value\", \"5\"], [\"value\", \"10\"], [\"value\", \"20\"], [\"value\", \"50\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\", 4, \"ngIf\"], [\"class\", \"mt-8 flex items-center justify-center\", 4, \"ngIf\"], [3, \"value\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"mt-3\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"text-center\", \"py-16\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"xl:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-2\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-6\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-2\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"group-hover:scale-[1.02]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-xs\", \"mb-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [\"class\", \"bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded-full font-medium\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"mr-1\"], [\"class\", \"flex flex-wrap gap-1 mb-2\", 4, \"ngIf\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-crown\", \"mr-1\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-lg\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"line-clamp-2\", \"mb-4\"], [1, \"px-6\", \"pb-6\"], [1, \"mb-4\", \"relative\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"via-[#00e68a]\", \"to-[#00d4aa]\", \"hover:from-[#00e68a]\", \"hover:via-[#00d4aa]\", \"hover:to-[#00c199]\", \"text-white\", \"font-bold\", \"py-4\", \"px-6\", \"rounded-xl\", \"transition-all\", \"duration-500\", \"hover:scale-[1.03]\", \"hover:shadow-2xl\", \"hover:shadow-[#00ff9d]/30\", \"flex\", \"items-center\", \"justify-center\", \"group\", \"relative\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-transparent\", \"via-white/20\", \"to-transparent\", \"-skew-x-12\", \"-translate-x-full\", \"group-hover:translate-x-full\", \"transition-transform\", \"duration-1000\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-white/20\", \"rounded-full\", \"p-2\", \"mr-3\", \"group-hover:bg-white/30\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-tasks\", \"text-lg\", \"group-hover:scale-110\", \"group-hover:rotate-12\", \"transition-all\", \"duration-300\"], [1, \"text-lg\", \"tracking-wide\"], [1, \"ml-3\", \"bg-white/20\", \"rounded-full\", \"p-2\", \"group-hover:bg-white/30\", \"group-hover:translate-x-1\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-arrow-right\", \"text-sm\"], [1, \"absolute\", \"top-2\", \"right-4\", \"w-1\", \"h-1\", \"bg-white/60\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"bottom-3\", \"left-6\", \"w-1\", \"h-1\", \"bg-white/40\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.5s\"], [1, \"absolute\", \"top-4\", \"left-1/3\", \"w-0.5\", \"h-0.5\", \"bg-white/50\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"1s\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"bg-[#4f5fad]\", \"text-white\", \"text-xs\", \"font-bold\", \"rounded-full\", \"min-w-[24px]\", \"h-6\", \"px-2\", \"flex\", \"items-center\", \"justify-center\", \"animate-bounce\"], [1, \"absolute\", \"-top-2\", \"-left-2\", \"bg-[#f59e0b]\", \"text-white\", \"text-xs\", \"font-bold\", \"rounded-full\", \"w-5\", \"h-5\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-crown\", \"text-xs\"], [1, \"absolute\", \"-bottom-1\", \"left-2\", \"right-2\", \"bg-white/20\", \"rounded-full\", \"h-1\", \"overflow-hidden\"], [1, \"h-full\", \"bg-white/60\", \"rounded-full\", \"transition-all\", \"duration-1000\", \"ease-out\"], [1, \"mt-3\", \"text-center\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-xs\"], [1, \"flex\", \"items-center\"], [1, \"w-2\", \"h-2\", \"bg-[#00ff9d]\", \"rounded-full\", \"mr-1\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-shield-alt\", \"text-[#4f5fad]\", \"mr-1\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex\", \"items-center\", \"group/details\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\", \"group-hover/details:scale-110\", \"transition-transform\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\", \"title\", \"Modifier l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#f59e0b] hover:bg-[#f59e0b]/10 rounded-lg transition-all\", \"title\", \"Archiver l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#10b981] hover:bg-[#10b981]/10 rounded-lg transition-all\", \"title\", \"Activer l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\", \"title\", \"Supprimer l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/20\", \"text-orange-600\", \"dark:text-orange-400\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mb-2\"], [\"class\", \"bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-0.5 rounded text-xs\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-[#6d6870] dark:text-[#a0a0a0] text-xs\", 4, \"ngIf\"], [1, \"bg-[#4f5fad]/5\", \"dark:bg-[#00f7ff]/5\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-0.5\", \"rounded\", \"text-xs\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-xs\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"Archiver l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#f59e0b]\", \"hover:bg-[#f59e0b]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-archive\"], [\"title\", \"Activer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#10b981]\", \"hover:bg-[#10b981]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-play\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"mt-8\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-4\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-chevron-left\", \"mr-1\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"w-10 h-10 rounded-lg font-medium text-sm transition-all hover:scale-105\", 3, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\", \"ml-1\"], [1, \"mt-3\", \"pt-3\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"text-center\"], [1, \"w-10\", \"h-10\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"transition-all\", \"hover:scale-105\", 3, \"click\"]],\n        template: function EquipeListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n            i0.ɵɵtext(25, \" \\u00C9quipes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 15);\n            i0.ɵɵtext(27, \" G\\u00E9rez vos \\u00E9quipes et leurs membres avec style futuriste \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"span\", 17);\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31, \" \\u00E9quipe(s) au total \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_32_listener() {\n              return ctx.navigateToAddEquipe();\n            });\n            i0.ɵɵelement(33, \"i\", 19);\n            i0.ɵɵtext(34, \" Nouvelle \\u00C9quipe \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(35, \"div\", 20)(36, \"div\", 11)(37, \"div\", 21)(38, \"div\", 22)(39, \"label\", 23);\n            i0.ɵɵelement(40, \"i\", 24);\n            i0.ɵɵtext(41, \" Rechercher \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 25);\n            i0.ɵɵelement(43, \"input\", 26)(44, \"i\", 27);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\")(46, \"label\", 23);\n            i0.ɵɵelement(47, \"i\", 28);\n            i0.ɵɵtext(48, \" Statut \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"select\", 29);\n            i0.ɵɵtemplate(50, EquipeListComponent_option_50_Template, 2, 2, \"option\", 30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\")(52, \"label\", 23);\n            i0.ɵɵelement(53, \"i\", 31);\n            i0.ɵɵtext(54, \" Visibilit\\u00E9 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"select\", 29);\n            i0.ɵɵtemplate(56, EquipeListComponent_option_56_Template, 2, 2, \"option\", 30);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"div\", 32)(58, \"div\", 33)(59, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_59_listener() {\n              return ctx.resetFilters();\n            });\n            i0.ɵɵelement(60, \"i\", 35);\n            i0.ɵɵtext(61, \" R\\u00E9initialiser \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 36)(63, \"span\");\n            i0.ɵɵtext(64);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"div\", 33)(66, \"label\");\n            i0.ɵɵtext(67, \"Par page:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"select\", 37);\n            i0.ɵɵlistener(\"change\", function EquipeListComponent_Template_select_change_68_listener($event) {\n              return ctx.onItemsPerPageChange(+$event.target.value);\n            });\n            i0.ɵɵelementStart(69, \"option\", 38);\n            i0.ɵɵtext(70, \"5\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"option\", 39);\n            i0.ɵɵtext(72, \"10\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"option\", 40);\n            i0.ɵɵtext(74, \"20\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"option\", 41);\n            i0.ɵɵtext(76, \"50\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(77, EquipeListComponent_div_77_Template, 6, 0, \"div\", 42);\n            i0.ɵɵtemplate(78, EquipeListComponent_div_78_Template, 13, 1, \"div\", 43);\n            i0.ɵɵtemplate(79, EquipeListComponent_div_79_Template, 10, 0, \"div\", 44);\n            i0.ɵɵtemplate(80, EquipeListComponent_div_80_Template, 2, 1, \"div\", 45);\n            i0.ɵɵtemplate(81, EquipeListComponent_div_81_Template, 14, 7, \"div\", 46);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(30);\n            i0.ɵɵtextInterpolate(ctx.totalItems);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"formControl\", ctx.publicFilter);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.publicOptions);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", ctx.equipes.length, \" r\\u00E9sultat(s)\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", ctx.itemsPerPage);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0 && ctx.totalPages > 1);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.FormControlDirective, i5.SlicePipe],\n        styles: [\".hover-shadow[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a!important}.transition[_ngcontent-%COMP%]{transition:all .3s ease}.card-header.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#6610f2)!important}\"]\n      });\n    }\n  }\n  return EquipeListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}