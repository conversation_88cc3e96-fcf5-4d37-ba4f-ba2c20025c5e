{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { Kind } from \"graphql\";\nimport { getFragmentFromSelection, getDefaultValues, getOperationDefinition, getTypenameFromResult, makeReference, isField, resultKeyNameFromField, isReference, shouldInclude, cloneDeep, addTypenameToDocument, isNonEmptyArray, argumentsObjectFromField, canonicalStringify } from \"../../utilities/index.js\";\nimport { isArray, makeProcessedFieldsMerger, fieldNameFromStoreName, storeValueIsStoreObject, extractFragmentContext } from \"./helpers.js\";\nimport { normalizeReadFieldOptions } from \"./policies.js\";\n// Since there are only four possible combinations of context.clientOnly and\n// context.deferred values, we should need at most four \"flavors\" of any given\n// WriteContext. To avoid creating multiple copies of the same context, we cache\n// the contexts in the context.flavors Map (shared by all flavors) according to\n// their clientOnly and deferred values (always in that order).\nfunction getContextFlavor(context, clientOnly, deferred) {\n  var key = \"\".concat(clientOnly).concat(deferred);\n  var flavored = context.flavors.get(key);\n  if (!flavored) {\n    context.flavors.set(key, flavored = context.clientOnly === clientOnly && context.deferred === deferred ? context : __assign(__assign({}, context), {\n      clientOnly: clientOnly,\n      deferred: deferred\n    }));\n  }\n  return flavored;\n}\nvar StoreWriter = /** @class */function () {\n  function StoreWriter(cache, reader, fragments) {\n    this.cache = cache;\n    this.reader = reader;\n    this.fragments = fragments;\n  }\n  StoreWriter.prototype.writeToStore = function (store, _a) {\n    var _this = this;\n    var query = _a.query,\n      result = _a.result,\n      dataId = _a.dataId,\n      variables = _a.variables,\n      overwrite = _a.overwrite;\n    var operationDefinition = getOperationDefinition(query);\n    var merger = makeProcessedFieldsMerger();\n    variables = __assign(__assign({}, getDefaultValues(operationDefinition)), variables);\n    var context = __assign(__assign({\n      store: store,\n      written: Object.create(null),\n      merge: function (existing, incoming) {\n        return merger.merge(existing, incoming);\n      },\n      variables: variables,\n      varString: canonicalStringify(variables)\n    }, extractFragmentContext(query, this.fragments)), {\n      overwrite: !!overwrite,\n      incomingById: new Map(),\n      clientOnly: false,\n      deferred: false,\n      flavors: new Map()\n    });\n    var ref = this.processSelectionSet({\n      result: result || Object.create(null),\n      dataId: dataId,\n      selectionSet: operationDefinition.selectionSet,\n      mergeTree: {\n        map: new Map()\n      },\n      context: context\n    });\n    if (!isReference(ref)) {\n      throw newInvariantError(12, result);\n    }\n    // So far, the store has not been modified, so now it's time to process\n    // context.incomingById and merge those incoming fields into context.store.\n    context.incomingById.forEach(function (_a, dataId) {\n      var storeObject = _a.storeObject,\n        mergeTree = _a.mergeTree,\n        fieldNodeSet = _a.fieldNodeSet;\n      var entityRef = makeReference(dataId);\n      if (mergeTree && mergeTree.map.size) {\n        var applied = _this.applyMerges(mergeTree, entityRef, storeObject, context);\n        if (isReference(applied)) {\n          // Assume References returned by applyMerges have already been merged\n          // into the store. See makeMergeObjectsFunction in policies.ts for an\n          // example of how this can happen.\n          return;\n        }\n        // Otherwise, applyMerges returned a StoreObject, whose fields we should\n        // merge into the store (see store.merge statement below).\n        storeObject = applied;\n      }\n      if (globalThis.__DEV__ !== false && !context.overwrite) {\n        var fieldsWithSelectionSets_1 = Object.create(null);\n        fieldNodeSet.forEach(function (field) {\n          if (field.selectionSet) {\n            fieldsWithSelectionSets_1[field.name.value] = true;\n          }\n        });\n        var hasSelectionSet_1 = function (storeFieldName) {\n          return fieldsWithSelectionSets_1[fieldNameFromStoreName(storeFieldName)] === true;\n        };\n        var hasMergeFunction_1 = function (storeFieldName) {\n          var childTree = mergeTree && mergeTree.map.get(storeFieldName);\n          return Boolean(childTree && childTree.info && childTree.info.merge);\n        };\n        Object.keys(storeObject).forEach(function (storeFieldName) {\n          // If a merge function was defined for this field, trust that it\n          // did the right thing about (not) clobbering data. If the field\n          // has no selection set, it's a scalar field, so it doesn't need\n          // a merge function (even if it's an object, like JSON data).\n          if (hasSelectionSet_1(storeFieldName) && !hasMergeFunction_1(storeFieldName)) {\n            warnAboutDataLoss(entityRef, storeObject, storeFieldName, context.store);\n          }\n        });\n      }\n      store.merge(dataId, storeObject);\n    });\n    // Any IDs written explicitly to the cache will be retained as\n    // reachable root IDs for garbage collection purposes. Although this\n    // logic includes root IDs like ROOT_QUERY and ROOT_MUTATION, their\n    // retainment counts are effectively ignored because cache.gc() always\n    // includes them in its root ID set.\n    store.retain(ref.__ref);\n    return ref;\n  };\n  StoreWriter.prototype.processSelectionSet = function (_a) {\n    var _this = this;\n    var dataId = _a.dataId,\n      result = _a.result,\n      selectionSet = _a.selectionSet,\n      context = _a.context,\n      // This object allows processSelectionSet to report useful information\n      // to its callers without explicitly returning that information.\n      mergeTree = _a.mergeTree;\n    var policies = this.cache.policies;\n    // This variable will be repeatedly updated using context.merge to\n    // accumulate all fields that need to be written into the store.\n    var incoming = Object.create(null);\n    // If typename was not passed in, infer it. Note that typename is\n    // always passed in for tricky-to-infer cases such as \"Query\" for\n    // ROOT_QUERY.\n    var typename = dataId && policies.rootTypenamesById[dataId] || getTypenameFromResult(result, selectionSet, context.fragmentMap) || dataId && context.store.get(dataId, \"__typename\");\n    if (\"string\" === typeof typename) {\n      incoming.__typename = typename;\n    }\n    // This readField function will be passed as context.readField in the\n    // KeyFieldsContext object created within policies.identify (called below).\n    // In addition to reading from the existing context.store (thanks to the\n    // policies.readField(options, context) line at the very bottom), this\n    // version of readField can read from Reference objects that are currently\n    // pending in context.incomingById, which is important whenever keyFields\n    // need to be extracted from a child object that processSelectionSet has\n    // turned into a Reference.\n    var readField = function () {\n      var options = normalizeReadFieldOptions(arguments, incoming, context.variables);\n      if (isReference(options.from)) {\n        var info = context.incomingById.get(options.from.__ref);\n        if (info) {\n          var result_1 = policies.readField(__assign(__assign({}, options), {\n            from: info.storeObject\n          }), context);\n          if (result_1 !== void 0) {\n            return result_1;\n          }\n        }\n      }\n      return policies.readField(options, context);\n    };\n    var fieldNodeSet = new Set();\n    this.flattenFields(selectionSet, result,\n    // This WriteContext will be the default context value for fields returned\n    // by the flattenFields method, but some fields may be assigned a modified\n    // context, depending on the presence of @client and other directives.\n    context, typename).forEach(function (context, field) {\n      var _a;\n      var resultFieldKey = resultKeyNameFromField(field);\n      var value = result[resultFieldKey];\n      fieldNodeSet.add(field);\n      if (value !== void 0) {\n        var storeFieldName = policies.getStoreFieldName({\n          typename: typename,\n          fieldName: field.name.value,\n          field: field,\n          variables: context.variables\n        });\n        var childTree = getChildMergeTree(mergeTree, storeFieldName);\n        var incomingValue = _this.processFieldValue(value, field,\n        // Reset context.clientOnly and context.deferred to their default\n        // values before processing nested selection sets.\n        field.selectionSet ? getContextFlavor(context, false, false) : context, childTree);\n        // To determine if this field holds a child object with a merge function\n        // defined in its type policy (see PR #7070), we need to figure out the\n        // child object's __typename.\n        var childTypename = void 0;\n        // The field's value can be an object that has a __typename only if the\n        // field has a selection set. Otherwise incomingValue is scalar.\n        if (field.selectionSet && (isReference(incomingValue) || storeValueIsStoreObject(incomingValue))) {\n          childTypename = readField(\"__typename\", incomingValue);\n        }\n        var merge = policies.getMergeFunction(typename, field.name.value, childTypename);\n        if (merge) {\n          childTree.info = {\n            // TODO Check compatibility against any existing childTree.field?\n            field: field,\n            typename: typename,\n            merge: merge\n          };\n        } else {\n          maybeRecycleChildMergeTree(mergeTree, storeFieldName);\n        }\n        incoming = context.merge(incoming, (_a = {}, _a[storeFieldName] = incomingValue, _a));\n      } else if (globalThis.__DEV__ !== false && !context.clientOnly && !context.deferred && !addTypenameToDocument.added(field) &&\n      // If the field has a read function, it may be a synthetic field or\n      // provide a default value, so its absence from the written data should\n      // not be cause for alarm.\n      !policies.getReadFunction(typename, field.name.value)) {\n        globalThis.__DEV__ !== false && invariant.error(13, resultKeyNameFromField(field), result);\n      }\n    });\n    // Identify the result object, even if dataId was already provided,\n    // since we always need keyObject below.\n    try {\n      var _b = policies.identify(result, {\n          typename: typename,\n          selectionSet: selectionSet,\n          fragmentMap: context.fragmentMap,\n          storeObject: incoming,\n          readField: readField\n        }),\n        id = _b[0],\n        keyObject = _b[1];\n      // If dataId was not provided, fall back to the id just generated by\n      // policies.identify.\n      dataId = dataId || id;\n      // Write any key fields that were used during identification, even if\n      // they were not mentioned in the original query.\n      if (keyObject) {\n        // TODO Reverse the order of the arguments?\n        incoming = context.merge(incoming, keyObject);\n      }\n    } catch (e) {\n      // If dataId was provided, tolerate failure of policies.identify.\n      if (!dataId) throw e;\n    }\n    if (\"string\" === typeof dataId) {\n      var dataRef = makeReference(dataId);\n      // Avoid processing the same entity object using the same selection\n      // set more than once. We use an array instead of a Set since most\n      // entity IDs will be written using only one selection set, so the\n      // size of this array is likely to be very small, meaning indexOf is\n      // likely to be faster than Set.prototype.has.\n      var sets = context.written[dataId] || (context.written[dataId] = []);\n      if (sets.indexOf(selectionSet) >= 0) return dataRef;\n      sets.push(selectionSet);\n      // If we're about to write a result object into the store, but we\n      // happen to know that the exact same (===) result object would be\n      // returned if we were to reread the result with the same inputs,\n      // then we can skip the rest of the processSelectionSet work for\n      // this object, and immediately return a Reference to it.\n      if (this.reader && this.reader.isFresh(result, dataRef, selectionSet, context)) {\n        return dataRef;\n      }\n      var previous_1 = context.incomingById.get(dataId);\n      if (previous_1) {\n        previous_1.storeObject = context.merge(previous_1.storeObject, incoming);\n        previous_1.mergeTree = mergeMergeTrees(previous_1.mergeTree, mergeTree);\n        fieldNodeSet.forEach(function (field) {\n          return previous_1.fieldNodeSet.add(field);\n        });\n      } else {\n        context.incomingById.set(dataId, {\n          storeObject: incoming,\n          // Save a reference to mergeTree only if it is not empty, because\n          // empty MergeTrees may be recycled by maybeRecycleChildMergeTree and\n          // reused for entirely different parts of the result tree.\n          mergeTree: mergeTreeIsEmpty(mergeTree) ? void 0 : mergeTree,\n          fieldNodeSet: fieldNodeSet\n        });\n      }\n      return dataRef;\n    }\n    return incoming;\n  };\n  StoreWriter.prototype.processFieldValue = function (value, field, context, mergeTree) {\n    var _this = this;\n    if (!field.selectionSet || value === null) {\n      // In development, we need to clone scalar values so that they can be\n      // safely frozen with maybeDeepFreeze in readFromStore.ts. In production,\n      // it's cheaper to store the scalar values directly in the cache.\n      return globalThis.__DEV__ !== false ? cloneDeep(value) : value;\n    }\n    if (isArray(value)) {\n      return value.map(function (item, i) {\n        var value = _this.processFieldValue(item, field, context, getChildMergeTree(mergeTree, i));\n        maybeRecycleChildMergeTree(mergeTree, i);\n        return value;\n      });\n    }\n    return this.processSelectionSet({\n      result: value,\n      selectionSet: field.selectionSet,\n      context: context,\n      mergeTree: mergeTree\n    });\n  };\n  // Implements https://spec.graphql.org/draft/#sec-Field-Collection, but with\n  // some additions for tracking @client and @defer directives.\n  StoreWriter.prototype.flattenFields = function (selectionSet, result, context, typename) {\n    if (typename === void 0) {\n      typename = getTypenameFromResult(result, selectionSet, context.fragmentMap);\n    }\n    var fieldMap = new Map();\n    var policies = this.cache.policies;\n    var limitingTrie = new Trie(false); // No need for WeakMap, since limitingTrie does not escape.\n    (function flatten(selectionSet, inheritedContext) {\n      var visitedNode = limitingTrie.lookup(selectionSet,\n      // Because we take inheritedClientOnly and inheritedDeferred into\n      // consideration here (in addition to selectionSet), it's possible for\n      // the same selection set to be flattened more than once, if it appears\n      // in the query with different @client and/or @directive configurations.\n      inheritedContext.clientOnly, inheritedContext.deferred);\n      if (visitedNode.visited) return;\n      visitedNode.visited = true;\n      selectionSet.selections.forEach(function (selection) {\n        if (!shouldInclude(selection, context.variables)) return;\n        var clientOnly = inheritedContext.clientOnly,\n          deferred = inheritedContext.deferred;\n        if (\n        // Since the presence of @client or @defer on this field can only\n        // cause clientOnly or deferred to become true, we can skip the\n        // forEach loop if both clientOnly and deferred are already true.\n        !(clientOnly && deferred) && isNonEmptyArray(selection.directives)) {\n          selection.directives.forEach(function (dir) {\n            var name = dir.name.value;\n            if (name === \"client\") clientOnly = true;\n            if (name === \"defer\") {\n              var args = argumentsObjectFromField(dir, context.variables);\n              // The @defer directive takes an optional args.if boolean\n              // argument, similar to @include(if: boolean). Note that\n              // @defer(if: false) does not make context.deferred false, but\n              // instead behaves as if there was no @defer directive.\n              if (!args || args.if !== false) {\n                deferred = true;\n              }\n              // TODO In the future, we may want to record args.label using\n              // context.deferred, if a label is specified.\n            }\n          });\n        }\n\n        if (isField(selection)) {\n          var existing = fieldMap.get(selection);\n          if (existing) {\n            // If this field has been visited along another recursive path\n            // before, the final context should have clientOnly or deferred set\n            // to true only if *all* paths have the directive (hence the &&).\n            clientOnly = clientOnly && existing.clientOnly;\n            deferred = deferred && existing.deferred;\n          }\n          fieldMap.set(selection, getContextFlavor(context, clientOnly, deferred));\n        } else {\n          var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n          if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n            throw newInvariantError(14, selection.name.value);\n          }\n          if (fragment && policies.fragmentMatches(fragment, typename, result, context.variables)) {\n            flatten(fragment.selectionSet, getContextFlavor(context, clientOnly, deferred));\n          }\n        }\n      });\n    })(selectionSet, context);\n    return fieldMap;\n  };\n  StoreWriter.prototype.applyMerges = function (mergeTree, existing, incoming, context, getStorageArgs) {\n    var _a;\n    var _this = this;\n    if (mergeTree.map.size && !isReference(incoming)) {\n      var e_1 =\n      // Items in the same position in different arrays are not\n      // necessarily related to each other, so when incoming is an array\n      // we process its elements as if there was no existing data.\n      !isArray(incoming) && (\n      // Likewise, existing must be either a Reference or a StoreObject\n      // in order for its fields to be safe to merge with the fields of\n      // the incoming object.\n      isReference(existing) || storeValueIsStoreObject(existing)) ? existing : void 0;\n      // This narrowing is implied by mergeTree.map.size > 0 and\n      // !isReference(incoming), though TypeScript understandably cannot\n      // hope to infer this type.\n      var i_1 = incoming;\n      // The options.storage objects provided to read and merge functions\n      // are derived from the identity of the parent object plus a\n      // sequence of storeFieldName strings/numbers identifying the nested\n      // field name path of each field value to be merged.\n      if (e_1 && !getStorageArgs) {\n        getStorageArgs = [isReference(e_1) ? e_1.__ref : e_1];\n      }\n      // It's possible that applying merge functions to this subtree will\n      // not change the incoming data, so this variable tracks the fields\n      // that did change, so we can create a new incoming object when (and\n      // only when) at least one incoming field has changed. We use a Map\n      // to preserve the type of numeric keys.\n      var changedFields_1;\n      var getValue_1 = function (from, name) {\n        return isArray(from) ? typeof name === \"number\" ? from[name] : void 0 : context.store.getFieldValue(from, String(name));\n      };\n      mergeTree.map.forEach(function (childTree, storeFieldName) {\n        var eVal = getValue_1(e_1, storeFieldName);\n        var iVal = getValue_1(i_1, storeFieldName);\n        // If we have no incoming data, leave any existing data untouched.\n        if (void 0 === iVal) return;\n        if (getStorageArgs) {\n          getStorageArgs.push(storeFieldName);\n        }\n        var aVal = _this.applyMerges(childTree, eVal, iVal, context, getStorageArgs);\n        if (aVal !== iVal) {\n          changedFields_1 = changedFields_1 || new Map();\n          changedFields_1.set(storeFieldName, aVal);\n        }\n        if (getStorageArgs) {\n          invariant(getStorageArgs.pop() === storeFieldName);\n        }\n      });\n      if (changedFields_1) {\n        // Shallow clone i so we can add changed fields to it.\n        incoming = isArray(i_1) ? i_1.slice(0) : __assign({}, i_1);\n        changedFields_1.forEach(function (value, name) {\n          incoming[name] = value;\n        });\n      }\n    }\n    if (mergeTree.info) {\n      return this.cache.policies.runMergeFunction(existing, incoming, mergeTree.info, context, getStorageArgs && (_a = context.store).getStorage.apply(_a, getStorageArgs));\n    }\n    return incoming;\n  };\n  return StoreWriter;\n}();\nexport { StoreWriter };\nvar emptyMergeTreePool = [];\nfunction getChildMergeTree(_a, name) {\n  var map = _a.map;\n  if (!map.has(name)) {\n    map.set(name, emptyMergeTreePool.pop() || {\n      map: new Map()\n    });\n  }\n  return map.get(name);\n}\nfunction mergeMergeTrees(left, right) {\n  if (left === right || !right || mergeTreeIsEmpty(right)) return left;\n  if (!left || mergeTreeIsEmpty(left)) return right;\n  var info = left.info && right.info ? __assign(__assign({}, left.info), right.info) : left.info || right.info;\n  var needToMergeMaps = left.map.size && right.map.size;\n  var map = needToMergeMaps ? new Map() : left.map.size ? left.map : right.map;\n  var merged = {\n    info: info,\n    map: map\n  };\n  if (needToMergeMaps) {\n    var remainingRightKeys_1 = new Set(right.map.keys());\n    left.map.forEach(function (leftTree, key) {\n      merged.map.set(key, mergeMergeTrees(leftTree, right.map.get(key)));\n      remainingRightKeys_1.delete(key);\n    });\n    remainingRightKeys_1.forEach(function (key) {\n      merged.map.set(key, mergeMergeTrees(right.map.get(key), left.map.get(key)));\n    });\n  }\n  return merged;\n}\nfunction mergeTreeIsEmpty(tree) {\n  return !tree || !(tree.info || tree.map.size);\n}\nfunction maybeRecycleChildMergeTree(_a, name) {\n  var map = _a.map;\n  var childTree = map.get(name);\n  if (childTree && mergeTreeIsEmpty(childTree)) {\n    emptyMergeTreePool.push(childTree);\n    map.delete(name);\n  }\n}\nvar warnings = new Set();\n// Note that this function is unused in production, and thus should be\n// pruned by any well-configured minifier.\nfunction warnAboutDataLoss(existingRef, incomingObj, storeFieldName, store) {\n  var getChild = function (objOrRef) {\n    var child = store.getFieldValue(objOrRef, storeFieldName);\n    return typeof child === \"object\" && child;\n  };\n  var existing = getChild(existingRef);\n  if (!existing) return;\n  var incoming = getChild(incomingObj);\n  if (!incoming) return;\n  // It's always safe to replace a reference, since it refers to data\n  // safely stored elsewhere.\n  if (isReference(existing)) return;\n  // If the values are structurally equivalent, we do not need to worry\n  // about incoming replacing existing.\n  if (equal(existing, incoming)) return;\n  // If we're replacing every key of the existing object, then the\n  // existing data would be overwritten even if the objects were\n  // normalized, so warning would not be helpful here.\n  if (Object.keys(existing).every(function (key) {\n    return store.getFieldValue(incoming, key) !== void 0;\n  })) {\n    return;\n  }\n  var parentType = store.getFieldValue(existingRef, \"__typename\") || store.getFieldValue(incomingObj, \"__typename\");\n  var fieldName = fieldNameFromStoreName(storeFieldName);\n  var typeDotName = \"\".concat(parentType, \".\").concat(fieldName);\n  // Avoid warning more than once for the same type and field name.\n  if (warnings.has(typeDotName)) return;\n  warnings.add(typeDotName);\n  var childTypenames = [];\n  // Arrays do not have __typename fields, and always need a custom merge\n  // function, even if their elements are normalized entities.\n  if (!isArray(existing) && !isArray(incoming)) {\n    [existing, incoming].forEach(function (child) {\n      var typename = store.getFieldValue(child, \"__typename\");\n      if (typeof typename === \"string\" && !childTypenames.includes(typename)) {\n        childTypenames.push(typename);\n      }\n    });\n  }\n  globalThis.__DEV__ !== false && invariant.warn(15, fieldName, parentType, childTypenames.length ? \"either ensure all objects of type \" + childTypenames.join(\" and \") + \" have an ID or a custom merge function, or \" : \"\", typeDotName, __assign({}, existing), __assign({}, incoming));\n}\n//# sourceMappingURL=writeToStore.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}