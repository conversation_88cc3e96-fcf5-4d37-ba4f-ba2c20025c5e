{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\n\n/**\n * The {@link differenceInDays} function options.\n */\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const sign = compareLocalAsc(laterDate_, earlierDate_);\n  const difference = Math.abs(differenceInCalendarDays(laterDate_, earlierDate_));\n  laterDate_.setDate(laterDate_.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(compareLocalAsc(laterDate_, earlierDate_) === -sign);\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(laterDate, earlierDate) {\n  const diff = laterDate.getFullYear() - earlierDate.getFullYear() || laterDate.getMonth() - earlierDate.getMonth() || laterDate.getDate() - earlierDate.getDate() || laterDate.getHours() - earlierDate.getHours() || laterDate.getMinutes() - earlierDate.getMinutes() || laterDate.getSeconds() - earlierDate.getSeconds() || laterDate.getMilliseconds() - earlierDate.getMilliseconds();\n  if (diff < 0) return -1;\n  if (diff > 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;", "map": {"version": 3, "names": ["normalizeDates", "differenceInCalendarDays", "differenceInDays", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "sign", "compareLocalAsc", "difference", "Math", "abs", "setDate", "getDate", "isLastDayNotFull", "Number", "result", "diff", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/differenceInDays.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\n\n/**\n * The {@link differenceInDays} function options.\n */\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareLocalAsc(laterDate_, earlierDate_);\n  const difference = Math.abs(\n    differenceInCalendarDays(laterDate_, earlierDate_),\n  );\n\n  laterDate_.setDate(laterDate_.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(\n    compareLocalAsc(laterDate_, earlierDate_) === -sign,\n  );\n\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(laterDate, earlierDate) {\n  const diff =\n    laterDate.getFullYear() - earlierDate.getFullYear() ||\n    laterDate.getMonth() - earlierDate.getMonth() ||\n    laterDate.getDate() - earlierDate.getDate() ||\n    laterDate.getHours() - earlierDate.getHours() ||\n    laterDate.getMinutes() - earlierDate.getMinutes() ||\n    laterDate.getSeconds() - earlierDate.getSeconds() ||\n    laterDate.getMilliseconds() - earlierDate.getMilliseconds();\n\n  if (diff < 0) return -1;\n  if (diff > 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,wBAAwB,QAAQ,+BAA+B;;AAExE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAChE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGP,cAAc,CAC/CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,IAAI,GAAGC,eAAe,CAACJ,UAAU,EAAEC,YAAY,CAAC;EACtD,MAAMI,UAAU,GAAGC,IAAI,CAACC,GAAG,CACzBZ,wBAAwB,CAACK,UAAU,EAAEC,YAAY,CACnD,CAAC;EAEDD,UAAU,CAACQ,OAAO,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,GAAGN,IAAI,GAAGE,UAAU,CAAC;;EAE5D;EACA;EACA,MAAMK,gBAAgB,GAAGC,MAAM,CAC7BP,eAAe,CAACJ,UAAU,EAAEC,YAAY,CAAC,KAAK,CAACE,IACjD,CAAC;EAED,MAAMS,MAAM,GAAGT,IAAI,IAAIE,UAAU,GAAGK,gBAAgB,CAAC;EACrD;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA;AACA;AACA;AACA,SAASR,eAAeA,CAACP,SAAS,EAAEC,WAAW,EAAE;EAC/C,MAAMe,IAAI,GACRhB,SAAS,CAACiB,WAAW,CAAC,CAAC,GAAGhB,WAAW,CAACgB,WAAW,CAAC,CAAC,IACnDjB,SAAS,CAACkB,QAAQ,CAAC,CAAC,GAAGjB,WAAW,CAACiB,QAAQ,CAAC,CAAC,IAC7ClB,SAAS,CAACY,OAAO,CAAC,CAAC,GAAGX,WAAW,CAACW,OAAO,CAAC,CAAC,IAC3CZ,SAAS,CAACmB,QAAQ,CAAC,CAAC,GAAGlB,WAAW,CAACkB,QAAQ,CAAC,CAAC,IAC7CnB,SAAS,CAACoB,UAAU,CAAC,CAAC,GAAGnB,WAAW,CAACmB,UAAU,CAAC,CAAC,IACjDpB,SAAS,CAACqB,UAAU,CAAC,CAAC,GAAGpB,WAAW,CAACoB,UAAU,CAAC,CAAC,IACjDrB,SAAS,CAACsB,eAAe,CAAC,CAAC,GAAGrB,WAAW,CAACqB,eAAe,CAAC,CAAC;EAE7D,IAAIN,IAAI,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;EACvB,IAAIA,IAAI,GAAG,CAAC,EAAE,OAAO,CAAC;;EAEtB;EACA,OAAOA,IAAI;AACb;;AAEA;AACA,eAAejB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}