{"name": "@apollo/utils.sortast", "version": "2.0.1", "description": "Sort AST nodes in a document alphabetically", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/sortAst/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}, "dependencies": {"lodash.sortby": "^4.7.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}