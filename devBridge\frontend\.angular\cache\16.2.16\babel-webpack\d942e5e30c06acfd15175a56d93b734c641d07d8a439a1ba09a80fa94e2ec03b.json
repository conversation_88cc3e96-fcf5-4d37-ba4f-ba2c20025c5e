{"ast": null, "code": "/**\n * This function transforms a JS object `ObjMap<Promise<T>>` into\n * a `Promise<ObjMap<T>>`\n *\n * This is akin to bluebird's `Promise.props`, but implemented only using\n * `Promise.all` so it will work with any implementation of ES6 promises.\n */\nexport function promiseForObject(object) {\n  return Promise.all(Object.values(object)).then(resolvedValues => {\n    const resolvedObject = Object.create(null);\n    for (const [i, key] of Object.keys(object).entries()) {\n      resolvedObject[key] = resolvedValues[i];\n    }\n    return resolvedObject;\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}