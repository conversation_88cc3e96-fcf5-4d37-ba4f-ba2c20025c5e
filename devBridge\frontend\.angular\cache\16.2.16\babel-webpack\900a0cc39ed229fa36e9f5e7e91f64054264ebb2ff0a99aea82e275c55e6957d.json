{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { getNamedType, isLeafType } from '../../type/definition.mjs';\n\n/**\n * <PERSON>alar leafs\n *\n * A GraphQL document is valid only if all leaf fields (fields without\n * sub selections) are of scalar or enum types.\n */\nexport function ScalarLeafsRule(context) {\n  return {\n    Field(node) {\n      const type = context.getType();\n      const selectionSet = node.selectionSet;\n      if (type) {\n        if (isLeafType(getNamedType(type))) {\n          if (selectionSet) {\n            const fieldName = node.name.value;\n            const typeStr = inspect(type);\n            context.reportError(new GraphQLError(`Field \"${fieldName}\" must not have a selection since type \"${typeStr}\" has no subfields.`, {\n              nodes: selectionSet\n            }));\n          }\n        } else if (!selectionSet) {\n          const fieldName = node.name.value;\n          const typeStr = inspect(type);\n          context.reportError(new GraphQLError(`Field \"${fieldName}\" of type \"${typeStr}\" must have a selection of subfields. Did you mean \"${fieldName} { ... }\"?`, {\n            nodes: node\n          }));\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}