{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterable, callback) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  function mapResult(_x) {\n    return _mapResult.apply(this, arguments);\n  }\n  function _mapResult() {\n    _mapResult = _asyncToGenerator(function* (result) {\n      if (result.done) {\n        return result;\n      }\n      try {\n        return {\n          value: yield callback(result.value),\n          done: false\n        };\n      } catch (error) {\n        /* c8 ignore start */\n        // FIXME: add test case\n        if (typeof iterator.return === 'function') {\n          try {\n            yield iterator.return();\n          } catch (_e) {\n            /* ignore error */\n          }\n        }\n        throw error;\n        /* c8 ignore stop */\n      }\n    });\n    return _mapResult.apply(this, arguments);\n  }\n  return {\n    next() {\n      return _asyncToGenerator(function* () {\n        return mapResult(yield iterator.next());\n      })();\n    },\n    return() {\n      return _asyncToGenerator(function* () {\n        // If iterator.return() does not exist, then type R must be undefined.\n        return typeof iterator.return === 'function' ? mapResult(yield iterator.return()) : {\n          value: undefined,\n          done: true\n        };\n      })();\n    },\n    throw(error) {\n      return _asyncToGenerator(function* () {\n        if (typeof iterator.throw === 'function') {\n          return mapResult(yield iterator.throw(error));\n        }\n        throw error;\n      })();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}