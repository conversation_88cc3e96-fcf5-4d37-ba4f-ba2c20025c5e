{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n// Components\nimport { TasksComponent } from './tasks.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TasksComponent\n}, {\n  path: ':teamId',\n  component: TasksComponent\n}];\nexport let TasksModule = /*#__PURE__*/(() => {\n  class TasksModule {\n    static {\n      this.ɵfac = function TasksModule_Factory(t) {\n        return new (t || TasksModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: TasksModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, RouterModule.forChild(routes), SharedModule]\n      });\n    }\n  }\n  return TasksModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}