{"ast": null, "code": "import { GraphQLError } from '../error/GraphQLError.mjs';\n\n/**\n * Extracts the root type of the operation from the schema.\n *\n * @deprecated Please use `GraphQLSchema.getRootType` instead. Will be removed in v17\n */\nexport function getOperationRootType(schema, operation) {\n  if (operation.operation === 'query') {\n    const queryType = schema.getQueryType();\n    if (!queryType) {\n      throw new GraphQLError('Schema does not define the required query root type.', {\n        nodes: operation\n      });\n    }\n    return queryType;\n  }\n  if (operation.operation === 'mutation') {\n    const mutationType = schema.getMutationType();\n    if (!mutationType) {\n      throw new GraphQLError('Schema is not configured for mutations.', {\n        nodes: operation\n      });\n    }\n    return mutationType;\n  }\n  if (operation.operation === 'subscription') {\n    const subscriptionType = schema.getSubscriptionType();\n    if (!subscriptionType) {\n      throw new GraphQLError('Schema is not configured for subscriptions.', {\n        nodes: operation\n      });\n    }\n    return subscriptionType;\n  }\n  throw new GraphQLError('Can only have query, mutation and subscription operations.', {\n    nodes: operation\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}