{"ast": null, "code": "import { invariant } from '../jsutils/invariant.mjs';\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || invariant(false);\n    if (match.index >= position) {\n      break;\n    }\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n  return {\n    line,\n    column: position + 1 - lastLineStart\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}