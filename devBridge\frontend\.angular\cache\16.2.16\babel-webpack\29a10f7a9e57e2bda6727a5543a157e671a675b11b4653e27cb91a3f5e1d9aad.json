{"ast": null, "code": "/**\n * Memoizes the provided three-argument function.\n */\nexport function memoize3(fn) {\n  let cache0;\n  return function memoized(a1, a2, a3) {\n    if (cache0 === undefined) {\n      cache0 = new WeakMap();\n    }\n    let cache1 = cache0.get(a1);\n    if (cache1 === undefined) {\n      cache1 = new WeakMap();\n      cache0.set(a1, cache1);\n    }\n    let cache2 = cache1.get(a2);\n    if (cache2 === undefined) {\n      cache2 = new WeakMap();\n      cache1.set(a2, cache2);\n    }\n    let fnResult = cache2.get(a3);\n    if (fnResult === undefined) {\n      fnResult = fn(a1, a2, a3);\n      cache2.set(a3, fnResult);\n    }\n    return fnResult;\n  };\n}", "map": {"version": 3, "names": ["memoize3", "fn", "cache0", "memoized", "a1", "a2", "a3", "undefined", "WeakMap", "cache1", "get", "set", "cache2", "fnResult"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/jsutils/memoize3.mjs"], "sourcesContent": ["/**\n * Memoizes the provided three-argument function.\n */\nexport function memoize3(fn) {\n  let cache0;\n  return function memoized(a1, a2, a3) {\n    if (cache0 === undefined) {\n      cache0 = new WeakMap();\n    }\n\n    let cache1 = cache0.get(a1);\n\n    if (cache1 === undefined) {\n      cache1 = new WeakMap();\n      cache0.set(a1, cache1);\n    }\n\n    let cache2 = cache1.get(a2);\n\n    if (cache2 === undefined) {\n      cache2 = new WeakMap();\n      cache1.set(a2, cache2);\n    }\n\n    let fnResult = cache2.get(a3);\n\n    if (fnResult === undefined) {\n      fnResult = fn(a1, a2, a3);\n      cache2.set(a3, fnResult);\n    }\n\n    return fnResult;\n  };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,EAAE,EAAE;EAC3B,IAAIC,MAAM;EACV,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACnC,IAAIJ,MAAM,KAAKK,SAAS,EAAE;MACxBL,MAAM,GAAG,IAAIM,OAAO,CAAC,CAAC;IACxB;IAEA,IAAIC,MAAM,GAAGP,MAAM,CAACQ,GAAG,CAACN,EAAE,CAAC;IAE3B,IAAIK,MAAM,KAAKF,SAAS,EAAE;MACxBE,MAAM,GAAG,IAAID,OAAO,CAAC,CAAC;MACtBN,MAAM,CAACS,GAAG,CAACP,EAAE,EAAEK,MAAM,CAAC;IACxB;IAEA,IAAIG,MAAM,GAAGH,MAAM,CAACC,GAAG,CAACL,EAAE,CAAC;IAE3B,IAAIO,MAAM,KAAKL,SAAS,EAAE;MACxBK,MAAM,GAAG,IAAIJ,OAAO,CAAC,CAAC;MACtBC,MAAM,CAACE,GAAG,CAACN,EAAE,EAAEO,MAAM,CAAC;IACxB;IAEA,IAAIC,QAAQ,GAAGD,MAAM,CAACF,GAAG,CAACJ,EAAE,CAAC;IAE7B,IAAIO,QAAQ,KAAKN,SAAS,EAAE;MAC1BM,QAAQ,GAAGZ,EAAE,CAACG,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MACzBM,MAAM,CAACD,GAAG,CAACL,EAAE,EAAEO,QAAQ,CAAC;IAC1B;IAEA,OAAOA,QAAQ;EACjB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}