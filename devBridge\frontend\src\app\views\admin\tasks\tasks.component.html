<div class="tasks-container">
  <!-- Header -->
  <div class="tasks-header">
    <div class="header-left">
      <button mat-icon-button (click)="navigateToTeam()" matTooltip="Retour à l'équipe">
        <mat-icon>arrow_back</mat-icon>
      </button>

      <div class="team-info">
        <h1 class="team-name">{{ getTeamName() }}</h1>
        <div class="team-status" [style.color]="getTeamStatusColor()">
          <mat-icon>circle</mat-icon>
          <span>{{ team?.status || 'active' }}</span>
        </div>
      </div>
    </div>

    <div class="header-actions">
      <!-- Statistiques rapides -->
      <div class="quick-stats" *ngIf="getQuickStats() as stats">
        <div class="stat-item">
          <span class="stat-value">{{ stats.total }}</span>
          <span class="stat-label">Total</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ stats.todo }}</span>
          <span class="stat-label">À faire</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ stats.inProgress }}</span>
          <span class="stat-label">En cours</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ stats.done }}</span>
          <span class="stat-label">Terminé</span>
        </div>
        <div class="stat-item" *ngIf="stats.overdue > 0">
          <span class="stat-value overdue">{{ stats.overdue }}</span>
          <span class="stat-label">En retard</span>
        </div>
      </div>

      <!-- Actions -->
      <button mat-icon-button (click)="refresh()" matTooltip="Actualiser">
        <mat-icon>refresh</mat-icon>
      </button>

      <button mat-icon-button
              (click)="openAIAssistant()"
              [disabled]="!selectedTask"
              matTooltip="Assistant IA">
        <mat-icon>psychology</mat-icon>
      </button>

      <button mat-icon-button [matMenuTriggerFor]="viewMenu" matTooltip="Changer de vue">
        <mat-icon>view_module</mat-icon>
      </button>

      <mat-menu #viewMenu="matMenu">
        <button mat-menu-item (click)="switchView('kanban')" [class.active]="currentView === 'kanban'">
          <mat-icon>view_kanban</mat-icon>
          <span>Kanban</span>
        </button>
        <button mat-menu-item (click)="switchView('list')" [class.active]="currentView === 'list'">
          <mat-icon>list</mat-icon>
          <span>Liste</span>
        </button>
        <button mat-menu-item (click)="switchView('calendar')" [class.active]="currentView === 'calendar'">
          <mat-icon>calendar_today</mat-icon>
          <span>Calendrier</span>
        </button>
      </mat-menu>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="tasks-content">
    <!-- Vue Kanban -->
    <div class="kanban-view" *ngIf="currentView === 'kanban'">
      <app-kanban-board
        [teamId]="teamId"
        [filters]="kanbanFilters"
        (taskSelected)="onTaskSelected($event)"
        (taskCreated)="onTaskCreated($event)">
      </app-kanban-board>
    </div>

    <!-- Vue Liste (à implémenter) -->
    <div class="list-view" *ngIf="currentView === 'list'">
      <mat-card class="coming-soon">
        <mat-card-content>
          <div class="coming-soon-content">
            <mat-icon>list</mat-icon>
            <h3>Vue Liste</h3>
            <p>Cette vue sera bientôt disponible</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Vue Calendrier (à implémenter) -->
    <div class="calendar-view" *ngIf="currentView === 'calendar'">
      <mat-card class="coming-soon">
        <mat-card-content>
          <div class="coming-soon-content">
            <mat-icon>calendar_today</mat-icon>
            <h3>Vue Calendrier</h3>
            <p>Cette vue sera bientôt disponible</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Loading state -->
  <div class="loading-overlay" *ngIf="loading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Chargement des tâches...</p>
  </div>

  <!-- Error state -->
  <div class="error-container" *ngIf="error && !loading">
    <mat-icon color="warn">error</mat-icon>
    <h3>Erreur</h3>
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="refresh()">
      Réessayer
    </button>
  </div>
</div>

<!-- Panneau latéral pour les détails de tâche -->
<div class="task-details-panel" [class.open]="showTaskDetails" *ngIf="selectedTask">
  <div class="panel-header">
    <h3>Détails de la tâche</h3>
    <button mat-icon-button (click)="closeTaskDetails()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="panel-content">
    <div class="task-info">
      <h4>{{ selectedTask.title }}</h4>
      <p class="task-description" *ngIf="selectedTask.description">
        {{ selectedTask.description }}
      </p>

      <div class="task-meta">
        <div class="meta-item">
          <span class="label">Statut:</span>
          <span class="value status-{{ selectedTask.status }}">{{ selectedTask.status }}</span>
        </div>

        <div class="meta-item">
          <span class="label">Priorité:</span>
          <span class="value priority-{{ selectedTask.priority }}">{{ selectedTask.priority }}</span>
        </div>

        <div class="meta-item" *ngIf="selectedTask.category">
          <span class="label">Catégorie:</span>
          <span class="value">{{ selectedTask.category }}</span>
        </div>

        <div class="meta-item" *ngIf="selectedTask.dueDate">
          <span class="label">Échéance:</span>
          <span class="value">{{ selectedTask.dueDate | date:'dd/MM/yyyy' }}</span>
        </div>

        <div class="meta-item" *ngIf="selectedTask.estimatedHours">
          <span class="label">Estimation:</span>
          <span class="value">{{ selectedTask.estimatedHours }}h</span>
        </div>
      </div>

      <!-- Labels -->
      <div class="task-labels" *ngIf="selectedTask.labels && selectedTask.labels.length > 0">
        <span class="label-title">Labels:</span>
        <div class="labels-list">
          <span class="task-label"
                *ngFor="let label of selectedTask.labels"
                [style.background-color]="label.color">
            {{ label.name }}
          </span>
        </div>
      </div>

      <!-- Assignés -->
      <div class="task-assignees" *ngIf="selectedTask.assignedTo && selectedTask.assignedTo.length > 0">
        <span class="label-title">Assigné à:</span>
        <div class="assignees-list">
          <div class="assignee" *ngFor="let assignee of selectedTask.assignedTo">
            <div class="assignee-avatar">
              <img *ngIf="hasAssigneeImage(assignee)"
                   [src]="getAssigneeImage(assignee)"
                   [alt]="getAssigneeName(assignee)">
              <span *ngIf="!hasAssigneeImage(assignee)">
                {{ getAssigneeInitials(assignee) }}
              </span>
            </div>
            <span class="assignee-name">{{ getAssigneeName(assignee) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="task-actions">
      <button mat-raised-button color="primary" (click)="openAIAssistant()">
        <mat-icon>psychology</mat-icon>
        Assistant IA
      </button>
    </div>
  </div>
</div>

<!-- Panneau Assistant IA -->
<div class="ai-assistant-panel" [class.open]="showAIAssistant" *ngIf="selectedTask && showAIAssistant">
  <div class="panel-header">
    <h3>Assistant IA</h3>
    <button mat-icon-button (click)="closeAIAssistant()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="panel-content">
    <app-task-ai-assistant
      [task]="selectedTask"
      (taskUpdated)="onTaskUpdated($event)">
    </app-task-ai-assistant>
  </div>
</div>

<!-- Overlay pour les panneaux -->
<div class="panels-overlay"
     [class.active]="showTaskDetails || showAIAssistant"
     (click)="closeTaskDetails(); closeAIAssistant()">
</div>
