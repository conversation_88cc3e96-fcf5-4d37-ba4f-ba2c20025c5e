{"ast": null, "code": "import { isAbstractType, isInterfaceType, isListType, isNonNullType, isObjectType } from '../type/definition.mjs';\n\n/**\n * Provided two types, return true if the types are equal (invariant).\n */\nexport function isEqualType(typeA, typeB) {\n  // Equivalent types are equal.\n  if (typeA === typeB) {\n    return true;\n  } // If either type is non-null, the other must also be non-null.\n\n  if (isNonNullType(typeA) && isNonNullType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // If either type is a list, the other must also be a list.\n\n  if (isListType(typeA) && isListType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // Otherwise the types are not equal.\n\n  return false;\n}\n/**\n * Provided a type and a super type, return true if the first type is either\n * equal or a subset of the second super type (covariant).\n */\n\nexport function isTypeSubTypeOf(schema, maybeSubType, superType) {\n  // Equivalent type is a valid subtype\n  if (maybeSubType === superType) {\n    return true;\n  } // If superType is non-null, maybeSubType must also be non-null.\n\n  if (isNonNullType(superType)) {\n    if (isNonNullType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n    return false;\n  }\n  if (isNonNullType(maybeSubType)) {\n    // If superType is nullable, maybeSubType may be non-null or nullable.\n    return isTypeSubTypeOf(schema, maybeSubType.ofType, superType);\n  } // If superType type is a list, maybeSubType type must also be a list.\n\n  if (isListType(superType)) {\n    if (isListType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n    return false;\n  }\n  if (isListType(maybeSubType)) {\n    // If superType is not a list, maybeSubType must also be not a list.\n    return false;\n  } // If superType type is an abstract type, check if it is super type of maybeSubType.\n  // Otherwise, the child type is not a valid subtype of the parent type.\n\n  return isAbstractType(superType) && (isInterfaceType(maybeSubType) || isObjectType(maybeSubType)) && schema.isSubType(superType, maybeSubType);\n}\n/**\n * Provided two composite types, determine if they \"overlap\". Two composite\n * types overlap when the Sets of possible concrete types for each intersect.\n *\n * This is often used to determine if a fragment of a given type could possibly\n * be visited in a context of another type.\n *\n * This function is commutative.\n */\n\nexport function doTypesOverlap(schema, typeA, typeB) {\n  // Equivalent types overlap\n  if (typeA === typeB) {\n    return true;\n  }\n  if (isAbstractType(typeA)) {\n    if (isAbstractType(typeB)) {\n      // If both types are abstract, then determine if there is any intersection\n      // between possible concrete types of each.\n      return schema.getPossibleTypes(typeA).some(type => schema.isSubType(typeB, type));\n    } // Determine if the latter type is a possible concrete type of the former.\n\n    return schema.isSubType(typeA, typeB);\n  }\n  if (isAbstractType(typeB)) {\n    // Determine if the former type is a possible concrete type of the latter.\n    return schema.isSubType(typeB, typeA);\n  } // Otherwise the types do not overlap.\n\n  return false;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}