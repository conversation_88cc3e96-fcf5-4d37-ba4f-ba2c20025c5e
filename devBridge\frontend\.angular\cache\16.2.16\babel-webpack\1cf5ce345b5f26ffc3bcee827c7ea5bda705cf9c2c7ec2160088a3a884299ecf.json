{"ast": null, "code": "import { invariant } from \"../utilities/globals/index.js\";\nimport { createFragmentMap, getFragmentDefinitions, getOperationDefinition } from \"../utilities/index.js\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation } from \"./utils.js\";\n/** @internal */\nexport function maskOperation(data, document, cache) {\n  var _a;\n  if (!cache.fragmentMatches) {\n    if (globalThis.__DEV__ !== false) {\n      warnOnImproperCacheImplementation();\n    }\n    return data;\n  }\n  var definition = getOperationDefinition(document);\n  invariant(definition, 51);\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n  return maskDefinition(data, definition.selectionSet, {\n    operationType: definition.operation,\n    operationName: (_a = definition.name) === null || _a === void 0 ? void 0 : _a.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache: cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl()\n  });\n}\n//# sourceMappingURL=maskOperation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}