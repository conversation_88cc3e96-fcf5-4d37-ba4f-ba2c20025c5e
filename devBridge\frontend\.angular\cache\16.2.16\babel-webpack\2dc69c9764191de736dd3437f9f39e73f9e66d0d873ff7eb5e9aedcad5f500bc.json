{"ast": null, "code": "/**\n * Returns a number indicating whether a reference string comes before, or after,\n * or is the same as the given string in natural sort order.\n *\n * See: https://en.wikipedia.org/wiki/Natural_sort_order\n *\n */\nexport function naturalCompare(aStr, bStr) {\n  let aIndex = 0;\n  let bIndex = 0;\n  while (aIndex < aStr.length && bIndex < bStr.length) {\n    let aChar = aStr.charCodeAt(aIndex);\n    let bChar = bStr.charCodeAt(bIndex);\n    if (isDigit(aChar) && isDigit(bChar)) {\n      let aNum = 0;\n      do {\n        ++aIndex;\n        aNum = aNum * 10 + aChar - DIGIT_0;\n        aChar = aStr.charCodeAt(aIndex);\n      } while (isDigit(aChar) && aNum > 0);\n      let bNum = 0;\n      do {\n        ++bIndex;\n        bNum = bNum * 10 + bChar - DIGIT_0;\n        bChar = bStr.charCodeAt(bIndex);\n      } while (isDigit(bChar) && bNum > 0);\n      if (aNum < bNum) {\n        return -1;\n      }\n      if (aNum > bNum) {\n        return 1;\n      }\n    } else {\n      if (aChar < bChar) {\n        return -1;\n      }\n      if (aChar > bChar) {\n        return 1;\n      }\n      ++aIndex;\n      ++bIndex;\n    }\n  }\n  return aStr.length - bStr.length;\n}\nconst DIGIT_0 = 48;\nconst DIGIT_9 = 57;\nfunction isDigit(code) {\n  return !isNaN(code) && DIGIT_0 <= code && code <= DIGIT_9;\n}", "map": {"version": 3, "names": ["naturalCompare", "aStr", "bStr", "aIndex", "bIndex", "length", "aChar", "charCodeAt", "bChar", "isDigit", "aNum", "DIGIT_0", "bNum", "DIGIT_9", "code", "isNaN"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/jsutils/naturalCompare.mjs"], "sourcesContent": ["/**\n * Returns a number indicating whether a reference string comes before, or after,\n * or is the same as the given string in natural sort order.\n *\n * See: https://en.wikipedia.org/wiki/Natural_sort_order\n *\n */\nexport function naturalCompare(aStr, bStr) {\n  let aIndex = 0;\n  let bIndex = 0;\n\n  while (aIndex < aStr.length && bIndex < bStr.length) {\n    let aChar = aStr.charCodeAt(aIndex);\n    let bChar = bStr.charCodeAt(bIndex);\n\n    if (isDigit(aChar) && isDigit(bChar)) {\n      let aNum = 0;\n\n      do {\n        ++aIndex;\n        aNum = aNum * 10 + aChar - DIGIT_0;\n        aChar = aStr.charCodeAt(aIndex);\n      } while (isDigit(aChar) && aNum > 0);\n\n      let bNum = 0;\n\n      do {\n        ++bIndex;\n        bNum = bNum * 10 + bChar - DIGIT_0;\n        bChar = bStr.charCodeAt(bIndex);\n      } while (isDigit(bChar) && bNum > 0);\n\n      if (aNum < bNum) {\n        return -1;\n      }\n\n      if (aNum > bNum) {\n        return 1;\n      }\n    } else {\n      if (aChar < bChar) {\n        return -1;\n      }\n\n      if (aChar > bChar) {\n        return 1;\n      }\n\n      ++aIndex;\n      ++bIndex;\n    }\n  }\n\n  return aStr.length - bStr.length;\n}\nconst DIGIT_0 = 48;\nconst DIGIT_9 = 57;\n\nfunction isDigit(code) {\n  return !isNaN(code) && DIGIT_0 <= code && code <= DIGIT_9;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACzC,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EAEd,OAAOD,MAAM,GAAGF,IAAI,CAACI,MAAM,IAAID,MAAM,GAAGF,IAAI,CAACG,MAAM,EAAE;IACnD,IAAIC,KAAK,GAAGL,IAAI,CAACM,UAAU,CAACJ,MAAM,CAAC;IACnC,IAAIK,KAAK,GAAGN,IAAI,CAACK,UAAU,CAACH,MAAM,CAAC;IAEnC,IAAIK,OAAO,CAACH,KAAK,CAAC,IAAIG,OAAO,CAACD,KAAK,CAAC,EAAE;MACpC,IAAIE,IAAI,GAAG,CAAC;MAEZ,GAAG;QACD,EAAEP,MAAM;QACRO,IAAI,GAAGA,IAAI,GAAG,EAAE,GAAGJ,KAAK,GAAGK,OAAO;QAClCL,KAAK,GAAGL,IAAI,CAACM,UAAU,CAACJ,MAAM,CAAC;MACjC,CAAC,QAAQM,OAAO,CAACH,KAAK,CAAC,IAAII,IAAI,GAAG,CAAC;MAEnC,IAAIE,IAAI,GAAG,CAAC;MAEZ,GAAG;QACD,EAAER,MAAM;QACRQ,IAAI,GAAGA,IAAI,GAAG,EAAE,GAAGJ,KAAK,GAAGG,OAAO;QAClCH,KAAK,GAAGN,IAAI,CAACK,UAAU,CAACH,MAAM,CAAC;MACjC,CAAC,QAAQK,OAAO,CAACD,KAAK,CAAC,IAAII,IAAI,GAAG,CAAC;MAEnC,IAAIF,IAAI,GAAGE,IAAI,EAAE;QACf,OAAO,CAAC,CAAC;MACX;MAEA,IAAIF,IAAI,GAAGE,IAAI,EAAE;QACf,OAAO,CAAC;MACV;IACF,CAAC,MAAM;MACL,IAAIN,KAAK,GAAGE,KAAK,EAAE;QACjB,OAAO,CAAC,CAAC;MACX;MAEA,IAAIF,KAAK,GAAGE,KAAK,EAAE;QACjB,OAAO,CAAC;MACV;MAEA,EAAEL,MAAM;MACR,EAAEC,MAAM;IACV;EACF;EAEA,OAAOH,IAAI,CAACI,MAAM,GAAGH,IAAI,CAACG,MAAM;AAClC;AACA,MAAMM,OAAO,GAAG,EAAE;AAClB,MAAME,OAAO,GAAG,EAAE;AAElB,SAASJ,OAAOA,CAACK,IAAI,EAAE;EACrB,OAAO,CAACC,KAAK,CAACD,IAAI,CAAC,IAAIH,OAAO,IAAIG,IAAI,IAAIA,IAAI,IAAID,OAAO;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}