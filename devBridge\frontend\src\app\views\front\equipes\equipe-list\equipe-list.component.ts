import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { EquipeService } from 'src/app/services/equipe.service';
import { NotificationService } from 'src/app/services/notification.service';
import { Equipe } from 'src/app/models/equipe.model';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-equipe-list',
  templateUrl: './equipe-list.component.html',
  styleUrls: ['./equipe-list.component.css']
})
export class EquipeListComponent implements OnInit, OnDestroy {
  equipes: Equipe[] = [];
  loading = false;
  error: string | null = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private equipeService: EquipeService,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadEquipes();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    // Nettoyer les subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private setupSubscriptions(): void {
    // Écouter les créations d'équipes
    const teamCreatedSub = this.equipeService.teamCreated$.subscribe((newTeam) => {
      console.log('🎉 Nouvelle équipe créée:', newTeam);
      // Ajouter la nouvelle équipe à la liste
      this.equipes.push(newTeam);
      this.sortEquipes();
      this.notificationService.showSuccess(`L'équipe "${newTeam.name}" a été ajoutée à la liste`);
    });

    // Écouter les mises à jour d'équipes
    const teamUpdatedSub = this.equipeService.teamUpdated$.subscribe((updatedTeam) => {
      console.log('🔄 Équipe mise à jour:', updatedTeam);
      const index = this.equipes.findIndex(e => e._id === updatedTeam._id);
      if (index !== -1) {
        this.equipes[index] = updatedTeam;
        this.sortEquipes();
      }
    });

    // Écouter les suppressions d'équipes
    const teamDeletedSub = this.equipeService.teamDeleted$.subscribe((deletedTeamId) => {
      console.log('🗑️ Équipe supprimée:', deletedTeamId);
      this.equipes = this.equipes.filter(e => e._id !== deletedTeamId);
    });

    // Écouter les demandes de rafraîchissement
    const refreshSub = this.equipeService.teamsRefresh$.subscribe(() => {
      console.log('🔄 Rafraîchissement demandé');
      this.loadEquipes();
    });

    // Ajouter toutes les subscriptions au tableau pour le nettoyage
    this.subscriptions.push(teamCreatedSub, teamUpdatedSub, teamDeletedSub, refreshSub);
  }

  private sortEquipes(): void {
    this.equipes.sort((a, b) => {
      if (a.name && b.name) {
        return a.name.localeCompare(b.name);
      }
      return 0;
    });
  }

  loadEquipes(): void {
    this.loading = true;
    this.error = null;

    this.equipeService.getEquipes().pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: (data) => {
        console.log('Équipes chargées:', data);
        this.equipes = data;
        this.sortEquipes();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des équipes:', error);
        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';
        this.notificationService.showError('Erreur lors du chargement des équipes');
      }
    });
  }

  navigateToAddEquipe(): void {
    this.router.navigate(['/equipes/ajouter']);
  }

  navigateToEditEquipe(id: string): void {
    this.router.navigate(['/equipes/modifier', id]);
  }

  navigateToEquipeDetail(id: string): void {
    this.router.navigate(['/equipes/detail', id]);
  }

  deleteEquipe(id: string): void {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }

    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation
    const equipe = this.equipes.find(e => e._id === id);
    const equipeName = equipe?.name || 'cette équipe';

    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe "${equipeName}" ?`)) {
      this.loading = true;

      this.equipeService.deleteEquipe(id).pipe(
        finalize(() => this.loading = false)
      ).subscribe({
        next: () => {
          console.log('Équipe supprimée avec succès');
          this.notificationService.showSuccess(`L'équipe "${equipeName}" a été supprimée avec succès`);
          this.loadEquipes();
        },
        error: (error) => {
          console.error('Erreur lors de la suppression de l\'équipe:', error);
          this.error = 'Impossible de supprimer l\'équipe. Veuillez réessayer plus tard.';
          this.notificationService.showError(`Erreur lors de la suppression de l'équipe "${equipeName}"`);
        }
      });
    }
  }

  navigateToTasks(id: string): void {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }

    const equipe = this.equipes.find(e => e._id === id);
    if (!equipe) {
      this.notificationService.showError('Équipe non trouvée');
      return;
    }

    const equipeName = equipe.name || 'cette équipe';
    const memberCount = equipe.members?.length || 0;

    // Message informatif sur la gestion IA
    console.log(`🤖 IA activée pour ${equipeName}:`);
    console.log(`👥 ${memberCount} membres détectés`);
    console.log(`📋 L'IA va gérer ${memberCount} modules automatiquement`);

    // Notification à l'utilisateur
    this.notificationService.showSuccess(
      `🤖 IA activée pour ${equipeName} - Gestion de ${memberCount} modules`
    );

    // Naviguer vers la page des tâches avec gestion IA
    this.router.navigate(['/tasks', id], {
      queryParams: {
        aiMode: true,
        memberCount: memberCount,
        teamName: equipeName
      }
    });
  }

  // Méthode pour obtenir le nombre de tâches d'une équipe (simulé)
  getTaskCount(equipeId: string): number {
    // TODO: Implémenter l'appel API réel pour obtenir le nombre de tâches
    // Pour l'instant, retourner un nombre aléatoire pour la démonstration
    const counts = [0, 3, 7, 12, 5, 8, 15, 2];
    const index = equipeId.length % counts.length;
    return counts[index];
  }

  // Méthode pour obtenir le statut des tâches d'une équipe
  getTaskStatus(equipeId: string): { completed: number; total: number; percentage: number } {
    const total = this.getTaskCount(equipeId);
    const completed = Math.floor(total * 0.6); // 60% des tâches sont complétées
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { completed, total, percentage };
  }

  // Méthode pour rafraîchir manuellement la liste
  refreshEquipes(): void {
    console.log('🔄 Rafraîchissement manuel demandé');
    this.loadEquipes();
  }
}

