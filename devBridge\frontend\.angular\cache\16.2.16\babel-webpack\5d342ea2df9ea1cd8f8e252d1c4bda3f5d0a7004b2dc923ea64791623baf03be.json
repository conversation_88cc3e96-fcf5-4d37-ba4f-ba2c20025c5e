{"ast": null, "code": "/**\n * Contains a range of UTF-8 character offsets and token references that\n * identify the region of the source from which the AST derived.\n */\nexport class Location {\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The Token at which this Node begins.\n   */\n\n  /**\n   * The Token at which this Node ends.\n   */\n\n  /**\n   * The Source document the AST represents.\n   */\n  constructor(startToken, endToken, source) {\n    this.start = startToken.start;\n    this.end = endToken.end;\n    this.startToken = startToken;\n    this.endToken = endToken;\n    this.source = source;\n  }\n  get [Symbol.toStringTag]() {\n    return 'Location';\n  }\n  toJSON() {\n    return {\n      start: this.start,\n      end: this.end\n    };\n  }\n}\n/**\n * Represents a range of characters represented by a lexical token\n * within a Source.\n */\n\nexport class Token {\n  /**\n   * The kind of Token.\n   */\n\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The 1-indexed line number on which this Token appears.\n   */\n\n  /**\n   * The 1-indexed column number at which this Token begins.\n   */\n\n  /**\n   * For non-punctuation tokens, represents the interpreted value of the token.\n   *\n   * Note: is undefined for punctuation tokens, but typed as string for\n   * convenience in the parser.\n   */\n\n  /**\n   * Tokens exist as nodes in a double-linked-list amongst all tokens\n   * including ignored tokens. <SOF> is always the first node and <EOF>\n   * the last.\n   */\n  constructor(kind, start, end, line, column, value) {\n    this.kind = kind;\n    this.start = start;\n    this.end = end;\n    this.line = line;\n    this.column = column; // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    this.value = value;\n    this.prev = null;\n    this.next = null;\n  }\n  get [Symbol.toStringTag]() {\n    return 'Token';\n  }\n  toJSON() {\n    return {\n      kind: this.kind,\n      value: this.value,\n      line: this.line,\n      column: this.column\n    };\n  }\n}\n/**\n * The list of all possible AST node types.\n */\n\n/**\n * @internal\n */\nexport const QueryDocumentKeys = {\n  Name: [],\n  Document: ['definitions'],\n  OperationDefinition: ['name', 'variableDefinitions', 'directives', 'selectionSet'],\n  VariableDefinition: ['variable', 'type', 'defaultValue', 'directives'],\n  Variable: ['name'],\n  SelectionSet: ['selections'],\n  Field: ['alias', 'name', 'arguments', 'directives', 'selectionSet'],\n  Argument: ['name', 'value'],\n  FragmentSpread: ['name', 'directives'],\n  InlineFragment: ['typeCondition', 'directives', 'selectionSet'],\n  FragmentDefinition: ['name',\n  // Note: fragment variable definitions are deprecated and will removed in v17.0.0\n  'variableDefinitions', 'typeCondition', 'directives', 'selectionSet'],\n  IntValue: [],\n  FloatValue: [],\n  StringValue: [],\n  BooleanValue: [],\n  NullValue: [],\n  EnumValue: [],\n  ListValue: ['values'],\n  ObjectValue: ['fields'],\n  ObjectField: ['name', 'value'],\n  Directive: ['name', 'arguments'],\n  NamedType: ['name'],\n  ListType: ['type'],\n  NonNullType: ['type'],\n  SchemaDefinition: ['description', 'directives', 'operationTypes'],\n  OperationTypeDefinition: ['type'],\n  ScalarTypeDefinition: ['description', 'name', 'directives'],\n  ObjectTypeDefinition: ['description', 'name', 'interfaces', 'directives', 'fields'],\n  FieldDefinition: ['description', 'name', 'arguments', 'type', 'directives'],\n  InputValueDefinition: ['description', 'name', 'type', 'defaultValue', 'directives'],\n  InterfaceTypeDefinition: ['description', 'name', 'interfaces', 'directives', 'fields'],\n  UnionTypeDefinition: ['description', 'name', 'directives', 'types'],\n  EnumTypeDefinition: ['description', 'name', 'directives', 'values'],\n  EnumValueDefinition: ['description', 'name', 'directives'],\n  InputObjectTypeDefinition: ['description', 'name', 'directives', 'fields'],\n  DirectiveDefinition: ['description', 'name', 'arguments', 'locations'],\n  SchemaExtension: ['directives', 'operationTypes'],\n  ScalarTypeExtension: ['name', 'directives'],\n  ObjectTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  InterfaceTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  UnionTypeExtension: ['name', 'directives', 'types'],\n  EnumTypeExtension: ['name', 'directives', 'values'],\n  InputObjectTypeExtension: ['name', 'directives', 'fields']\n};\nconst kindValues = new Set(Object.keys(QueryDocumentKeys));\n/**\n * @internal\n */\n\nexport function isNode(maybeNode) {\n  const maybeKind = maybeNode === null || maybeNode === void 0 ? void 0 : maybeNode.kind;\n  return typeof maybeKind === 'string' && kindValues.has(maybeKind);\n}\n/** Name */\n\nvar OperationTypeNode;\n(function (OperationTypeNode) {\n  OperationTypeNode['QUERY'] = 'query';\n  OperationTypeNode['MUTATION'] = 'mutation';\n  OperationTypeNode['SUBSCRIPTION'] = 'subscription';\n})(OperationTypeNode || (OperationTypeNode = {}));\nexport { OperationTypeNode };", "map": {"version": 3, "names": ["Location", "constructor", "startToken", "endToken", "source", "start", "end", "Symbol", "toStringTag", "toJSON", "Token", "kind", "line", "column", "value", "prev", "next", "QueryDocumentKeys", "Name", "Document", "OperationDefinition", "VariableDefinition", "Variable", "SelectionSet", "Field", "Argument", "FragmentSpread", "InlineFragment", "FragmentDefinition", "IntValue", "FloatValue", "StringValue", "BooleanValue", "Null<PERSON><PERSON>ue", "EnumValue", "ListValue", "ObjectValue", "ObjectField", "Directive", "NamedType", "ListType", "NonNullType", "SchemaDefinition", "OperationTypeDefinition", "ScalarTypeDefinition", "ObjectTypeDefinition", "FieldDefinition", "InputValueDefinition", "InterfaceTypeDefinition", "UnionTypeDefinition", "EnumTypeDefinition", "EnumValueDefinition", "InputObjectTypeDefinition", "DirectiveDefinition", "SchemaExtension", "ScalarTypeExtension", "ObjectTypeExtension", "InterfaceTypeExtension", "UnionTypeExtension", "EnumTypeExtension", "InputObjectTypeExtension", "kind<PERSON><PERSON>ues", "Set", "Object", "keys", "isNode", "maybeNode", "<PERSON><PERSON><PERSON>", "has", "OperationTypeNode"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/language/ast.mjs"], "sourcesContent": ["/**\n * Contains a range of UTF-8 character offsets and token references that\n * identify the region of the source from which the AST derived.\n */\nexport class Location {\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The Token at which this Node begins.\n   */\n\n  /**\n   * The Token at which this Node ends.\n   */\n\n  /**\n   * The Source document the AST represents.\n   */\n  constructor(startToken, endToken, source) {\n    this.start = startToken.start;\n    this.end = endToken.end;\n    this.startToken = startToken;\n    this.endToken = endToken;\n    this.source = source;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Location';\n  }\n\n  toJSON() {\n    return {\n      start: this.start,\n      end: this.end,\n    };\n  }\n}\n/**\n * Represents a range of characters represented by a lexical token\n * within a Source.\n */\n\nexport class Token {\n  /**\n   * The kind of Token.\n   */\n\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The 1-indexed line number on which this Token appears.\n   */\n\n  /**\n   * The 1-indexed column number at which this Token begins.\n   */\n\n  /**\n   * For non-punctuation tokens, represents the interpreted value of the token.\n   *\n   * Note: is undefined for punctuation tokens, but typed as string for\n   * convenience in the parser.\n   */\n\n  /**\n   * Tokens exist as nodes in a double-linked-list amongst all tokens\n   * including ignored tokens. <SOF> is always the first node and <EOF>\n   * the last.\n   */\n  constructor(kind, start, end, line, column, value) {\n    this.kind = kind;\n    this.start = start;\n    this.end = end;\n    this.line = line;\n    this.column = column; // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    this.value = value;\n    this.prev = null;\n    this.next = null;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Token';\n  }\n\n  toJSON() {\n    return {\n      kind: this.kind,\n      value: this.value,\n      line: this.line,\n      column: this.column,\n    };\n  }\n}\n/**\n * The list of all possible AST node types.\n */\n\n/**\n * @internal\n */\nexport const QueryDocumentKeys = {\n  Name: [],\n  Document: ['definitions'],\n  OperationDefinition: [\n    'name',\n    'variableDefinitions',\n    'directives',\n    'selectionSet',\n  ],\n  VariableDefinition: ['variable', 'type', 'defaultValue', 'directives'],\n  Variable: ['name'],\n  SelectionSet: ['selections'],\n  Field: ['alias', 'name', 'arguments', 'directives', 'selectionSet'],\n  Argument: ['name', 'value'],\n  FragmentSpread: ['name', 'directives'],\n  InlineFragment: ['typeCondition', 'directives', 'selectionSet'],\n  FragmentDefinition: [\n    'name', // Note: fragment variable definitions are deprecated and will removed in v17.0.0\n    'variableDefinitions',\n    'typeCondition',\n    'directives',\n    'selectionSet',\n  ],\n  IntValue: [],\n  FloatValue: [],\n  StringValue: [],\n  BooleanValue: [],\n  NullValue: [],\n  EnumValue: [],\n  ListValue: ['values'],\n  ObjectValue: ['fields'],\n  ObjectField: ['name', 'value'],\n  Directive: ['name', 'arguments'],\n  NamedType: ['name'],\n  ListType: ['type'],\n  NonNullType: ['type'],\n  SchemaDefinition: ['description', 'directives', 'operationTypes'],\n  OperationTypeDefinition: ['type'],\n  ScalarTypeDefinition: ['description', 'name', 'directives'],\n  ObjectTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  FieldDefinition: ['description', 'name', 'arguments', 'type', 'directives'],\n  InputValueDefinition: [\n    'description',\n    'name',\n    'type',\n    'defaultValue',\n    'directives',\n  ],\n  InterfaceTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  UnionTypeDefinition: ['description', 'name', 'directives', 'types'],\n  EnumTypeDefinition: ['description', 'name', 'directives', 'values'],\n  EnumValueDefinition: ['description', 'name', 'directives'],\n  InputObjectTypeDefinition: ['description', 'name', 'directives', 'fields'],\n  DirectiveDefinition: ['description', 'name', 'arguments', 'locations'],\n  SchemaExtension: ['directives', 'operationTypes'],\n  ScalarTypeExtension: ['name', 'directives'],\n  ObjectTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  InterfaceTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  UnionTypeExtension: ['name', 'directives', 'types'],\n  EnumTypeExtension: ['name', 'directives', 'values'],\n  InputObjectTypeExtension: ['name', 'directives', 'fields'],\n};\nconst kindValues = new Set(Object.keys(QueryDocumentKeys));\n/**\n * @internal\n */\n\nexport function isNode(maybeNode) {\n  const maybeKind =\n    maybeNode === null || maybeNode === void 0 ? void 0 : maybeNode.kind;\n  return typeof maybeKind === 'string' && kindValues.has(maybeKind);\n}\n/** Name */\n\nvar OperationTypeNode;\n\n(function (OperationTypeNode) {\n  OperationTypeNode['QUERY'] = 'query';\n  OperationTypeNode['MUTATION'] = 'mutation';\n  OperationTypeNode['SUBSCRIPTION'] = 'subscription';\n})(OperationTypeNode || (OperationTypeNode = {}));\n\nexport { OperationTypeNode };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,CAAC;EACpB;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;EACEC,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IACxC,IAAI,CAACC,KAAK,GAAGH,UAAU,CAACG,KAAK;IAC7B,IAAI,CAACC,GAAG,GAAGH,QAAQ,CAACG,GAAG;IACvB,IAAI,CAACJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;EAEA,KAAKG,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,UAAU;EACnB;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO;MACLJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,GAAG,EAAE,IAAI,CAACA;IACZ,CAAC;EACH;AACF;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMI,KAAK,CAAC;EACjB;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;EACET,WAAWA,CAACU,IAAI,EAAEN,KAAK,EAAEC,GAAG,EAAEM,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACjD,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACN,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM,CAAC,CAAC;;IAEtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,IAAI;EAClB;EAEA,KAAKT,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,OAAO;EAChB;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO;MACLE,IAAI,EAAE,IAAI,CAACA,IAAI;MACfG,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBF,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;EACH;AACF;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMI,iBAAiB,GAAG;EAC/BC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CAAC,aAAa,CAAC;EACzBC,mBAAmB,EAAE,CACnB,MAAM,EACN,qBAAqB,EACrB,YAAY,EACZ,cAAc,CACf;EACDC,kBAAkB,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,CAAC;EACtEC,QAAQ,EAAE,CAAC,MAAM,CAAC;EAClBC,YAAY,EAAE,CAAC,YAAY,CAAC;EAC5BC,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,CAAC;EACnEC,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC3BC,cAAc,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EACtCC,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,CAAC;EAC/DC,kBAAkB,EAAE,CAClB,MAAM;EAAE;EACR,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,cAAc,CACf;EACDC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,EAAE;EACdC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAAC,QAAQ,CAAC;EACrBC,WAAW,EAAE,CAAC,QAAQ,CAAC;EACvBC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC9BC,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;EAChCC,SAAS,EAAE,CAAC,MAAM,CAAC;EACnBC,QAAQ,EAAE,CAAC,MAAM,CAAC;EAClBC,WAAW,EAAE,CAAC,MAAM,CAAC;EACrBC,gBAAgB,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,gBAAgB,CAAC;EACjEC,uBAAuB,EAAE,CAAC,MAAM,CAAC;EACjCC,oBAAoB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;EAC3DC,oBAAoB,EAAE,CACpB,aAAa,EACb,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,QAAQ,CACT;EACDC,eAAe,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,CAAC;EAC3EC,oBAAoB,EAAE,CACpB,aAAa,EACb,MAAM,EACN,MAAM,EACN,cAAc,EACd,YAAY,CACb;EACDC,uBAAuB,EAAE,CACvB,aAAa,EACb,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,QAAQ,CACT;EACDC,mBAAmB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC;EACnEC,kBAAkB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC;EACnEC,mBAAmB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;EAC1DC,yBAAyB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC;EAC1EC,mBAAmB,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC;EACtEC,eAAe,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACjDC,mBAAmB,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC3CC,mBAAmB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC;EACnEC,sBAAsB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC;EACtEC,kBAAkB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC;EACnDC,iBAAiB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC;EACnDC,wBAAwB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,QAAQ;AAC3D,CAAC;AACD,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAAC/C,iBAAiB,CAAC,CAAC;AAC1D;AACA;AACA;;AAEA,OAAO,SAASgD,MAAMA,CAACC,SAAS,EAAE;EAChC,MAAMC,SAAS,GACbD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvD,IAAI;EACtE,OAAO,OAAOwD,SAAS,KAAK,QAAQ,IAAIN,UAAU,CAACO,GAAG,CAACD,SAAS,CAAC;AACnE;AACA;;AAEA,IAAIE,iBAAiB;AAErB,CAAC,UAAUA,iBAAiB,EAAE;EAC5BA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC1CA,iBAAiB,CAAC,cAAc,CAAC,GAAG,cAAc;AACpD,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjD,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}