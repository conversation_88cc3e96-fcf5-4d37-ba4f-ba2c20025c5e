{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Components\nimport { TasksComponent } from './tasks.component';\nconst routes = [{\n  path: '',\n  component: TasksComponent\n}, {\n  path: ':teamId',\n  component: TasksComponent\n}];\nexport let TasksModule = class TasksModule {};\nTasksModule = __decorate([NgModule({\n  declarations: [TasksComponent, KanbanBoardComponent, TaskAiAssistantComponent],\n  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule.forChild(routes),\n  // Angular Material\n  MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n  // CDK\n  DragDropModule, OverlayModule]\n})], TasksModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "TasksComponent", "routes", "path", "component", "TasksModule", "__decorate", "declarations", "KanbanBoardComponent", "TaskAiAssistantComponent", "imports", "ReactiveFormsModule", "FormsModule", "<PERSON><PERSON><PERSON><PERSON>", "MatButtonModule", "MatCardModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatChipsModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatDialogModule", "MatMenuModule", "MatTooltipModule", "MatTabsModule", "MatSlideToggleModule", "MatCheckboxModule", "MatBadgeModule", "MatExpansionModule", "MatListModule", "MatDividerModule", "DragDropModule", "OverlayModule"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\admin\\tasks\\tasks.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n\n// Components\nimport { TasksComponent } from './tasks.component';\n\nconst routes = [\n  {\n    path: '',\n    component: TasksComponent\n  },\n  {\n    path: ':teamId',\n    component: TasksComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TasksComponent,\n    KanbanBoardComponent,\n    TaskAiAssistantComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule.forChild(routes),\n\n    // Angular Material\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDialogModule,\n    MatMenuModule,\n    MatTooltipModule,\n    MatTabsModule,\n    MatSlideToggleModule,\n    MatCheckboxModule,\n    MatBadgeModule,\n    MatExpansionModule,\n    MatListModule,\n    MatDividerModule,\n\n    // CDK\n    DragDropModule,\n    OverlayModule\n  ]\n})\nexport class TasksModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAK9C;AACA,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEH;CACZ,CACF;AA0CM,WAAMI,WAAW,GAAjB,MAAMA,WAAW,GAAI;AAAfA,WAAW,GAAAC,UAAA,EAxCvBR,QAAQ,CAAC;EACRS,YAAY,EAAE,CACZN,cAAc,EACdO,oBAAoB,EACpBC,wBAAwB,CACzB;EACDC,OAAO,EAAE,CACPX,YAAY,EACZY,mBAAmB,EACnBC,WAAW,EACXZ,YAAY,CAACa,QAAQ,CAACX,MAAM,CAAC;EAE7B;EACAY,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;EAEhB;EACAC,cAAc,EACdC,aAAa;CAEhB,CAAC,C,EACW/B,WAAW,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}