{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMonths} function options.\n */\n\n/**\n * @name addMonths\n * @category Month Helpers\n * @summary Add the specified number of months to the given date.\n *\n * @description\n * Add the specified number of months to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be added.\n * @param options - The options object\n *\n * @returns The new date with the months added\n *\n * @example\n * // Add 5 months to 1 September 2014:\n * const result = addMonths(new Date(2014, 8, 1), 5)\n * //=> Sun Feb 01 2015 00:00:00\n *\n * // Add one month to 30 January 2023:\n * const result = addMonths(new Date(2023, 0, 30), 1)\n * //=> Tue Feb 28 2023 00:00:00\n */\nexport function addMonths(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n  if (!amount) {\n    // If 0 months, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  const dayOfMonth = _date.getDate();\n\n  // The JS Date object supports date math by accepting out-of-bounds values for\n  // month, day, etc. For example, new Date(2020, 0, 0) returns 31 Dec 2019 and\n  // new Date(2020, 13, 1) returns 1 Feb 2021.  This is *almost* the behavior we\n  // want except that dates will wrap around the end of a month, meaning that\n  // new Date(2020, 13, 31) will return 3 Mar 2021 not 28 Feb 2021 as desired. So\n  // we'll default to the end of the desired month by adding 1 to the desired\n  // month and using a date of 0 to back up one day to the end of the desired\n  // month.\n  const endOfDesiredMonth = constructFrom(options?.in || date, _date.getTime());\n  endOfDesiredMonth.setMonth(_date.getMonth() + amount + 1, 0);\n  const daysInMonth = endOfDesiredMonth.getDate();\n  if (dayOfMonth >= daysInMonth) {\n    // If we're already at the end of the month, then this is the correct date\n    // and we're done.\n    return endOfDesiredMonth;\n  } else {\n    // Otherwise, we now know that setting the original day-of-month value won't\n    // cause an overflow, so set the desired day-of-month. Note that we can't\n    // just set the date of `endOfDesiredMonth` because that object may have had\n    // its time changed in the unusual case where where a DST transition was on\n    // the last day of the month and its local time was in the hour skipped or\n    // repeated next to a DST transition.  So we use `date` instead which is\n    // guaranteed to still have the original time.\n    _date.setFullYear(endOfDesiredMonth.getFullYear(), endOfDesiredMonth.getMonth(), dayOfMonth);\n    return _date;\n  }\n}\n\n// Fallback for modularized imports:\nexport default addMonths;", "map": {"version": 3, "names": ["constructFrom", "toDate", "addMonths", "date", "amount", "options", "_date", "in", "isNaN", "NaN", "dayOfMonth", "getDate", "endOfDesiredMonth", "getTime", "setMonth", "getMonth", "daysInMonth", "setFullYear", "getFullYear"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/addMonths.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMonths} function options.\n */\n\n/**\n * @name addMonths\n * @category Month Helpers\n * @summary Add the specified number of months to the given date.\n *\n * @description\n * Add the specified number of months to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be added.\n * @param options - The options object\n *\n * @returns The new date with the months added\n *\n * @example\n * // Add 5 months to 1 September 2014:\n * const result = addMonths(new Date(2014, 8, 1), 5)\n * //=> Sun Feb 01 2015 00:00:00\n *\n * // Add one month to 30 January 2023:\n * const result = addMonths(new Date(2023, 0, 30), 1)\n * //=> Tue Feb 28 2023 00:00:00\n */\nexport function addMonths(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n  if (!amount) {\n    // If 0 months, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  const dayOfMonth = _date.getDate();\n\n  // The JS Date object supports date math by accepting out-of-bounds values for\n  // month, day, etc. For example, new Date(2020, 0, 0) returns 31 Dec 2019 and\n  // new Date(2020, 13, 1) returns 1 Feb 2021.  This is *almost* the behavior we\n  // want except that dates will wrap around the end of a month, meaning that\n  // new Date(2020, 13, 31) will return 3 Mar 2021 not 28 Feb 2021 as desired. So\n  // we'll default to the end of the desired month by adding 1 to the desired\n  // month and using a date of 0 to back up one day to the end of the desired\n  // month.\n  const endOfDesiredMonth = constructFrom(options?.in || date, _date.getTime());\n  endOfDesiredMonth.setMonth(_date.getMonth() + amount + 1, 0);\n  const daysInMonth = endOfDesiredMonth.getDate();\n  if (dayOfMonth >= daysInMonth) {\n    // If we're already at the end of the month, then this is the correct date\n    // and we're done.\n    return endOfDesiredMonth;\n  } else {\n    // Otherwise, we now know that setting the original day-of-month value won't\n    // cause an overflow, so set the desired day-of-month. Note that we can't\n    // just set the date of `endOfDesiredMonth` because that object may have had\n    // its time changed in the unusual case where where a DST transition was on\n    // the last day of the month and its local time was in the hour skipped or\n    // repeated next to a DST transition.  So we use `date` instead which is\n    // guaranteed to still have the original time.\n    _date.setFullYear(\n      endOfDesiredMonth.getFullYear(),\n      endOfDesiredMonth.getMonth(),\n      dayOfMonth,\n    );\n    return _date;\n  }\n}\n\n// Fallback for modularized imports:\nexport default addMonths;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/C,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,IAAIC,KAAK,CAACJ,MAAM,CAAC,EAAE,OAAOJ,aAAa,CAACK,OAAO,EAAEE,EAAE,IAAIJ,IAAI,EAAEM,GAAG,CAAC;EACjE,IAAI,CAACL,MAAM,EAAE;IACX;IACA,OAAOE,KAAK;EACd;EACA,MAAMI,UAAU,GAAGJ,KAAK,CAACK,OAAO,CAAC,CAAC;;EAElC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,iBAAiB,GAAGZ,aAAa,CAACK,OAAO,EAAEE,EAAE,IAAIJ,IAAI,EAAEG,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;EAC7ED,iBAAiB,CAACE,QAAQ,CAACR,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAGX,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5D,MAAMY,WAAW,GAAGJ,iBAAiB,CAACD,OAAO,CAAC,CAAC;EAC/C,IAAID,UAAU,IAAIM,WAAW,EAAE;IAC7B;IACA;IACA,OAAOJ,iBAAiB;EAC1B,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACAN,KAAK,CAACW,WAAW,CACfL,iBAAiB,CAACM,WAAW,CAAC,CAAC,EAC/BN,iBAAiB,CAACG,QAAQ,CAAC,CAAC,EAC5BL,UACF,CAAC;IACD,OAAOJ,KAAK;EACd;AACF;;AAEA;AACA,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}