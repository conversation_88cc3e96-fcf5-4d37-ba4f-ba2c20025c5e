{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { closestIndexTo } from \"./closestIndexTo.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link closestTo} function options.\n */\n\n/**\n * The {@link closestTo} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateToCompare - Date to compare argument type.\n * @typeParam DatesType - Dates array argument type.\n * @typeParam Options - Options type.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates, options) {\n  const [dateToCompare_, ...dates_] = normalizeDates(options?.in, dateToCompare, ...dates);\n  const index = closestIndexTo(dateToCompare_, dates_);\n  if (typeof index === \"number\" && isNaN(index)) return constructFrom(dateToCompare_, NaN);\n  if (index !== undefined) return dates_[index];\n}\n\n// Fallback for modularized imports:\nexport default closestTo;", "map": {"version": 3, "names": ["normalizeDates", "closestIndexTo", "constructFrom", "closestTo", "dateToCompare", "dates", "options", "dateToCompare_", "dates_", "in", "index", "isNaN", "NaN", "undefined"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/closestTo.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { closestIndexTo } from \"./closestIndexTo.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link closestTo} function options.\n */\n\n/**\n * The {@link closestTo} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateToCompare - Date to compare argument type.\n * @typeParam DatesType - Dates array argument type.\n * @typeParam Options - Options type.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates, options) {\n  const [dateToCompare_, ...dates_] = normalizeDates(\n    options?.in,\n    dateToCompare,\n    ...dates,\n  );\n\n  const index = closestIndexTo(dateToCompare_, dates_);\n\n  if (typeof index === \"number\" && isNaN(index))\n    return constructFrom(dateToCompare_, NaN);\n\n  if (index !== undefined) return dates_[index];\n}\n\n// Fallback for modularized imports:\nexport default closestTo;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,aAAa,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACvD,MAAM,CAACC,cAAc,EAAE,GAAGC,MAAM,CAAC,GAAGR,cAAc,CAChDM,OAAO,EAAEG,EAAE,EACXL,aAAa,EACb,GAAGC,KACL,CAAC;EAED,MAAMK,KAAK,GAAGT,cAAc,CAACM,cAAc,EAAEC,MAAM,CAAC;EAEpD,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAIC,KAAK,CAACD,KAAK,CAAC,EAC3C,OAAOR,aAAa,CAACK,cAAc,EAAEK,GAAG,CAAC;EAE3C,IAAIF,KAAK,KAAKG,SAAS,EAAE,OAAOL,MAAM,CAACE,KAAK,CAAC;AAC/C;;AAEA;AACA,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}