{"ast": null, "code": "import { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * Unlike `valueFromAST()`, no type is provided. The resulting JavaScript value\n * will reflect the provided GraphQL value AST.\n *\n * | GraphQL Value        | JavaScript Value |\n * | -------------------- | ---------------- |\n * | Input Object         | Object           |\n * | List                 | Array            |\n * | Boolean              | Boolean          |\n * | String / Enum        | String           |\n * | Int / Float          | Number           |\n * | Null                 | null             |\n *\n */\n\nexport function valueFromASTUntyped(valueNode, variables) {\n  switch (valueNode.kind) {\n    case Kind.NULL:\n      return null;\n    case Kind.INT:\n      return parseInt(valueNode.value, 10);\n    case Kind.FLOAT:\n      return parseFloat(valueNode.value);\n    case Kind.STRING:\n    case Kind.ENUM:\n    case Kind.BOOLEAN:\n      return valueNode.value;\n    case Kind.LIST:\n      return valueNode.values.map(node => valueFromASTUntyped(node, variables));\n    case Kind.OBJECT:\n      return keyValMap(valueNode.fields, field => field.name.value, field => valueFromASTUntyped(field.value, variables));\n    case Kind.VARIABLE:\n      return variables === null || variables === void 0 ? void 0 : variables[valueNode.name.value];\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}