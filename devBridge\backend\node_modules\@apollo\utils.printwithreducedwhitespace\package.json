{"name": "@apollo/utils.printwithreducedwhitespace", "version": "2.0.1", "description": "Print an AST with as little whitespace as possible", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "packages/printWithReducedWhitespace/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=14"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}