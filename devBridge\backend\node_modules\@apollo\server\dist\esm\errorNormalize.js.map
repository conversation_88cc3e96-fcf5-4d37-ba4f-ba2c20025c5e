{"version": 3, "file": "errorNormalize.js", "sourceRoot": "", "sources": ["../../src/errorNormalize.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,YAAY,GAGb,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAE1D,OAAO,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAC7E,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAUjD,MAAM,UAAU,wBAAwB,CACtC,MAA8B,EAC9B,UAMI,EAAE;IAKN,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9D,MAAM,cAAc,GAAG,kBAAkB,EAAE,CAAC;IAE5C,OAAO;QACL,cAAc;QACd,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC;gBACH,OAAO,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,eAAe,EAAE,CAAC;gBACzB,IAAI,OAAO,CAAC,iCAAiC,EAAE,CAAC;oBAG9C,OAAO,WAAW,CAAC,eAAe,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBAEN,OAAO;wBACL,OAAO,EAAE,uBAAuB;wBAChC,UAAU,EAAE,EAAE,IAAI,EAAE,qBAAqB,CAAC,qBAAqB,EAAE;qBAClE,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,CAAC;KACH,CAAC;IAEF,SAAS,WAAW,CAAC,UAAmB;QACtC,MAAM,YAAY,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAEpD,MAAM,UAAU,GAA2B;YACzC,GAAG,YAAY,CAAC,UAAU;YAC1B,IAAI,EACF,YAAY,CAAC,UAAU,CAAC,IAAI;gBAC5B,qBAAqB,CAAC,qBAAqB;SAC9C,CAAC;QAEF,IAAI,wBAAwB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,oBAAoB,CAAC,cAAc,EAAE;gBACnC,OAAO,EAAE,IAAI,SAAS,EAAE;gBACxB,GAAG,UAAU,CAAC,IAAI;aACnB,CAAC,CAAC;YACH,OAAO,UAAU,CAAC,IAAI,CAAC;QACzB,CAAC;QAED,IAAI,OAAO,CAAC,iCAAiC,EAAE,CAAC;YAK9C,UAAU,CAAC,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC;IAClD,CAAC;AACH,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,UAAmB;IAC7C,OAAO,UAAU,YAAY,KAAK;QAChC,CAAC,CAAC,UAAU;QACZ,CAAC,CAAC,IAAI,YAAY,CAAC,0BAA0B,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,UAAmB,EACnB,iCAAyC,EAAE;IAE3C,MAAM,KAAK,GAAU,WAAW,CAAC,UAAU,CAAC,CAAC;IAE7C,OAAO,KAAK,YAAY,YAAY;QAClC,CAAC,CAAC,KAAK;QACP,CAAC,CAAC,IAAI,YAAY,CAAC,8BAA8B,GAAG,KAAK,CAAC,OAAO,EAAE;YAC/D,aAAa,EAAE,KAAK;SACrB,CAAC,CAAC;AACT,CAAC;AAED,SAAS,wBAAwB,CAAC,CAAU;IAC1C,OAAO,CACL,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,QAAQ;QACrB,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,OAAQ,CAAS,CAAC,MAAM,KAAK,QAAQ,CAAC;QAC3D,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,IAAK,CAAS,CAAC,OAAO,YAAY,GAAG,CAAC,CACzD,CAAC;AACJ,CAAC"}