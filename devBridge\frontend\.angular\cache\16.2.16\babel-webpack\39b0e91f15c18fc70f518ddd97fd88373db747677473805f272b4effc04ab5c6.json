{"ast": null, "code": "/**\n * Creates a keyed JS object from an array, given a function to produce the keys\n * for each value in the array.\n *\n * This provides a convenient lookup for the array items if the key function\n * produces unique results.\n * ```ts\n * const phoneBook = [\n *   { name: '<PERSON>', num: '555-1234' },\n *   { name: '<PERSON>', num: '867-5309' }\n * ]\n *\n * const entriesByName = keyMap(\n *   phoneBook,\n *   entry => entry.name\n * )\n *\n * // {\n * //   Jon: { name: '<PERSON>', num: '555-1234' },\n * //   Jenny: { name: '<PERSON>', num: '867-5309' }\n * // }\n *\n * const jennyEntry = entriesByName['Jenny']\n *\n * // { name: 'Jenny', num: '857-6309' }\n * ```\n */\nexport function keyMap(list, keyFn) {\n  const result = Object.create(null);\n  for (const item of list) {\n    result[keyFn(item)] = item;\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}