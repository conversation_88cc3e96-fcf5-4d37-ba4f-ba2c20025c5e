{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { finalize, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction EquipeListComponent_option_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction EquipeListComponent_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r8.label, \" \");\n  }\n}\nfunction EquipeListComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"div\", 49)(3, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 51);\n    i0.ɵɵtext(5, \" Chargement des \\u00E9quipes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"h3\", 57);\n    i0.ɵɵtext(7, \" Erreur de chargement des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 58);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_78_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.loadEquipes());\n    });\n    i0.ɵɵelement(11, \"i\", 60);\n    i0.ɵɵtext(12, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 64);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 65);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_79_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(8, \"i\", 67);\n    i0.ɵɵtext(9, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 110);\n    i0.ɵɵelement(1, \"i\", 111);\n    i0.ɵɵtext(2, \" Compl\\u00E8te \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" #\", tag_r23, \" \");\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equipe_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", equipe_r14.tags.length - 3, \" \");\n  }\n}\nfunction EquipeListComponent_div_80_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_80_div_1_div_20_span_1_Template, 2, 1, \"span\", 113);\n    i0.ɵɵtemplate(2, EquipeListComponent_div_80_div_1_div_20_span_2_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equipe_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", equipe_r14.tags.slice(0, 3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equipe_r14.tags.length > 3);\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r26.navigateToEditEquipe(equipe_r14._id));\n    });\n    i0.ɵɵelement(1, \"i\", 118);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_51_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.archiveTeam(equipe_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 120);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.activateTeam(equipe_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 122);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_button_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_button_53_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const equipe_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r35.deleteEquipe(equipe_r14._id));\n    });\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeListComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"div\", 9)(3, \"div\", 71);\n    i0.ɵɵelementStart(4, \"div\", 72)(5, \"div\", 73)(6, \"div\", 56)(7, \"div\", 74)(8, \"h3\", 75);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"span\", 77);\n    i0.ɵɵelement(14, \"i\", 78);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, EquipeListComponent_div_80_div_1_span_16_Template, 3, 0, \"span\", 79);\n    i0.ɵɵelementStart(17, \"span\", 80);\n    i0.ɵɵelement(18, \"i\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, EquipeListComponent_div_80_div_1_div_20_Template, 3, 2, \"div\", 82);\n    i0.ɵɵelementStart(21, \"div\", 83);\n    i0.ɵɵelement(22, \"i\", 84);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 85);\n    i0.ɵɵelement(25, \"i\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"p\", 87);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"slice\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 88)(30, \"div\", 89)(31, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r38.navigateToTasks(equipe_r14._id));\n    });\n    i0.ɵɵelement(32, \"div\", 91);\n    i0.ɵɵelementStart(33, \"div\", 92)(34, \"div\", 93);\n    i0.ɵɵelement(35, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 95);\n    i0.ɵɵtext(37, \"G\\u00E9rer les T\\u00E2ches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 96);\n    i0.ɵɵelement(39, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"div\", 98)(41, \"div\", 99)(42, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 101);\n    i0.ɵɵelement(44, \"i\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 103)(46, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_80_div_1_Template_button_click_46_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const equipe_r14 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r14._id && ctx_r40.navigateToEquipeDetail(equipe_r14._id));\n    });\n    i0.ɵɵelement(47, \"i\", 105);\n    i0.ɵɵtext(48, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 33);\n    i0.ɵɵtemplate(50, EquipeListComponent_div_80_div_1_button_50_Template, 2, 0, \"button\", 106);\n    i0.ɵɵtemplate(51, EquipeListComponent_div_80_div_1_button_51_Template, 2, 0, \"button\", 107);\n    i0.ɵɵtemplate(52, EquipeListComponent_div_80_div_1_button_52_Template, 2, 0, \"button\", 108);\n    i0.ɵɵtemplate(53, EquipeListComponent_div_80_div_1_button_53_Template, 2, 0, \"button\", 109);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"px-2 py-1 rounded-full text-xs font-medium \" + ctx_r13.getStatusBadgeClass(equipe_r14.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getStatusText(equipe_r14.status), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r13.getMemberCount(equipe_r14), \"/\", equipe_r14.maxMembers || 10, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.isTeamFull(equipe_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(equipe_r14.isPublic ? \"bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400\" : \"bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(equipe_r14.isPublic ? \"fas fa-globe\" : \"fas fa-lock\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.isPublic ? \"Publique\" : \"Priv\\u00E9e\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equipe_r14.tags && equipe_r14.tags.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r13.getAdminDisplayName(equipe_r14.admin), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r14.description && equipe_r14.description.length > 80 ? i0.ɵɵpipeBind3(28, 19, equipe_r14.description, 0, 80) + \"...\" : equipe_r14.description || \"Aucune description disponible\", \" \");\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14) && equipe_r14.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canEditTeam(equipe_r14) && equipe_r14.status === \"archived\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.canDeleteTeam(equipe_r14));\n  }\n}\nfunction EquipeListComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_80_div_1_Template, 54, 23, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.equipes);\n  }\n}\nfunction EquipeListComponent_div_81_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_button_7_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const i_r43 = restoredCtx.index;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onPageChange(i_r43 + 1));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r43 = ctx.index;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gradient-to-r\", ctx_r41.currentPage === i_r43 + 1)(\"from-[#4f5fad]\", ctx_r41.currentPage === i_r43 + 1)(\"to-[#7826b5]\", ctx_r41.currentPage === i_r43 + 1)(\"dark:from-[#00f7ff]\", ctx_r41.currentPage === i_r43 + 1)(\"dark:to-[#4f5fad]\", ctx_r41.currentPage === i_r43 + 1)(\"text-white\", ctx_r41.currentPage === i_r43 + 1)(\"text-[#6d6870]\", ctx_r41.currentPage !== i_r43 + 1)(\"dark:text-[#a0a0a0]\", ctx_r41.currentPage !== i_r43 + 1)(\"hover:text-[#4f5fad]\", ctx_r41.currentPage !== i_r43 + 1)(\"dark:hover:text-[#00f7ff]\", ctx_r41.currentPage !== i_r43 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r43 + 1, \" \");\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipeListComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126)(2, \"div\", 33)(3, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onPageChange(ctx_r46.currentPage - 1));\n    });\n    i0.ɵɵelement(4, \"i\", 128);\n    i0.ɵɵtext(5, \" Pr\\u00E9c\\u00E9dent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 129);\n    i0.ɵɵtemplate(7, EquipeListComponent_div_81_button_7_Template, 2, 21, \"button\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_81_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onPageChange(ctx_r48.currentPage + 1));\n    });\n    i0.ɵɵtext(9, \" Suivant \");\n    i0.ɵɵelement(10, \"i\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 132)(12, \"span\", 83);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(6, _c0).constructor(ctx_r6.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" Page \", ctx_r6.currentPage, \" sur \", ctx_r6.totalPages, \" (\", ctx_r6.totalItems, \" \\u00E9quipe(s) au total) \");\n  }\n}\nexport class EquipeListComponent {\n  constructor(equipeService, router, notificationService, authService) {\n    this.equipeService = equipeService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.authService = authService;\n    this.equipes = [];\n    this.loading = false;\n    this.error = null;\n    // Nouvelles propriétés pour les fonctionnalités avancées\n    this.searchControl = new FormControl('');\n    this.statusFilter = new FormControl('all');\n    this.publicFilter = new FormControl('all');\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalPages = 0;\n    this.totalItems = 0;\n    // Options pour les filtres\n    this.statusOptions = [{\n      value: 'all',\n      label: 'Tous les statuts'\n    }, {\n      value: 'active',\n      label: 'Actives'\n    }, {\n      value: 'inactive',\n      label: 'Inactives'\n    }, {\n      value: 'archived',\n      label: 'Archivées'\n    }];\n    this.publicOptions = [{\n      value: 'all',\n      label: 'Toutes'\n    }, {\n      value: 'true',\n      label: 'Publiques'\n    }, {\n      value: 'false',\n      label: 'Privées'\n    }];\n    // Colonnes affichées\n    this.displayedColumns = ['name', 'admin', 'members', 'status', 'actions'];\n    this.currentUser = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    this.setupSearchSubscription();\n    this.setupFilterSubscriptions();\n    this.loadEquipes();\n  }\n  setupSearchSubscription() {\n    this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(() => {\n      this.currentPage = 1;\n      this.loadEquipes();\n    });\n  }\n  setupFilterSubscriptions() {\n    this.statusFilter.valueChanges.subscribe(() => {\n      this.currentPage = 1;\n      this.loadEquipes();\n    });\n    this.publicFilter.valueChanges.subscribe(() => {\n      this.currentPage = 1;\n      this.loadEquipes();\n    });\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.error = null;\n    const filters = {\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n    // Ajouter les filtres si ils ne sont pas \"all\"\n    const searchValue = this.searchControl.value?.trim();\n    if (searchValue) {\n      filters.search = searchValue;\n    }\n    const statusValue = this.statusFilter.value;\n    if (statusValue && statusValue !== 'all') {\n      filters.status = statusValue;\n    }\n    const publicValue = this.publicFilter.value;\n    if (publicValue && publicValue !== 'all') {\n      filters.isPublic = publicValue === 'true';\n    }\n    this.equipeService.getEquipesWithFilters(filters).pipe(finalize(() => this.loading = false)).subscribe({\n      next: response => {\n        console.log('Équipes chargées:', response);\n        this.equipes = response.teams;\n        this.totalPages = response.pagination.total;\n        this.totalItems = response.pagination.totalItems;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n        // Fallback vers l'ancienne méthode\n        this.loadEquipesLegacy();\n      }\n    });\n  }\n  loadEquipesLegacy() {\n    this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        console.log('Équipes chargées (legacy):', data);\n        this.equipes = data;\n        this.totalItems = data.length;\n        this.totalPages = Math.ceil(data.length / this.itemsPerPage);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes (legacy):', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n  navigateToAddEquipe() {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n  navigateToEditEquipe(id) {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n  navigateToEquipeDetail(id) {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n      this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n  navigateToTasks(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    // Naviguer vers la page des tâches de l'équipe (route admin)\n    this.router.navigate(['/admin/tasks', id]);\n  }\n  // Méthode pour obtenir le nombre de tâches d'une équipe (simulé)\n  getTaskCount(equipeId) {\n    // TODO: Implémenter l'appel API réel pour obtenir le nombre de tâches\n    // Pour l'instant, retourner un nombre aléatoire pour la démonstration\n    const counts = [0, 5, 9, 15, 7, 11, 18, 3];\n    const index = equipeId.length % counts.length;\n    return counts[index];\n  }\n  // Méthode pour obtenir le statut des tâches d'une équipe\n  getTaskStatus(equipeId) {\n    const total = this.getTaskCount(equipeId);\n    const completed = Math.floor(total * 0.7); // 70% des tâches sont complétées (admin a plus de contrôle)\n    const percentage = total > 0 ? Math.round(completed / total * 100) : 0;\n    return {\n      completed,\n      total,\n      percentage\n    };\n  }\n  // Nouvelles méthodes pour les fonctionnalités avancées\n  /**\n   * Gestion de la pagination\n   */\n  onPageChange(page) {\n    this.currentPage = page;\n    this.loadEquipes();\n  }\n  /**\n   * Changer le nombre d'éléments par page\n   */\n  onItemsPerPageChange(itemsPerPage) {\n    this.itemsPerPage = itemsPerPage;\n    this.currentPage = 1;\n    this.loadEquipes();\n  }\n  /**\n   * Réinitialiser tous les filtres\n   */\n  resetFilters() {\n    this.searchControl.setValue('');\n    this.statusFilter.setValue('all');\n    this.publicFilter.setValue('all');\n    this.currentPage = 1;\n    this.loadEquipes();\n  }\n  /**\n   * Archiver une équipe\n   */\n  archiveTeam(equipe) {\n    if (!equipe._id) return;\n    if (confirm(`Êtes-vous sûr de vouloir archiver l'équipe \"${equipe.name}\" ?`)) {\n      this.loading = true;\n      this.equipeService.archiveTeam(equipe._id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: response => {\n          this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été archivée`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'archivage:', error);\n          this.notificationService.showError('Erreur lors de l\\'archivage de l\\'équipe');\n        }\n      });\n    }\n  }\n  /**\n   * Activer une équipe\n   */\n  activateTeam(equipe) {\n    if (!equipe._id) return;\n    this.loading = true;\n    this.equipeService.activateTeam(equipe._id).pipe(finalize(() => this.loading = false)).subscribe({\n      next: response => {\n        this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été activée`);\n        this.loadEquipes();\n      },\n      error: error => {\n        console.error('Erreur lors de l\\'activation:', error);\n        this.notificationService.showError('Erreur lors de l\\'activation de l\\'équipe');\n      }\n    });\n  }\n  /**\n   * Vérifier si l'utilisateur peut modifier une équipe\n   */\n  canEditTeam(equipe) {\n    if (!this.currentUser) return false;\n    // Admin système peut tout modifier\n    if (this.currentUser.role === 'admin') return true;\n    // Admin de l'équipe peut modifier\n    return this.equipeService.isTeamAdmin(equipe, this.currentUser.id);\n  }\n  /**\n   * Vérifier si l'utilisateur peut supprimer une équipe\n   */\n  canDeleteTeam(equipe) {\n    return this.canEditTeam(equipe);\n  }\n  /**\n   * Obtenir le nom d'affichage d'un admin\n   */\n  getAdminDisplayName(admin) {\n    if (!admin) return 'Non assigné';\n    if (typeof admin === 'string') return admin;\n    return admin.fullName || admin.username || admin.email || 'Utilisateur';\n  }\n  /**\n   * Obtenir le nombre de membres d'une équipe\n   */\n  getMemberCount(equipe) {\n    return equipe.memberCount || equipe.members?.length || 0;\n  }\n  /**\n   * Obtenir le badge de statut\n   */\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case 'active':\n        return 'badge-success';\n      case 'inactive':\n        return 'badge-warning';\n      case 'archived':\n        return 'badge-secondary';\n      default:\n        return 'badge-primary';\n    }\n  }\n  /**\n   * Obtenir le texte du statut\n   */\n  getStatusText(status) {\n    switch (status) {\n      case 'active':\n        return 'Active';\n      case 'inactive':\n        return 'Inactive';\n      case 'archived':\n        return 'Archivée';\n      default:\n        return 'Inconnue';\n    }\n  }\n  /**\n   * Vérifier si une équipe est pleine\n   */\n  isTeamFull(equipe) {\n    return equipe.isFullTeam || false;\n  }\n  /**\n   * Obtenir les slots disponibles\n   */\n  getAvailableSlots(equipe) {\n    return equipe.availableSlots || 0;\n  }\n  static {\n    this.ɵfac = function EquipeListComponent_Factory(t) {\n      return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeListComponent,\n      selectors: [[\"app-equipe-list\"]],\n      decls: 82,\n      vars: 13,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"mt-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"font-medium\"], [1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [1, \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-4\", \"gap-4\"], [1, \"lg:col-span-2\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Nom d'\\u00E9quipe, description, tags...\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-10\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"text-[#2d3748]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]\", \"dark:placeholder-[#a0a0a0]\", \"transition-all\", 3, \"formControl\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-filter\", \"mr-1\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"text-[#2d3748]\", \"dark:text-[#e0e0e0]\", \"transition-all\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-4\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"px-2\", \"py-1\", \"bg-[#f8fafc]\", \"dark:bg-[#0f0f0f]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded\", \"text-xs\", 3, \"value\", \"change\"], [\"value\", \"5\"], [\"value\", \"10\"], [\"value\", \"20\"], [\"value\", \"50\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\", 4, \"ngIf\"], [\"class\", \"mt-8 flex items-center justify-center\", 4, \"ngIf\"], [3, \"value\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"mt-3\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"text-center\", \"py-16\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"xl:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-2\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-6\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-2\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"group-hover:scale-[1.02]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-xs\", \"mb-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [\"class\", \"bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded-full font-medium\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"mr-1\"], [\"class\", \"flex flex-wrap gap-1 mb-2\", 4, \"ngIf\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-crown\", \"mr-1\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-lg\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"line-clamp-2\", \"mb-4\"], [1, \"px-6\", \"pb-6\"], [1, \"mb-4\", \"relative\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"via-[#00e68a]\", \"to-[#00d4aa]\", \"hover:from-[#00e68a]\", \"hover:via-[#00d4aa]\", \"hover:to-[#00c199]\", \"text-white\", \"font-bold\", \"py-4\", \"px-6\", \"rounded-xl\", \"transition-all\", \"duration-500\", \"hover:scale-[1.03]\", \"hover:shadow-2xl\", \"hover:shadow-[#00ff9d]/30\", \"flex\", \"items-center\", \"justify-center\", \"group\", \"relative\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-transparent\", \"via-white/20\", \"to-transparent\", \"-skew-x-12\", \"-translate-x-full\", \"group-hover:translate-x-full\", \"transition-transform\", \"duration-1000\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-white/20\", \"rounded-full\", \"p-2\", \"mr-3\", \"group-hover:bg-white/30\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-tasks\", \"text-lg\", \"group-hover:scale-110\", \"group-hover:rotate-12\", \"transition-all\", \"duration-300\"], [1, \"text-lg\", \"tracking-wide\"], [1, \"ml-3\", \"bg-white/20\", \"rounded-full\", \"p-2\", \"group-hover:bg-white/30\", \"group-hover:translate-x-1\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-arrow-right\", \"text-sm\"], [1, \"absolute\", \"top-2\", \"right-4\", \"w-1\", \"h-1\", \"bg-white/60\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"bottom-3\", \"left-6\", \"w-1\", \"h-1\", \"bg-white/40\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"0.5s\"], [1, \"absolute\", \"top-4\", \"left-1/3\", \"w-0.5\", \"h-0.5\", \"bg-white/50\", \"rounded-full\", \"animate-pulse\", 2, \"animation-delay\", \"1s\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"bg-[#4f5fad]\", \"text-white\", \"text-xs\", \"font-bold\", \"rounded-full\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"animate-bounce\"], [1, \"fas\", \"fa-crown\", \"text-xs\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex\", \"items-center\", \"group/details\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\", \"group-hover/details:scale-110\", \"transition-transform\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\", \"title\", \"Modifier l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#f59e0b] hover:bg-[#f59e0b]/10 rounded-lg transition-all\", \"title\", \"Archiver l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#10b981] hover:bg-[#10b981]/10 rounded-lg transition-all\", \"title\", \"Activer l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\", \"title\", \"Supprimer l'\\u00E9quipe\", 3, \"click\", 4, \"ngIf\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/20\", \"text-orange-600\", \"dark:text-orange-400\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mb-2\"], [\"class\", \"bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-0.5 rounded text-xs\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-[#6d6870] dark:text-[#a0a0a0] text-xs\", 4, \"ngIf\"], [1, \"bg-[#4f5fad]/5\", \"dark:bg-[#00f7ff]/5\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-0.5\", \"rounded\", \"text-xs\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-xs\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"Archiver l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#f59e0b]\", \"hover:bg-[#f59e0b]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-archive\"], [\"title\", \"Activer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#10b981]\", \"hover:bg-[#10b981]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-play\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"mt-8\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-4\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-chevron-left\", \"mr-1\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"w-10 h-10 rounded-lg font-medium text-sm transition-all hover:scale-105\", 3, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\", \"ml-1\"], [1, \"mt-3\", \"pt-3\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\", \"text-center\"], [1, \"w-10\", \"h-10\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"transition-all\", \"hover:scale-105\", 3, \"click\"]],\n      template: function EquipeListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25, \" \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27, \" G\\u00E9rez vos \\u00E9quipes et leurs membres avec style futuriste \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"span\", 17);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" \\u00E9quipe(s) au total \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_32_listener() {\n            return ctx.navigateToAddEquipe();\n          });\n          i0.ɵɵelement(33, \"i\", 19);\n          i0.ɵɵtext(34, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 20)(36, \"div\", 11)(37, \"div\", 21)(38, \"div\", 22)(39, \"label\", 23);\n          i0.ɵɵelement(40, \"i\", 24);\n          i0.ɵɵtext(41, \" Rechercher \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 25);\n          i0.ɵɵelement(43, \"input\", 26)(44, \"i\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\")(46, \"label\", 23);\n          i0.ɵɵelement(47, \"i\", 28);\n          i0.ɵɵtext(48, \" Statut \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"select\", 29);\n          i0.ɵɵtemplate(50, EquipeListComponent_option_50_Template, 2, 2, \"option\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\")(52, \"label\", 23);\n          i0.ɵɵelement(53, \"i\", 31);\n          i0.ɵɵtext(54, \" Visibilit\\u00E9 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"select\", 29);\n          i0.ɵɵtemplate(56, EquipeListComponent_option_56_Template, 2, 2, \"option\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\", 32)(58, \"div\", 33)(59, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_59_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelement(60, \"i\", 35);\n          i0.ɵɵtext(61, \" R\\u00E9initialiser \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 36)(63, \"span\");\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 33)(66, \"label\");\n          i0.ɵɵtext(67, \"Par page:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"select\", 37);\n          i0.ɵɵlistener(\"change\", function EquipeListComponent_Template_select_change_68_listener($event) {\n            return ctx.onItemsPerPageChange(+$event.target.value);\n          });\n          i0.ɵɵelementStart(69, \"option\", 38);\n          i0.ɵɵtext(70, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 39);\n          i0.ɵɵtext(72, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"option\", 40);\n          i0.ɵɵtext(74, \"20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"option\", 41);\n          i0.ɵɵtext(76, \"50\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(77, EquipeListComponent_div_77_Template, 6, 0, \"div\", 42);\n          i0.ɵɵtemplate(78, EquipeListComponent_div_78_Template, 13, 1, \"div\", 43);\n          i0.ɵɵtemplate(79, EquipeListComponent_div_79_Template, 10, 0, \"div\", 44);\n          i0.ɵɵtemplate(80, EquipeListComponent_div_80_Template, 2, 1, \"div\", 45);\n          i0.ɵɵtemplate(81, EquipeListComponent_div_81_Template, 14, 7, \"div\", 46);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(30);\n          i0.ɵɵtextInterpolate(ctx.totalItems);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formControl\", ctx.publicFilter);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.publicOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\", ctx.equipes.length, \" r\\u00E9sultat(s)\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.itemsPerPage);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0 && ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.FormControlDirective, i5.SlicePipe],\n      styles: [\".hover-shadow[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n  }\\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .card-header.bg-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkVBQUU7SUFDRSwyQkFBMkI7SUFDM0Isa0RBQWtEO0VBQ3BEOztFQUVBO0lBQ0UseUJBQXlCO0VBQzNCOztFQUVBO0lBQ0UsK0RBQStEO0VBQ2pFIiwiZmlsZSI6ImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5ob3Zlci1zaGFkb3c6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAudHJhbnNpdGlvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLmNhcmQtaGVhZGVyLmJnLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjNjYxMGYyKSAhaW1wb3J0YW50O1xyXG4gIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtbGlzdC9lcXVpcGUtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJFQUFFO0lBQ0UsMkJBQTJCO0lBQzNCLGtEQUFrRDtFQUNwRDs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLCtEQUErRDtFQUNqRTtBQUNGLDR1QkFBNHVCIiwic291cmNlc0NvbnRlbnQiOlsiICAuaG92ZXItc2hhZG93OmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMTBweCAyMHB4IHJnYmEoMCwwLDAsMC4xKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLnRyYW5zaXRpb24ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWhlYWRlci5iZy1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwN2JmZiwgIzY2MTBmMikgIWltcG9ydGFudDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "finalize", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r7", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "option_r8", "ɵɵelement", "ɵɵlistener", "EquipeListComponent_div_78_Template_button_click_10_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "loadEquipes", "ctx_r3", "error", "EquipeListComponent_div_79_Template_button_click_7_listener", "_r12", "ctx_r11", "navigateToAddEquipe", "tag_r23", "equipe_r14", "tags", "length", "ɵɵtemplate", "EquipeListComponent_div_80_div_1_div_20_span_1_Template", "EquipeListComponent_div_80_div_1_div_20_span_2_Template", "slice", "EquipeListComponent_div_80_div_1_button_50_Template_button_click_0_listener", "_r28", "$implicit", "ctx_r26", "_id", "navigateToEditEquipe", "EquipeListComponent_div_80_div_1_button_51_Template_button_click_0_listener", "_r31", "ctx_r29", "archiveTeam", "EquipeListComponent_div_80_div_1_button_52_Template_button_click_0_listener", "_r34", "ctx_r32", "activateTeam", "EquipeListComponent_div_80_div_1_button_53_Template_button_click_0_listener", "_r37", "ctx_r35", "deleteEquipe", "EquipeListComponent_div_80_div_1_span_16_Template", "EquipeListComponent_div_80_div_1_div_20_Template", "EquipeListComponent_div_80_div_1_Template_button_click_31_listener", "restoredCtx", "_r39", "ctx_r38", "navigateToTasks", "EquipeListComponent_div_80_div_1_Template_button_click_46_listener", "ctx_r40", "navigateToEquipeDetail", "EquipeListComponent_div_80_div_1_button_50_Template", "EquipeListComponent_div_80_div_1_button_51_Template", "EquipeListComponent_div_80_div_1_button_52_Template", "EquipeListComponent_div_80_div_1_button_53_Template", "name", "ɵɵclassMap", "ctx_r13", "getStatusBadgeClass", "status", "getStatusText", "ɵɵtextInterpolate2", "getMemberCount", "maxMembers", "isTeamFull", "isPublic", "getAdminDisplayName", "admin", "description", "ɵɵpipeBind3", "canEditTeam", "canDeleteTeam", "EquipeListComponent_div_80_div_1_Template", "ctx_r5", "equipes", "EquipeListComponent_div_81_button_7_Template_button_click_0_listener", "_r45", "i_r43", "index", "ctx_r44", "onPageChange", "ɵɵclassProp", "ctx_r41", "currentPage", "EquipeListComponent_div_81_Template_button_click_3_listener", "_r47", "ctx_r46", "EquipeListComponent_div_81_button_7_Template", "EquipeListComponent_div_81_Template_button_click_8_listener", "ctx_r48", "ctx_r6", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "ɵɵtextInterpolate3", "totalItems", "EquipeListComponent", "equipeService", "router", "notificationService", "authService", "loading", "searchControl", "statusFilter", "publicFilter", "itemsPerPage", "statusOptions", "publicOptions", "displayedColumns", "currentUser", "getCurrentUser", "ngOnInit", "setupSearchSubscription", "setupFilterSubscriptions", "valueChanges", "pipe", "subscribe", "filters", "page", "limit", "searchValue", "trim", "search", "statusValue", "publicValue", "getEquipesWithFilters", "next", "response", "console", "log", "teams", "pagination", "total", "showError", "loadEquipesLegacy", "getEquipes", "data", "Math", "ceil", "navigate", "id", "equipe", "find", "e", "equipeName", "confirm", "showSuccess", "getTaskCount", "equipeId", "counts", "getTaskStatus", "completed", "floor", "percentage", "round", "onItemsPerPageChange", "resetFilters", "setValue", "role", "isTeamAdmin", "fullName", "username", "email", "memberCount", "members", "isFullTeam", "getAvailableSlots", "availableSlots", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "Router", "i3", "NotificationService", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "EquipeListComponent_Template", "rf", "ctx", "EquipeListComponent_Template_button_click_32_listener", "EquipeListComponent_option_50_Template", "EquipeListComponent_option_56_Template", "EquipeListComponent_Template_button_click_59_listener", "EquipeListComponent_Template_select_change_68_listener", "$event", "target", "EquipeListComponent_div_77_Template", "EquipeListComponent_div_78_Template", "EquipeListComponent_div_79_Template", "EquipeListComponent_div_80_Template", "EquipeListComponent_div_81_Template", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.ts", "C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { FormControl } from '@angular/forms';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { NotificationService } from 'src/app/services/notification.service';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport {\r\n  Equipe,\r\n  TeamSearchFilters,\r\n  TeamListResponse\r\n} from 'src/app/models/equipe.model';\r\nimport { finalize, debounceTime, distinctUntilChanged } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-equipe-list',\r\n  templateUrl: './equipe-list.component.html',\r\n  styleUrls: ['./equipe-list.component.css']\r\n})\r\nexport class EquipeListComponent implements OnInit {\r\n  equipes: Equipe[] = [];\r\n  loading = false;\r\n  error: string | null = null;\r\n\r\n  // Nouvelles propriétés pour les fonctionnalités avancées\r\n  searchControl = new FormControl('');\r\n  statusFilter = new FormControl('all');\r\n  publicFilter = new FormControl('all');\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n  totalPages = 0;\r\n  totalItems = 0;\r\n\r\n  // Options pour les filtres\r\n  statusOptions = [\r\n    { value: 'all', label: 'Tous les statuts' },\r\n    { value: 'active', label: 'Actives' },\r\n    { value: 'inactive', label: 'Inactives' },\r\n    { value: 'archived', label: 'Archivées' }\r\n  ];\r\n\r\n  publicOptions = [\r\n    { value: 'all', label: 'Toutes' },\r\n    { value: 'true', label: 'Publiques' },\r\n    { value: 'false', label: 'Privées' }\r\n  ];\r\n\r\n  // Colonnes affichées\r\n  displayedColumns = ['name', 'admin', 'members', 'status', 'actions'];\r\n\r\n  currentUser: any;\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private router: Router,\r\n    private notificationService: NotificationService,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.currentUser = this.authService.getCurrentUser();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.setupSearchSubscription();\r\n    this.setupFilterSubscriptions();\r\n    this.loadEquipes();\r\n  }\r\n\r\n  private setupSearchSubscription(): void {\r\n    this.searchControl.valueChanges.pipe(\r\n      debounceTime(300),\r\n      distinctUntilChanged()\r\n    ).subscribe(() => {\r\n      this.currentPage = 1;\r\n      this.loadEquipes();\r\n    });\r\n  }\r\n\r\n  private setupFilterSubscriptions(): void {\r\n    this.statusFilter.valueChanges.subscribe(() => {\r\n      this.currentPage = 1;\r\n      this.loadEquipes();\r\n    });\r\n\r\n    this.publicFilter.valueChanges.subscribe(() => {\r\n      this.currentPage = 1;\r\n      this.loadEquipes();\r\n    });\r\n  }\r\n\r\n  loadEquipes(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    const filters: TeamSearchFilters = {\r\n      page: this.currentPage,\r\n      limit: this.itemsPerPage\r\n    };\r\n\r\n    // Ajouter les filtres si ils ne sont pas \"all\"\r\n    const searchValue = this.searchControl.value?.trim();\r\n    if (searchValue) {\r\n      filters.search = searchValue;\r\n    }\r\n\r\n    const statusValue = this.statusFilter.value;\r\n    if (statusValue && statusValue !== 'all') {\r\n      filters.status = statusValue;\r\n    }\r\n\r\n    const publicValue = this.publicFilter.value;\r\n    if (publicValue && publicValue !== 'all') {\r\n      filters.isPublic = publicValue === 'true';\r\n    }\r\n\r\n    this.equipeService.getEquipesWithFilters(filters).pipe(\r\n      finalize(() => this.loading = false)\r\n    ).subscribe({\r\n      next: (response: TeamListResponse) => {\r\n        console.log('Équipes chargées:', response);\r\n        this.equipes = response.teams;\r\n        this.totalPages = response.pagination.total;\r\n        this.totalItems = response.pagination.totalItems;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des équipes:', error);\r\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\r\n        this.notificationService.showError('Erreur lors du chargement des équipes');\r\n\r\n        // Fallback vers l'ancienne méthode\r\n        this.loadEquipesLegacy();\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadEquipesLegacy(): void {\r\n    this.equipeService.getEquipes().pipe(\r\n      finalize(() => this.loading = false)\r\n    ).subscribe({\r\n      next: (data) => {\r\n        console.log('Équipes chargées (legacy):', data);\r\n        this.equipes = data;\r\n        this.totalItems = data.length;\r\n        this.totalPages = Math.ceil(data.length / this.itemsPerPage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des équipes (legacy):', error);\r\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\r\n        this.notificationService.showError('Erreur lors du chargement des équipes');\r\n      }\r\n    });\r\n  }\r\n\r\n  navigateToAddEquipe(): void {\r\n    this.router.navigate(['/equipes/ajouter']);\r\n  }\r\n\r\n  navigateToEditEquipe(id: string): void {\r\n    this.router.navigate(['/equipes/modifier', id]);\r\n  }\r\n\r\n  navigateToEquipeDetail(id: string): void {\r\n    this.router.navigate(['/equipes/detail', id]);\r\n  }\r\n\r\n  deleteEquipe(id: string): void {\r\n    if (!id) {\r\n      console.error('ID est indéfini');\r\n      this.notificationService.showError('ID d\\'équipe invalide');\r\n      return;\r\n    }\r\n\r\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\r\n    const equipe = this.equipes.find(e => e._id === id);\r\n    const equipeName = equipe?.name || 'cette équipe';\r\n\r\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.deleteEquipe(id).pipe(\r\n        finalize(() => this.loading = false)\r\n      ).subscribe({\r\n        next: () => {\r\n          console.log('Équipe supprimée avec succès');\r\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\r\n          this.loadEquipes();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\r\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\r\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  navigateToTasks(id: string): void {\r\n    if (!id) {\r\n      console.error('ID est indéfini');\r\n      this.notificationService.showError('ID d\\'équipe invalide');\r\n      return;\r\n    }\r\n\r\n    const equipe = this.equipes.find(e => e._id === id);\r\n    const equipeName = equipe?.name || 'cette équipe';\r\n\r\n    // Naviguer vers la page des tâches de l'équipe (route admin)\r\n    this.router.navigate(['/admin/tasks', id]);\r\n  }\r\n\r\n  // Méthode pour obtenir le nombre de tâches d'une équipe (simulé)\r\n  getTaskCount(equipeId: string): number {\r\n    // TODO: Implémenter l'appel API réel pour obtenir le nombre de tâches\r\n    // Pour l'instant, retourner un nombre aléatoire pour la démonstration\r\n    const counts = [0, 5, 9, 15, 7, 11, 18, 3];\r\n    const index = equipeId.length % counts.length;\r\n    return counts[index];\r\n  }\r\n\r\n  // Méthode pour obtenir le statut des tâches d'une équipe\r\n  getTaskStatus(equipeId: string): { completed: number; total: number; percentage: number } {\r\n    const total = this.getTaskCount(equipeId);\r\n    const completed = Math.floor(total * 0.7); // 70% des tâches sont complétées (admin a plus de contrôle)\r\n    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;\r\n\r\n    return { completed, total, percentage };\r\n  }\r\n\r\n  // Nouvelles méthodes pour les fonctionnalités avancées\r\n\r\n  /**\r\n   * Gestion de la pagination\r\n   */\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.loadEquipes();\r\n  }\r\n\r\n  /**\r\n   * Changer le nombre d'éléments par page\r\n   */\r\n  onItemsPerPageChange(itemsPerPage: number): void {\r\n    this.itemsPerPage = itemsPerPage;\r\n    this.currentPage = 1;\r\n    this.loadEquipes();\r\n  }\r\n\r\n  /**\r\n   * Réinitialiser tous les filtres\r\n   */\r\n  resetFilters(): void {\r\n    this.searchControl.setValue('');\r\n    this.statusFilter.setValue('all');\r\n    this.publicFilter.setValue('all');\r\n    this.currentPage = 1;\r\n    this.loadEquipes();\r\n  }\r\n\r\n  /**\r\n   * Archiver une équipe\r\n   */\r\n  archiveTeam(equipe: Equipe): void {\r\n    if (!equipe._id) return;\r\n\r\n    if (confirm(`Êtes-vous sûr de vouloir archiver l'équipe \"${equipe.name}\" ?`)) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.archiveTeam(equipe._id).pipe(\r\n        finalize(() => this.loading = false)\r\n      ).subscribe({\r\n        next: (response) => {\r\n          this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été archivée`);\r\n          this.loadEquipes();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'archivage:', error);\r\n          this.notificationService.showError('Erreur lors de l\\'archivage de l\\'équipe');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Activer une équipe\r\n   */\r\n  activateTeam(equipe: Equipe): void {\r\n    if (!equipe._id) return;\r\n\r\n    this.loading = true;\r\n\r\n    this.equipeService.activateTeam(equipe._id).pipe(\r\n      finalize(() => this.loading = false)\r\n    ).subscribe({\r\n      next: (response) => {\r\n        this.notificationService.showSuccess(`L'équipe \"${equipe.name}\" a été activée`);\r\n        this.loadEquipes();\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de l\\'activation:', error);\r\n        this.notificationService.showError('Erreur lors de l\\'activation de l\\'équipe');\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Vérifier si l'utilisateur peut modifier une équipe\r\n   */\r\n  canEditTeam(equipe: Equipe): boolean {\r\n    if (!this.currentUser) return false;\r\n\r\n    // Admin système peut tout modifier\r\n    if (this.currentUser.role === 'admin') return true;\r\n\r\n    // Admin de l'équipe peut modifier\r\n    return this.equipeService.isTeamAdmin(equipe, this.currentUser.id);\r\n  }\r\n\r\n  /**\r\n   * Vérifier si l'utilisateur peut supprimer une équipe\r\n   */\r\n  canDeleteTeam(equipe: Equipe): boolean {\r\n    return this.canEditTeam(equipe);\r\n  }\r\n\r\n  /**\r\n   * Obtenir le nom d'affichage d'un admin\r\n   */\r\n  getAdminDisplayName(admin: any): string {\r\n    if (!admin) return 'Non assigné';\r\n\r\n    if (typeof admin === 'string') return admin;\r\n\r\n    return admin.fullName || admin.username || admin.email || 'Utilisateur';\r\n  }\r\n\r\n  /**\r\n   * Obtenir le nombre de membres d'une équipe\r\n   */\r\n  getMemberCount(equipe: Equipe): number {\r\n    return equipe.memberCount || equipe.members?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Obtenir le badge de statut\r\n   */\r\n  getStatusBadgeClass(status?: string): string {\r\n    switch (status) {\r\n      case 'active': return 'badge-success';\r\n      case 'inactive': return 'badge-warning';\r\n      case 'archived': return 'badge-secondary';\r\n      default: return 'badge-primary';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtenir le texte du statut\r\n   */\r\n  getStatusText(status?: string): string {\r\n    switch (status) {\r\n      case 'active': return 'Active';\r\n      case 'inactive': return 'Inactive';\r\n      case 'archived': return 'Archivée';\r\n      default: return 'Inconnue';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifier si une équipe est pleine\r\n   */\r\n  isTeamFull(equipe: Equipe): boolean {\r\n    return equipe.isFullTeam || false;\r\n  }\r\n\r\n  /**\r\n   * Obtenir les slots disponibles\r\n   */\r\n  getAvailableSlots(equipe: Equipe): number {\r\n    return equipe.availableSlots || 0;\r\n  }\r\n}\r\n\r\n", "<div\r\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\r\n    <!-- Header futuriste -->\r\n    <div class=\"mb-8 relative\">\r\n      <!-- Decorative top border -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\r\n      >\r\n        <div\r\n          class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\r\n        >\r\n          <div class=\"mb-4 lg:mb-0\">\r\n            <h1\r\n              class=\"text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide\"\r\n            >\r\n              Équipes\r\n            </h1>\r\n            <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\r\n              Gérez vos équipes et leurs membres avec style futuriste\r\n            </p>\r\n            <div class=\"mt-2 text-xs text-[#6d6870] dark:text-[#e0e0e0]\">\r\n              <span class=\"font-medium\">{{ totalItems }}</span> équipe(s) au total\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            (click)=\"navigateToAddEquipe()\"\r\n            class=\"relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\r\n          >\r\n            <i\r\n              class=\"fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300\"\r\n            ></i>\r\n            Nouvelle Équipe\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filtres et Recherche -->\r\n    <div class=\"mb-6\">\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\r\n      >\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n          <!-- Recherche -->\r\n          <div class=\"lg:col-span-2\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\r\n              <i class=\"fas fa-search mr-1\"></i>\r\n              Rechercher\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                [formControl]=\"searchControl\"\r\n                type=\"text\"\r\n                placeholder=\"Nom d'équipe, description, tags...\"\r\n                class=\"w-full px-4 py-3 pl-10 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] placeholder-[#6d6870] dark:placeholder-[#a0a0a0] transition-all\"\r\n              />\r\n              <i class=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6d6870] dark:text-[#a0a0a0]\"></i>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Filtre Statut -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\r\n              <i class=\"fas fa-filter mr-1\"></i>\r\n              Statut\r\n            </label>\r\n            <select\r\n              [formControl]=\"statusFilter\"\r\n              class=\"w-full px-4 py-3 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] transition-all\"\r\n            >\r\n              <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\r\n                {{ option.label }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n\r\n          <!-- Filtre Visibilité -->\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\r\n              <i class=\"fas fa-eye mr-1\"></i>\r\n              Visibilité\r\n            </label>\r\n            <select\r\n              [formControl]=\"publicFilter\"\r\n              class=\"w-full px-4 py-3 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent text-[#2d3748] dark:text-[#e0e0e0] transition-all\"\r\n            >\r\n              <option *ngFor=\"let option of publicOptions\" [value]=\"option.value\">\r\n                {{ option.label }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions des filtres -->\r\n        <div class=\"flex items-center justify-between mt-4 pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10\">\r\n          <div class=\"flex items-center space-x-2\">\r\n            <button\r\n              (click)=\"resetFilters()\"\r\n              class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] text-sm font-medium transition-colors\"\r\n            >\r\n              <i class=\"fas fa-undo mr-1\"></i>\r\n              Réinitialiser\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-4 text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            <span>{{ equipes.length }} résultat(s)</span>\r\n            <div class=\"flex items-center space-x-2\">\r\n              <label>Par page:</label>\r\n              <select\r\n                [value]=\"itemsPerPage\"\r\n                (change)=\"onItemsPerPageChange(+($any($event.target).value))\"\r\n                class=\"px-2 py-1 bg-[#f8fafc] dark:bg-[#0f0f0f] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded text-xs\"\r\n              >\r\n                <option value=\"5\">5</option>\r\n                <option value=\"10\">10</option>\r\n                <option value=\"20\">20</option>\r\n                <option value=\"50\">50</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div\r\n      *ngIf=\"loading\"\r\n      class=\"flex flex-col items-center justify-center py-16\"\r\n    >\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-12 h-12 border-3 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n      <p\r\n        class=\"mt-4 text-[#4f5fad] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\r\n      >\r\n        Chargement des équipes...\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Error Alert -->\r\n    <div *ngIf=\"error\" class=\"mb-6\">\r\n      <div\r\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\r\n      >\r\n        <div class=\"flex items-start\">\r\n          <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\r\n            <i class=\"fas fa-exclamation-triangle\"></i>\r\n          </div>\r\n          <div class=\"flex-1\">\r\n            <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\r\n              Erreur de chargement des équipes\r\n            </h3>\r\n            <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\r\n              {{ error }}\r\n            </p>\r\n            <button\r\n              (click)=\"loadEquipes()\"\r\n              class=\"mt-3 bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30 transition-colors\"\r\n            >\r\n              <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No Teams -->\r\n    <div\r\n      *ngIf=\"!loading && !error && equipes.length === 0\"\r\n      class=\"text-center py-16\"\r\n    >\r\n      <div\r\n        class=\"w-20 h-20 mx-auto mb-6 text-[#4f5fad] dark:text-[#00f7ff] opacity-70\"\r\n      >\r\n        <i class=\"fas fa-users text-5xl\"></i>\r\n      </div>\r\n      <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\r\n        Aucune équipe trouvée\r\n      </h3>\r\n      <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm mb-6\">\r\n        Commencez par créer une nouvelle équipe pour organiser vos projets et\r\n        membres\r\n      </p>\r\n      <button\r\n        (click)=\"navigateToAddEquipe()\"\r\n        class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\r\n      >\r\n        <i class=\"fas fa-plus-circle mr-2\"></i>\r\n        Créer une équipe\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Teams Grid -->\r\n    <div\r\n      *ngIf=\"!loading && equipes.length > 0\"\r\n      class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\"\r\n    >\r\n      <div\r\n        *ngFor=\"let equipe of equipes\"\r\n        class=\"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\"\r\n      >\r\n        <!-- Header avec gradient -->\r\n        <div class=\"relative\">\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <div class=\"p-6\">\r\n            <div class=\"flex items-start justify-between mb-4\">\r\n              <div class=\"flex-1\">\r\n                <div class=\"flex items-center justify-between mb-2\">\r\n                  <h3\r\n                    class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] group-hover:scale-[1.02] transition-transform duration-300 origin-left\"\r\n                  >\r\n                    {{ equipe.name }}\r\n                  </h3>\r\n                  <!-- Badge de statut -->\r\n                  <span\r\n                    [class]=\"'px-2 py-1 rounded-full text-xs font-medium ' + getStatusBadgeClass(equipe.status)\"\r\n                  >\r\n                    {{ getStatusText(equipe.status) }}\r\n                  </span>\r\n                </div>\r\n\r\n                <div class=\"flex items-center space-x-2 text-xs mb-2\">\r\n                  <!-- Nombre de membres -->\r\n                  <span\r\n                    class=\"bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full font-medium\"\r\n                  >\r\n                    <i class=\"fas fa-users mr-1\"></i>\r\n                    {{ getMemberCount(equipe) }}/{{ equipe.maxMembers || 10 }}\r\n                  </span>\r\n\r\n                  <!-- Badge équipe pleine -->\r\n                  <span\r\n                    *ngIf=\"isTeamFull(equipe)\"\r\n                    class=\"bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded-full font-medium\"\r\n                  >\r\n                    <i class=\"fas fa-exclamation-triangle mr-1\"></i>\r\n                    Complète\r\n                  </span>\r\n\r\n                  <!-- Badge visibilité -->\r\n                  <span\r\n                    [class]=\"equipe.isPublic ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' : 'bg-gray-100 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400'\"\r\n                    class=\"px-2 py-1 rounded-full font-medium\"\r\n                  >\r\n                    <i [class]=\"equipe.isPublic ? 'fas fa-globe' : 'fas fa-lock'\" class=\"mr-1\"></i>\r\n                    {{ equipe.isPublic ? 'Publique' : 'Privée' }}\r\n                  </span>\r\n                </div>\r\n\r\n                <!-- Tags -->\r\n                <div *ngIf=\"equipe.tags && equipe.tags.length > 0\" class=\"flex flex-wrap gap-1 mb-2\">\r\n                  <span\r\n                    *ngFor=\"let tag of equipe.tags.slice(0, 3)\"\r\n                    class=\"bg-[#4f5fad]/5 dark:bg-[#00f7ff]/5 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-0.5 rounded text-xs\"\r\n                  >\r\n                    #{{ tag }}\r\n                  </span>\r\n                  <span\r\n                    *ngIf=\"equipe.tags.length > 3\"\r\n                    class=\"text-[#6d6870] dark:text-[#a0a0a0] text-xs\"\r\n                  >\r\n                    +{{ equipe.tags.length - 3 }}\r\n                  </span>\r\n                </div>\r\n\r\n                <!-- Admin -->\r\n                <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                  <i class=\"fas fa-crown mr-1\"></i>\r\n                  Admin: {{ getAdminDisplayName(equipe.admin) }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Avatar de l'équipe -->\r\n              <div\r\n                class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg\"\r\n              >\r\n                <i class=\"fas fa-users text-white text-lg\"></i>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Description -->\r\n            <p\r\n              class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0] line-clamp-2 mb-4\"\r\n            >\r\n              {{\r\n                equipe.description && equipe.description.length > 80\r\n                  ? (equipe.description | slice : 0 : 80) + \"...\"\r\n                  : equipe.description || \"Aucune description disponible\"\r\n              }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"px-6 pb-6\">\r\n          <!-- Bouton Tasks Principal -->\r\n          <div class=\"mb-4 relative\">\r\n            <button\r\n              (click)=\"equipe._id && navigateToTasks(equipe._id)\"\r\n              class=\"w-full bg-gradient-to-r from-[#00ff9d] via-[#00e68a] to-[#00d4aa] hover:from-[#00e68a] hover:via-[#00d4aa] hover:to-[#00c199] text-white font-bold py-4 px-6 rounded-xl transition-all duration-500 hover:scale-[1.03] hover:shadow-2xl hover:shadow-[#00ff9d]/30 flex items-center justify-center group relative overflow-hidden\"\r\n            >\r\n              <!-- Effet de brillance animé -->\r\n              <div class=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"></div>\r\n\r\n              <!-- Icône avec animation -->\r\n              <div class=\"relative z-10 flex items-center justify-center\">\r\n                <div class=\"bg-white/20 rounded-full p-2 mr-3 group-hover:bg-white/30 transition-all duration-300\">\r\n                  <i class=\"fas fa-tasks text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300\"></i>\r\n                </div>\r\n                <span class=\"text-lg tracking-wide\">Gérer les Tâches</span>\r\n                <div class=\"ml-3 bg-white/20 rounded-full p-2 group-hover:bg-white/30 group-hover:translate-x-1 transition-all duration-300\">\r\n                  <i class=\"fas fa-arrow-right text-sm\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Particules d'effet -->\r\n              <div class=\"absolute top-2 right-4 w-1 h-1 bg-white/60 rounded-full animate-pulse\"></div>\r\n              <div class=\"absolute bottom-3 left-6 w-1 h-1 bg-white/40 rounded-full animate-pulse\" style=\"animation-delay: 0.5s;\"></div>\r\n              <div class=\"absolute top-4 left-1/3 w-0.5 h-0.5 bg-white/50 rounded-full animate-pulse\" style=\"animation-delay: 1s;\"></div>\r\n            </button>\r\n\r\n            <!-- Badge Admin -->\r\n            <div class=\"absolute -top-2 -right-2 bg-[#4f5fad] text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center animate-bounce\">\r\n              <i class=\"fas fa-crown text-xs\"></i>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"flex items-center justify-between pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10\"\r\n          >\r\n            <!-- Bouton Détails -->\r\n            <button\r\n              (click)=\"equipe._id && navigateToEquipeDetail(equipe._id)\"\r\n              class=\"text-[#4f5fad] dark:text-[#00f7ff] hover:text-[#7826b5] dark:hover:text-[#4f5fad] text-sm font-medium transition-colors flex items-center group/details\"\r\n            >\r\n              <i\r\n                class=\"fas fa-eye mr-1 group-hover/details:scale-110 transition-transform\"\r\n              ></i>\r\n              Détails\r\n            </button>\r\n\r\n            <!-- Actions rapides -->\r\n            <div class=\"flex items-center space-x-2\">\r\n              <!-- Modifier (seulement si autorisé) -->\r\n              <button\r\n                *ngIf=\"canEditTeam(equipe)\"\r\n                (click)=\"equipe._id && navigateToEditEquipe(equipe._id)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\"\r\n                title=\"Modifier l'équipe\"\r\n              >\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n\r\n              <!-- Archiver/Activer -->\r\n              <button\r\n                *ngIf=\"canEditTeam(equipe) && equipe.status === 'active'\"\r\n                (click)=\"archiveTeam(equipe)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#f59e0b] hover:bg-[#f59e0b]/10 rounded-lg transition-all\"\r\n                title=\"Archiver l'équipe\"\r\n              >\r\n                <i class=\"fas fa-archive\"></i>\r\n              </button>\r\n\r\n              <button\r\n                *ngIf=\"canEditTeam(equipe) && equipe.status === 'archived'\"\r\n                (click)=\"activateTeam(equipe)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#10b981] hover:bg-[#10b981]/10 rounded-lg transition-all\"\r\n                title=\"Activer l'équipe\"\r\n              >\r\n                <i class=\"fas fa-play\"></i>\r\n              </button>\r\n\r\n              <!-- Supprimer (seulement si autorisé) -->\r\n              <button\r\n                *ngIf=\"canDeleteTeam(equipe)\"\r\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\r\n                title=\"Supprimer l'équipe\"\r\n              >\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div\r\n      *ngIf=\"!loading && equipes.length > 0 && totalPages > 1\"\r\n      class=\"mt-8 flex items-center justify-center\"\r\n    >\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-4 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\r\n      >\r\n        <div class=\"flex items-center space-x-2\">\r\n          <!-- Bouton Précédent -->\r\n          <button\r\n            [disabled]=\"currentPage === 1\"\r\n            (click)=\"onPageChange(currentPage - 1)\"\r\n            class=\"px-3 py-2 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n          >\r\n            <i class=\"fas fa-chevron-left mr-1\"></i>\r\n            Précédent\r\n          </button>\r\n\r\n          <!-- Numéros de pages -->\r\n          <div class=\"flex items-center space-x-1\">\r\n            <button\r\n              *ngFor=\"let page of [].constructor(totalPages); let i = index\"\r\n              [class.bg-gradient-to-r]=\"currentPage === i + 1\"\r\n              [class.from-[#4f5fad]]=\"currentPage === i + 1\"\r\n              [class.to-[#7826b5]]=\"currentPage === i + 1\"\r\n              [class.dark:from-[#00f7ff]]=\"currentPage === i + 1\"\r\n              [class.dark:to-[#4f5fad]]=\"currentPage === i + 1\"\r\n              [class.text-white]=\"currentPage === i + 1\"\r\n              [class.text-[#6d6870]]=\"currentPage !== i + 1\"\r\n              [class.dark:text-[#a0a0a0]]=\"currentPage !== i + 1\"\r\n              [class.hover:text-[#4f5fad]]=\"currentPage !== i + 1\"\r\n              [class.dark:hover:text-[#00f7ff]]=\"currentPage !== i + 1\"\r\n              (click)=\"onPageChange(i + 1)\"\r\n              class=\"w-10 h-10 rounded-lg font-medium text-sm transition-all hover:scale-105\"\r\n            >\r\n              {{ i + 1 }}\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Bouton Suivant -->\r\n          <button\r\n            [disabled]=\"currentPage === totalPages\"\r\n            (click)=\"onPageChange(currentPage + 1)\"\r\n            class=\"px-3 py-2 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n          >\r\n            Suivant\r\n            <i class=\"fas fa-chevron-right ml-1\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Informations de pagination -->\r\n        <div class=\"mt-3 pt-3 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10 text-center\">\r\n          <span class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            Page {{ currentPage }} sur {{ totalPages }}\r\n            ({{ totalItems }} équipe(s) au total)\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,gBAAgB;AAS5C,SAASC,QAAQ,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;;;;ICgG/DC,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACjEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAcAT,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,KAAA,CAAsB;IACjEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;;;IAsCVT,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAW,SAAA,cAEO;IAITX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAINH,EAAA,CAAAC,cAAA,cAAgC;IAMxBD,EAAA,CAAAW,SAAA,YAA2C;IAC7CX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBnB,EAAA,CAAAW,SAAA,aAAsC;IAACX,EAAA,CAAAE,MAAA,wBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAPPH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAY,MAAA,CAAAC,KAAA,MACF;;;;;;IAaRrB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAW,SAAA,YAAqC;IACvCX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAE,MAAA,gGAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAY,UAAA,mBAAAU,4DAAA;MAAAtB,EAAA,CAAAc,aAAA,CAAAS,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAM,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAG/BzB,EAAA,CAAAW,SAAA,YAAuC;IACvCX,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAgDGH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAW,SAAA,aAAgD;IAChDX,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAcPH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,OAAAkB,OAAA,MACF;;;;;IACA1B,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,OAAAmB,UAAA,CAAAC,IAAA,CAAAC,MAAA,UACF;;;;;IAZF7B,EAAA,CAAAC,cAAA,eAAqF;IACnFD,EAAA,CAAA8B,UAAA,IAAAC,uDAAA,oBAKO;IACP/B,EAAA,CAAA8B,UAAA,IAAAE,uDAAA,oBAKO;IACThC,EAAA,CAAAG,YAAA,EAAM;;;;IAXcH,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,YAAAuB,UAAA,CAAAC,IAAA,CAAAK,KAAA,OAA0B;IAMzCjC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAuB,UAAA,CAAAC,IAAA,CAAAC,MAAA,KAA4B;;;;;;IAsFnC7B,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAY,UAAA,mBAAAsB,4EAAA;MAAAlC,EAAA,CAAAc,aAAA,CAAAqB,IAAA;MAAA,MAAAR,UAAA,GAAA3B,EAAA,CAAAiB,aAAA,GAAAmB,SAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAS,UAAA,CAAAW,GAAA,IAAcD,OAAA,CAAAE,oBAAA,CAAAZ,UAAA,CAAAW,GAAA,CAAgC;IAAA,EAAC;IAIxDtC,EAAA,CAAAW,SAAA,aAA2B;IAC7BX,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAY,UAAA,mBAAA4B,4EAAA;MAAAxC,EAAA,CAAAc,aAAA,CAAA2B,IAAA;MAAA,MAAAd,UAAA,GAAA3B,EAAA,CAAAiB,aAAA,GAAAmB,SAAA;MAAA,MAAAM,OAAA,GAAA1C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAwB,OAAA,CAAAC,WAAA,CAAAhB,UAAA,CAAmB;IAAA,EAAC;IAI7B3B,EAAA,CAAAW,SAAA,aAA8B;IAChCX,EAAA,CAAAG,YAAA,EAAS;;;;;;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAY,UAAA,mBAAAgC,4EAAA;MAAA5C,EAAA,CAAAc,aAAA,CAAA+B,IAAA;MAAA,MAAAlB,UAAA,GAAA3B,EAAA,CAAAiB,aAAA,GAAAmB,SAAA;MAAA,MAAAU,OAAA,GAAA9C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA4B,OAAA,CAAAC,YAAA,CAAApB,UAAA,CAAoB;IAAA,EAAC;IAI9B3B,EAAA,CAAAW,SAAA,aAA2B;IAC7BX,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAY,UAAA,mBAAAoC,4EAAA;MAAAhD,EAAA,CAAAc,aAAA,CAAAmC,IAAA;MAAA,MAAAtB,UAAA,GAAA3B,EAAA,CAAAiB,aAAA,GAAAmB,SAAA;MAAA,MAAAc,OAAA,GAAAlD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAS,UAAA,CAAAW,GAAA,IAAcY,OAAA,CAAAC,YAAA,CAAAxB,UAAA,CAAAW,GAAA,CAAwB;IAAA,EAAC;IAIhDtC,EAAA,CAAAW,SAAA,aAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7LjBH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAW,SAAA,aAEO;IAKPX,EAAA,CAAAC,cAAA,cAAiB;IAOPD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAAsD;IAKlDD,EAAA,CAAAW,SAAA,aAAiC;IACjCX,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGPH,EAAA,CAAA8B,UAAA,KAAAsB,iDAAA,mBAMO;IAGPpD,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAW,SAAA,aAA+E;IAC/EX,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAA8B,UAAA,KAAAuB,gDAAA,kBAaM;IAGNrD,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAW,SAAA,aAAiC;IACjCX,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAW,SAAA,aAA+C;IACjDX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAE,MAAA,IAKF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKRH,EAAA,CAAAC,cAAA,eAAuB;IAIjBD,EAAA,CAAAY,UAAA,mBAAA0C,mEAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAc,aAAA,CAAA0C,IAAA;MAAA,MAAA7B,UAAA,GAAA4B,WAAA,CAAAnB,SAAA;MAAA,MAAAqB,OAAA,GAAAzD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAS,UAAA,CAAAW,GAAA,IAAcmB,OAAA,CAAAC,eAAA,CAAA/B,UAAA,CAAAW,GAAA,CAA2B;IAAA,EAAC;IAInDtC,EAAA,CAAAW,SAAA,eAA+L;IAG/LX,EAAA,CAAAC,cAAA,eAA4D;IAExDD,EAAA,CAAAW,SAAA,aAA4G;IAC9GX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,kCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA6H;IAC3HD,EAAA,CAAAW,SAAA,aAA0C;IAC5CX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAW,SAAA,eAAyF;IAG3FX,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,gBAAqJ;IACnJD,EAAA,CAAAW,SAAA,cAAoC;IACtCX,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAEC;IAGGD,EAAA,CAAAY,UAAA,mBAAA+C,mEAAA;MAAA,MAAAJ,WAAA,GAAAvD,EAAA,CAAAc,aAAA,CAAA0C,IAAA;MAAA,MAAA7B,UAAA,GAAA4B,WAAA,CAAAnB,SAAA;MAAA,MAAAwB,OAAA,GAAA5D,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAS,UAAA,CAAAW,GAAA,IAAcsB,OAAA,CAAAC,sBAAA,CAAAlC,UAAA,CAAAW,GAAA,CAAkC;IAAA,EAAC;IAG1DtC,EAAA,CAAAW,SAAA,cAEK;IACLX,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,eAAyC;IAEvCD,EAAA,CAAA8B,UAAA,KAAAgC,mDAAA,sBAOS;IAGT9D,EAAA,CAAA8B,UAAA,KAAAiC,mDAAA,sBAOS;IAET/D,EAAA,CAAA8B,UAAA,KAAAkC,mDAAA,sBAOS;IAGThE,EAAA,CAAA8B,UAAA,KAAAmC,mDAAA,sBAOS;IACXjE,EAAA,CAAAG,YAAA,EAAM;;;;;IA1KEH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAmB,UAAA,CAAAuC,IAAA,MACF;IAGElE,EAAA,CAAAO,SAAA,GAA4F;IAA5FP,EAAA,CAAAmE,UAAA,iDAAAC,OAAA,CAAAC,mBAAA,CAAA1C,UAAA,CAAA2C,MAAA,EAA4F;IAE5FtE,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA4D,OAAA,CAAAG,aAAA,CAAA5C,UAAA,CAAA2C,MAAA,OACF;IASEtE,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAwE,kBAAA,MAAAJ,OAAA,CAAAK,cAAA,CAAA9C,UAAA,QAAAA,UAAA,CAAA+C,UAAA,YACF;IAIG1E,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAgE,OAAA,CAAAO,UAAA,CAAAhD,UAAA,EAAwB;IASzB3B,EAAA,CAAAO,SAAA,GAAuK;IAAvKP,EAAA,CAAAmE,UAAA,CAAAxC,UAAA,CAAAiD,QAAA,+IAAuK;IAGpK5E,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAmE,UAAA,CAAAxC,UAAA,CAAAiD,QAAA,kCAA0D;IAC7D5E,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAmB,UAAA,CAAAiD,QAAA,mCACF;IAII5E,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAI,UAAA,SAAAuB,UAAA,CAAAC,IAAA,IAAAD,UAAA,CAAAC,IAAA,CAAAC,MAAA,KAA2C;IAkB/C7B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,aAAA4D,OAAA,CAAAS,mBAAA,CAAAlD,UAAA,CAAAmD,KAAA,OACF;IAeF9E,EAAA,CAAAO,SAAA,GAKF;IALEP,EAAA,CAAAQ,kBAAA,MAAAmB,UAAA,CAAAoD,WAAA,IAAApD,UAAA,CAAAoD,WAAA,CAAAlD,MAAA,QAAA7B,EAAA,CAAAgF,WAAA,SAAArD,UAAA,CAAAoD,WAAA,mBAAApD,UAAA,CAAAoD,WAAA,yCAKF;IAwDK/E,EAAA,CAAAO,SAAA,IAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAgE,OAAA,CAAAa,WAAA,CAAAtD,UAAA,EAAyB;IAUzB3B,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAAgE,OAAA,CAAAa,WAAA,CAAAtD,UAAA,KAAAA,UAAA,CAAA2C,MAAA,cAAuD;IASvDtE,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAI,UAAA,SAAAgE,OAAA,CAAAa,WAAA,CAAAtD,UAAA,KAAAA,UAAA,CAAA2C,MAAA,gBAAyD;IAUzDtE,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAgE,OAAA,CAAAc,aAAA,CAAAvD,UAAA,EAA2B;;;;;IA3LxC3B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA8B,UAAA,IAAAqD,yCAAA,oBAiMM;IACRnF,EAAA,CAAAG,YAAA,EAAM;;;;IAjMiBH,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAI,UAAA,YAAAgF,MAAA,CAAAC,OAAA,CAAU;;;;;;IAwNzBrF,EAAA,CAAAC,cAAA,kBAcC;IAFCD,EAAA,CAAAY,UAAA,mBAAA0E,qEAAA;MAAA,MAAA/B,WAAA,GAAAvD,EAAA,CAAAc,aAAA,CAAAyE,IAAA;MAAA,MAAAC,KAAA,GAAAjC,WAAA,CAAAkC,KAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAwE,OAAA,CAAAC,YAAA,CAAAH,KAAA,GAAiB,CAAC,CAAC;IAAA,EAAC;IAG7BxF,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdPH,EAAA,CAAA4F,WAAA,qBAAAC,OAAA,CAAAC,WAAA,KAAAN,KAAA,KAAgD,mBAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,sBAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,6BAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,2BAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,oBAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,wBAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,6BAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,8BAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA,mCAAAK,OAAA,CAAAC,WAAA,KAAAN,KAAA;IAahDxF,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAgF,KAAA,UACF;;;;;;;;;IApCRxF,EAAA,CAAAC,cAAA,eAGC;IAQOD,EAAA,CAAAY,UAAA,mBAAAmF,4DAAA;MAAA/F,EAAA,CAAAc,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAAjG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA+E,OAAA,CAAAN,YAAA,CAAAM,OAAA,CAAAH,WAAA,GAA2B,CAAC,CAAC;IAAA,EAAC;IAGvC9F,EAAA,CAAAW,SAAA,aAAwC;IACxCX,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAA8B,UAAA,IAAAoE,4CAAA,uBAgBS;IACXlG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAY,UAAA,mBAAAuF,4DAAA;MAAAnG,EAAA,CAAAc,aAAA,CAAAkF,IAAA;MAAA,MAAAI,OAAA,GAAApG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAkF,OAAA,CAAAT,YAAA,CAAAS,OAAA,CAAAN,WAAA,GAA2B,CAAC,CAAC;IAAA,EAAC;IAGvC9F,EAAA,CAAAE,MAAA,gBACA;IAAAF,EAAA,CAAAW,SAAA,cAAyC;IAC3CX,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,gBAAyF;IAErFD,EAAA,CAAAE,MAAA,IAEF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IA7CLH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,aAAAiG,MAAA,CAAAP,WAAA,OAA8B;IAWX9F,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsG,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,MAAA,CAAAI,UAAA,EAA+B;IAoBlDzG,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAiG,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAI,UAAA,CAAuC;IAYvCzG,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAA0G,kBAAA,WAAAL,MAAA,CAAAP,WAAA,WAAAO,MAAA,CAAAI,UAAA,QAAAJ,MAAA,CAAAM,UAAA,+BAEF;;;AD1dV,OAAM,MAAOC,mBAAmB;EAiC9BJ,YACUK,aAA4B,EAC5BC,MAAc,EACdC,mBAAwC,EACxCC,WAA4B;IAH5B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IApCrB,KAAA3B,OAAO,GAAa,EAAE;IACtB,KAAA4B,OAAO,GAAG,KAAK;IACf,KAAA5F,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAA6F,aAAa,GAAG,IAAItH,WAAW,CAAC,EAAE,CAAC;IACnC,KAAAuH,YAAY,GAAG,IAAIvH,WAAW,CAAC,KAAK,CAAC;IACrC,KAAAwH,YAAY,GAAG,IAAIxH,WAAW,CAAC,KAAK,CAAC;IACrC,KAAAkG,WAAW,GAAG,CAAC;IACf,KAAAuB,YAAY,GAAG,EAAE;IACjB,KAAAZ,UAAU,GAAG,CAAC;IACd,KAAAE,UAAU,GAAG,CAAC;IAEd;IACA,KAAAW,aAAa,GAAG,CACd;MAAEhH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAkB,CAAE,EAC3C;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAW,CAAE,EACzC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAW,CAAE,CAC1C;IAED,KAAA8G,aAAa,GAAG,CACd;MAAEjH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAQ,CAAE,EACjC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAW,CAAE,EACrC;MAAEH,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAS,CAAE,CACrC;IAED;IACA,KAAA+G,gBAAgB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;IAUlE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACT,WAAW,CAACU,cAAc,EAAE;EACtD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAAC1G,WAAW,EAAE;EACpB;EAEQyG,uBAAuBA,CAAA;IAC7B,IAAI,CAACV,aAAa,CAACY,YAAY,CAACC,IAAI,CAClCjI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACiI,SAAS,CAAC,MAAK;MACf,IAAI,CAAClC,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC3E,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEQ0G,wBAAwBA,CAAA;IAC9B,IAAI,CAACV,YAAY,CAACW,YAAY,CAACE,SAAS,CAAC,MAAK;MAC5C,IAAI,CAAClC,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC3E,WAAW,EAAE;IACpB,CAAC,CAAC;IAEF,IAAI,CAACiG,YAAY,CAACU,YAAY,CAACE,SAAS,CAAC,MAAK;MAC5C,IAAI,CAAClC,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC3E,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC8F,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC5F,KAAK,GAAG,IAAI;IAEjB,MAAM4G,OAAO,GAAsB;MACjCC,IAAI,EAAE,IAAI,CAACpC,WAAW;MACtBqC,KAAK,EAAE,IAAI,CAACd;KACb;IAED;IACA,MAAMe,WAAW,GAAG,IAAI,CAAClB,aAAa,CAAC5G,KAAK,EAAE+H,IAAI,EAAE;IACpD,IAAID,WAAW,EAAE;MACfH,OAAO,CAACK,MAAM,GAAGF,WAAW;;IAG9B,MAAMG,WAAW,GAAG,IAAI,CAACpB,YAAY,CAAC7G,KAAK;IAC3C,IAAIiI,WAAW,IAAIA,WAAW,KAAK,KAAK,EAAE;MACxCN,OAAO,CAAC3D,MAAM,GAAGiE,WAAW;;IAG9B,MAAMC,WAAW,GAAG,IAAI,CAACpB,YAAY,CAAC9G,KAAK;IAC3C,IAAIkI,WAAW,IAAIA,WAAW,KAAK,KAAK,EAAE;MACxCP,OAAO,CAACrD,QAAQ,GAAG4D,WAAW,KAAK,MAAM;;IAG3C,IAAI,CAAC3B,aAAa,CAAC4B,qBAAqB,CAACR,OAAO,CAAC,CAACF,IAAI,CACpDlI,QAAQ,CAAC,MAAM,IAAI,CAACoH,OAAO,GAAG,KAAK,CAAC,CACrC,CAACe,SAAS,CAAC;MACVU,IAAI,EAAGC,QAA0B,IAAI;QACnCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,QAAQ,CAAC;QAC1C,IAAI,CAACtD,OAAO,GAAGsD,QAAQ,CAACG,KAAK;QAC7B,IAAI,CAACrC,UAAU,GAAGkC,QAAQ,CAACI,UAAU,CAACC,KAAK;QAC3C,IAAI,CAACrC,UAAU,GAAGgC,QAAQ,CAACI,UAAU,CAACpC,UAAU;MAClD,CAAC;MACDtF,KAAK,EAAGA,KAAK,IAAI;QACfuH,OAAO,CAACvH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,uCAAuC,CAAC;QAE3E;QACA,IAAI,CAACC,iBAAiB,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACrC,aAAa,CAACsC,UAAU,EAAE,CAACpB,IAAI,CAClClI,QAAQ,CAAC,MAAM,IAAI,CAACoH,OAAO,GAAG,KAAK,CAAC,CACrC,CAACe,SAAS,CAAC;MACVU,IAAI,EAAGU,IAAI,IAAI;QACbR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,IAAI,CAAC;QAC/C,IAAI,CAAC/D,OAAO,GAAG+D,IAAI;QACnB,IAAI,CAACzC,UAAU,GAAGyC,IAAI,CAACvH,MAAM;QAC7B,IAAI,CAAC4E,UAAU,GAAG4C,IAAI,CAACC,IAAI,CAACF,IAAI,CAACvH,MAAM,GAAG,IAAI,CAACwF,YAAY,CAAC;MAC9D,CAAC;MACDhG,KAAK,EAAGA,KAAK,IAAI;QACfuH,OAAO,CAACvH,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,uCAAuC,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAxH,mBAAmBA,CAAA;IACjB,IAAI,CAACqF,MAAM,CAACyC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAhH,oBAAoBA,CAACiH,EAAU;IAC7B,IAAI,CAAC1C,MAAM,CAACyC,QAAQ,CAAC,CAAC,mBAAmB,EAAEC,EAAE,CAAC,CAAC;EACjD;EAEA3F,sBAAsBA,CAAC2F,EAAU;IAC/B,IAAI,CAAC1C,MAAM,CAACyC,QAAQ,CAAC,CAAC,iBAAiB,EAAEC,EAAE,CAAC,CAAC;EAC/C;EAEArG,YAAYA,CAACqG,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPZ,OAAO,CAACvH,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACpE,OAAO,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrH,GAAG,KAAKkH,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAEvF,IAAI,IAAI,cAAc;IAEjD,IAAI2F,OAAO,CAAC,gDAAgDD,UAAU,KAAK,CAAC,EAAE;MAC5E,IAAI,CAAC3C,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACJ,aAAa,CAAC1D,YAAY,CAACqG,EAAE,CAAC,CAACzB,IAAI,CACtClI,QAAQ,CAAC,MAAM,IAAI,CAACoH,OAAO,GAAG,KAAK,CAAC,CACrC,CAACe,SAAS,CAAC;QACVU,IAAI,EAAEA,CAAA,KAAK;UACTE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAAC9B,mBAAmB,CAAC+C,WAAW,CAAC,aAAaF,UAAU,+BAA+B,CAAC;UAC5F,IAAI,CAACzI,WAAW,EAAE;QACpB,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACfuH,OAAO,CAACvH,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GAAG,kEAAkE;UAC/E,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,8CAA8CW,UAAU,GAAG,CAAC;QACjG;OACD,CAAC;;EAEN;EAEAlG,eAAeA,CAAC8F,EAAU;IACxB,IAAI,CAACA,EAAE,EAAE;MACPZ,OAAO,CAACvH,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF,MAAMQ,MAAM,GAAG,IAAI,CAACpE,OAAO,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrH,GAAG,KAAKkH,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAEvF,IAAI,IAAI,cAAc;IAEjD;IACA,IAAI,CAAC4C,MAAM,CAACyC,QAAQ,CAAC,CAAC,cAAc,EAAEC,EAAE,CAAC,CAAC;EAC5C;EAEA;EACAO,YAAYA,CAACC,QAAgB;IAC3B;IACA;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1C,MAAMxE,KAAK,GAAGuE,QAAQ,CAACnI,MAAM,GAAGoI,MAAM,CAACpI,MAAM;IAC7C,OAAOoI,MAAM,CAACxE,KAAK,CAAC;EACtB;EAEA;EACAyE,aAAaA,CAACF,QAAgB;IAC5B,MAAMhB,KAAK,GAAG,IAAI,CAACe,YAAY,CAACC,QAAQ,CAAC;IACzC,MAAMG,SAAS,GAAGd,IAAI,CAACe,KAAK,CAACpB,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMqB,UAAU,GAAGrB,KAAK,GAAG,CAAC,GAAGK,IAAI,CAACiB,KAAK,CAAEH,SAAS,GAAGnB,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;IAExE,OAAO;MAAEmB,SAAS;MAAEnB,KAAK;MAAEqB;IAAU,CAAE;EACzC;EAEA;EAEA;;;EAGA1E,YAAYA,CAACuC,IAAY;IACvB,IAAI,CAACpC,WAAW,GAAGoC,IAAI;IACvB,IAAI,CAAC/G,WAAW,EAAE;EACpB;EAEA;;;EAGAoJ,oBAAoBA,CAAClD,YAAoB;IACvC,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACvB,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC3E,WAAW,EAAE;EACpB;EAEA;;;EAGAqJ,YAAYA,CAAA;IACV,IAAI,CAACtD,aAAa,CAACuD,QAAQ,CAAC,EAAE,CAAC;IAC/B,IAAI,CAACtD,YAAY,CAACsD,QAAQ,CAAC,KAAK,CAAC;IACjC,IAAI,CAACrD,YAAY,CAACqD,QAAQ,CAAC,KAAK,CAAC;IACjC,IAAI,CAAC3E,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC3E,WAAW,EAAE;EACpB;EAEA;;;EAGAwB,WAAWA,CAAC8G,MAAc;IACxB,IAAI,CAACA,MAAM,CAACnH,GAAG,EAAE;IAEjB,IAAIuH,OAAO,CAAC,+CAA+CJ,MAAM,CAACvF,IAAI,KAAK,CAAC,EAAE;MAC5E,IAAI,CAAC+C,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACJ,aAAa,CAAClE,WAAW,CAAC8G,MAAM,CAACnH,GAAG,CAAC,CAACyF,IAAI,CAC7ClI,QAAQ,CAAC,MAAM,IAAI,CAACoH,OAAO,GAAG,KAAK,CAAC,CACrC,CAACe,SAAS,CAAC;QACVU,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC5B,mBAAmB,CAAC+C,WAAW,CAAC,aAAaL,MAAM,CAACvF,IAAI,kBAAkB,CAAC;UAChF,IAAI,CAAC/C,WAAW,EAAE;QACpB,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACfuH,OAAO,CAACvH,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,0CAA0C,CAAC;QAChF;OACD,CAAC;;EAEN;EAEA;;;EAGAlG,YAAYA,CAAC0G,MAAc;IACzB,IAAI,CAACA,MAAM,CAACnH,GAAG,EAAE;IAEjB,IAAI,CAAC2E,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACJ,aAAa,CAAC9D,YAAY,CAAC0G,MAAM,CAACnH,GAAG,CAAC,CAACyF,IAAI,CAC9ClI,QAAQ,CAAC,MAAM,IAAI,CAACoH,OAAO,GAAG,KAAK,CAAC,CACrC,CAACe,SAAS,CAAC;MACVU,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,mBAAmB,CAAC+C,WAAW,CAAC,aAAaL,MAAM,CAACvF,IAAI,iBAAiB,CAAC;QAC/E,IAAI,CAAC/C,WAAW,EAAE;MACpB,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfuH,OAAO,CAACvH,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC0F,mBAAmB,CAACkC,SAAS,CAAC,2CAA2C,CAAC;MACjF;KACD,CAAC;EACJ;EAEA;;;EAGAhE,WAAWA,CAACwE,MAAc;IACxB,IAAI,CAAC,IAAI,CAAChC,WAAW,EAAE,OAAO,KAAK;IAEnC;IACA,IAAI,IAAI,CAACA,WAAW,CAACiD,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAElD;IACA,OAAO,IAAI,CAAC7D,aAAa,CAAC8D,WAAW,CAAClB,MAAM,EAAE,IAAI,CAAChC,WAAW,CAAC+B,EAAE,CAAC;EACpE;EAEA;;;EAGAtE,aAAaA,CAACuE,MAAc;IAC1B,OAAO,IAAI,CAACxE,WAAW,CAACwE,MAAM,CAAC;EACjC;EAEA;;;EAGA5E,mBAAmBA,CAACC,KAAU;IAC5B,IAAI,CAACA,KAAK,EAAE,OAAO,aAAa;IAEhC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;IAE3C,OAAOA,KAAK,CAAC8F,QAAQ,IAAI9F,KAAK,CAAC+F,QAAQ,IAAI/F,KAAK,CAACgG,KAAK,IAAI,aAAa;EACzE;EAEA;;;EAGArG,cAAcA,CAACgF,MAAc;IAC3B,OAAOA,MAAM,CAACsB,WAAW,IAAItB,MAAM,CAACuB,OAAO,EAAEnJ,MAAM,IAAI,CAAC;EAC1D;EAEA;;;EAGAwC,mBAAmBA,CAACC,MAAe;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,UAAU;QAAE,OAAO,eAAe;MACvC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC;QAAS,OAAO,eAAe;;EAEnC;EAEA;;;EAGAC,aAAaA,CAACD,MAAe;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC;QAAS,OAAO,UAAU;;EAE9B;EAEA;;;EAGAK,UAAUA,CAAC8E,MAAc;IACvB,OAAOA,MAAM,CAACwB,UAAU,IAAI,KAAK;EACnC;EAEA;;;EAGAC,iBAAiBA,CAACzB,MAAc;IAC9B,OAAOA,MAAM,CAAC0B,cAAc,IAAI,CAAC;EACnC;;;uBAtWWvE,mBAAmB,EAAA5G,EAAA,CAAAoL,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAtL,EAAA,CAAAoL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAxL,EAAA,CAAAoL,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAA1L,EAAA,CAAAoL,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAnBhF,mBAAmB;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBhCnM,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAW,SAAA,aAEO;UAMPX,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAW,SAAA,aAAmE;UAWrEX,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAI7CD,EAAA,CAAAW,SAAA,cAEO;UAKPX,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAE,MAAA,2EACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,eAA6D;UACjCD,EAAA,CAAAE,MAAA,IAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,iCACpD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAY,UAAA,mBAAAyL,sDAAA;YAAA,OAASD,GAAA,CAAA3K,mBAAA,EAAqB;UAAA,EAAC;UAG/BzB,EAAA,CAAAW,SAAA,aAEK;UACLX,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAkB;UAQRD,EAAA,CAAAW,SAAA,aAAkC;UAClCX,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAW,SAAA,iBAKE;UAEJX,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAW,SAAA,aAAkC;UAClCX,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAA8B,UAAA,KAAAwK,sCAAA,qBAES;UACXtM,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAW,SAAA,aAA+B;UAC/BX,EAAA,CAAAE,MAAA,yBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAA8B,UAAA,KAAAyK,sCAAA,qBAES;UACXvM,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAA+G;UAGzGD,EAAA,CAAAY,UAAA,mBAAA4L,sDAAA;YAAA,OAASJ,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UAGxBxK,EAAA,CAAAW,SAAA,aAAgC;UAChCX,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,eAAoF;UAC5ED,EAAA,CAAAE,MAAA,IAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAAC,cAAA,eAAyC;UAChCD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAAC,cAAA,kBAIC;UAFCD,EAAA,CAAAY,UAAA,oBAAA6L,uDAAAC,MAAA;YAAA,OAAUN,GAAA,CAAA7B,oBAAA,EAAAmC,MAAA,CAAAC,MAAA,CAAArM,KAAA,CAAkD;UAAA,EAAC;UAG7DN,EAAA,CAAAC,cAAA,kBAAkB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5BH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9BH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAS1CH,EAAA,CAAA8B,UAAA,KAAA8K,mCAAA,kBAiBM;UAGN5M,EAAA,CAAA8B,UAAA,KAAA+K,mCAAA,mBAwBM;UAGN7M,EAAA,CAAA8B,UAAA,KAAAgL,mCAAA,mBAuBM;UAGN9M,EAAA,CAAA8B,UAAA,KAAAiL,mCAAA,kBAsMM;UAGN/M,EAAA,CAAA8B,UAAA,KAAAkL,mCAAA,mBA0DM;UACRhN,EAAA,CAAAG,YAAA,EAAM;;;UAvbgCH,EAAA,CAAAO,SAAA,IAAgB;UAAhBP,EAAA,CAAAiN,iBAAA,CAAAb,GAAA,CAAAzF,UAAA,CAAgB;UA+BxC3G,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAAlF,aAAA,CAA6B;UAgB/BlH,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAAjF,YAAA,CAA4B;UAGDnH,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAA9E,aAAA,CAAgB;UAa3CtH,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAAhF,YAAA,CAA4B;UAGDpH,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAA7E,aAAA,CAAgB;UAoBvCvH,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,kBAAA,KAAA4L,GAAA,CAAA/G,OAAA,CAAAxD,MAAA,sBAAgC;UAIlC7B,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAA/E,YAAA,CAAsB;UAiB/BrH,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,SAAAgM,GAAA,CAAAnF,OAAA,CAAa;UAmBVjH,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,SAAAgM,GAAA,CAAA/K,KAAA,CAAW;UA4BdrB,EAAA,CAAAO,SAAA,GAAgD;UAAhDP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAAnF,OAAA,KAAAmF,GAAA,CAAA/K,KAAA,IAAA+K,GAAA,CAAA/G,OAAA,CAAAxD,MAAA,OAAgD;UA0BhD7B,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAAnF,OAAA,IAAAmF,GAAA,CAAA/G,OAAA,CAAAxD,MAAA,KAAoC;UAyMpC7B,EAAA,CAAAO,SAAA,GAAsD;UAAtDP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAAnF,OAAA,IAAAmF,GAAA,CAAA/G,OAAA,CAAAxD,MAAA,QAAAuK,GAAA,CAAA3F,UAAA,KAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}