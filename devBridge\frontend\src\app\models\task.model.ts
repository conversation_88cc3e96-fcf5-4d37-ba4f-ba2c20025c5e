import { User } from './user.model';

export interface Task {
  _id?: string;
  title: string;
  description?: string;

  // Statut Kanban étendu
  status: 'backlog' | 'todo' | 'in-progress' | 'review' | 'testing' | 'done' | 'archived';

  // Position dans la colonne Kanban
  position?: number;

  priority: 'lowest' | 'low' | 'medium' | 'high' | 'highest' | 'critical';

  // Dates importantes
  dueDate?: Date | string;
  startDate?: Date | string;
  completedDate?: Date | string;

  // Assignation multiple
  assignedTo?: (string | User)[]; // IDs ou objets User

  teamId: string; // ID de l'équipe à laquelle la tâche appartient
  createdBy?: string | User; // ID ou objet User

  // Catégorie et labels
  category?: 'feature' | 'bug' | 'improvement' | 'task' | 'epic' | 'story';
  labels?: TaskLabel[];

  // Estimation et temps
  estimatedHours?: number;
  actualHours?: number;
  storyPoints?: number;

  // Dépendances et hiérarchie
  dependencies?: TaskDependency[];
  parentTask?: string | Task;
  subtasks?: (string | Task)[];

  // Pièces jointes
  attachments?: TaskAttachment[];

  // Commentaires
  comments?: TaskComment[];

  // Intégration IA
  aiSuggestions?: {
    enabled?: boolean;
    lastAnalysis?: Date | string;
    suggestions?: AISuggestion[];
    autoGenerated?: {
      description?: boolean;
      subtasks?: boolean;
      estimation?: boolean;
    };
  };

  // Métriques et tracking
  metrics?: {
    viewCount?: number;
    editCount?: number;
    lastViewed?: Date | string;
    lastEdited?: Date | string;
    timeInStatus?: TimeInStatus[];
  };

  // Récurrence
  recurrence?: {
    enabled?: boolean;
    pattern?: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval?: number;
    endDate?: Date | string;
    nextDue?: Date | string;
  };

  // Workflow personnalisé
  workflow?: {
    currentStep?: number;
    steps?: WorkflowStep[];
  };

  // Archivage
  isArchived?: boolean;
  archivedAt?: Date | string;
  archivedBy?: string | User;

  // Propriétés virtuelles
  isOverdue?: boolean;
  daysUntilDue?: number;
  completionRate?: number;
  totalTimeSpent?: number;
  isBlocked?: boolean;

  createdAt?: Date | string;
  updatedAt?: Date | string;
}

// ==================== INTERFACES ASSOCIÉES ====================

export interface TaskLabel {
  name: string;
  color: string;
}

export interface TaskDependency {
  task: string | Task;
  type: 'blocks' | 'blocked_by' | 'relates_to';
}

export interface TaskAttachment {
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedBy: string | User;
  uploadedAt: Date | string;
}

export interface TaskComment {
  _id?: string;
  user: string | User;
  content: string;
  createdAt: Date | string;
  editedAt?: Date | string;
  isAIGenerated?: boolean;
}

export interface AISuggestion {
  type: 'optimization' | 'assignment' | 'deadline' | 'priority' | 'breakdown';
  title: string;
  description: string;
  content: string;
  confidence: number;
  createdAt: Date | string;
  applied?: boolean;
  details?: string;
  impact?: string;
}

export interface TimeInStatus {
  status: string;
  duration?: number; // en minutes
  startTime: Date | string;
  endTime?: Date | string;
}

export interface WorkflowStep {
  name: string;
  status: 'pending' | 'completed' | 'skipped';
  assignee?: string | User;
  completedAt?: Date | string;
  notes?: string;
}

// ==================== KANBAN ====================

export interface KanbanBoard {
  teamId: string;
  teamName: string;
  kanbanBoard: {
    backlog: Task[];
    todo: Task[];
    'in-progress': Task[];
    review: Task[];
    testing: Task[];
    done: Task[];
    archived: Task[];
  };
  stats: {
    total: number;
    byStatus: { [key: string]: number };
    overdue: number;
    blocked: number;
  };
  filters?: KanbanFilters;
}

export interface KanbanFilters {
  includeArchived?: boolean;
  assignedTo?: string[];
  labels?: string[];
  category?: string;
}

export interface MoveTaskRequest {
  newStatus: string;
  newPosition: number;
  oldStatus?: string;
}

// ==================== REQUÊTES ====================

export interface CreateTaskRequest {
  title: string;
  description?: string;
  status?: string;
  position?: number;
  priority?: string;
  category?: string;
  dueDate?: Date | string;
  assignedTo?: string[];
  labels?: TaskLabel[];
  estimatedHours?: number;
  storyPoints?: number;
  parentTask?: string;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  category?: string;
  dueDate?: Date | string;
  assignedTo?: string[];
  labels?: TaskLabel[];
  estimatedHours?: number;
  actualHours?: number;
  storyPoints?: number;
}

export interface AddCommentRequest {
  content: string;
}

export interface AddLabelRequest {
  name: string;
  color?: string;
  action?: 'add' | 'remove';
}

export interface AssignTaskRequest {
  userIds: string[];
}

// ==================== STATISTIQUES ====================

export interface TaskStatistics {
  teamId: string;
  teamName: string;
  period: number;
  statistics: {
    byStatus: Array<{
      _id: string;
      count: number;
      avgHours: number;
      totalHours: number;
    }>;
    total: number;
    overdue: number;
  };
}

// ==================== IA ====================

export interface AIAnalysisResponse {
  message: string;
  task: Task;
  suggestions: AISuggestion[];
}

export interface GenerateSubtasksResponse {
  message: string;
  task: Task;
  subtasks: Task[];
}

export interface EstimateEffortResponse {
  message: string;
  task: Task;
  estimatedHours: number;
}

export interface GenerateDescriptionResponse {
  message: string;
  task: Task;
  description: string;
}
