{"ast": null, "code": "export default function symbolObservablePonyfill(root) {\n  var result;\n  var Symbol = root.Symbol;\n  if (typeof Symbol === 'function') {\n    if (Symbol.observable) {\n      result = Symbol.observable;\n    } else {\n      if (typeof Symbol.for === 'function') {\n        // This just needs to be something that won't trample other user's Symbol.for use\n        // It also will guide people to the source of their issues, if this is problematic.\n        // META: It's a resource locator!\n        result = Symbol.for('https://github.com/benlesh/symbol-observable');\n      } else {\n        // Symbol.for didn't exist! The best we can do at this point is a totally \n        // unique symbol. Note that the string argument here is a descriptor, not\n        // an identifier. This symbol is unique.\n        result = Symbol('https://github.com/benlesh/symbol-observable');\n      }\n      try {\n        Symbol.observable = result;\n      } catch (err) {\n        // Do nothing. In some environments, users have frozen `Symbol` for security reasons,\n        // if it is frozen assigning to it will throw. In this case, we don't care, because\n        // they will need to use the returned value from the ponyfill.\n      }\n    }\n  } else {\n    result = '@@observable';\n  }\n  return result;\n}\n;", "map": {"version": 3, "names": ["symbolObservablePonyfill", "root", "result", "Symbol", "observable", "for", "err"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/symbol-observable/es/ponyfill.js"], "sourcesContent": ["export default function symbolObservablePonyfill(root) {\n\tvar result;\n\tvar Symbol = root.Symbol;\n\n\tif (typeof Symbol === 'function') {\n\t\tif (Symbol.observable) {\n\t\t\tresult = Symbol.observable;\n\t\t} else {\n\n\t\t\tif (typeof Symbol.for === 'function') {\n\t\t\t\t// This just needs to be something that won't trample other user's Symbol.for use\n\t\t\t\t// It also will guide people to the source of their issues, if this is problematic.\n\t\t\t\t// META: It's a resource locator!\n\t\t\t\tresult = Symbol.for('https://github.com/benlesh/symbol-observable');\n\t\t\t} else {\n\t\t\t\t// Symbol.for didn't exist! The best we can do at this point is a totally \n\t\t\t\t// unique symbol. Note that the string argument here is a descriptor, not\n\t\t\t\t// an identifier. This symbol is unique.\n\t\t\t\tresult = Symbol('https://github.com/benlesh/symbol-observable');\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tSymbol.observable = result;\n\t\t\t} catch (err) {\n\t\t\t\t// Do nothing. In some environments, users have frozen `Symbol` for security reasons,\n\t\t\t\t// if it is frozen assigning to it will throw. In this case, we don't care, because\n\t\t\t\t// they will need to use the returned value from the ponyfill.\n\t\t\t}\n\t\t}\n\t} else {\n\t\tresult = '@@observable';\n\t}\n\n\treturn result;\n};\n"], "mappings": "AAAA,eAAe,SAASA,wBAAwBA,CAACC,IAAI,EAAE;EACtD,IAAIC,MAAM;EACV,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;EAExB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IACjC,IAAIA,MAAM,CAACC,UAAU,EAAE;MACtBF,MAAM,GAAGC,MAAM,CAACC,UAAU;IAC3B,CAAC,MAAM;MAEN,IAAI,OAAOD,MAAM,CAACE,GAAG,KAAK,UAAU,EAAE;QACrC;QACA;QACA;QACAH,MAAM,GAAGC,MAAM,CAACE,GAAG,CAAC,8CAA8C,CAAC;MACpE,CAAC,MAAM;QACN;QACA;QACA;QACAH,MAAM,GAAGC,MAAM,CAAC,8CAA8C,CAAC;MAChE;MACA,IAAI;QACHA,MAAM,CAACC,UAAU,GAAGF,MAAM;MAC3B,CAAC,CAAC,OAAOI,GAAG,EAAE;QACb;QACA;QACA;MAAA;IAEF;EACD,CAAC,MAAM;IACNJ,MAAM,GAAG,cAAc;EACxB;EAEA,OAAOA,MAAM;AACd;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}