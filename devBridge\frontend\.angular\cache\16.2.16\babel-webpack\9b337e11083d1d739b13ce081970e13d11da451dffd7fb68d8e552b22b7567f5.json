{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { storeKeyNameFromField, argumentsObjectFromField, isReference, getStoreKeyName, isNonNullObject, stringifyForDisplay } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName, storeValueIsStoreObject, selectionSetMatchesResult, TypeOrFieldNameRegExp, defaultDataIdFromObject, isArray } from \"./helpers.js\";\nimport { cacheSlot } from \"./reactiveVars.js\";\nimport { keyArgsFnFromSpecifier, keyFieldsFnFromSpecifier } from \"./key-extractor.js\";\nimport { disableWarningsSlot } from \"../../masking/index.js\";\nfunction argsFromFieldSpecifier(spec) {\n  return spec.args !== void 0 ? spec.args : spec.field ? argumentsObjectFromField(spec.field, spec.variables) : null;\n}\nvar nullKeyFieldsFn = function () {\n  return void 0;\n};\nvar simpleKeyArgsFn = function (_args, context) {\n  return context.fieldName;\n};\n// These merge functions can be selected by specifying merge:true or\n// merge:false in a field policy.\nvar mergeTrueFn = function (existing, incoming, _a) {\n  var mergeObjects = _a.mergeObjects;\n  return mergeObjects(existing, incoming);\n};\nvar mergeFalseFn = function (_, incoming) {\n  return incoming;\n};\nvar Policies = /** @class */function () {\n  function Policies(config) {\n    this.config = config;\n    this.typePolicies = Object.create(null);\n    this.toBeAdded = Object.create(null);\n    // Map from subtype names to sets of supertype names. Note that this\n    // representation inverts the structure of possibleTypes (whose keys are\n    // supertypes and whose values are arrays of subtypes) because it tends\n    // to be much more efficient to search upwards than downwards.\n    this.supertypeMap = new Map();\n    // Any fuzzy subtypes specified by possibleTypes will be converted to\n    // RegExp objects and recorded here. Every key of this map can also be\n    // found in supertypeMap. In many cases this Map will be empty, which\n    // means no fuzzy subtype checking will happen in fragmentMatches.\n    this.fuzzySubtypes = new Map();\n    this.rootIdsByTypename = Object.create(null);\n    this.rootTypenamesById = Object.create(null);\n    this.usingPossibleTypes = false;\n    this.config = __assign({\n      dataIdFromObject: defaultDataIdFromObject\n    }, config);\n    this.cache = this.config.cache;\n    this.setRootTypename(\"Query\");\n    this.setRootTypename(\"Mutation\");\n    this.setRootTypename(\"Subscription\");\n    if (config.possibleTypes) {\n      this.addPossibleTypes(config.possibleTypes);\n    }\n    if (config.typePolicies) {\n      this.addTypePolicies(config.typePolicies);\n    }\n  }\n  Policies.prototype.identify = function (object, partialContext) {\n    var _a;\n    var policies = this;\n    var typename = partialContext && (partialContext.typename || ((_a = partialContext.storeObject) === null || _a === void 0 ? void 0 : _a.__typename)) || object.__typename;\n    // It should be possible to write root Query fields with writeFragment,\n    // using { __typename: \"Query\", ... } as the data, but it does not make\n    // sense to allow the same identification behavior for the Mutation and\n    // Subscription types, since application code should never be writing\n    // directly to (or reading directly from) those root objects.\n    if (typename === this.rootTypenamesById.ROOT_QUERY) {\n      return [\"ROOT_QUERY\"];\n    }\n    // Default context.storeObject to object if not otherwise provided.\n    var storeObject = partialContext && partialContext.storeObject || object;\n    var context = __assign(__assign({}, partialContext), {\n      typename: typename,\n      storeObject: storeObject,\n      readField: partialContext && partialContext.readField || function () {\n        var options = normalizeReadFieldOptions(arguments, storeObject);\n        return policies.readField(options, {\n          store: policies.cache[\"data\"],\n          variables: options.variables\n        });\n      }\n    });\n    var id;\n    var policy = typename && this.getTypePolicy(typename);\n    var keyFn = policy && policy.keyFn || this.config.dataIdFromObject;\n    disableWarningsSlot.withValue(true, function () {\n      while (keyFn) {\n        var specifierOrId = keyFn(__assign(__assign({}, object), storeObject), context);\n        if (isArray(specifierOrId)) {\n          keyFn = keyFieldsFnFromSpecifier(specifierOrId);\n        } else {\n          id = specifierOrId;\n          break;\n        }\n      }\n    });\n    id = id ? String(id) : void 0;\n    return context.keyObject ? [id, context.keyObject] : [id];\n  };\n  Policies.prototype.addTypePolicies = function (typePolicies) {\n    var _this = this;\n    Object.keys(typePolicies).forEach(function (typename) {\n      var _a = typePolicies[typename],\n        queryType = _a.queryType,\n        mutationType = _a.mutationType,\n        subscriptionType = _a.subscriptionType,\n        incoming = __rest(_a, [\"queryType\", \"mutationType\", \"subscriptionType\"]);\n      // Though {query,mutation,subscription}Type configurations are rare,\n      // it's important to call setRootTypename as early as possible,\n      // since these configurations should apply consistently for the\n      // entire lifetime of the cache. Also, since only one __typename can\n      // qualify as one of these root types, these three properties cannot\n      // be inherited, unlike the rest of the incoming properties. That\n      // restriction is convenient, because the purpose of this.toBeAdded\n      // is to delay the processing of type/field policies until the first\n      // time they're used, allowing policies to be added in any order as\n      // long as all relevant policies (including policies for supertypes)\n      // have been added by the time a given policy is used for the first\n      // time. In other words, since inheritance doesn't matter for these\n      // properties, there's also no need to delay their processing using\n      // the this.toBeAdded queue.\n      if (queryType) _this.setRootTypename(\"Query\", typename);\n      if (mutationType) _this.setRootTypename(\"Mutation\", typename);\n      if (subscriptionType) _this.setRootTypename(\"Subscription\", typename);\n      if (hasOwn.call(_this.toBeAdded, typename)) {\n        _this.toBeAdded[typename].push(incoming);\n      } else {\n        _this.toBeAdded[typename] = [incoming];\n      }\n    });\n  };\n  Policies.prototype.updateTypePolicy = function (typename, incoming) {\n    var _this = this;\n    var existing = this.getTypePolicy(typename);\n    var keyFields = incoming.keyFields,\n      fields = incoming.fields;\n    function setMerge(existing, merge) {\n      existing.merge = typeof merge === \"function\" ? merge\n      // Pass merge:true as a shorthand for a merge implementation\n      // that returns options.mergeObjects(existing, incoming).\n      : merge === true ? mergeTrueFn\n      // Pass merge:false to make incoming always replace existing\n      // without any warnings about data clobbering.\n      : merge === false ? mergeFalseFn : existing.merge;\n    }\n    // Type policies can define merge functions, as an alternative to\n    // using field policies to merge child objects.\n    setMerge(existing, incoming.merge);\n    existing.keyFn =\n    // Pass false to disable normalization for this typename.\n    keyFields === false ? nullKeyFieldsFn\n    // Pass an array of strings to use those fields to compute a\n    // composite ID for objects of this typename.\n    : isArray(keyFields) ? keyFieldsFnFromSpecifier(keyFields)\n    // Pass a function to take full control over identification.\n    : typeof keyFields === \"function\" ? keyFields\n    // Leave existing.keyFn unchanged if above cases fail.\n    : existing.keyFn;\n    if (fields) {\n      Object.keys(fields).forEach(function (fieldName) {\n        var existing = _this.getFieldPolicy(typename, fieldName, true);\n        var incoming = fields[fieldName];\n        if (typeof incoming === \"function\") {\n          existing.read = incoming;\n        } else {\n          var keyArgs = incoming.keyArgs,\n            read = incoming.read,\n            merge = incoming.merge;\n          existing.keyFn =\n          // Pass false to disable argument-based differentiation of\n          // field identities.\n          keyArgs === false ? simpleKeyArgsFn\n          // Pass an array of strings to use named arguments to\n          // compute a composite identity for the field.\n          : isArray(keyArgs) ? keyArgsFnFromSpecifier(keyArgs)\n          // Pass a function to take full control over field identity.\n          : typeof keyArgs === \"function\" ? keyArgs\n          // Leave existing.keyFn unchanged if above cases fail.\n          : existing.keyFn;\n          if (typeof read === \"function\") {\n            existing.read = read;\n          }\n          setMerge(existing, merge);\n        }\n        if (existing.read && existing.merge) {\n          // If we have both a read and a merge function, assume\n          // keyArgs:false, because read and merge together can take\n          // responsibility for interpreting arguments in and out. This\n          // default assumption can always be overridden by specifying\n          // keyArgs explicitly in the FieldPolicy.\n          existing.keyFn = existing.keyFn || simpleKeyArgsFn;\n        }\n      });\n    }\n  };\n  Policies.prototype.setRootTypename = function (which, typename) {\n    if (typename === void 0) {\n      typename = which;\n    }\n    var rootId = \"ROOT_\" + which.toUpperCase();\n    var old = this.rootTypenamesById[rootId];\n    if (typename !== old) {\n      invariant(!old || old === which, 6, which);\n      // First, delete any old __typename associated with this rootId from\n      // rootIdsByTypename.\n      if (old) delete this.rootIdsByTypename[old];\n      // Now make this the only __typename that maps to this rootId.\n      this.rootIdsByTypename[typename] = rootId;\n      // Finally, update the __typename associated with this rootId.\n      this.rootTypenamesById[rootId] = typename;\n    }\n  };\n  Policies.prototype.addPossibleTypes = function (possibleTypes) {\n    var _this = this;\n    this.usingPossibleTypes = true;\n    Object.keys(possibleTypes).forEach(function (supertype) {\n      // Make sure all types have an entry in this.supertypeMap, even if\n      // their supertype set is empty, so we can return false immediately\n      // from policies.fragmentMatches for unknown supertypes.\n      _this.getSupertypeSet(supertype, true);\n      possibleTypes[supertype].forEach(function (subtype) {\n        _this.getSupertypeSet(subtype, true).add(supertype);\n        var match = subtype.match(TypeOrFieldNameRegExp);\n        if (!match || match[0] !== subtype) {\n          // TODO Don't interpret just any invalid typename as a RegExp.\n          _this.fuzzySubtypes.set(subtype, new RegExp(subtype));\n        }\n      });\n    });\n  };\n  Policies.prototype.getTypePolicy = function (typename) {\n    var _this = this;\n    if (!hasOwn.call(this.typePolicies, typename)) {\n      var policy_1 = this.typePolicies[typename] = Object.create(null);\n      policy_1.fields = Object.create(null);\n      // When the TypePolicy for typename is first accessed, instead of\n      // starting with an empty policy object, inherit any properties or\n      // fields from the type policies of the supertypes of typename.\n      //\n      // Any properties or fields defined explicitly within the TypePolicy\n      // for typename will take precedence, and if there are multiple\n      // supertypes, the properties of policies whose types were added\n      // later via addPossibleTypes will take precedence over those of\n      // earlier supertypes. TODO Perhaps we should warn about these\n      // conflicts in development, and recommend defining the property\n      // explicitly in the subtype policy?\n      //\n      // Field policy inheritance is atomic/shallow: you can't inherit a\n      // field policy and then override just its read function, since read\n      // and merge functions often need to cooperate, so changing only one\n      // of them would be a recipe for inconsistency.\n      //\n      // Once the TypePolicy for typename has been accessed, its properties can\n      // still be updated directly using addTypePolicies, but future changes to\n      // inherited supertype policies will not be reflected in this subtype\n      // policy, because this code runs at most once per typename.\n      var supertypes_1 = this.supertypeMap.get(typename);\n      if (!supertypes_1 && this.fuzzySubtypes.size) {\n        // To make the inheritance logic work for unknown typename strings that\n        // may have fuzzy supertypes, we give this typename an empty supertype\n        // set and then populate it with any fuzzy supertypes that match.\n        supertypes_1 = this.getSupertypeSet(typename, true);\n        // This only works for typenames that are directly matched by a fuzzy\n        // supertype. What if there is an intermediate chain of supertypes?\n        // While possible, that situation can only be solved effectively by\n        // specifying the intermediate relationships via possibleTypes, manually\n        // and in a non-fuzzy way.\n        this.fuzzySubtypes.forEach(function (regExp, fuzzy) {\n          if (regExp.test(typename)) {\n            // The fuzzy parameter is just the original string version of regExp\n            // (not a valid __typename string), but we can look up the\n            // associated supertype(s) in this.supertypeMap.\n            var fuzzySupertypes = _this.supertypeMap.get(fuzzy);\n            if (fuzzySupertypes) {\n              fuzzySupertypes.forEach(function (supertype) {\n                return supertypes_1.add(supertype);\n              });\n            }\n          }\n        });\n      }\n      if (supertypes_1 && supertypes_1.size) {\n        supertypes_1.forEach(function (supertype) {\n          var _a = _this.getTypePolicy(supertype),\n            fields = _a.fields,\n            rest = __rest(_a, [\"fields\"]);\n          Object.assign(policy_1, rest);\n          Object.assign(policy_1.fields, fields);\n        });\n      }\n    }\n    var inbox = this.toBeAdded[typename];\n    if (inbox && inbox.length) {\n      // Merge the pending policies into this.typePolicies, in the order they\n      // were originally passed to addTypePolicy.\n      inbox.splice(0).forEach(function (policy) {\n        _this.updateTypePolicy(typename, policy);\n      });\n    }\n    return this.typePolicies[typename];\n  };\n  Policies.prototype.getFieldPolicy = function (typename, fieldName, createIfMissing) {\n    if (typename) {\n      var fieldPolicies = this.getTypePolicy(typename).fields;\n      return fieldPolicies[fieldName] || createIfMissing && (fieldPolicies[fieldName] = Object.create(null));\n    }\n  };\n  Policies.prototype.getSupertypeSet = function (subtype, createIfMissing) {\n    var supertypeSet = this.supertypeMap.get(subtype);\n    if (!supertypeSet && createIfMissing) {\n      this.supertypeMap.set(subtype, supertypeSet = new Set());\n    }\n    return supertypeSet;\n  };\n  Policies.prototype.fragmentMatches = function (fragment, typename, result, variables) {\n    var _this = this;\n    if (!fragment.typeCondition) return true;\n    // If the fragment has a type condition but the object we're matching\n    // against does not have a __typename, the fragment cannot match.\n    if (!typename) return false;\n    var supertype = fragment.typeCondition.name.value;\n    // Common case: fragment type condition and __typename are the same.\n    if (typename === supertype) return true;\n    if (this.usingPossibleTypes && this.supertypeMap.has(supertype)) {\n      var typenameSupertypeSet = this.getSupertypeSet(typename, true);\n      var workQueue_1 = [typenameSupertypeSet];\n      var maybeEnqueue_1 = function (subtype) {\n        var supertypeSet = _this.getSupertypeSet(subtype, false);\n        if (supertypeSet && supertypeSet.size && workQueue_1.indexOf(supertypeSet) < 0) {\n          workQueue_1.push(supertypeSet);\n        }\n      };\n      // We need to check fuzzy subtypes only if we encountered fuzzy\n      // subtype strings in addPossibleTypes, and only while writing to\n      // the cache, since that's when selectionSetMatchesResult gives a\n      // strong signal of fragment matching. The StoreReader class calls\n      // policies.fragmentMatches without passing a result object, so\n      // needToCheckFuzzySubtypes is always false while reading.\n      var needToCheckFuzzySubtypes = !!(result && this.fuzzySubtypes.size);\n      var checkingFuzzySubtypes = false;\n      // It's important to keep evaluating workQueue.length each time through\n      // the loop, because the queue can grow while we're iterating over it.\n      for (var i = 0; i < workQueue_1.length; ++i) {\n        var supertypeSet = workQueue_1[i];\n        if (supertypeSet.has(supertype)) {\n          if (!typenameSupertypeSet.has(supertype)) {\n            if (checkingFuzzySubtypes) {\n              globalThis.__DEV__ !== false && invariant.warn(7, typename, supertype);\n            }\n            // Record positive results for faster future lookup.\n            // Unfortunately, we cannot safely cache negative results,\n            // because new possibleTypes data could always be added to the\n            // Policies class.\n            typenameSupertypeSet.add(supertype);\n          }\n          return true;\n        }\n        supertypeSet.forEach(maybeEnqueue_1);\n        if (needToCheckFuzzySubtypes &&\n        // Start checking fuzzy subtypes only after exhausting all\n        // non-fuzzy subtypes (after the final iteration of the loop).\n        i === workQueue_1.length - 1 &&\n        // We could wait to compare fragment.selectionSet to result\n        // after we verify the supertype, but this check is often less\n        // expensive than that search, and we will have to do the\n        // comparison anyway whenever we find a potential match.\n        selectionSetMatchesResult(fragment.selectionSet, result, variables)) {\n          // We don't always need to check fuzzy subtypes (if no result\n          // was provided, or !this.fuzzySubtypes.size), but, when we do,\n          // we only want to check them once.\n          needToCheckFuzzySubtypes = false;\n          checkingFuzzySubtypes = true;\n          // If we find any fuzzy subtypes that match typename, extend the\n          // workQueue to search through the supertypes of those fuzzy\n          // subtypes. Otherwise the for-loop will terminate and we'll\n          // return false below.\n          this.fuzzySubtypes.forEach(function (regExp, fuzzyString) {\n            var match = typename.match(regExp);\n            if (match && match[0] === typename) {\n              maybeEnqueue_1(fuzzyString);\n            }\n          });\n        }\n      }\n    }\n    return false;\n  };\n  Policies.prototype.hasKeyArgs = function (typename, fieldName) {\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    return !!(policy && policy.keyFn);\n  };\n  Policies.prototype.getStoreFieldName = function (fieldSpec) {\n    var typename = fieldSpec.typename,\n      fieldName = fieldSpec.fieldName;\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    var storeFieldName;\n    var keyFn = policy && policy.keyFn;\n    if (keyFn && typename) {\n      var context = {\n        typename: typename,\n        fieldName: fieldName,\n        field: fieldSpec.field || null,\n        variables: fieldSpec.variables\n      };\n      var args = argsFromFieldSpecifier(fieldSpec);\n      while (keyFn) {\n        var specifierOrString = keyFn(args, context);\n        if (isArray(specifierOrString)) {\n          keyFn = keyArgsFnFromSpecifier(specifierOrString);\n        } else {\n          // If the custom keyFn returns a falsy value, fall back to\n          // fieldName instead.\n          storeFieldName = specifierOrString || fieldName;\n          break;\n        }\n      }\n    }\n    if (storeFieldName === void 0) {\n      storeFieldName = fieldSpec.field ? storeKeyNameFromField(fieldSpec.field, fieldSpec.variables) : getStoreKeyName(fieldName, argsFromFieldSpecifier(fieldSpec));\n    }\n    // Returning false from a keyArgs function is like configuring\n    // keyArgs: false, but more dynamic.\n    if (storeFieldName === false) {\n      return fieldName;\n    }\n    // Make sure custom field names start with the actual field.name.value\n    // of the field, so we can always figure out which properties of a\n    // StoreObject correspond to which original field names.\n    return fieldName === fieldNameFromStoreName(storeFieldName) ? storeFieldName : fieldName + \":\" + storeFieldName;\n  };\n  Policies.prototype.readField = function (options, context) {\n    var objectOrReference = options.from;\n    if (!objectOrReference) return;\n    var nameOrField = options.field || options.fieldName;\n    if (!nameOrField) return;\n    if (options.typename === void 0) {\n      var typename = context.store.getFieldValue(objectOrReference, \"__typename\");\n      if (typename) options.typename = typename;\n    }\n    var storeFieldName = this.getStoreFieldName(options);\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var existing = context.store.getFieldValue(objectOrReference, storeFieldName);\n    var policy = this.getFieldPolicy(options.typename, fieldName, false);\n    var read = policy && policy.read;\n    if (read) {\n      var readOptions = makeFieldFunctionOptions(this, objectOrReference, options, context, context.store.getStorage(isReference(objectOrReference) ? objectOrReference.__ref : objectOrReference, storeFieldName));\n      // Call read(existing, readOptions) with cacheSlot holding this.cache.\n      return cacheSlot.withValue(this.cache, read, [existing, readOptions]);\n    }\n    return existing;\n  };\n  Policies.prototype.getReadFunction = function (typename, fieldName) {\n    var policy = this.getFieldPolicy(typename, fieldName, false);\n    return policy && policy.read;\n  };\n  Policies.prototype.getMergeFunction = function (parentTypename, fieldName, childTypename) {\n    var policy = this.getFieldPolicy(parentTypename, fieldName, false);\n    var merge = policy && policy.merge;\n    if (!merge && childTypename) {\n      policy = this.getTypePolicy(childTypename);\n      merge = policy && policy.merge;\n    }\n    return merge;\n  };\n  Policies.prototype.runMergeFunction = function (existing, incoming, _a, context, storage) {\n    var field = _a.field,\n      typename = _a.typename,\n      merge = _a.merge;\n    if (merge === mergeTrueFn) {\n      // Instead of going to the trouble of creating a full\n      // FieldFunctionOptions object and calling mergeTrueFn, we can\n      // simply call mergeObjects, as mergeTrueFn would.\n      return makeMergeObjectsFunction(context.store)(existing, incoming);\n    }\n    if (merge === mergeFalseFn) {\n      // Likewise for mergeFalseFn, whose implementation is even simpler.\n      return incoming;\n    }\n    // If cache.writeQuery or cache.writeFragment was called with\n    // options.overwrite set to true, we still call merge functions, but\n    // the existing data is always undefined, so the merge function will\n    // not attempt to combine the incoming data with the existing data.\n    if (context.overwrite) {\n      existing = void 0;\n    }\n    return merge(existing, incoming, makeFieldFunctionOptions(this,\n    // Unlike options.readField for read functions, we do not fall\n    // back to the current object if no foreignObjOrRef is provided,\n    // because it's not clear what the current object should be for\n    // merge functions: the (possibly undefined) existing object, or\n    // the incoming object? If you think your merge function needs\n    // to read sibling fields in order to produce a new value for\n    // the current field, you might want to rethink your strategy,\n    // because that's a recipe for making merge behavior sensitive\n    // to the order in which fields are written into the cache.\n    // However, readField(name, ref) is useful for merge functions\n    // that need to deduplicate child objects and references.\n    void 0, {\n      typename: typename,\n      fieldName: field.name.value,\n      field: field,\n      variables: context.variables\n    }, context, storage || Object.create(null)));\n  };\n  return Policies;\n}();\nexport { Policies };\nfunction makeFieldFunctionOptions(policies, objectOrReference, fieldSpec, context, storage) {\n  var storeFieldName = policies.getStoreFieldName(fieldSpec);\n  var fieldName = fieldNameFromStoreName(storeFieldName);\n  var variables = fieldSpec.variables || context.variables;\n  var _a = context.store,\n    toReference = _a.toReference,\n    canRead = _a.canRead;\n  return {\n    args: argsFromFieldSpecifier(fieldSpec),\n    field: fieldSpec.field || null,\n    fieldName: fieldName,\n    storeFieldName: storeFieldName,\n    variables: variables,\n    isReference: isReference,\n    toReference: toReference,\n    storage: storage,\n    cache: policies.cache,\n    canRead: canRead,\n    readField: function () {\n      return policies.readField(normalizeReadFieldOptions(arguments, objectOrReference, variables), context);\n    },\n    mergeObjects: makeMergeObjectsFunction(context.store)\n  };\n}\nexport function normalizeReadFieldOptions(readFieldArgs, objectOrReference, variables) {\n  var fieldNameOrOptions = readFieldArgs[0],\n    from = readFieldArgs[1],\n    argc = readFieldArgs.length;\n  var options;\n  if (typeof fieldNameOrOptions === \"string\") {\n    options = {\n      fieldName: fieldNameOrOptions,\n      // Default to objectOrReference only when no second argument was\n      // passed for the from parameter, not when undefined is explicitly\n      // passed as the second argument.\n      from: argc > 1 ? from : objectOrReference\n    };\n  } else {\n    options = __assign({}, fieldNameOrOptions);\n    // Default to objectOrReference only when fieldNameOrOptions.from is\n    // actually omitted, rather than just undefined.\n    if (!hasOwn.call(options, \"from\")) {\n      options.from = objectOrReference;\n    }\n  }\n  if (globalThis.__DEV__ !== false && options.from === void 0) {\n    globalThis.__DEV__ !== false && invariant.warn(8, stringifyForDisplay(Array.from(readFieldArgs)));\n  }\n  if (void 0 === options.variables) {\n    options.variables = variables;\n  }\n  return options;\n}\nfunction makeMergeObjectsFunction(store) {\n  return function mergeObjects(existing, incoming) {\n    if (isArray(existing) || isArray(incoming)) {\n      throw newInvariantError(9);\n    }\n    // These dynamic checks are necessary because the parameters of a\n    // custom merge function can easily have the any type, so the type\n    // system cannot always enforce the StoreObject | Reference parameter\n    // types of options.mergeObjects.\n    if (isNonNullObject(existing) && isNonNullObject(incoming)) {\n      var eType = store.getFieldValue(existing, \"__typename\");\n      var iType = store.getFieldValue(incoming, \"__typename\");\n      var typesDiffer = eType && iType && eType !== iType;\n      if (typesDiffer) {\n        return incoming;\n      }\n      if (isReference(existing) && storeValueIsStoreObject(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // existing.__ref, preferring/overwriting any fields contributed by the\n        // newer incoming StoreObject.\n        store.merge(existing.__ref, incoming);\n        return existing;\n      }\n      if (storeValueIsStoreObject(existing) && isReference(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // incoming.__ref, taking fields from the older existing object only if\n        // those fields are not already present in the newer StoreObject\n        // identified by incoming.__ref.\n        store.merge(existing, incoming.__ref);\n        return incoming;\n      }\n      if (storeValueIsStoreObject(existing) && storeValueIsStoreObject(incoming)) {\n        return __assign(__assign({}, existing), incoming);\n      }\n    }\n    return incoming;\n  };\n}\n//# sourceMappingURL=policies.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}