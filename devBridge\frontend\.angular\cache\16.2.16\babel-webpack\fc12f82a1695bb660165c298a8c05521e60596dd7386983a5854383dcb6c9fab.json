{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/task.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"@angular/material/tooltip\";\nimport * as i8 from \"@angular/material/expansion\";\nfunction TaskAiAssistantComponent_div_12_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.task.estimatedHours, \"h \");\n  }\n}\nfunction TaskAiAssistantComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"span\", 16)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 16)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TaskAiAssistantComponent_div_12_span_12_Template, 4, 1, \"span\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.task.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.task.priority, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.task.category || \"Non d\\u00E9finie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.task.estimatedHours);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_mat_spinner_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 18);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const i_r13 = i0.ɵɵnextContext().index;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.applySuggestion(i_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.loading.applySuggestion);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_div_14_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"strong\");\n    i0.ɵɵtext(2, \"Impact:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r12.impact, \" \");\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-expansion-panel\")(2, \"mat-expansion-panel-header\")(3, \"mat-panel-title\");\n    i0.ɵɵtext(4, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 35)(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"span\", 16)(10, \"strong\");\n    i0.ɵɵtext(11, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 16)(14, \"strong\");\n    i0.ɵɵtext(15, \"Confiance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TaskAiAssistantComponent_div_41_div_7_div_1_div_14_span_17_Template, 4, 1, \"span\", 17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(suggestion_r12.details);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r12.type, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getConfidenceText(suggestion_r12.confidence), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", suggestion_r12.impact);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 28)(6, \"h5\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, TaskAiAssistantComponent_div_41_div_7_div_1_button_13_Template, 3, 1, \"button\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, TaskAiAssistantComponent_div_41_div_7_div_1_div_14_Template, 18, 4, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"applied\", suggestion_r12.applied);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", ctx_r11.getConfidenceColor(suggestion_r12.confidence));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getSuggestionIcon(suggestion_r12.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(suggestion_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r11.getConfidenceColor(suggestion_r12.confidence));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (suggestion_r12.confidence * 100).toFixed(0), \"% \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canApplySuggestion(suggestion_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", suggestion_r12.details);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, TaskAiAssistantComponent_div_41_div_7_div_1_Template, 15, 12, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.suggestions);\n  }\n}\nfunction TaskAiAssistantComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h4\");\n    i0.ɵɵtext(3, \"Suggestions IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_41_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.toggleSuggestions());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, TaskAiAssistantComponent_div_41_div_7_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.showSuggestions ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showSuggestions);\n  }\n}\nfunction TaskAiAssistantComponent_div_42_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"mat-icon\", 44);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Une nouvelle analyse est recommand\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_42_div_24_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.analyzeTask());\n    });\n    i0.ɵɵtext(6, \" Analyser maintenant \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskAiAssistantComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"h4\");\n    i0.ɵɵtext(2, \"Statistiques IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"div\", 39)(5, \"span\", 40);\n    i0.ɵɵtext(6, \"Derni\\u00E8re analyse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 41);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39)(10, \"span\", 40);\n    i0.ɵɵtext(11, \"Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 41);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"span\", 40);\n    i0.ɵɵtext(16, \"Non appliqu\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 41);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 39)(20, \"span\", 40);\n    i0.ɵɵtext(21, \"Confiance moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 41);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, TaskAiAssistantComponent_div_42_div_24_Template, 7, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.getLastAnalysisTime());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.suggestions.length);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.getUnappliedSuggestionsCount());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r6.getAverageConfidence() * 100).toFixed(0), \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.shouldReanalyze());\n  }\n}\nfunction TaskAiAssistantComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"smart_toy_outlined\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Assistant IA d\\u00E9sactiv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"L'assistant IA n'est pas activ\\u00E9 pour cette t\\u00E2che.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskAiAssistantComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Aucune suggestion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Lancez une analyse pour obtenir des suggestions IA.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_div_44_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.analyzeTask());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Analyser la t\\u00E2che \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TaskAiAssistantComponent = /*#__PURE__*/(() => {\n  class TaskAiAssistantComponent {\n    constructor(taskService, snackBar) {\n      this.taskService = taskService;\n      this.snackBar = snackBar;\n      this.taskUpdated = new EventEmitter();\n      this.subtasksGenerated = new EventEmitter();\n      this.loading = {\n        analyze: false,\n        subtasks: false,\n        estimate: false,\n        description: false,\n        applySuggestion: false\n      };\n      this.suggestions = [];\n      this.showSuggestions = true;\n    }\n    ngOnInit() {\n      this.loadSuggestions();\n    }\n    // Charger les suggestions existantes\n    loadSuggestions() {\n      if (this.task.aiSuggestions?.suggestions) {\n        this.suggestions = this.task.aiSuggestions.suggestions;\n      }\n    }\n    // Analyser la tâche avec l'IA\n    analyzeTask() {\n      if (!this.task._id) return;\n      this.loading.analyze = true;\n      this.taskService.analyzeTaskWithAI(this.task._id).subscribe({\n        next: response => {\n          this.suggestions = response.suggestions;\n          this.task = response.task;\n          this.taskUpdated.emit(this.task);\n          this.loading.analyze = false;\n          this.snackBar.open(`Analyse terminée - ${response.suggestions.length} suggestion(s) trouvée(s)`, 'Fermer', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          console.error('Erreur analyse IA:', error);\n          this.loading.analyze = false;\n          this.snackBar.open('Erreur lors de l\\'analyse IA', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Générer des sous-tâches avec l'IA\n    generateSubtasks() {\n      if (!this.task._id) return;\n      this.loading.subtasks = true;\n      this.taskService.generateSubtasksWithAI(this.task._id).subscribe({\n        next: response => {\n          this.task = response.task;\n          this.taskUpdated.emit(this.task);\n          this.subtasksGenerated.emit(response.subtasks);\n          this.loading.subtasks = false;\n          this.snackBar.open(`${response.subtasks.length} sous-tâche(s) générée(s) avec succès`, 'Fermer', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          console.error('Erreur génération sous-tâches:', error);\n          this.loading.subtasks = false;\n          this.snackBar.open('Erreur lors de la génération des sous-tâches', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Estimer l'effort avec l'IA\n    estimateEffort() {\n      if (!this.task._id) return;\n      this.loading.estimate = true;\n      this.taskService.estimateEffortWithAI(this.task._id).subscribe({\n        next: response => {\n          this.task = response.task;\n          this.taskUpdated.emit(this.task);\n          this.loading.estimate = false;\n          this.snackBar.open(`Effort estimé: ${response.estimatedHours}h`, 'Fermer', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          console.error('Erreur estimation effort:', error);\n          this.loading.estimate = false;\n          this.snackBar.open('Erreur lors de l\\'estimation d\\'effort', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Générer une description avec l'IA\n    generateDescription() {\n      if (!this.task._id) return;\n      this.loading.description = true;\n      this.taskService.generateDescriptionWithAI(this.task._id).subscribe({\n        next: response => {\n          this.task = response.task;\n          this.taskUpdated.emit(this.task);\n          this.loading.description = false;\n          this.snackBar.open('Description générée avec succès', 'Fermer', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          console.error('Erreur génération description:', error);\n          this.loading.description = false;\n          this.snackBar.open('Erreur lors de la génération de description', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Appliquer une suggestion IA\n    applySuggestion(suggestionIndex) {\n      if (!this.task._id) return;\n      this.loading.applySuggestion = true;\n      this.taskService.applySuggestion(this.task._id, suggestionIndex).subscribe({\n        next: response => {\n          this.task = response.task;\n          this.suggestions[suggestionIndex] = response.suggestion;\n          this.taskUpdated.emit(this.task);\n          this.loading.applySuggestion = false;\n          this.snackBar.open('Suggestion appliquée avec succès', 'Fermer', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          console.error('Erreur application suggestion:', error);\n          this.loading.applySuggestion = false;\n          this.snackBar.open('Erreur lors de l\\'application de la suggestion', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Obtenir l'icône pour un type de suggestion\n    getSuggestionIcon(type) {\n      const icons = {\n        'optimization': 'tune',\n        'assignment': 'person_add',\n        'deadline': 'schedule',\n        'priority': 'flag',\n        'breakdown': 'list'\n      };\n      return icons[type] || 'lightbulb';\n    }\n    // Obtenir la couleur pour un niveau de confiance\n    getConfidenceColor(confidence) {\n      if (confidence >= 0.8) return '#10b981'; // Vert\n      if (confidence >= 0.6) return '#f59e0b'; // Orange\n      return '#ef4444'; // Rouge\n    }\n    // Obtenir le texte pour un niveau de confiance\n    getConfidenceText(confidence) {\n      if (confidence >= 0.8) return 'Haute confiance';\n      if (confidence >= 0.6) return 'Confiance moyenne';\n      return 'Faible confiance';\n    }\n    // Vérifier si une suggestion peut être appliquée automatiquement\n    canApplySuggestion(suggestion) {\n      return !suggestion.applied && ['priority', 'deadline'].includes(suggestion.type);\n    }\n    // Basculer l'affichage des suggestions\n    toggleSuggestions() {\n      this.showSuggestions = !this.showSuggestions;\n    }\n    // Vérifier si l'IA est activée pour cette tâche\n    isAIEnabled() {\n      return this.task.aiSuggestions?.enabled !== false;\n    }\n    // Vérifier si la tâche a été générée automatiquement\n    isAutoGenerated(field) {\n      return this.task.aiSuggestions?.autoGenerated?.[field] || false;\n    }\n    // Obtenir le temps depuis la dernière analyse\n    getLastAnalysisTime() {\n      if (!this.task.aiSuggestions?.lastAnalysis) return 'Jamais';\n      const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n      const now = new Date();\n      const diffMs = now.getTime() - lastAnalysis.getTime();\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffDays = Math.floor(diffHours / 24);\n      if (diffDays > 0) return `Il y a ${diffDays} jour(s)`;\n      if (diffHours > 0) return `Il y a ${diffHours} heure(s)`;\n      return 'Récemment';\n    }\n    // Vérifier si une nouvelle analyse est recommandée\n    shouldReanalyze() {\n      if (!this.task.aiSuggestions?.lastAnalysis) return true;\n      const lastAnalysis = new Date(this.task.aiSuggestions.lastAnalysis);\n      const now = new Date();\n      const diffHours = (now.getTime() - lastAnalysis.getTime()) / (1000 * 60 * 60);\n      return diffHours > 24; // Recommander une nouvelle analyse après 24h\n    }\n    // Obtenir le nombre de suggestions non appliquées\n    getUnappliedSuggestionsCount() {\n      return this.suggestions.filter(s => !s.applied).length;\n    }\n    // Obtenir le score de confiance moyen\n    getAverageConfidence() {\n      if (this.suggestions.length === 0) return 0;\n      const total = this.suggestions.reduce((sum, s) => sum + s.confidence, 0);\n      return total / this.suggestions.length;\n    }\n    static {\n      this.ɵfac = function TaskAiAssistantComponent_Factory(t) {\n        return new (t || TaskAiAssistantComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TaskAiAssistantComponent,\n        selectors: [[\"app-task-ai-assistant\"]],\n        inputs: {\n          task: \"task\"\n        },\n        outputs: {\n          taskUpdated: \"taskUpdated\",\n          subtasksGenerated: \"subtasksGenerated\"\n        },\n        decls: 45,\n        vars: 17,\n        consts: [[1, \"ai-assistant-container\"], [1, \"ai-header\"], [1, \"ai-title\"], [1, \"ai-status\"], [\"class\", \"task-info\", 4, \"ngIf\"], [1, \"ai-actions\"], [1, \"action-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", 3, \"disabled\", \"click\"], [\"class\", \"ai-suggestions\", 4, \"ngIf\"], [\"class\", \"ai-stats\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"task-info\"], [1, \"task-meta\"], [1, \"meta-item\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"ai-suggestions\"], [1, \"suggestions-header\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"class\", \"suggestions-list\", 4, \"ngIf\"], [1, \"suggestions-list\"], [\"class\", \"suggestion-item\", 3, \"applied\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [1, \"suggestion-header\"], [1, \"suggestion-icon\"], [1, \"suggestion-content\"], [1, \"suggestion-actions\"], [1, \"confidence-badge\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Appliquer la suggestion\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"suggestion-details\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Appliquer la suggestion\", 3, \"disabled\", \"click\"], [1, \"suggestion-details\"], [1, \"details-content\"], [1, \"suggestion-metadata\"], [1, \"ai-stats\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"reanalysis-recommendation\", 4, \"ngIf\"], [1, \"reanalysis-recommendation\"], [\"color\", \"warn\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"empty-state\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n        template: function TaskAiAssistantComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"psychology\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"h3\");\n            i0.ɵɵtext(6, \"Assistant IA\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"span\");\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(12, TaskAiAssistantComponent_div_12_Template, 13, 4, \"div\", 4);\n            i0.ɵɵelementStart(13, \"div\", 5)(14, \"h4\");\n            i0.ɵɵtext(15, \"Actions disponibles\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 6)(17, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_17_listener() {\n              return ctx.analyzeTask();\n            });\n            i0.ɵɵelementStart(18, \"mat-icon\");\n            i0.ɵɵtext(19, \"analytics\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"span\");\n            i0.ɵɵtext(21, \"Analyser\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(22, TaskAiAssistantComponent_mat_spinner_22_Template, 1, 0, \"mat-spinner\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_23_listener() {\n              return ctx.generateSubtasks();\n            });\n            i0.ɵɵelementStart(24, \"mat-icon\");\n            i0.ɵɵtext(25, \"list\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"span\");\n            i0.ɵɵtext(27, \"Sous-t\\u00E2ches\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, TaskAiAssistantComponent_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_29_listener() {\n              return ctx.estimateEffort();\n            });\n            i0.ɵɵelementStart(30, \"mat-icon\");\n            i0.ɵɵtext(31, \"timer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"span\");\n            i0.ɵɵtext(33, \"Estimer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, TaskAiAssistantComponent_mat_spinner_34_Template, 1, 0, \"mat-spinner\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function TaskAiAssistantComponent_Template_button_click_35_listener() {\n              return ctx.generateDescription();\n            });\n            i0.ɵɵelementStart(36, \"mat-icon\");\n            i0.ɵɵtext(37, \"description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"span\");\n            i0.ɵɵtext(39, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(40, TaskAiAssistantComponent_mat_spinner_40_Template, 1, 0, \"mat-spinner\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(41, TaskAiAssistantComponent_div_41_Template, 8, 2, \"div\", 11);\n            i0.ɵɵtemplate(42, TaskAiAssistantComponent_div_42_Template, 25, 5, \"div\", 12);\n            i0.ɵɵtemplate(43, TaskAiAssistantComponent_div_43_Template, 7, 0, \"div\", 13);\n            i0.ɵɵtemplate(44, TaskAiAssistantComponent_div_44_Template, 11, 0, \"div\", 13);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassProp(\"enabled\", ctx.isAIEnabled());\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isAIEnabled() ? \"smart_toy\" : \"smart_toy_outlined\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isAIEnabled() ? \"Activ\\u00E9\" : \"D\\u00E9sactiv\\u00E9\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.task);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.loading.analyze);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading.analyze);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading.subtasks);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading.subtasks);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading.estimate);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading.estimate);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading.description);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading.description);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.suggestions.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAIEnabled());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAIEnabled());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAIEnabled() && ctx.suggestions.length === 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.MatButton, i4.MatIconButton, i5.MatIcon, i6.MatProgressSpinner, i7.MatTooltip, i8.MatExpansionPanel, i8.MatExpansionPanelHeader, i8.MatExpansionPanelTitle],\n        styles: [\".ai-assistant-container[_ngcontent-%COMP%]{padding:1rem;height:100%;overflow-y:auto}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;padding-bottom:1rem;border-bottom:1px solid #e2e8f0}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#8b5cf6;font-size:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#1e293b;font-weight:600}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.25rem .75rem;border-radius:12px;font-size:.875rem;background:#f1f5f9;color:#64748b}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status.enabled[_ngcontent-%COMP%]{background:#dcfce7;color:#16a34a}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status.enabled[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#16a34a}.ai-assistant-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding:1rem;background:#f8fafc;border-radius:8px;border-left:4px solid #8b5cf6}.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#1e293b;font-weight:600}.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.875rem;color:#64748b}.ai-assistant-container[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;color:#1e293b;font-weight:600}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:.75rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;padding:1rem;min-height:80px;position:relative}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#1e293b;font-weight:600}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{margin-bottom:1rem;border:1px solid #e2e8f0;border-radius:8px;overflow:hidden;transition:all .2s ease}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover{box-shadow:0 2px 8px #0000001a}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item.applied[_ngcontent-%COMP%]{background:#f0fdf4;border-color:#16a34a}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item.applied[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]{background:#dcfce7}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;padding:1rem;background:#f8fafc}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]{flex-shrink:0}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%]{flex:1}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#1e293b;font-weight:600;font-size:.875rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#64748b;font-size:.875rem;line-height:1.4}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:600;color:#fff}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]{padding:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1rem;color:#64748b;font-size:.875rem;line-height:1.5}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{font-size:.75rem;color:#64748b}.ai-assistant-container[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .suggestion-metadata[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#1e293b}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;color:#1e293b;font-weight:600}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:.75rem;margin-bottom:1rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{padding:.75rem;background:#f8fafc;border-radius:6px;text-align:center}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{display:block;font-size:.75rem;color:#64748b;margin-bottom:.25rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:1.125rem;font-weight:600;color:#1e293b}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem;background:#fef3c7;border:1px solid #f59e0b;border-radius:6px;font-size:.875rem}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f59e0b}.ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .reanalysis-recommendation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex:1;color:#92400e}.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;text-align:center;color:#64748b}.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem;margin-bottom:1rem;opacity:.5}.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#1e293b}.ai-assistant-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1.5rem;font-size:.875rem}@media (max-width: 480px){.ai-assistant-container[_ngcontent-%COMP%]   .ai-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%], .ai-assistant-container[_ngcontent-%COMP%]   .ai-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.ai-assistant-container[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important;gap:.75rem!important}.ai-assistant-container[_ngcontent-%COMP%]   .suggestion-header[_ngcontent-%COMP%]   .suggestion-actions[_ngcontent-%COMP%]{align-self:flex-end}}\"]\n      });\n    }\n  }\n  return TaskAiAssistantComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}