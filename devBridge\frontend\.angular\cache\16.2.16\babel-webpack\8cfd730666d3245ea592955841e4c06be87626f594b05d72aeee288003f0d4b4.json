{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\n/**\n * separateOperations accepts a single AST document which may contain many\n * operations and fragments and returns a collection of AST documents each of\n * which contains a single operation as well the fragment definitions it\n * refers to.\n */\n\nexport function separateOperations(documentAST) {\n  const operations = [];\n  const depGraph = Object.create(null); // Populate metadata and build a dependency graph.\n\n  for (const definitionNode of documentAST.definitions) {\n    switch (definitionNode.kind) {\n      case Kind.OPERATION_DEFINITION:\n        operations.push(definitionNode);\n        break;\n      case Kind.FRAGMENT_DEFINITION:\n        depGraph[definitionNode.name.value] = collectDependencies(definitionNode.selectionSet);\n        break;\n      default: // ignore non-executable definitions\n    }\n  } // For each operation, produce a new synthesized AST which includes only what\n  // is necessary for completing that operation.\n\n  const separatedDocumentASTs = Object.create(null);\n  for (const operation of operations) {\n    const dependencies = new Set();\n    for (const fragmentName of collectDependencies(operation.selectionSet)) {\n      collectTransitiveDependencies(dependencies, depGraph, fragmentName);\n    } // Provides the empty string for anonymous operations.\n\n    const operationName = operation.name ? operation.name.value : ''; // The list of definition nodes to be included for this operation, sorted\n    // to retain the same order as the original document.\n\n    separatedDocumentASTs[operationName] = {\n      kind: Kind.DOCUMENT,\n      definitions: documentAST.definitions.filter(node => node === operation || node.kind === Kind.FRAGMENT_DEFINITION && dependencies.has(node.name.value))\n    };\n  }\n  return separatedDocumentASTs;\n}\n\n// From a dependency graph, collects a list of transitive dependencies by\n// recursing through a dependency graph.\nfunction collectTransitiveDependencies(collected, depGraph, fromName) {\n  if (!collected.has(fromName)) {\n    collected.add(fromName);\n    const immediateDeps = depGraph[fromName];\n    if (immediateDeps !== undefined) {\n      for (const toName of immediateDeps) {\n        collectTransitiveDependencies(collected, depGraph, toName);\n      }\n    }\n  }\n}\nfunction collectDependencies(selectionSet) {\n  const dependencies = [];\n  visit(selectionSet, {\n    FragmentSpread(node) {\n      dependencies.push(node.name.value);\n    }\n  });\n  return dependencies;\n}", "map": {"version": 3, "names": ["Kind", "visit", "separateOperations", "documentAST", "operations", "depGraph", "Object", "create", "definitionNode", "definitions", "kind", "OPERATION_DEFINITION", "push", "FRAGMENT_DEFINITION", "name", "value", "collectDependencies", "selectionSet", "separatedDocumentASTs", "operation", "dependencies", "Set", "fragmentName", "collectTransitiveDependencies", "operationName", "DOCUMENT", "filter", "node", "has", "collected", "fromName", "add", "immediateDeps", "undefined", "to<PERSON>ame", "FragmentSpread"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/utilities/separateOperations.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\n/**\n * separateOperations accepts a single AST document which may contain many\n * operations and fragments and returns a collection of AST documents each of\n * which contains a single operation as well the fragment definitions it\n * refers to.\n */\n\nexport function separateOperations(documentAST) {\n  const operations = [];\n  const depGraph = Object.create(null); // Populate metadata and build a dependency graph.\n\n  for (const definitionNode of documentAST.definitions) {\n    switch (definitionNode.kind) {\n      case Kind.OPERATION_DEFINITION:\n        operations.push(definitionNode);\n        break;\n\n      case Kind.FRAGMENT_DEFINITION:\n        depGraph[definitionNode.name.value] = collectDependencies(\n          definitionNode.selectionSet,\n        );\n        break;\n\n      default: // ignore non-executable definitions\n    }\n  } // For each operation, produce a new synthesized AST which includes only what\n  // is necessary for completing that operation.\n\n  const separatedDocumentASTs = Object.create(null);\n\n  for (const operation of operations) {\n    const dependencies = new Set();\n\n    for (const fragmentName of collectDependencies(operation.selectionSet)) {\n      collectTransitiveDependencies(dependencies, depGraph, fragmentName);\n    } // Provides the empty string for anonymous operations.\n\n    const operationName = operation.name ? operation.name.value : ''; // The list of definition nodes to be included for this operation, sorted\n    // to retain the same order as the original document.\n\n    separatedDocumentASTs[operationName] = {\n      kind: Kind.DOCUMENT,\n      definitions: documentAST.definitions.filter(\n        (node) =>\n          node === operation ||\n          (node.kind === Kind.FRAGMENT_DEFINITION &&\n            dependencies.has(node.name.value)),\n      ),\n    };\n  }\n\n  return separatedDocumentASTs;\n}\n\n// From a dependency graph, collects a list of transitive dependencies by\n// recursing through a dependency graph.\nfunction collectTransitiveDependencies(collected, depGraph, fromName) {\n  if (!collected.has(fromName)) {\n    collected.add(fromName);\n    const immediateDeps = depGraph[fromName];\n\n    if (immediateDeps !== undefined) {\n      for (const toName of immediateDeps) {\n        collectTransitiveDependencies(collected, depGraph, toName);\n      }\n    }\n  }\n}\n\nfunction collectDependencies(selectionSet) {\n  const dependencies = [];\n  visit(selectionSet, {\n    FragmentSpread(node) {\n      dependencies.push(node.name.value);\n    },\n  });\n  return dependencies;\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAACC,WAAW,EAAE;EAC9C,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtC,KAAK,MAAMC,cAAc,IAAIL,WAAW,CAACM,WAAW,EAAE;IACpD,QAAQD,cAAc,CAACE,IAAI;MACzB,KAAKV,IAAI,CAACW,oBAAoB;QAC5BP,UAAU,CAACQ,IAAI,CAACJ,cAAc,CAAC;QAC/B;MAEF,KAAKR,IAAI,CAACa,mBAAmB;QAC3BR,QAAQ,CAACG,cAAc,CAACM,IAAI,CAACC,KAAK,CAAC,GAAGC,mBAAmB,CACvDR,cAAc,CAACS,YACjB,CAAC;QACD;MAEF,QAAQ,CAAC;IACX;EACF,CAAC,CAAC;EACF;;EAEA,MAAMC,qBAAqB,GAAGZ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAEjD,KAAK,MAAMY,SAAS,IAAIf,UAAU,EAAE;IAClC,MAAMgB,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE9B,KAAK,MAAMC,YAAY,IAAIN,mBAAmB,CAACG,SAAS,CAACF,YAAY,CAAC,EAAE;MACtEM,6BAA6B,CAACH,YAAY,EAAEf,QAAQ,EAAEiB,YAAY,CAAC;IACrE,CAAC,CAAC;;IAEF,MAAME,aAAa,GAAGL,SAAS,CAACL,IAAI,GAAGK,SAAS,CAACL,IAAI,CAACC,KAAK,GAAG,EAAE,CAAC,CAAC;IAClE;;IAEAG,qBAAqB,CAACM,aAAa,CAAC,GAAG;MACrCd,IAAI,EAAEV,IAAI,CAACyB,QAAQ;MACnBhB,WAAW,EAAEN,WAAW,CAACM,WAAW,CAACiB,MAAM,CACxCC,IAAI,IACHA,IAAI,KAAKR,SAAS,IACjBQ,IAAI,CAACjB,IAAI,KAAKV,IAAI,CAACa,mBAAmB,IACrCO,YAAY,CAACQ,GAAG,CAACD,IAAI,CAACb,IAAI,CAACC,KAAK,CACtC;IACF,CAAC;EACH;EAEA,OAAOG,qBAAqB;AAC9B;;AAEA;AACA;AACA,SAASK,6BAA6BA,CAACM,SAAS,EAAExB,QAAQ,EAAEyB,QAAQ,EAAE;EACpE,IAAI,CAACD,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,EAAE;IAC5BD,SAAS,CAACE,GAAG,CAACD,QAAQ,CAAC;IACvB,MAAME,aAAa,GAAG3B,QAAQ,CAACyB,QAAQ,CAAC;IAExC,IAAIE,aAAa,KAAKC,SAAS,EAAE;MAC/B,KAAK,MAAMC,MAAM,IAAIF,aAAa,EAAE;QAClCT,6BAA6B,CAACM,SAAS,EAAExB,QAAQ,EAAE6B,MAAM,CAAC;MAC5D;IACF;EACF;AACF;AAEA,SAASlB,mBAAmBA,CAACC,YAAY,EAAE;EACzC,MAAMG,YAAY,GAAG,EAAE;EACvBnB,KAAK,CAACgB,YAAY,EAAE;IAClBkB,cAAcA,CAACR,IAAI,EAAE;MACnBP,YAAY,CAACR,IAAI,CAACe,IAAI,CAACb,IAAI,CAACC,KAAK,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAOK,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}