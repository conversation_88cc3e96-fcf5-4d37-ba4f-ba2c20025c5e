{"ast": null, "code": "import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name millisecondsToMinutes\n * @category Conversion Helpers\n * @summary Convert milliseconds to minutes.\n *\n * @description\n * Convert a number of milliseconds to a full number of minutes.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in minutes\n *\n * @example\n * // Convert 60000 milliseconds to minutes:\n * const result = millisecondsToMinutes(60000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToMinutes(119999)\n * //=> 1\n */\nexport function millisecondsToMinutes(milliseconds) {\n  const minutes = milliseconds / millisecondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToMinutes;", "map": {"version": 3, "names": ["millisecondsInMinute", "millisecondsToMinutes", "milliseconds", "minutes", "Math", "trunc"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/millisecondsToMinutes.js"], "sourcesContent": ["import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name millisecondsToMinutes\n * @category Conversion Helpers\n * @summary Convert milliseconds to minutes.\n *\n * @description\n * Convert a number of milliseconds to a full number of minutes.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in minutes\n *\n * @example\n * // Convert 60000 milliseconds to minutes:\n * const result = millisecondsToMinutes(60000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToMinutes(119999)\n * //=> 1\n */\nexport function millisecondsToMinutes(milliseconds) {\n  const minutes = milliseconds / millisecondsInMinute;\n  return Math.trunc(minutes);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToMinutes;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,gBAAgB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,YAAY,EAAE;EAClD,MAAMC,OAAO,GAAGD,YAAY,GAAGF,oBAAoB;EACnD,OAAOI,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;AAC5B;;AAEA;AACA,eAAeF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}