{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { isEqualType, isTypeSubTypeOf } from '../utilities/typeComparators.mjs';\nimport { isEnumType, isInputObjectType, isInputType, isInterfaceType, isNamedType, isNonNullType, isObjectType, isOutputType, isRequiredArgument, isRequiredInputField, isUnionType } from './definition.mjs';\nimport { GraphQLDeprecatedDirective, isDirective } from './directives.mjs';\nimport { isIntrospectionType } from './introspection.mjs';\nimport { assertSchema } from './schema.mjs';\n/**\n * Implements the \"Type Validation\" sub-sections of the specification's\n * \"Type System\" section.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the Schema is valid.\n */\n\nexport function validateSchema(schema) {\n  // First check to ensure the provided value is in fact a GraphQLSchema.\n  assertSchema(schema); // If this Schema has already been validated, return the previous results.\n\n  if (schema.__validationErrors) {\n    return schema.__validationErrors;\n  } // Validate the schema, producing a list of errors.\n\n  const context = new SchemaValidationContext(schema);\n  validateRootTypes(context);\n  validateDirectives(context);\n  validateTypes(context); // Persist the results of validation before returning to ensure validation\n  // does not run multiple times for this schema.\n\n  const errors = context.getErrors();\n  schema.__validationErrors = errors;\n  return errors;\n}\n/**\n * Utility function which asserts a schema is valid by throwing an error if\n * it is invalid.\n */\n\nexport function assertValidSchema(schema) {\n  const errors = validateSchema(schema);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}\nclass SchemaValidationContext {\n  constructor(schema) {\n    this._errors = [];\n    this.schema = schema;\n  }\n  reportError(message, nodes) {\n    const _nodes = Array.isArray(nodes) ? nodes.filter(Boolean) : nodes;\n    this._errors.push(new GraphQLError(message, {\n      nodes: _nodes\n    }));\n  }\n  getErrors() {\n    return this._errors;\n  }\n}\nfunction validateRootTypes(context) {\n  const schema = context.schema;\n  const queryType = schema.getQueryType();\n  if (!queryType) {\n    context.reportError('Query root type must be provided.', schema.astNode);\n  } else if (!isObjectType(queryType)) {\n    var _getOperationTypeNode;\n    context.reportError(`Query root type must be Object type, it cannot be ${inspect(queryType)}.`, (_getOperationTypeNode = getOperationTypeNode(schema, OperationTypeNode.QUERY)) !== null && _getOperationTypeNode !== void 0 ? _getOperationTypeNode : queryType.astNode);\n  }\n  const mutationType = schema.getMutationType();\n  if (mutationType && !isObjectType(mutationType)) {\n    var _getOperationTypeNode2;\n    context.reportError('Mutation root type must be Object type if provided, it cannot be ' + `${inspect(mutationType)}.`, (_getOperationTypeNode2 = getOperationTypeNode(schema, OperationTypeNode.MUTATION)) !== null && _getOperationTypeNode2 !== void 0 ? _getOperationTypeNode2 : mutationType.astNode);\n  }\n  const subscriptionType = schema.getSubscriptionType();\n  if (subscriptionType && !isObjectType(subscriptionType)) {\n    var _getOperationTypeNode3;\n    context.reportError('Subscription root type must be Object type if provided, it cannot be ' + `${inspect(subscriptionType)}.`, (_getOperationTypeNode3 = getOperationTypeNode(schema, OperationTypeNode.SUBSCRIPTION)) !== null && _getOperationTypeNode3 !== void 0 ? _getOperationTypeNode3 : subscriptionType.astNode);\n  }\n}\nfunction getOperationTypeNode(schema, operation) {\n  var _flatMap$find;\n  return (_flatMap$find = [schema.astNode, ...schema.extensionASTNodes].flatMap(\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n  schemaNode => {\n    var _schemaNode$operation;\n    return (/* c8 ignore next */\n      (_schemaNode$operation = schemaNode === null || schemaNode === void 0 ? void 0 : schemaNode.operationTypes) !== null && _schemaNode$operation !== void 0 ? _schemaNode$operation : []\n    );\n  }).find(operationNode => operationNode.operation === operation)) === null || _flatMap$find === void 0 ? void 0 : _flatMap$find.type;\n}\nfunction validateDirectives(context) {\n  for (const directive of context.schema.getDirectives()) {\n    // Ensure all directives are in fact GraphQL directives.\n    if (!isDirective(directive)) {\n      context.reportError(`Expected directive but got: ${inspect(directive)}.`, directive === null || directive === void 0 ? void 0 : directive.astNode);\n      continue;\n    } // Ensure they are named correctly.\n\n    validateName(context, directive); // TODO: Ensure proper locations.\n    // Ensure the arguments are valid.\n\n    for (const arg of directive.args) {\n      // Ensure they are named correctly.\n      validateName(context, arg); // Ensure the type is an input type.\n\n      if (!isInputType(arg.type)) {\n        context.reportError(`The type of @${directive.name}(${arg.name}:) must be Input Type ` + `but got: ${inspect(arg.type)}.`, arg.astNode);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode;\n        context.reportError(`Required argument @${directive.name}(${arg.name}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode = arg.astNode) === null || _arg$astNode === void 0 ? void 0 : _arg$astNode.type]);\n      }\n    }\n  }\n}\nfunction validateName(context, node) {\n  // Ensure names are valid, however introspection types opt out.\n  if (node.name.startsWith('__')) {\n    context.reportError(`Name \"${node.name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`, node.astNode);\n  }\n}\nfunction validateTypes(context) {\n  const validateInputObjectCircularRefs = createInputObjectCircularRefsValidator(context);\n  const typeMap = context.schema.getTypeMap();\n  for (const type of Object.values(typeMap)) {\n    // Ensure all provided types are in fact GraphQL type.\n    if (!isNamedType(type)) {\n      context.reportError(`Expected GraphQL named type but got: ${inspect(type)}.`, type.astNode);\n      continue;\n    } // Ensure it is named correctly (excluding introspection types).\n\n    if (!isIntrospectionType(type)) {\n      validateName(context, type);\n    }\n    if (isObjectType(type)) {\n      // Ensure fields are valid\n      validateFields(context, type); // Ensure objects implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isInterfaceType(type)) {\n      // Ensure fields are valid.\n      validateFields(context, type); // Ensure interfaces implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isUnionType(type)) {\n      // Ensure Unions include valid member types.\n      validateUnionMembers(context, type);\n    } else if (isEnumType(type)) {\n      // Ensure Enums have valid values.\n      validateEnumValues(context, type);\n    } else if (isInputObjectType(type)) {\n      // Ensure Input Object fields are valid.\n      validateInputFields(context, type); // Ensure Input Objects do not contain non-nullable circular references\n\n      validateInputObjectCircularRefs(type);\n    }\n  }\n}\nfunction validateFields(context, type) {\n  const fields = Object.values(type.getFields()); // Objects and Interfaces both must define one or more fields.\n\n  if (fields.length === 0) {\n    context.reportError(`Type ${type.name} must define one or more fields.`, [type.astNode, ...type.extensionASTNodes]);\n  }\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an output type\n\n    if (!isOutputType(field.type)) {\n      var _field$astNode;\n      context.reportError(`The type of ${type.name}.${field.name} must be Output Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode = field.astNode) === null || _field$astNode === void 0 ? void 0 : _field$astNode.type);\n    } // Ensure the arguments are valid\n\n    for (const arg of field.args) {\n      const argName = arg.name; // Ensure they are named correctly.\n\n      validateName(context, arg); // Ensure the type is an input type\n\n      if (!isInputType(arg.type)) {\n        var _arg$astNode2;\n        context.reportError(`The type of ${type.name}.${field.name}(${argName}:) must be Input ` + `Type but got: ${inspect(arg.type)}.`, (_arg$astNode2 = arg.astNode) === null || _arg$astNode2 === void 0 ? void 0 : _arg$astNode2.type);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode3;\n        context.reportError(`Required argument ${type.name}.${field.name}(${argName}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode3 = arg.astNode) === null || _arg$astNode3 === void 0 ? void 0 : _arg$astNode3.type]);\n      }\n    }\n  }\n}\nfunction validateInterfaces(context, type) {\n  const ifaceTypeNames = Object.create(null);\n  for (const iface of type.getInterfaces()) {\n    if (!isInterfaceType(iface)) {\n      context.reportError(`Type ${inspect(type)} must only implement Interface types, ` + `it cannot implement ${inspect(iface)}.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (type === iface) {\n      context.reportError(`Type ${type.name} cannot implement itself because it would create a circular reference.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (ifaceTypeNames[iface.name]) {\n      context.reportError(`Type ${type.name} can only implement ${iface.name} once.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    ifaceTypeNames[iface.name] = true;\n    validateTypeImplementsAncestors(context, type, iface);\n    validateTypeImplementsInterface(context, type, iface);\n  }\n}\nfunction validateTypeImplementsInterface(context, type, iface) {\n  const typeFieldMap = type.getFields(); // Assert each interface field is implemented.\n\n  for (const ifaceField of Object.values(iface.getFields())) {\n    const fieldName = ifaceField.name;\n    const typeField = typeFieldMap[fieldName]; // Assert interface field exists on type.\n\n    if (!typeField) {\n      context.reportError(`Interface field ${iface.name}.${fieldName} expected but ${type.name} does not provide it.`, [ifaceField.astNode, type.astNode, ...type.extensionASTNodes]);\n      continue;\n    } // Assert interface field type is satisfied by type field type, by being\n    // a valid subtype. (covariant)\n\n    if (!isTypeSubTypeOf(context.schema, typeField.type, ifaceField.type)) {\n      var _ifaceField$astNode, _typeField$astNode;\n      context.reportError(`Interface field ${iface.name}.${fieldName} expects type ` + `${inspect(ifaceField.type)} but ${type.name}.${fieldName} ` + `is type ${inspect(typeField.type)}.`, [(_ifaceField$astNode = ifaceField.astNode) === null || _ifaceField$astNode === void 0 ? void 0 : _ifaceField$astNode.type, (_typeField$astNode = typeField.astNode) === null || _typeField$astNode === void 0 ? void 0 : _typeField$astNode.type]);\n    } // Assert each interface field arg is implemented.\n\n    for (const ifaceArg of ifaceField.args) {\n      const argName = ifaceArg.name;\n      const typeArg = typeField.args.find(arg => arg.name === argName); // Assert interface field arg exists on object field.\n\n      if (!typeArg) {\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) expected but ${type.name}.${fieldName} does not provide it.`, [ifaceArg.astNode, typeField.astNode]);\n        continue;\n      } // Assert interface field arg type matches object field arg type.\n      // (invariant)\n      // TODO: change to contravariant?\n\n      if (!isEqualType(ifaceArg.type, typeArg.type)) {\n        var _ifaceArg$astNode, _typeArg$astNode;\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) ` + `expects type ${inspect(ifaceArg.type)} but ` + `${type.name}.${fieldName}(${argName}:) is type ` + `${inspect(typeArg.type)}.`, [(_ifaceArg$astNode = ifaceArg.astNode) === null || _ifaceArg$astNode === void 0 ? void 0 : _ifaceArg$astNode.type, (_typeArg$astNode = typeArg.astNode) === null || _typeArg$astNode === void 0 ? void 0 : _typeArg$astNode.type]);\n      } // TODO: validate default values?\n    } // Assert additional arguments must not be required.\n\n    for (const typeArg of typeField.args) {\n      const argName = typeArg.name;\n      const ifaceArg = ifaceField.args.find(arg => arg.name === argName);\n      if (!ifaceArg && isRequiredArgument(typeArg)) {\n        context.reportError(`Object field ${type.name}.${fieldName} includes required argument ${argName} that is missing from the Interface field ${iface.name}.${fieldName}.`, [typeArg.astNode, ifaceField.astNode]);\n      }\n    }\n  }\n}\nfunction validateTypeImplementsAncestors(context, type, iface) {\n  const ifaceInterfaces = type.getInterfaces();\n  for (const transitive of iface.getInterfaces()) {\n    if (!ifaceInterfaces.includes(transitive)) {\n      context.reportError(transitive === type ? `Type ${type.name} cannot implement ${iface.name} because it would create a circular reference.` : `Type ${type.name} must implement ${transitive.name} because it is implemented by ${iface.name}.`, [...getAllImplementsInterfaceNodes(iface, transitive), ...getAllImplementsInterfaceNodes(type, iface)]);\n    }\n  }\n}\nfunction validateUnionMembers(context, union) {\n  const memberTypes = union.getTypes();\n  if (memberTypes.length === 0) {\n    context.reportError(`Union type ${union.name} must define one or more member types.`, [union.astNode, ...union.extensionASTNodes]);\n  }\n  const includedTypeNames = Object.create(null);\n  for (const memberType of memberTypes) {\n    if (includedTypeNames[memberType.name]) {\n      context.reportError(`Union type ${union.name} can only include type ${memberType.name} once.`, getUnionMemberTypeNodes(union, memberType.name));\n      continue;\n    }\n    includedTypeNames[memberType.name] = true;\n    if (!isObjectType(memberType)) {\n      context.reportError(`Union type ${union.name} can only include Object types, ` + `it cannot include ${inspect(memberType)}.`, getUnionMemberTypeNodes(union, String(memberType)));\n    }\n  }\n}\nfunction validateEnumValues(context, enumType) {\n  const enumValues = enumType.getValues();\n  if (enumValues.length === 0) {\n    context.reportError(`Enum type ${enumType.name} must define one or more values.`, [enumType.astNode, ...enumType.extensionASTNodes]);\n  }\n  for (const enumValue of enumValues) {\n    // Ensure valid name.\n    validateName(context, enumValue);\n  }\n}\nfunction validateInputFields(context, inputObj) {\n  const fields = Object.values(inputObj.getFields());\n  if (fields.length === 0) {\n    context.reportError(`Input Object type ${inputObj.name} must define one or more fields.`, [inputObj.astNode, ...inputObj.extensionASTNodes]);\n  } // Ensure the arguments are valid\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an input type\n\n    if (!isInputType(field.type)) {\n      var _field$astNode2;\n      context.reportError(`The type of ${inputObj.name}.${field.name} must be Input Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode2 = field.astNode) === null || _field$astNode2 === void 0 ? void 0 : _field$astNode2.type);\n    }\n    if (isRequiredInputField(field) && field.deprecationReason != null) {\n      var _field$astNode3;\n      context.reportError(`Required input field ${inputObj.name}.${field.name} cannot be deprecated.`, [getDeprecatedDirectiveNode(field.astNode), (_field$astNode3 = field.astNode) === null || _field$astNode3 === void 0 ? void 0 : _field$astNode3.type]);\n    }\n  }\n}\nfunction createInputObjectCircularRefsValidator(context) {\n  // Modified copy of algorithm from 'src/validation/rules/NoFragmentCycles.js'.\n  // Tracks already visited types to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedTypes = Object.create(null); // Array of types nodes used to produce meaningful errors\n\n  const fieldPath = []; // Position in the type path\n\n  const fieldPathIndexByTypeName = Object.create(null);\n  return detectCycleRecursive; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(inputObj) {\n    if (visitedTypes[inputObj.name]) {\n      return;\n    }\n    visitedTypes[inputObj.name] = true;\n    fieldPathIndexByTypeName[inputObj.name] = fieldPath.length;\n    const fields = Object.values(inputObj.getFields());\n    for (const field of fields) {\n      if (isNonNullType(field.type) && isInputObjectType(field.type.ofType)) {\n        const fieldType = field.type.ofType;\n        const cycleIndex = fieldPathIndexByTypeName[fieldType.name];\n        fieldPath.push(field);\n        if (cycleIndex === undefined) {\n          detectCycleRecursive(fieldType);\n        } else {\n          const cyclePath = fieldPath.slice(cycleIndex);\n          const pathStr = cyclePath.map(fieldObj => fieldObj.name).join('.');\n          context.reportError(`Cannot reference Input Object \"${fieldType.name}\" within itself through a series of non-null fields: \"${pathStr}\".`, cyclePath.map(fieldObj => fieldObj.astNode));\n        }\n        fieldPath.pop();\n      }\n    }\n    fieldPathIndexByTypeName[inputObj.name] = undefined;\n  }\n}\nfunction getAllImplementsInterfaceNodes(type, iface) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = type;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(typeNode => {\n    var _typeNode$interfaces;\n    return (/* c8 ignore next */\n      (_typeNode$interfaces = typeNode.interfaces) !== null && _typeNode$interfaces !== void 0 ? _typeNode$interfaces : []\n    );\n  }).filter(ifaceNode => ifaceNode.name.value === iface.name);\n}\nfunction getUnionMemberTypeNodes(union, typeName) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = union;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(unionNode => {\n    var _unionNode$types;\n    return (/* c8 ignore next */\n      (_unionNode$types = unionNode.types) !== null && _unionNode$types !== void 0 ? _unionNode$types : []\n    );\n  }).filter(typeNode => typeNode.name.value === typeName);\n}\nfunction getDeprecatedDirectiveNode(definitionNode) {\n  var _definitionNode$direc;\n  return definitionNode === null || definitionNode === void 0 ? void 0 : (_definitionNode$direc = definitionNode.directives) === null || _definitionNode$direc === void 0 ? void 0 : _definitionNode$direc.find(node => node.name.value === GraphQLDeprecatedDirective.name);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}