{"ast": null, "code": "import { syntaxError } from '../error/syntaxError.mjs';\nimport { Token } from './ast.mjs';\nimport { dedentBlockStringLines } from './blockString.mjs';\nimport { isDigit, isNameContinue, isNameStart } from './characterClasses.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nexport class Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new Token(TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = this.token = this.lookahead();\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n    if (token.kind !== TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === TokenKind.COMMENT);\n    }\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nexport function isPunctuatorTokenKind(kind) {\n  return kind === TokenKind.BANG || kind === TokenKind.DOLLAR || kind === TokenKind.AMP || kind === TokenKind.PAREN_L || kind === TokenKind.PAREN_R || kind === TokenKind.SPREAD || kind === TokenKind.COLON || kind === TokenKind.EQUALS || kind === TokenKind.AT || kind === TokenKind.BRACKET_L || kind === TokenKind.BRACKET_R || kind === TokenKind.BRACE_L || kind === TokenKind.PIPE || kind === TokenKind.BRACE_R;\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return code >= 0x0000 && code <= 0xd7ff || code >= 0xe000 && code <= 0x10ffff;\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return isLeadingSurrogate(body.charCodeAt(location)) && isTrailingSurrogate(body.charCodeAt(location + 1));\n}\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n  if (code === undefined) {\n    return TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, TokenKind.BANG, position, position + 1);\n      case 0x0024:\n        // $\n        return createToken(lexer, TokenKind.DOLLAR, position, position + 1);\n      case 0x0026:\n        // &\n        return createToken(lexer, TokenKind.AMP, position, position + 1);\n      case 0x0028:\n        // (\n        return createToken(lexer, TokenKind.PAREN_L, position, position + 1);\n      case 0x0029:\n        // )\n        return createToken(lexer, TokenKind.PAREN_R, position, position + 1);\n      case 0x002e:\n        // .\n        if (body.charCodeAt(position + 1) === 0x002e && body.charCodeAt(position + 2) === 0x002e) {\n          return createToken(lexer, TokenKind.SPREAD, position, position + 3);\n        }\n        break;\n      case 0x003a:\n        // :\n        return createToken(lexer, TokenKind.COLON, position, position + 1);\n      case 0x003d:\n        // =\n        return createToken(lexer, TokenKind.EQUALS, position, position + 1);\n      case 0x0040:\n        // @\n        return createToken(lexer, TokenKind.AT, position, position + 1);\n      case 0x005b:\n        // [\n        return createToken(lexer, TokenKind.BRACKET_L, position, position + 1);\n      case 0x005d:\n        // ]\n        return createToken(lexer, TokenKind.BRACKET_R, position, position + 1);\n      case 0x007b:\n        // {\n        return createToken(lexer, TokenKind.BRACE_L, position, position + 1);\n      case 0x007c:\n        // |\n        return createToken(lexer, TokenKind.PIPE, position, position + 1);\n      case 0x007d:\n        // }\n        return createToken(lexer, TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022) {\n          return readBlockString(lexer, position);\n        }\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if (isDigit(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if (isNameStart(code)) {\n      return readName(lexer, position);\n    }\n    throw syntaxError(lexer.source, position, code === 0x0027 ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?' : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position) ? `Unexpected character: ${printCodePointAt(lexer, position)}.` : `Invalid character: ${printCodePointAt(lexer, position)}.`);\n  }\n  return createToken(lexer, TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n  return createToken(lexer, TokenKind.COMMENT, start, position, body.slice(start + 1, position));\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n    if (isDigit(code)) {\n      throw syntaxError(lexer.source, position, `Invalid number, unexpected digit after 0: ${printCodePointAt(lexer, position)}.`);\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || isNameStart(code)) {\n    throw syntaxError(lexer.source, position, `Invalid number, expected digit but got: ${printCodePointAt(lexer, position)}.`);\n  }\n  return createToken(lexer, isFloat ? TokenKind.FLOAT : TokenKind.INT, start, position, body.slice(start, position));\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!isDigit(firstCode)) {\n    throw syntaxError(lexer.source, start, `Invalid number, expected digit but got: ${printCodePointAt(lexer, start)}.`);\n  }\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while (isDigit(body.charCodeAt(position))) {\n    ++position;\n  }\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape = body.charCodeAt(position + 1) === 0x0075 // u\n      ? body.charCodeAt(position + 2) === 0x007b // {\n      ? readEscapedUnicodeVariableWidth(lexer, position) : readEscapedUnicodeFixedWidth(lexer, position) : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(lexer.source, position, `Invalid character within String: ${printCodePointAt(lexer, position)}.`);\n    }\n  }\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n      return {\n        value: String.fromCodePoint(point),\n        size\n      };\n    } // Append this hex digit to the code point.\n\n    point = point << 4 | readHexDigit(code);\n    if (point < 0) {\n      break;\n    }\n  }\n  throw syntaxError(lexer.source, position, `Invalid Unicode escape sequence: \"${body.slice(position, position + size)}\".`);\n}\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (body.charCodeAt(position + 6) === 0x005c && body.charCodeAt(position + 7) === 0x0075) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12\n        };\n      }\n    }\n  }\n  throw syntaxError(lexer.source, position, `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`);\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return readHexDigit(body.charCodeAt(position)) << 12 | readHexDigit(body.charCodeAt(position + 1)) << 8 | readHexDigit(body.charCodeAt(position + 2)) << 4 | readHexDigit(body.charCodeAt(position + 3));\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n  ? code - 0x0030 : code >= 0x0041 && code <= 0x0046 // A-F\n  ? code - 0x0037 : code >= 0x0061 && code <= 0x0066 // a-f\n  ? code - 0x0057 : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2\n      };\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2\n      };\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2\n      };\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2\n      };\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2\n      };\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2\n      };\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2\n      };\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2\n      };\n  }\n  throw syntaxError(lexer.source, position, `Invalid character escape sequence: \"${body.slice(position, position + 2)}\".`);\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (code === 0x0022 && body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(lexer, TokenKind.BLOCK_STRING, start, position + 3,\n      // Return a string of the lines joined with U+000A.\n      dedentBlockStringLines(blockLines).join('\\n'));\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (code === 0x005c && body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022 && body.charCodeAt(position + 3) === 0x0022) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(lexer.source, position, `Invalid character within String: ${printCodePointAt(lexer, position)}.`);\n    }\n  }\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n    if (isNameContinue(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n  return createToken(lexer, TokenKind.NAME, start, position, body.slice(start, position));\n}", "map": {"version": 3, "names": ["syntaxError", "Token", "dedentBlockStringLines", "isDigit", "isNameContinue", "isNameStart", "TokenKind", "<PERSON><PERSON>", "constructor", "source", "startOfFileToken", "SOF", "lastToken", "token", "line", "lineStart", "Symbol", "toStringTag", "advance", "<PERSON><PERSON><PERSON>", "kind", "EOF", "next", "nextToken", "readNextToken", "end", "prev", "COMMENT", "isPunctuatorTokenKind", "BANG", "DOLLAR", "AMP", "PAREN_L", "PAREN_R", "SPREAD", "COLON", "EQUALS", "AT", "BRACKET_L", "BRACKET_R", "BRACE_L", "PIPE", "BRACE_R", "isUnicodeScalarValue", "code", "isSupplementaryCodePoint", "body", "location", "isLeadingSurrogate", "charCodeAt", "isTrailingSurrogate", "printCodePointAt", "lexer", "codePointAt", "undefined", "char", "String", "fromCodePoint", "toString", "toUpperCase", "padStart", "createToken", "start", "value", "col", "<PERSON><PERSON><PERSON><PERSON>", "length", "position", "readComment", "readBlockString", "readString", "readNumber", "readName", "slice", "firstCode", "isFloat", "readDigits", "FLOAT", "INT", "chunkStart", "STRING", "escape", "readEscapedUnicodeVariableWidth", "readEscapedUnicodeFixedWidth", "readEscapedCharacter", "size", "point", "readHexDigit", "read16BitHexCode", "trailingCode", "currentLine", "blockLines", "push", "BLOCK_STRING", "join", "NAME"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/language/lexer.mjs"], "sourcesContent": ["import { syntaxError } from '../error/syntaxError.mjs';\nimport { Token } from './ast.mjs';\nimport { dedentBlockStringLines } from './blockString.mjs';\nimport { isDigit, isNameContinue, isNameStart } from './characterClasses.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nexport class Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new Token(TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = (this.token = this.lookahead());\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n\n    if (token.kind !== TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === TokenKind.COMMENT);\n    }\n\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nexport function isPunctuatorTokenKind(kind) {\n  return (\n    kind === TokenKind.BANG ||\n    kind === TokenKind.DOLLAR ||\n    kind === TokenKind.AMP ||\n    kind === TokenKind.PAREN_L ||\n    kind === TokenKind.PAREN_R ||\n    kind === TokenKind.SPREAD ||\n    kind === TokenKind.COLON ||\n    kind === TokenKind.EQUALS ||\n    kind === TokenKind.AT ||\n    kind === TokenKind.BRACKET_L ||\n    kind === TokenKind.BRACKET_R ||\n    kind === TokenKind.BRACE_L ||\n    kind === TokenKind.PIPE ||\n    kind === TokenKind.BRACE_R\n  );\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return (\n    (code >= 0x0000 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0x10ffff)\n  );\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return (\n    isLeadingSurrogate(body.charCodeAt(location)) &&\n    isTrailingSurrogate(body.charCodeAt(location + 1))\n  );\n}\n\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\n\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n\n  if (code === undefined) {\n    return TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, TokenKind.BANG, position, position + 1);\n\n      case 0x0024:\n        // $\n        return createToken(lexer, TokenKind.DOLLAR, position, position + 1);\n\n      case 0x0026:\n        // &\n        return createToken(lexer, TokenKind.AMP, position, position + 1);\n\n      case 0x0028:\n        // (\n        return createToken(lexer, TokenKind.PAREN_L, position, position + 1);\n\n      case 0x0029:\n        // )\n        return createToken(lexer, TokenKind.PAREN_R, position, position + 1);\n\n      case 0x002e:\n        // .\n        if (\n          body.charCodeAt(position + 1) === 0x002e &&\n          body.charCodeAt(position + 2) === 0x002e\n        ) {\n          return createToken(lexer, TokenKind.SPREAD, position, position + 3);\n        }\n\n        break;\n\n      case 0x003a:\n        // :\n        return createToken(lexer, TokenKind.COLON, position, position + 1);\n\n      case 0x003d:\n        // =\n        return createToken(lexer, TokenKind.EQUALS, position, position + 1);\n\n      case 0x0040:\n        // @\n        return createToken(lexer, TokenKind.AT, position, position + 1);\n\n      case 0x005b:\n        // [\n        return createToken(lexer, TokenKind.BRACKET_L, position, position + 1);\n\n      case 0x005d:\n        // ]\n        return createToken(lexer, TokenKind.BRACKET_R, position, position + 1);\n\n      case 0x007b:\n        // {\n        return createToken(lexer, TokenKind.BRACE_L, position, position + 1);\n\n      case 0x007c:\n        // |\n        return createToken(lexer, TokenKind.PIPE, position, position + 1);\n\n      case 0x007d:\n        // }\n        return createToken(lexer, TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (\n          body.charCodeAt(position + 1) === 0x0022 &&\n          body.charCodeAt(position + 2) === 0x0022\n        ) {\n          return readBlockString(lexer, position);\n        }\n\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if (isDigit(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if (isNameStart(code)) {\n      return readName(lexer, position);\n    }\n\n    throw syntaxError(\n      lexer.source,\n      position,\n      code === 0x0027\n        ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?'\n        : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position)\n        ? `Unexpected character: ${printCodePointAt(lexer, position)}.`\n        : `Invalid character: ${printCodePointAt(lexer, position)}.`,\n    );\n  }\n\n  return createToken(lexer, TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    TokenKind.COMMENT,\n    start,\n    position,\n    body.slice(start + 1, position),\n  );\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n\n    if (isDigit(code)) {\n      throw syntaxError(\n        lexer.source,\n        position,\n        `Invalid number, unexpected digit after 0: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || isNameStart(code)) {\n    throw syntaxError(\n      lexer.source,\n      position,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        position,\n      )}.`,\n    );\n  }\n\n  return createToken(\n    lexer,\n    isFloat ? TokenKind.FLOAT : TokenKind.INT,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!isDigit(firstCode)) {\n    throw syntaxError(\n      lexer.source,\n      start,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        start,\n      )}.`,\n    );\n  }\n\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while (isDigit(body.charCodeAt(position))) {\n    ++position;\n  }\n\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape =\n        body.charCodeAt(position + 1) === 0x0075 // u\n          ? body.charCodeAt(position + 2) === 0x007b // {\n            ? readEscapedUnicodeVariableWidth(lexer, position)\n            : readEscapedUnicodeFixedWidth(lexer, position)\n          : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n\n      return {\n        value: String.fromCodePoint(point),\n        size,\n      };\n    } // Append this hex digit to the code point.\n\n    point = (point << 4) | readHexDigit(code);\n\n    if (point < 0) {\n      break;\n    }\n  }\n\n  throw syntaxError(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(\n      position,\n      position + size,\n    )}\".`,\n  );\n}\n\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6,\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (\n      body.charCodeAt(position + 6) === 0x005c &&\n      body.charCodeAt(position + 7) === 0x0075\n    ) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12,\n        };\n      }\n    }\n  }\n\n  throw syntaxError(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`,\n  );\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return (\n    (readHexDigit(body.charCodeAt(position)) << 12) |\n    (readHexDigit(body.charCodeAt(position + 1)) << 8) |\n    (readHexDigit(body.charCodeAt(position + 2)) << 4) |\n    readHexDigit(body.charCodeAt(position + 3))\n  );\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n    ? code - 0x0030\n    : code >= 0x0041 && code <= 0x0046 // A-F\n    ? code - 0x0037\n    : code >= 0x0061 && code <= 0x0066 // a-f\n    ? code - 0x0057\n    : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2,\n      };\n\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2,\n      };\n\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2,\n      };\n\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2,\n      };\n\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2,\n      };\n\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2,\n      };\n\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2,\n      };\n\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2,\n      };\n  }\n\n  throw syntaxError(\n    lexer.source,\n    position,\n    `Invalid character escape sequence: \"${body.slice(\n      position,\n      position + 2,\n    )}\".`,\n  );\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (\n      code === 0x0022 &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(\n        lexer,\n        TokenKind.BLOCK_STRING,\n        start,\n        position + 3, // Return a string of the lines joined with U+000A.\n        dedentBlockStringLines(blockLines).join('\\n'),\n      );\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (\n      code === 0x005c &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022 &&\n      body.charCodeAt(position + 3) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n\n    if (isNameContinue(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    TokenKind.NAME,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,KAAK,QAAQ,WAAW;AACjC,SAASC,sBAAsB,QAAQ,mBAAmB;AAC1D,SAASC,OAAO,EAAEC,cAAc,EAAEC,WAAW,QAAQ,wBAAwB;AAC7E,SAASC,SAAS,QAAQ,iBAAiB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,KAAK,CAAC;EACjB;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;;EAEE;AACF;AACA;EACEC,WAAWA,CAACC,MAAM,EAAE;IAClB,MAAMC,gBAAgB,GAAG,IAAIT,KAAK,CAACK,SAAS,CAACK,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,SAAS,GAAGF,gBAAgB;IACjC,IAAI,CAACG,KAAK,GAAGH,gBAAgB;IAC7B,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,SAAS,GAAG,CAAC;EACpB;EAEA,KAAKC,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,OAAO;EAChB;EACA;AACF;AACA;;EAEEC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACN,SAAS,GAAG,IAAI,CAACC,KAAK;IAC3B,MAAMA,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG,IAAI,CAACM,SAAS,CAAC,CAAE;IAC7C,OAAON,KAAK;EACd;EACA;AACF;AACA;AACA;;EAEEM,SAASA,CAAA,EAAG;IACV,IAAIN,KAAK,GAAG,IAAI,CAACA,KAAK;IAEtB,IAAIA,KAAK,CAACO,IAAI,KAAKd,SAAS,CAACe,GAAG,EAAE;MAChC,GAAG;QACD,IAAIR,KAAK,CAACS,IAAI,EAAE;UACdT,KAAK,GAAGA,KAAK,CAACS,IAAI;QACpB,CAAC,MAAM;UACL;UACA,MAAMC,SAAS,GAAGC,aAAa,CAAC,IAAI,EAAEX,KAAK,CAACY,GAAG,CAAC,CAAC,CAAC;;UAElDZ,KAAK,CAACS,IAAI,GAAGC,SAAS,CAAC,CAAC;;UAExBA,SAAS,CAACG,IAAI,GAAGb,KAAK;UACtBA,KAAK,GAAGU,SAAS;QACnB;MACF,CAAC,QAAQV,KAAK,CAACO,IAAI,KAAKd,SAAS,CAACqB,OAAO;IAC3C;IAEA,OAAOd,KAAK;EACd;AACF;AACA;AACA;AACA;;AAEA,OAAO,SAASe,qBAAqBA,CAACR,IAAI,EAAE;EAC1C,OACEA,IAAI,KAAKd,SAAS,CAACuB,IAAI,IACvBT,IAAI,KAAKd,SAAS,CAACwB,MAAM,IACzBV,IAAI,KAAKd,SAAS,CAACyB,GAAG,IACtBX,IAAI,KAAKd,SAAS,CAAC0B,OAAO,IAC1BZ,IAAI,KAAKd,SAAS,CAAC2B,OAAO,IAC1Bb,IAAI,KAAKd,SAAS,CAAC4B,MAAM,IACzBd,IAAI,KAAKd,SAAS,CAAC6B,KAAK,IACxBf,IAAI,KAAKd,SAAS,CAAC8B,MAAM,IACzBhB,IAAI,KAAKd,SAAS,CAAC+B,EAAE,IACrBjB,IAAI,KAAKd,SAAS,CAACgC,SAAS,IAC5BlB,IAAI,KAAKd,SAAS,CAACiC,SAAS,IAC5BnB,IAAI,KAAKd,SAAS,CAACkC,OAAO,IAC1BpB,IAAI,KAAKd,SAAS,CAACmC,IAAI,IACvBrB,IAAI,KAAKd,SAAS,CAACoC,OAAO;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,OACGA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAAMA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,QAAS;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,wBAAwBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAChD,OACEC,kBAAkB,CAACF,IAAI,CAACG,UAAU,CAACF,QAAQ,CAAC,CAAC,IAC7CG,mBAAmB,CAACJ,IAAI,CAACG,UAAU,CAACF,QAAQ,GAAG,CAAC,CAAC,CAAC;AAEtD;AAEA,SAASC,kBAAkBA,CAACJ,IAAI,EAAE;EAChC,OAAOA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AACzC;AAEA,SAASM,mBAAmBA,CAACN,IAAI,EAAE;EACjC,OAAOA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASO,gBAAgBA,CAACC,KAAK,EAAEL,QAAQ,EAAE;EACzC,MAAMH,IAAI,GAAGQ,KAAK,CAAC3C,MAAM,CAACqC,IAAI,CAACO,WAAW,CAACN,QAAQ,CAAC;EAEpD,IAAIH,IAAI,KAAKU,SAAS,EAAE;IACtB,OAAOhD,SAAS,CAACe,GAAG;EACtB,CAAC,MAAM,IAAIuB,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;IAC3C;IACA,MAAMW,IAAI,GAAGC,MAAM,CAACC,aAAa,CAACb,IAAI,CAAC;IACvC,OAAOW,IAAI,KAAK,GAAG,GAAG,MAAM,GAAI,IAAGA,IAAK,GAAE;EAC5C,CAAC,CAAC;;EAEF,OAAO,IAAI,GAAGX,IAAI,CAACc,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AAChE;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACT,KAAK,EAAEhC,IAAI,EAAE0C,KAAK,EAAErC,GAAG,EAAEsC,KAAK,EAAE;EACnD,MAAMjD,IAAI,GAAGsC,KAAK,CAACtC,IAAI;EACvB,MAAMkD,GAAG,GAAG,CAAC,GAAGF,KAAK,GAAGV,KAAK,CAACrC,SAAS;EACvC,OAAO,IAAId,KAAK,CAACmB,IAAI,EAAE0C,KAAK,EAAErC,GAAG,EAAEX,IAAI,EAAEkD,GAAG,EAAED,KAAK,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASvC,aAAaA,CAAC4B,KAAK,EAAEU,KAAK,EAAE;EACnC,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMmB,UAAU,GAAGnB,IAAI,CAACoB,MAAM;EAC9B,IAAIC,QAAQ,GAAGL,KAAK;EAEpB,OAAOK,QAAQ,GAAGF,UAAU,EAAE;IAC5B,MAAMrB,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,CAAC;;IAExC,QAAQvB,IAAI;MACV;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAK,MAAM,CAAC,CAAC;;MAEb,KAAK,MAAM,CAAC,CAAC;;MAEb,KAAK,MAAM,CAAC,CAAC;;MAEb,KAAK,MAAM;QACT;QACA,EAAEuB,QAAQ;QACV;MACF;MACA;MACA;MACA;;MAEA,KAAK,MAAM;QACT;QACA,EAAEA,QAAQ;QACV,EAAEf,KAAK,CAACtC,IAAI;QACZsC,KAAK,CAACrC,SAAS,GAAGoD,QAAQ;QAC1B;MAEF,KAAK,MAAM;QACT;QACA,IAAIrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;UAC5CA,QAAQ,IAAI,CAAC;QACf,CAAC,MAAM;UACL,EAAEA,QAAQ;QACZ;QAEA,EAAEf,KAAK,CAACtC,IAAI;QACZsC,KAAK,CAACrC,SAAS,GAAGoD,QAAQ;QAC1B;MACF;;MAEA,KAAK,MAAM;QACT;QACA,OAAOC,WAAW,CAAChB,KAAK,EAAEe,QAAQ,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACuB,IAAI,EAAEsC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEnE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACwB,MAAM,EAAEqC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAErE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACyB,GAAG,EAAEoC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAElE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC0B,OAAO,EAAEmC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEtE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC2B,OAAO,EAAEkC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEtE,KAAK,MAAM;QACT;QACA,IACErB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EACxC;UACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC4B,MAAM,EAAEiC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;QACrE;QAEA;MAEF,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC6B,KAAK,EAAEgC,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEpE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC8B,MAAM,EAAE+B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAErE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC+B,EAAE,EAAE8B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEjE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACgC,SAAS,EAAE6B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAExE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACiC,SAAS,EAAE4B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAExE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACkC,OAAO,EAAE2B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEtE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACmC,IAAI,EAAE0B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAEnE,KAAK,MAAM;QACT;QACA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACoC,OAAO,EAAEyB,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC;MACtE;;MAEA,KAAK,MAAM;QACT;QACA,IACErB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EACxC;UACA,OAAOE,eAAe,CAACjB,KAAK,EAAEe,QAAQ,CAAC;QACzC;QAEA,OAAOG,UAAU,CAAClB,KAAK,EAAEe,QAAQ,CAAC;IACtC,CAAC,CAAC;;IAEF,IAAIhE,OAAO,CAACyC,IAAI,CAAC,IAAIA,IAAI,KAAK,MAAM,EAAE;MACpC,OAAO2B,UAAU,CAACnB,KAAK,EAAEe,QAAQ,EAAEvB,IAAI,CAAC;IAC1C,CAAC,CAAC;;IAEF,IAAIvC,WAAW,CAACuC,IAAI,CAAC,EAAE;MACrB,OAAO4B,QAAQ,CAACpB,KAAK,EAAEe,QAAQ,CAAC;IAClC;IAEA,MAAMnE,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACRvB,IAAI,KAAK,MAAM,GACX,iFAAiF,GACjFD,oBAAoB,CAACC,IAAI,CAAC,IAAIC,wBAAwB,CAACC,IAAI,EAAEqB,QAAQ,CAAC,GACrE,yBAAwBhB,gBAAgB,CAACC,KAAK,EAAEe,QAAQ,CAAE,GAAE,GAC5D,sBAAqBhB,gBAAgB,CAACC,KAAK,EAAEe,QAAQ,CAAE,GAC9D,CAAC;EACH;EAEA,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAACe,GAAG,EAAE4C,UAAU,EAAEA,UAAU,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,WAAWA,CAAChB,KAAK,EAAEU,KAAK,EAAE;EACjC,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMmB,UAAU,GAAGnB,IAAI,CAACoB,MAAM;EAC9B,IAAIC,QAAQ,GAAGL,KAAK,GAAG,CAAC;EAExB,OAAOK,QAAQ,GAAGF,UAAU,EAAE;IAC5B,MAAMrB,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,CAAC;;IAExC,IAAIvB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;MACtC;IACF,CAAC,CAAC;;IAEF,IAAID,oBAAoB,CAACC,IAAI,CAAC,EAAE;MAC9B,EAAEuB,QAAQ;IACZ,CAAC,MAAM,IAAItB,wBAAwB,CAACC,IAAI,EAAEqB,QAAQ,CAAC,EAAE;MACnDA,QAAQ,IAAI,CAAC;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEA,OAAON,WAAW,CAChBT,KAAK,EACL9C,SAAS,CAACqB,OAAO,EACjBmC,KAAK,EACLK,QAAQ,EACRrB,IAAI,CAAC2B,KAAK,CAACX,KAAK,GAAG,CAAC,EAAEK,QAAQ,CAChC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,UAAUA,CAACnB,KAAK,EAAEU,KAAK,EAAEY,SAAS,EAAE;EAC3C,MAAM5B,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,IAAIqB,QAAQ,GAAGL,KAAK;EACpB,IAAIlB,IAAI,GAAG8B,SAAS;EACpB,IAAIC,OAAO,GAAG,KAAK,CAAC,CAAC;;EAErB,IAAI/B,IAAI,KAAK,MAAM,EAAE;IACnBA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAAC,EAAEkB,QAAQ,CAAC;EACpC,CAAC,CAAC;;EAEF,IAAIvB,IAAI,KAAK,MAAM,EAAE;IACnBA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAAC,EAAEkB,QAAQ,CAAC;IAElC,IAAIhE,OAAO,CAACyC,IAAI,CAAC,EAAE;MACjB,MAAM5C,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,6CAA4ChB,gBAAgB,CAC3DC,KAAK,EACLe,QACF,CAAE,GACJ,CAAC;IACH;EACF,CAAC,MAAM;IACLA,QAAQ,GAAGS,UAAU,CAACxB,KAAK,EAAEe,QAAQ,EAAEvB,IAAI,CAAC;IAC5CA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC;EAClC,CAAC,CAAC;;EAEF,IAAIvB,IAAI,KAAK,MAAM,EAAE;IACnB+B,OAAO,GAAG,IAAI;IACd/B,IAAI,GAAGE,IAAI,CAACG,UAAU,CAAC,EAAEkB,QAAQ,CAAC;IAClCA,QAAQ,GAAGS,UAAU,CAACxB,KAAK,EAAEe,QAAQ,EAAEvB,IAAI,CAAC;IAC5CA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC;EAClC,CAAC,CAAC;;EAEF,IAAIvB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;IACtC+B,OAAO,GAAG,IAAI;IACd/B,IAAI,GAAGE,IAAI,CAACG,UAAU,CAAC,EAAEkB,QAAQ,CAAC,CAAC,CAAC;;IAEpC,IAAIvB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;MACtCA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAAC,EAAEkB,QAAQ,CAAC;IACpC;IAEAA,QAAQ,GAAGS,UAAU,CAACxB,KAAK,EAAEe,QAAQ,EAAEvB,IAAI,CAAC;IAC5CA,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC;EAClC,CAAC,CAAC;;EAEF,IAAIvB,IAAI,KAAK,MAAM,IAAIvC,WAAW,CAACuC,IAAI,CAAC,EAAE;IACxC,MAAM5C,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,2CAA0ChB,gBAAgB,CACzDC,KAAK,EACLe,QACF,CAAE,GACJ,CAAC;EACH;EAEA,OAAON,WAAW,CAChBT,KAAK,EACLuB,OAAO,GAAGrE,SAAS,CAACuE,KAAK,GAAGvE,SAAS,CAACwE,GAAG,EACzChB,KAAK,EACLK,QAAQ,EACRrB,IAAI,CAAC2B,KAAK,CAACX,KAAK,EAAEK,QAAQ,CAC5B,CAAC;AACH;AACA;AACA;AACA;;AAEA,SAASS,UAAUA,CAACxB,KAAK,EAAEU,KAAK,EAAEY,SAAS,EAAE;EAC3C,IAAI,CAACvE,OAAO,CAACuE,SAAS,CAAC,EAAE;IACvB,MAAM1E,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZqD,KAAK,EACJ,2CAA0CX,gBAAgB,CACzDC,KAAK,EACLU,KACF,CAAE,GACJ,CAAC;EACH;EAEA,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,IAAIqB,QAAQ,GAAGL,KAAK,GAAG,CAAC,CAAC,CAAC;;EAE1B,OAAO3D,OAAO,CAAC2C,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,EAAE;IACzC,EAAEA,QAAQ;EACZ;EAEA,OAAOA,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,UAAUA,CAAClB,KAAK,EAAEU,KAAK,EAAE;EAChC,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMmB,UAAU,GAAGnB,IAAI,CAACoB,MAAM;EAC9B,IAAIC,QAAQ,GAAGL,KAAK,GAAG,CAAC;EACxB,IAAIiB,UAAU,GAAGZ,QAAQ;EACzB,IAAIJ,KAAK,GAAG,EAAE;EAEd,OAAOI,QAAQ,GAAGF,UAAU,EAAE;IAC5B,MAAMrB,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,CAAC;;IAExC,IAAIvB,IAAI,KAAK,MAAM,EAAE;MACnBmB,KAAK,IAAIjB,IAAI,CAAC2B,KAAK,CAACM,UAAU,EAAEZ,QAAQ,CAAC;MACzC,OAAON,WAAW,CAACT,KAAK,EAAE9C,SAAS,CAAC0E,MAAM,EAAElB,KAAK,EAAEK,QAAQ,GAAG,CAAC,EAAEJ,KAAK,CAAC;IACzE,CAAC,CAAC;;IAEF,IAAInB,IAAI,KAAK,MAAM,EAAE;MACnBmB,KAAK,IAAIjB,IAAI,CAAC2B,KAAK,CAACM,UAAU,EAAEZ,QAAQ,CAAC;MACzC,MAAMc,MAAM,GACVnC,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC;MAAA,EACrCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC;MAAA,EACvCe,+BAA+B,CAAC9B,KAAK,EAAEe,QAAQ,CAAC,GAChDgB,4BAA4B,CAAC/B,KAAK,EAAEe,QAAQ,CAAC,GAC/CiB,oBAAoB,CAAChC,KAAK,EAAEe,QAAQ,CAAC;MAC3CJ,KAAK,IAAIkB,MAAM,CAAClB,KAAK;MACrBI,QAAQ,IAAIc,MAAM,CAACI,IAAI;MACvBN,UAAU,GAAGZ,QAAQ;MACrB;IACF,CAAC,CAAC;;IAEF,IAAIvB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;MACtC;IACF,CAAC,CAAC;;IAEF,IAAID,oBAAoB,CAACC,IAAI,CAAC,EAAE;MAC9B,EAAEuB,QAAQ;IACZ,CAAC,MAAM,IAAItB,wBAAwB,CAACC,IAAI,EAAEqB,QAAQ,CAAC,EAAE;MACnDA,QAAQ,IAAI,CAAC;IACf,CAAC,MAAM;MACL,MAAMnE,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,oCAAmChB,gBAAgB,CAClDC,KAAK,EACLe,QACF,CAAE,GACJ,CAAC;IACH;EACF;EAEA,MAAMnE,WAAW,CAACoD,KAAK,CAAC3C,MAAM,EAAE0D,QAAQ,EAAE,sBAAsB,CAAC;AACnE,CAAC,CAAC;;AAEF,SAASe,+BAA+BA,CAAC9B,KAAK,EAAEe,QAAQ,EAAE;EACxD,MAAMrB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,IAAIwC,KAAK,GAAG,CAAC;EACb,IAAID,IAAI,GAAG,CAAC,CAAC,CAAC;;EAEd,OAAOA,IAAI,GAAG,EAAE,EAAE;IAChB,MAAMzC,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAGkB,IAAI,EAAE,CAAC,CAAC,CAAC;;IAEjD,IAAIzC,IAAI,KAAK,MAAM,EAAE;MACnB;MACA,IAAIyC,IAAI,GAAG,CAAC,IAAI,CAAC1C,oBAAoB,CAAC2C,KAAK,CAAC,EAAE;QAC5C;MACF;MAEA,OAAO;QACLvB,KAAK,EAAEP,MAAM,CAACC,aAAa,CAAC6B,KAAK,CAAC;QAClCD;MACF,CAAC;IACH,CAAC,CAAC;;IAEFC,KAAK,GAAIA,KAAK,IAAI,CAAC,GAAIC,YAAY,CAAC3C,IAAI,CAAC;IAEzC,IAAI0C,KAAK,GAAG,CAAC,EAAE;MACb;IACF;EACF;EAEA,MAAMtF,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,qCAAoCrB,IAAI,CAAC2B,KAAK,CAC7CN,QAAQ,EACRA,QAAQ,GAAGkB,IACb,CAAE,IACJ,CAAC;AACH;AAEA,SAASF,4BAA4BA,CAAC/B,KAAK,EAAEe,QAAQ,EAAE;EACrD,MAAMrB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMF,IAAI,GAAG4C,gBAAgB,CAAC1C,IAAI,EAAEqB,QAAQ,GAAG,CAAC,CAAC;EAEjD,IAAIxB,oBAAoB,CAACC,IAAI,CAAC,EAAE;IAC9B,OAAO;MACLmB,KAAK,EAAEP,MAAM,CAACC,aAAa,CAACb,IAAI,CAAC;MACjCyC,IAAI,EAAE;IACR,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,IAAIrC,kBAAkB,CAACJ,IAAI,CAAC,EAAE;IAC5B;IACA,IACEE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EACxC;MACA,MAAMsB,YAAY,GAAGD,gBAAgB,CAAC1C,IAAI,EAAEqB,QAAQ,GAAG,CAAC,CAAC;MAEzD,IAAIjB,mBAAmB,CAACuC,YAAY,CAAC,EAAE;QACrC;QACA;QACA;QACA;QACA;QACA;QACA,OAAO;UACL1B,KAAK,EAAEP,MAAM,CAACC,aAAa,CAACb,IAAI,EAAE6C,YAAY,CAAC;UAC/CJ,IAAI,EAAE;QACR,CAAC;MACH;IACF;EACF;EAEA,MAAMrF,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,qCAAoCrB,IAAI,CAAC2B,KAAK,CAACN,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAE,IAC1E,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASqB,gBAAgBA,CAAC1C,IAAI,EAAEqB,QAAQ,EAAE;EACxC;EACA;EACA,OACGoB,YAAY,CAACzC,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,IAAI,EAAE,GAC7CoB,YAAY,CAACzC,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE,GACjDoB,YAAY,CAACzC,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAE,GAClDoB,YAAY,CAACzC,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,CAAC;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASoB,YAAYA,CAAC3C,IAAI,EAAE;EAC1B,OAAOA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,CAAC;EAAA,EACpCA,IAAI,GAAG,MAAM,GACbA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,CAAC;EAAA,EACjCA,IAAI,GAAG,MAAM,GACbA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,CAAC;EAAA,EACjCA,IAAI,GAAG,MAAM,GACb,CAAC,CAAC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASwC,oBAAoBA,CAAChC,KAAK,EAAEe,QAAQ,EAAE;EAC7C,MAAMrB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMF,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC;EAE1C,QAAQvB,IAAI;IACV,KAAK,MAAM;MACT;MACA,OAAO;QACLmB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;IAEH,KAAK,MAAM;MACT;MACA,OAAO;QACLtB,KAAK,EAAE,QAAQ;QACfsB,IAAI,EAAE;MACR,CAAC;EACL;EAEA,MAAMrF,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,uCAAsCrB,IAAI,CAAC2B,KAAK,CAC/CN,QAAQ,EACRA,QAAQ,GAAG,CACb,CAAE,IACJ,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,eAAeA,CAACjB,KAAK,EAAEU,KAAK,EAAE;EACrC,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMmB,UAAU,GAAGnB,IAAI,CAACoB,MAAM;EAC9B,IAAInD,SAAS,GAAGqC,KAAK,CAACrC,SAAS;EAC/B,IAAIoD,QAAQ,GAAGL,KAAK,GAAG,CAAC;EACxB,IAAIiB,UAAU,GAAGZ,QAAQ;EACzB,IAAIuB,WAAW,GAAG,EAAE;EACpB,MAAMC,UAAU,GAAG,EAAE;EAErB,OAAOxB,QAAQ,GAAGF,UAAU,EAAE;IAC5B,MAAMrB,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC,CAAC,CAAC;;IAExC,IACEvB,IAAI,KAAK,MAAM,IACfE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EACxC;MACAuB,WAAW,IAAI5C,IAAI,CAAC2B,KAAK,CAACM,UAAU,EAAEZ,QAAQ,CAAC;MAC/CwB,UAAU,CAACC,IAAI,CAACF,WAAW,CAAC;MAC5B,MAAM7E,KAAK,GAAGgD,WAAW,CACvBT,KAAK,EACL9C,SAAS,CAACuF,YAAY,EACtB/B,KAAK,EACLK,QAAQ,GAAG,CAAC;MAAE;MACdjE,sBAAsB,CAACyF,UAAU,CAAC,CAACG,IAAI,CAAC,IAAI,CAC9C,CAAC;MACD1C,KAAK,CAACtC,IAAI,IAAI6E,UAAU,CAACzB,MAAM,GAAG,CAAC;MACnCd,KAAK,CAACrC,SAAS,GAAGA,SAAS;MAC3B,OAAOF,KAAK;IACd,CAAC,CAAC;;IAEF,IACE+B,IAAI,KAAK,MAAM,IACfE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,IACxCrB,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EACxC;MACAuB,WAAW,IAAI5C,IAAI,CAAC2B,KAAK,CAACM,UAAU,EAAEZ,QAAQ,CAAC;MAC/CY,UAAU,GAAGZ,QAAQ,GAAG,CAAC,CAAC,CAAC;;MAE3BA,QAAQ,IAAI,CAAC;MACb;IACF,CAAC,CAAC;;IAEF,IAAIvB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;MACtC8C,WAAW,IAAI5C,IAAI,CAAC2B,KAAK,CAACM,UAAU,EAAEZ,QAAQ,CAAC;MAC/CwB,UAAU,CAACC,IAAI,CAACF,WAAW,CAAC;MAE5B,IAAI9C,IAAI,KAAK,MAAM,IAAIE,IAAI,CAACG,UAAU,CAACkB,QAAQ,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;QAC/DA,QAAQ,IAAI,CAAC;MACf,CAAC,MAAM;QACL,EAAEA,QAAQ;MACZ;MAEAuB,WAAW,GAAG,EAAE;MAChBX,UAAU,GAAGZ,QAAQ;MACrBpD,SAAS,GAAGoD,QAAQ;MACpB;IACF,CAAC,CAAC;;IAEF,IAAIxB,oBAAoB,CAACC,IAAI,CAAC,EAAE;MAC9B,EAAEuB,QAAQ;IACZ,CAAC,MAAM,IAAItB,wBAAwB,CAACC,IAAI,EAAEqB,QAAQ,CAAC,EAAE;MACnDA,QAAQ,IAAI,CAAC;IACf,CAAC,MAAM;MACL,MAAMnE,WAAW,CACfoD,KAAK,CAAC3C,MAAM,EACZ0D,QAAQ,EACP,oCAAmChB,gBAAgB,CAClDC,KAAK,EACLe,QACF,CAAE,GACJ,CAAC;IACH;EACF;EAEA,MAAMnE,WAAW,CAACoD,KAAK,CAAC3C,MAAM,EAAE0D,QAAQ,EAAE,sBAAsB,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,QAAQA,CAACpB,KAAK,EAAEU,KAAK,EAAE;EAC9B,MAAMhB,IAAI,GAAGM,KAAK,CAAC3C,MAAM,CAACqC,IAAI;EAC9B,MAAMmB,UAAU,GAAGnB,IAAI,CAACoB,MAAM;EAC9B,IAAIC,QAAQ,GAAGL,KAAK,GAAG,CAAC;EAExB,OAAOK,QAAQ,GAAGF,UAAU,EAAE;IAC5B,MAAMrB,IAAI,GAAGE,IAAI,CAACG,UAAU,CAACkB,QAAQ,CAAC;IAEtC,IAAI/D,cAAc,CAACwC,IAAI,CAAC,EAAE;MACxB,EAAEuB,QAAQ;IACZ,CAAC,MAAM;MACL;IACF;EACF;EAEA,OAAON,WAAW,CAChBT,KAAK,EACL9C,SAAS,CAACyF,IAAI,EACdjC,KAAK,EACLK,QAAQ,EACRrB,IAAI,CAAC2B,KAAK,CAACX,KAAK,EAAEK,QAAQ,CAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}