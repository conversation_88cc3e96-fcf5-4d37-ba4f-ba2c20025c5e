{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isEnumType } from '../../type/definition.mjs';\n\n/**\n * Unique enum value names\n *\n * A GraphQL enum type is only valid if all its values are uniquely named.\n */\nexport function UniqueEnumValueNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownValueNames = Object.create(null);\n  return {\n    EnumTypeDefinition: checkValueUniqueness,\n    EnumTypeExtension: checkValueUniqueness\n  };\n  function checkValueUniqueness(node) {\n    var _node$values;\n    const typeName = node.name.value;\n    if (!knownValueNames[typeName]) {\n      knownValueNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const valueNodes = (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n    const valueNames = knownValueNames[typeName];\n    for (const valueDef of valueNodes) {\n      const valueName = valueDef.name.value;\n      const existingType = existingTypeMap[typeName];\n      if (isEnumType(existingType) && existingType.getValue(valueName)) {\n        context.reportError(new GraphQLError(`Enum value \"${typeName}.${valueName}\" already exists in the schema. It cannot also be defined in this type extension.`, {\n          nodes: valueDef.name\n        }));\n      } else if (valueNames[valueName]) {\n        context.reportError(new GraphQLError(`Enum value \"${typeName}.${valueName}\" can only be defined once.`, {\n          nodes: [valueNames[valueName], valueDef.name]\n        }));\n      } else {\n        valueNames[valueName] = valueDef.name;\n      }\n    }\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}