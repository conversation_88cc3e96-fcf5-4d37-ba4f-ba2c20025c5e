{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { didYouMean } from '../jsutils/didYouMean.mjs';\nimport { identityFunc } from '../jsutils/identityFunc.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { mapValue } from '../jsutils/mapValue.mjs';\nimport { suggestionList } from '../jsutils/suggestionList.mjs';\nimport { toObjMap } from '../jsutils/toObjMap.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { print } from '../language/printer.mjs';\nimport { valueFromASTUntyped } from '../utilities/valueFromASTUntyped.mjs';\nimport { assertEnumValueName, assertName } from './assertName.mjs';\nexport function isType(type) {\n  return isScalarType(type) || isObjectType(type) || isInterfaceType(type) || isUnionType(type) || isEnumType(type) || isInputObjectType(type) || isListType(type) || isNonNullType(type);\n}\nexport function assertType(type) {\n  if (!isType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL type.`);\n  }\n  return type;\n}\n/**\n * There are predicates for each kind of GraphQL type.\n */\n\nexport function isScalarType(type) {\n  return instanceOf(type, GraphQLScalarType);\n}\nexport function assertScalarType(type) {\n  if (!isScalarType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Scalar type.`);\n  }\n  return type;\n}\nexport function isObjectType(type) {\n  return instanceOf(type, GraphQLObjectType);\n}\nexport function assertObjectType(type) {\n  if (!isObjectType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Object type.`);\n  }\n  return type;\n}\nexport function isInterfaceType(type) {\n  return instanceOf(type, GraphQLInterfaceType);\n}\nexport function assertInterfaceType(type) {\n  if (!isInterfaceType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Interface type.`);\n  }\n  return type;\n}\nexport function isUnionType(type) {\n  return instanceOf(type, GraphQLUnionType);\n}\nexport function assertUnionType(type) {\n  if (!isUnionType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Union type.`);\n  }\n  return type;\n}\nexport function isEnumType(type) {\n  return instanceOf(type, GraphQLEnumType);\n}\nexport function assertEnumType(type) {\n  if (!isEnumType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Enum type.`);\n  }\n  return type;\n}\nexport function isInputObjectType(type) {\n  return instanceOf(type, GraphQLInputObjectType);\n}\nexport function assertInputObjectType(type) {\n  if (!isInputObjectType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Input Object type.`);\n  }\n  return type;\n}\nexport function isListType(type) {\n  return instanceOf(type, GraphQLList);\n}\nexport function assertListType(type) {\n  if (!isListType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL List type.`);\n  }\n  return type;\n}\nexport function isNonNullType(type) {\n  return instanceOf(type, GraphQLNonNull);\n}\nexport function assertNonNullType(type) {\n  if (!isNonNullType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL Non-Null type.`);\n  }\n  return type;\n}\n/**\n * These types may be used as input types for arguments and directives.\n */\n\nexport function isInputType(type) {\n  return isScalarType(type) || isEnumType(type) || isInputObjectType(type) || isWrappingType(type) && isInputType(type.ofType);\n}\nexport function assertInputType(type) {\n  if (!isInputType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL input type.`);\n  }\n  return type;\n}\n/**\n * These types may be used as output types as the result of fields.\n */\n\nexport function isOutputType(type) {\n  return isScalarType(type) || isObjectType(type) || isInterfaceType(type) || isUnionType(type) || isEnumType(type) || isWrappingType(type) && isOutputType(type.ofType);\n}\nexport function assertOutputType(type) {\n  if (!isOutputType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL output type.`);\n  }\n  return type;\n}\n/**\n * These types may describe types which may be leaf values.\n */\n\nexport function isLeafType(type) {\n  return isScalarType(type) || isEnumType(type);\n}\nexport function assertLeafType(type) {\n  if (!isLeafType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL leaf type.`);\n  }\n  return type;\n}\n/**\n * These types may describe the parent context of a selection set.\n */\n\nexport function isCompositeType(type) {\n  return isObjectType(type) || isInterfaceType(type) || isUnionType(type);\n}\nexport function assertCompositeType(type) {\n  if (!isCompositeType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL composite type.`);\n  }\n  return type;\n}\n/**\n * These types may describe the parent context of a selection set.\n */\n\nexport function isAbstractType(type) {\n  return isInterfaceType(type) || isUnionType(type);\n}\nexport function assertAbstractType(type) {\n  if (!isAbstractType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL abstract type.`);\n  }\n  return type;\n}\n/**\n * List Type Wrapper\n *\n * A list is a wrapping type which points to another type.\n * Lists are often created within the context of defining the fields of\n * an object type.\n *\n * Example:\n *\n * ```ts\n * const PersonType = new GraphQLObjectType({\n *   name: 'Person',\n *   fields: () => ({\n *     parents: { type: new GraphQLList(PersonType) },\n *     children: { type: new GraphQLList(PersonType) },\n *   })\n * })\n * ```\n */\n\nexport class GraphQLList {\n  constructor(ofType) {\n    isType(ofType) || devAssert(false, `Expected ${inspect(ofType)} to be a GraphQL type.`);\n    this.ofType = ofType;\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLList';\n  }\n  toString() {\n    return '[' + String(this.ofType) + ']';\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n/**\n * Non-Null Type Wrapper\n *\n * A non-null is a wrapping type which points to another type.\n * Non-null types enforce that their values are never null and can ensure\n * an error is raised if this ever occurs during a request. It is useful for\n * fields which you can make a strong guarantee on non-nullability, for example\n * usually the id field of a database row will never be null.\n *\n * Example:\n *\n * ```ts\n * const RowType = new GraphQLObjectType({\n *   name: 'Row',\n *   fields: () => ({\n *     id: { type: new GraphQLNonNull(GraphQLString) },\n *   })\n * })\n * ```\n * Note: the enforcement of non-nullability occurs within the executor.\n */\n\nexport class GraphQLNonNull {\n  constructor(ofType) {\n    isNullableType(ofType) || devAssert(false, `Expected ${inspect(ofType)} to be a GraphQL nullable type.`);\n    this.ofType = ofType;\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLNonNull';\n  }\n  toString() {\n    return String(this.ofType) + '!';\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n/**\n * These types wrap and modify other types\n */\n\nexport function isWrappingType(type) {\n  return isListType(type) || isNonNullType(type);\n}\nexport function assertWrappingType(type) {\n  if (!isWrappingType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL wrapping type.`);\n  }\n  return type;\n}\n/**\n * These types can all accept null as a value.\n */\n\nexport function isNullableType(type) {\n  return isType(type) && !isNonNullType(type);\n}\nexport function assertNullableType(type) {\n  if (!isNullableType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL nullable type.`);\n  }\n  return type;\n}\nexport function getNullableType(type) {\n  if (type) {\n    return isNonNullType(type) ? type.ofType : type;\n  }\n}\n/**\n * These named types do not include modifiers like List or NonNull.\n */\n\nexport function isNamedType(type) {\n  return isScalarType(type) || isObjectType(type) || isInterfaceType(type) || isUnionType(type) || isEnumType(type) || isInputObjectType(type);\n}\nexport function assertNamedType(type) {\n  if (!isNamedType(type)) {\n    throw new Error(`Expected ${inspect(type)} to be a GraphQL named type.`);\n  }\n  return type;\n}\nexport function getNamedType(type) {\n  if (type) {\n    let unwrappedType = type;\n    while (isWrappingType(unwrappedType)) {\n      unwrappedType = unwrappedType.ofType;\n    }\n    return unwrappedType;\n  }\n}\n/**\n * Used while defining GraphQL types to allow for circular references in\n * otherwise immutable type definitions.\n */\n\nexport function resolveReadonlyArrayThunk(thunk) {\n  return typeof thunk === 'function' ? thunk() : thunk;\n}\nexport function resolveObjMapThunk(thunk) {\n  return typeof thunk === 'function' ? thunk() : thunk;\n}\n/**\n * Custom extensions\n *\n * @remarks\n * Use a unique identifier name for your extension, for example the name of\n * your library or project. Do not use a shortened identifier as this increases\n * the risk of conflicts. We recommend you add at most one extension field,\n * an object which can contain all the values you need.\n */\n\n/**\n * Scalar Type Definition\n *\n * The leaf values of any request and input values to arguments are\n * Scalars (or Enums) and are defined with a name and a series of functions\n * used to parse input from ast or variables and to ensure validity.\n *\n * If a type's serialize function returns `null` or does not return a value\n * (i.e. it returns `undefined`) then an error will be raised and a `null`\n * value will be returned in the response. It is always better to validate\n *\n * Example:\n *\n * ```ts\n * const OddType = new GraphQLScalarType({\n *   name: 'Odd',\n *   serialize(value) {\n *     if (!Number.isFinite(value)) {\n *       throw new Error(\n *         `Scalar \"Odd\" cannot represent \"${value}\" since it is not a finite number.`,\n *       );\n *     }\n *\n *     if (value % 2 === 0) {\n *       throw new Error(`Scalar \"Odd\" cannot represent \"${value}\" since it is even.`);\n *     }\n *     return value;\n *   }\n * });\n * ```\n */\nexport class GraphQLScalarType {\n  constructor(config) {\n    var _config$parseValue, _config$serialize, _config$parseLiteral, _config$extensionASTN;\n    const parseValue = (_config$parseValue = config.parseValue) !== null && _config$parseValue !== void 0 ? _config$parseValue : identityFunc;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.specifiedByURL = config.specifiedByURL;\n    this.serialize = (_config$serialize = config.serialize) !== null && _config$serialize !== void 0 ? _config$serialize : identityFunc;\n    this.parseValue = parseValue;\n    this.parseLiteral = (_config$parseLiteral = config.parseLiteral) !== null && _config$parseLiteral !== void 0 ? _config$parseLiteral : (node, variables) => parseValue(valueFromASTUntyped(node, variables));\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN = config.extensionASTNodes) !== null && _config$extensionASTN !== void 0 ? _config$extensionASTN : [];\n    config.specifiedByURL == null || typeof config.specifiedByURL === 'string' || devAssert(false, `${this.name} must provide \"specifiedByURL\" as a string, ` + `but got: ${inspect(config.specifiedByURL)}.`);\n    config.serialize == null || typeof config.serialize === 'function' || devAssert(false, `${this.name} must provide \"serialize\" function. If this custom Scalar is also used as an input type, ensure \"parseValue\" and \"parseLiteral\" functions are also provided.`);\n    if (config.parseLiteral) {\n      typeof config.parseValue === 'function' && typeof config.parseLiteral === 'function' || devAssert(false, `${this.name} must provide both \"parseValue\" and \"parseLiteral\" functions.`);\n    }\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLScalarType';\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      specifiedByURL: this.specifiedByURL,\n      serialize: this.serialize,\n      parseValue: this.parseValue,\n      parseLiteral: this.parseLiteral,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n\n/**\n * Object Type Definition\n *\n * Almost all of the GraphQL types you define will be object types. Object types\n * have a name, but most importantly describe their fields.\n *\n * Example:\n *\n * ```ts\n * const AddressType = new GraphQLObjectType({\n *   name: 'Address',\n *   fields: {\n *     street: { type: GraphQLString },\n *     number: { type: GraphQLInt },\n *     formatted: {\n *       type: GraphQLString,\n *       resolve(obj) {\n *         return obj.number + ' ' + obj.street\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * When two types need to refer to each other, or a type needs to refer to\n * itself in a field, you can use a function expression (aka a closure or a\n * thunk) to supply the fields lazily.\n *\n * Example:\n *\n * ```ts\n * const PersonType = new GraphQLObjectType({\n *   name: 'Person',\n *   fields: () => ({\n *     name: { type: GraphQLString },\n *     bestFriend: { type: PersonType },\n *   })\n * });\n * ```\n */\nexport class GraphQLObjectType {\n  constructor(config) {\n    var _config$extensionASTN2;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.isTypeOf = config.isTypeOf;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN2 = config.extensionASTNodes) !== null && _config$extensionASTN2 !== void 0 ? _config$extensionASTN2 : [];\n    this._fields = () => defineFieldMap(config);\n    this._interfaces = () => defineInterfaces(config);\n    config.isTypeOf == null || typeof config.isTypeOf === 'function' || devAssert(false, `${this.name} must provide \"isTypeOf\" as a function, ` + `but got: ${inspect(config.isTypeOf)}.`);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLObjectType';\n  }\n  getFields() {\n    if (typeof this._fields === 'function') {\n      this._fields = this._fields();\n    }\n    return this._fields;\n  }\n  getInterfaces() {\n    if (typeof this._interfaces === 'function') {\n      this._interfaces = this._interfaces();\n    }\n    return this._interfaces;\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      interfaces: this.getInterfaces(),\n      fields: fieldsToFieldsConfig(this.getFields()),\n      isTypeOf: this.isTypeOf,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\nfunction defineInterfaces(config) {\n  var _config$interfaces;\n  const interfaces = resolveReadonlyArrayThunk((_config$interfaces = config.interfaces) !== null && _config$interfaces !== void 0 ? _config$interfaces : []);\n  Array.isArray(interfaces) || devAssert(false, `${config.name} interfaces must be an Array or a function which returns an Array.`);\n  return interfaces;\n}\nfunction defineFieldMap(config) {\n  const fieldMap = resolveObjMapThunk(config.fields);\n  isPlainObj(fieldMap) || devAssert(false, `${config.name} fields must be an object with field names as keys or a function which returns such an object.`);\n  return mapValue(fieldMap, (fieldConfig, fieldName) => {\n    var _fieldConfig$args;\n    isPlainObj(fieldConfig) || devAssert(false, `${config.name}.${fieldName} field config must be an object.`);\n    fieldConfig.resolve == null || typeof fieldConfig.resolve === 'function' || devAssert(false, `${config.name}.${fieldName} field resolver must be a function if ` + `provided, but got: ${inspect(fieldConfig.resolve)}.`);\n    const argsConfig = (_fieldConfig$args = fieldConfig.args) !== null && _fieldConfig$args !== void 0 ? _fieldConfig$args : {};\n    isPlainObj(argsConfig) || devAssert(false, `${config.name}.${fieldName} args must be an object with argument names as keys.`);\n    return {\n      name: assertName(fieldName),\n      description: fieldConfig.description,\n      type: fieldConfig.type,\n      args: defineArguments(argsConfig),\n      resolve: fieldConfig.resolve,\n      subscribe: fieldConfig.subscribe,\n      deprecationReason: fieldConfig.deprecationReason,\n      extensions: toObjMap(fieldConfig.extensions),\n      astNode: fieldConfig.astNode\n    };\n  });\n}\nexport function defineArguments(config) {\n  return Object.entries(config).map(([argName, argConfig]) => ({\n    name: assertName(argName),\n    description: argConfig.description,\n    type: argConfig.type,\n    defaultValue: argConfig.defaultValue,\n    deprecationReason: argConfig.deprecationReason,\n    extensions: toObjMap(argConfig.extensions),\n    astNode: argConfig.astNode\n  }));\n}\nfunction isPlainObj(obj) {\n  return isObjectLike(obj) && !Array.isArray(obj);\n}\nfunction fieldsToFieldsConfig(fields) {\n  return mapValue(fields, field => ({\n    description: field.description,\n    type: field.type,\n    args: argsToArgsConfig(field.args),\n    resolve: field.resolve,\n    subscribe: field.subscribe,\n    deprecationReason: field.deprecationReason,\n    extensions: field.extensions,\n    astNode: field.astNode\n  }));\n}\n/**\n * @internal\n */\n\nexport function argsToArgsConfig(args) {\n  return keyValMap(args, arg => arg.name, arg => ({\n    description: arg.description,\n    type: arg.type,\n    defaultValue: arg.defaultValue,\n    deprecationReason: arg.deprecationReason,\n    extensions: arg.extensions,\n    astNode: arg.astNode\n  }));\n}\nexport function isRequiredArgument(arg) {\n  return isNonNullType(arg.type) && arg.defaultValue === undefined;\n}\n\n/**\n * Interface Type Definition\n *\n * When a field can return one of a heterogeneous set of types, a Interface type\n * is used to describe what types are possible, what fields are in common across\n * all types, as well as a function to determine which type is actually used\n * when the field is resolved.\n *\n * Example:\n *\n * ```ts\n * const EntityType = new GraphQLInterfaceType({\n *   name: 'Entity',\n *   fields: {\n *     name: { type: GraphQLString }\n *   }\n * });\n * ```\n */\nexport class GraphQLInterfaceType {\n  constructor(config) {\n    var _config$extensionASTN3;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.resolveType = config.resolveType;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN3 = config.extensionASTNodes) !== null && _config$extensionASTN3 !== void 0 ? _config$extensionASTN3 : [];\n    this._fields = defineFieldMap.bind(undefined, config);\n    this._interfaces = defineInterfaces.bind(undefined, config);\n    config.resolveType == null || typeof config.resolveType === 'function' || devAssert(false, `${this.name} must provide \"resolveType\" as a function, ` + `but got: ${inspect(config.resolveType)}.`);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLInterfaceType';\n  }\n  getFields() {\n    if (typeof this._fields === 'function') {\n      this._fields = this._fields();\n    }\n    return this._fields;\n  }\n  getInterfaces() {\n    if (typeof this._interfaces === 'function') {\n      this._interfaces = this._interfaces();\n    }\n    return this._interfaces;\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      interfaces: this.getInterfaces(),\n      fields: fieldsToFieldsConfig(this.getFields()),\n      resolveType: this.resolveType,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\n\n/**\n * Union Type Definition\n *\n * When a field can return one of a heterogeneous set of types, a Union type\n * is used to describe what types are possible as well as providing a function\n * to determine which type is actually used when the field is resolved.\n *\n * Example:\n *\n * ```ts\n * const PetType = new GraphQLUnionType({\n *   name: 'Pet',\n *   types: [ DogType, CatType ],\n *   resolveType(value) {\n *     if (value instanceof Dog) {\n *       return DogType;\n *     }\n *     if (value instanceof Cat) {\n *       return CatType;\n *     }\n *   }\n * });\n * ```\n */\nexport class GraphQLUnionType {\n  constructor(config) {\n    var _config$extensionASTN4;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.resolveType = config.resolveType;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN4 = config.extensionASTNodes) !== null && _config$extensionASTN4 !== void 0 ? _config$extensionASTN4 : [];\n    this._types = defineTypes.bind(undefined, config);\n    config.resolveType == null || typeof config.resolveType === 'function' || devAssert(false, `${this.name} must provide \"resolveType\" as a function, ` + `but got: ${inspect(config.resolveType)}.`);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLUnionType';\n  }\n  getTypes() {\n    if (typeof this._types === 'function') {\n      this._types = this._types();\n    }\n    return this._types;\n  }\n  toConfig() {\n    return {\n      name: this.name,\n      description: this.description,\n      types: this.getTypes(),\n      resolveType: this.resolveType,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\nfunction defineTypes(config) {\n  const types = resolveReadonlyArrayThunk(config.types);\n  Array.isArray(types) || devAssert(false, `Must provide Array of types or a function which returns such an array for Union ${config.name}.`);\n  return types;\n}\n\n/**\n * Enum Type Definition\n *\n * Some leaf values of requests and input values are Enums. GraphQL serializes\n * Enum values as strings, however internally Enums can be represented by any\n * kind of type, often integers.\n *\n * Example:\n *\n * ```ts\n * const RGBType = new GraphQLEnumType({\n *   name: 'RGB',\n *   values: {\n *     RED: { value: 0 },\n *     GREEN: { value: 1 },\n *     BLUE: { value: 2 }\n *   }\n * });\n * ```\n *\n * Note: If a value is not provided in a definition, the name of the enum value\n * will be used as its internal value.\n */\nexport class GraphQLEnumType {\n  /* <T> */\n  constructor(config) {\n    var _config$extensionASTN5;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN5 = config.extensionASTNodes) !== null && _config$extensionASTN5 !== void 0 ? _config$extensionASTN5 : [];\n    this._values = defineEnumValues(this.name, config.values);\n    this._valueLookup = new Map(this._values.map(enumValue => [enumValue.value, enumValue]));\n    this._nameLookup = keyMap(this._values, value => value.name);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLEnumType';\n  }\n  getValues() {\n    return this._values;\n  }\n  getValue(name) {\n    return this._nameLookup[name];\n  }\n  serialize(outputValue) {\n    const enumValue = this._valueLookup.get(outputValue);\n    if (enumValue === undefined) {\n      throw new GraphQLError(`Enum \"${this.name}\" cannot represent value: ${inspect(outputValue)}`);\n    }\n    return enumValue.name;\n  }\n  parseValue(inputValue) /* T */\n  {\n    if (typeof inputValue !== 'string') {\n      const valueStr = inspect(inputValue);\n      throw new GraphQLError(`Enum \"${this.name}\" cannot represent non-string value: ${valueStr}.` + didYouMeanEnumValue(this, valueStr));\n    }\n    const enumValue = this.getValue(inputValue);\n    if (enumValue == null) {\n      throw new GraphQLError(`Value \"${inputValue}\" does not exist in \"${this.name}\" enum.` + didYouMeanEnumValue(this, inputValue));\n    }\n    return enumValue.value;\n  }\n  parseLiteral(valueNode, _variables) /* T */\n  {\n    // Note: variables will be resolved to a value before calling this function.\n    if (valueNode.kind !== Kind.ENUM) {\n      const valueStr = print(valueNode);\n      throw new GraphQLError(`Enum \"${this.name}\" cannot represent non-enum value: ${valueStr}.` + didYouMeanEnumValue(this, valueStr), {\n        nodes: valueNode\n      });\n    }\n    const enumValue = this.getValue(valueNode.value);\n    if (enumValue == null) {\n      const valueStr = print(valueNode);\n      throw new GraphQLError(`Value \"${valueStr}\" does not exist in \"${this.name}\" enum.` + didYouMeanEnumValue(this, valueStr), {\n        nodes: valueNode\n      });\n    }\n    return enumValue.value;\n  }\n  toConfig() {\n    const values = keyValMap(this.getValues(), value => value.name, value => ({\n      description: value.description,\n      value: value.value,\n      deprecationReason: value.deprecationReason,\n      extensions: value.extensions,\n      astNode: value.astNode\n    }));\n    return {\n      name: this.name,\n      description: this.description,\n      values,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\nfunction didYouMeanEnumValue(enumType, unknownValueStr) {\n  const allNames = enumType.getValues().map(value => value.name);\n  const suggestedValues = suggestionList(unknownValueStr, allNames);\n  return didYouMean('the enum value', suggestedValues);\n}\nfunction defineEnumValues(typeName, valueMap) {\n  isPlainObj(valueMap) || devAssert(false, `${typeName} values must be an object with value names as keys.`);\n  return Object.entries(valueMap).map(([valueName, valueConfig]) => {\n    isPlainObj(valueConfig) || devAssert(false, `${typeName}.${valueName} must refer to an object with a \"value\" key ` + `representing an internal value but got: ${inspect(valueConfig)}.`);\n    return {\n      name: assertEnumValueName(valueName),\n      description: valueConfig.description,\n      value: valueConfig.value !== undefined ? valueConfig.value : valueName,\n      deprecationReason: valueConfig.deprecationReason,\n      extensions: toObjMap(valueConfig.extensions),\n      astNode: valueConfig.astNode\n    };\n  });\n}\n\n/**\n * Input Object Type Definition\n *\n * An input object defines a structured collection of fields which may be\n * supplied to a field argument.\n *\n * Using `NonNull` will ensure that a value must be provided by the query\n *\n * Example:\n *\n * ```ts\n * const GeoPoint = new GraphQLInputObjectType({\n *   name: 'GeoPoint',\n *   fields: {\n *     lat: { type: new GraphQLNonNull(GraphQLFloat) },\n *     lon: { type: new GraphQLNonNull(GraphQLFloat) },\n *     alt: { type: GraphQLFloat, defaultValue: 0 },\n *   }\n * });\n * ```\n */\nexport class GraphQLInputObjectType {\n  constructor(config) {\n    var _config$extensionASTN6;\n    this.name = assertName(config.name);\n    this.description = config.description;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN6 = config.extensionASTNodes) !== null && _config$extensionASTN6 !== void 0 ? _config$extensionASTN6 : [];\n    this._fields = defineInputFieldMap.bind(undefined, config);\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLInputObjectType';\n  }\n  getFields() {\n    if (typeof this._fields === 'function') {\n      this._fields = this._fields();\n    }\n    return this._fields;\n  }\n  toConfig() {\n    const fields = mapValue(this.getFields(), field => ({\n      description: field.description,\n      type: field.type,\n      defaultValue: field.defaultValue,\n      deprecationReason: field.deprecationReason,\n      extensions: field.extensions,\n      astNode: field.astNode\n    }));\n    return {\n      name: this.name,\n      description: this.description,\n      fields,\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes\n    };\n  }\n  toString() {\n    return this.name;\n  }\n  toJSON() {\n    return this.toString();\n  }\n}\nfunction defineInputFieldMap(config) {\n  const fieldMap = resolveObjMapThunk(config.fields);\n  isPlainObj(fieldMap) || devAssert(false, `${config.name} fields must be an object with field names as keys or a function which returns such an object.`);\n  return mapValue(fieldMap, (fieldConfig, fieldName) => {\n    !('resolve' in fieldConfig) || devAssert(false, `${config.name}.${fieldName} field has a resolve property, but Input Types cannot define resolvers.`);\n    return {\n      name: assertName(fieldName),\n      description: fieldConfig.description,\n      type: fieldConfig.type,\n      defaultValue: fieldConfig.defaultValue,\n      deprecationReason: fieldConfig.deprecationReason,\n      extensions: toObjMap(fieldConfig.extensions),\n      astNode: fieldConfig.astNode\n    };\n  });\n}\nexport function isRequiredInputField(field) {\n  return isNonNullType(field.type) && field.defaultValue === undefined;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}