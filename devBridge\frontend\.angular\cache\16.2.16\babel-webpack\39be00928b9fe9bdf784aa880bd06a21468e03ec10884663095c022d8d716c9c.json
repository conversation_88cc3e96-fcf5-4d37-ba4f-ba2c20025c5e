{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\nimport { isSameSecond } from \"./isSameSecond.js\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}