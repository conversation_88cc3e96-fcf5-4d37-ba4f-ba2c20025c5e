{"ast": null, "code": "import { setWeek } from \"../../../setWeek.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// Local week of year\nexport class LocalWeekParser extends Parser {\n  priority = 100;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"w\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"wo\":\n        return match.ordinalNumber(dateString, {\n          unit: \"week\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n  set(date, _flags, value, options) {\n    return startOfWeek(setWeek(date, value, options), options);\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"i\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}