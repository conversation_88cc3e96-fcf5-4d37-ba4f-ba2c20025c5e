{"ast": null, "code": "import { setISOWeek } from \"../../../setISOWeek.js\";\nimport { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// ISO week of year\nexport class ISOWeekParser extends Parser {\n  priority = 100;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"I\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"Io\":\n        return match.ordinalNumber(dateString, {\n          unit: \"week\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n  set(date, _flags, value) {\n    return startOfISOWeek(setISOWeek(date, value));\n  }\n  incompatibleTokens = [\"y\", \"Y\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"w\", \"d\", \"D\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}