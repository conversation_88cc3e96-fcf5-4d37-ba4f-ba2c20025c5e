{"ast": null, "code": "import { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType } from '../../../type/definition.mjs';\nimport { isIntrospectionType } from '../../../type/introspection.mjs';\n\n/**\n * Prohibit introspection queries\n *\n * A GraphQL document is only valid if all fields selected are not fields that\n * return an introspection type.\n *\n * Note: This rule is optional and is not part of the Validation section of the\n * GraphQL Specification. This rule effectively disables introspection, which\n * does not reflect best practices and should only be done if absolutely necessary.\n */\nexport function NoSchemaIntrospectionCustomRule(context) {\n  return {\n    Field(node) {\n      const type = getNamedType(context.getType());\n      if (type && isIntrospectionType(type)) {\n        context.reportError(new GraphQLError(`GraphQL introspection has been disabled, but the requested query contained the field \"${node.name.value}\".`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}