{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\n// CDK\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { OverlayModule } from '@angular/cdk/overlay';\n// Components\nimport { TasksComponent } from './tasks.component';\nimport { KanbanBoardComponent } from '../../../components/kanban-board/kanban-board.component';\nimport { TaskAiAssistantComponent } from '../../../components/task-ai-assistant/task-ai-assistant.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TasksComponent\n}, {\n  path: ':teamId',\n  component: TasksComponent\n}];\nexport class TasksModule {\n  static {\n    this.ɵfac = function TasksModule_Factory(t) {\n      return new (t || TasksModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TasksModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule.forChild(routes),\n      // Angular Material\n      MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n      // CDK\n      DragDropModule, OverlayModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TasksModule, {\n    declarations: [TasksComponent, KanbanBoardComponent, TaskAiAssistantComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, i1.RouterModule,\n    // Angular Material\n    MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatChipsModule, MatProgressSpinnerModule, MatSnackBarModule, MatDialogModule, MatMenuModule, MatTooltipModule, MatTabsModule, MatSlideToggleModule, MatCheckboxModule, MatBadgeModule, MatExpansionModule, MatListModule, MatDividerModule,\n    // CDK\n    DragDropModule, OverlayModule],\n    exports: [TasksComponent, KanbanBoardComponent, TaskAiAssistantComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatChipsModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatDialogModule", "MatMenuModule", "MatTooltipModule", "MatTabsModule", "MatSlideToggleModule", "MatCheckboxModule", "MatBadgeModule", "MatExpansionModule", "MatListModule", "MatDividerModule", "DragDropModule", "OverlayModule", "TasksComponent", "KanbanBoardComponent", "TaskAiAssistantComponent", "routes", "path", "component", "TasksModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\front\\tasks\\tasks.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// CDK\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { OverlayModule } from '@angular/cdk/overlay';\n\n// Components\nimport { TasksComponent } from './tasks.component';\nimport { KanbanBoardComponent } from '../../../components/kanban-board/kanban-board.component';\nimport { TaskAiAssistantComponent } from '../../../components/task-ai-assistant/task-ai-assistant.component';\n\nconst routes = [\n  {\n    path: '',\n    component: TasksComponent\n  },\n  {\n    path: ':teamId',\n    component: TasksComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TasksComponent,\n    KanbanBoardComponent,\n    TaskAiAssistantComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule.forChild(routes),\n    \n    // Angular Material\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDialogModule,\n    MatMenuModule,\n    MatTooltipModule,\n    MatTabsModule,\n    MatSlideToggleModule,\n    MatCheckboxModule,\n    MatBadgeModule,\n    MatExpansionModule,\n    MatListModule,\n    MatDividerModule,\n    \n    // CDK\n    DragDropModule,\n    OverlayModule\n  ],\n  exports: [\n    TasksComponent,\n    KanbanBoardComponent,\n    TaskAiAssistantComponent\n  ]\n})\nexport class TasksModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD;AACA,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,oBAAoB,QAAQ,yDAAyD;AAC9F,SAASC,wBAAwB,QAAQ,mEAAmE;;;AAE5G,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEL;CACZ,CACF;AA+CD,OAAM,MAAOM,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAtCpBjC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,CAAC+B,QAAQ,CAACJ,MAAM,CAAC;MAE7B;MACA1B,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;MAEhB;MACAC,cAAc,EACdC,aAAa;IAAA;EAAA;;;2EAQJO,WAAW;IAAAE,YAAA,GA3CpBR,cAAc,EACdC,oBAAoB,EACpBC,wBAAwB;IAAAO,OAAA,GAGxBpC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EAAAmC,EAAA,CAAAlC,YAAA;IAGX;IACAC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB;IAEhB;IACAC,cAAc,EACdC,aAAa;IAAAY,OAAA,GAGbX,cAAc,EACdC,oBAAoB,EACpBC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}