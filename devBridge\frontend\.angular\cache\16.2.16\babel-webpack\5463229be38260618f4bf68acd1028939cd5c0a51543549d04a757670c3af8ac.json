{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique variable names\n *\n * A GraphQL operation is only valid if all its variables are uniquely named.\n */\nexport function UniqueVariableNamesRule(context) {\n  return {\n    OperationDefinition(operationNode) {\n      var _operationNode$variab;\n\n      // See: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const variableDefinitions = (_operationNode$variab = operationNode.variableDefinitions) !== null && _operationNode$variab !== void 0 ? _operationNode$variab : [];\n      const seenVariableDefinitions = groupBy(variableDefinitions, node => node.variable.name.value);\n      for (const [variableName, variableNodes] of seenVariableDefinitions) {\n        if (variableNodes.length > 1) {\n          context.reportError(new GraphQLError(`There can be only one variable named \"$${variableName}\".`, {\n            nodes: variableNodes.map(node => node.variable.name)\n          }));\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}