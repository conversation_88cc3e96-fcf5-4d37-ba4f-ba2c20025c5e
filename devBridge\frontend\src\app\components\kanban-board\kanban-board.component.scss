.kanban-board-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  
  .kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .stats-container {
      display: flex;
      gap: 2rem;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        mat-icon {
          font-size: 1.25rem;
          color: #64748b;
        }
        
        .stat-value {
          font-size: 1.5rem;
          font-weight: 600;
          color: #1e293b;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: #64748b;
        }
        
        &.overdue {
          mat-icon, .stat-value {
            color: #ef4444;
          }
        }
        
        &.blocked {
          mat-icon, .stat-value {
            color: #f59e0b;
          }
        }
      }
    }
    
    .actions-container {
      display: flex;
      gap: 0.5rem;
    }
  }
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 1rem;
    color: #64748b;
    
    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }
  }
  
  .kanban-board {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    overflow-x: auto;
    flex: 1;
    
    .kanban-column {
      min-width: 300px;
      max-width: 350px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border-top: 4px solid;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 200px);
      
      .column-header {
        padding: 1rem;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px 4px 0 0;
        
        .column-title {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 600;
          
          .task-count {
            opacity: 0.8;
            font-size: 0.875rem;
          }
        }
        
        .add-task-btn {
          color: white;
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
      
      .tasks-container {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
        min-height: 200px;
        
        .task-card {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          margin-bottom: 0.75rem;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;
          
          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
          }
          
          &.cdk-drag-animating {
            transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
          }
          
          .priority-indicator {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
          }
          
          .task-content {
            padding: 1rem;
            padding-left: 1.25rem;
            
            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;
              
              .task-category {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                
                .category-icon {
                  font-size: 1rem;
                  color: #64748b;
                }
                
                .task-id {
                  font-size: 0.75rem;
                  color: #94a3b8;
                  font-family: monospace;
                }
              }
              
              .task-actions {
                display: flex;
                gap: 0.25rem;
                
                .overdue-icon {
                  color: #ef4444;
                  font-size: 1rem;
                }
                
                .blocked-icon {
                  color: #f59e0b;
                  font-size: 1rem;
                }
              }
            }
            
            .task-title {
              margin: 0 0 0.5rem 0;
              font-size: 0.875rem;
              font-weight: 600;
              color: #1e293b;
              line-height: 1.4;
            }
            
            .task-description {
              margin: 0 0 0.75rem 0;
              font-size: 0.75rem;
              color: #64748b;
              line-height: 1.4;
            }
            
            .task-labels {
              display: flex;
              flex-wrap: wrap;
              gap: 0.25rem;
              margin-bottom: 0.75rem;
              
              .task-label {
                padding: 0.125rem 0.5rem;
                border-radius: 12px;
                font-size: 0.625rem;
                font-weight: 500;
                color: white;
              }
              
              .more-labels {
                font-size: 0.625rem;
                color: #64748b;
                padding: 0.125rem 0.25rem;
              }
            }
            
            .task-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .task-assignees {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                
                .assignee-avatar {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  background: #e2e8f0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.625rem;
                  font-weight: 600;
                  color: #64748b;
                  
                  img {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    object-fit: cover;
                  }
                }
                
                .more-assignees {
                  font-size: 0.625rem;
                  color: #64748b;
                  margin-left: 0.25rem;
                }
              }
              
              .task-meta {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                
                > span {
                  display: flex;
                  align-items: center;
                  gap: 0.125rem;
                  font-size: 0.625rem;
                  color: #64748b;
                  
                  mat-icon {
                    font-size: 0.875rem;
                    width: 0.875rem;
                    height: 0.875rem;
                  }
                  
                  &.overdue {
                    color: #ef4444;
                  }
                }
              }
            }
            
            .ai-indicator {
              position: absolute;
              top: 0.5rem;
              right: 0.5rem;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border-radius: 12px;
              padding: 0.125rem 0.375rem;
              display: flex;
              align-items: center;
              gap: 0.125rem;
              font-size: 0.625rem;
              
              mat-icon {
                font-size: 0.75rem;
                width: 0.75rem;
                height: 0.75rem;
              }
            }
          }
          
          .task-drag-preview {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            
            .drag-preview-content {
              padding: 1rem;
              
              h4 {
                margin: 0 0 0.25rem 0;
                font-size: 0.875rem;
                font-weight: 600;
              }
              
              p {
                margin: 0;
                font-size: 0.75rem;
                color: #64748b;
              }
            }
          }
        }
        
        .empty-column {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          text-align: center;
          color: #94a3b8;
          
          mat-icon {
            font-size: 3rem;
            width: 3rem;
            height: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
          }
          
          p {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
          }
        }
      }
    }
  }
}

// Drag and drop styles
.cdk-drag-placeholder {
  opacity: 0.4;
  background: #f1f5f9;
  border: 2px dashed #cbd5e1;
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// Responsive design
@media (max-width: 768px) {
  .kanban-board-container {
    .kanban-header {
      padding: 0.75rem 1rem;
      
      .stats-container {
        gap: 1rem;
        
        .stat-item {
          .stat-value {
            font-size: 1.25rem;
          }
          
          .stat-label {
            display: none;
          }
        }
      }
    }
    
    .kanban-board {
      padding: 1rem;
      
      .kanban-column {
        min-width: 280px;
      }
    }
  }
}
