{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isThisWeek} function options.\n */\n\n/**\n * @name isThisWeek\n * @category Week Helpers\n * @summary Is the given date in the same week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same week as the current date?\n *\n * @param date - The date to check\n * @param options - The object with options\n *\n * @returns The date is in this week\n *\n * @example\n * // If today is 25 September 2014, is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21))\n * //=> true\n *\n * @example\n * // If today is 25 September 2014 and week starts with Monday\n * // is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21), { weekStartsOn: 1 })\n * //=> false\n */\nexport function isThisWeek(date, options) {\n  return isSameWeek(constructFrom(options?.in || date, date), constructNow(options?.in || date), options);\n}\n\n// Fallback for modularized imports:\nexport default isThisWeek;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "isSameWeek", "isThisWeek", "date", "options", "in"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/isThisWeek.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isThisWeek} function options.\n */\n\n/**\n * @name isThisWeek\n * @category Week Helpers\n * @summary Is the given date in the same week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same week as the current date?\n *\n * @param date - The date to check\n * @param options - The object with options\n *\n * @returns The date is in this week\n *\n * @example\n * // If today is 25 September 2014, is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21))\n * //=> true\n *\n * @example\n * // If today is 25 September 2014 and week starts with Monday\n * // is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21), { weekStartsOn: 1 })\n * //=> false\n */\nexport function isThisWeek(date, options) {\n  return isSameWeek(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n    options,\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisWeek;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,UAAU,CACfF,aAAa,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,EAAEA,IAAI,CAAC,EACxCH,YAAY,CAACI,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAAC,EACjCC,OACF,CAAC;AACH;;AAEA;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}