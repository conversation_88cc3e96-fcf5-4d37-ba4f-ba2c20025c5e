{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n// Components\nimport { TasksComponent } from './tasks.component';\nconst routes = [{\n  path: '',\n  component: TasksComponent\n}, {\n  path: ':teamId',\n  component: TasksComponent\n}];\nexport let TasksModule = class TasksModule {};\nTasksModule = __decorate([NgModule({\n  declarations: [TasksComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],\n  exports: [TasksComponent]\n})], TasksModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "TasksComponent", "routes", "path", "component", "TasksModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\front\\tasks\\tasks.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n\n// Components\nimport { TasksComponent } from './tasks.component';\n\nconst routes = [\n  {\n    path: '',\n    component: TasksComponent\n  },\n  {\n    path: ':teamId',\n    component: TasksComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TasksComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    SharedModule\n  ],\n  exports: [\n    TasksComponent\n  ]\n})\nexport class TasksModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,+BAA+B;AAE5D;AACA,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEH;CACZ,CACF;AAeM,WAAMI,WAAW,GAAjB,MAAMA,WAAW,GAAI;AAAfA,WAAW,GAAAC,UAAA,EAbvBT,QAAQ,CAAC;EACRU,YAAY,EAAE,CACZN,cAAc,CACf;EACDO,OAAO,EAAE,CACPV,YAAY,EACZC,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY,CACb;EACDU,OAAO,EAAE,CACPT,cAAc;CAEjB,CAAC,C,EACWI,WAAW,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}