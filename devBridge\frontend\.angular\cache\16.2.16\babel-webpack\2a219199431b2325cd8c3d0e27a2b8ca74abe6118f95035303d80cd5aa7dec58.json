{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfYesterday} function options.\n */\n\n/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n  const date = constructNow(options?.in);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}