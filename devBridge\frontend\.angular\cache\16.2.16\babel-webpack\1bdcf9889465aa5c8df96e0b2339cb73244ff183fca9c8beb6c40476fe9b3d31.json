{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/task.service\";\nimport * as i3 from \"../../../services/equipe.service\";\nimport * as i4 from \"../../../services/authuser.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../components/kanban-board/kanban-board.component\";\nimport * as i9 from \"../../../components/task-ai-assistant/task-ai-assistant.component\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/card\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/menu\";\nimport * as i15 from \"@angular/material/tooltip\";\nfunction TasksComponent_div_15_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"span\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4, \"En retard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stats_r9 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stats_r9.overdue);\n  }\n}\nfunction TasksComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵtext(5, \"Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"span\", 25);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 26);\n    i0.ɵɵtext(10, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"span\", 25);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 26);\n    i0.ɵɵtext(15, \"En cours\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 24)(17, \"span\", 25);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 26);\n    i0.ɵɵtext(20, \"Termin\\u00E9\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, TasksComponent_div_15_div_21_Template, 5, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stats_r9 = ctx.ngIf;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stats_r9.total);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.todo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.inProgress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(stats_r9.done);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", stats_r9.overdue > 0);\n  }\n}\nfunction TasksComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"app-kanban-board\", 30);\n    i0.ɵɵlistener(\"taskSelected\", function TasksComponent_div_43_Template_app_kanban_board_taskSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onTaskSelected($event));\n    })(\"taskCreated\", function TasksComponent_div_43_Template_app_kanban_board_taskCreated_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onTaskCreated($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"teamId\", ctx_r2.teamId)(\"filters\", ctx_r2.kanbanFilters);\n  }\n}\nfunction TasksComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"mat-card\", 32)(2, \"mat-card-content\")(3, \"div\", 33)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"list\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Vue Liste\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Cette vue sera bient\\u00F4t disponible\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TasksComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 32)(2, \"mat-card-content\")(3, \"div\", 33)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Vue Calendrier\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Cette vue sera bient\\u00F4t disponible\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TasksComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"mat-spinner\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des t\\u00E2ches...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TasksComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-icon\", 38);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Erreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_47_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.refresh());\n    });\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.error);\n  }\n}\nfunction TasksComponent_div_48_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.selectedTask.description, \" \");\n  }\n}\nfunction TasksComponent_div_48_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"Cat\\u00E9gorie:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.selectedTask.category);\n  }\n}\nfunction TasksComponent_div_48_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"\\u00C9ch\\u00E9ance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r19.selectedTask.dueDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction TasksComponent_div_48_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵtext(2, \"Estimation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r20.selectedTask.estimatedHours, \"h\");\n  }\n}\nfunction TasksComponent_div_48_div_26_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r24 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", label_r24.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", label_r24.name, \" \");\n  }\n}\nfunction TasksComponent_div_48_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Labels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵtemplate(4, TasksComponent_div_48_div_26_span_4_Template, 2, 3, \"span\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.selectedTask.labels);\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 68);\n  }\n  if (rf & 2) {\n    const assignee_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r27.getAssigneeImage(assignee_r26), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r27.getAssigneeName(assignee_r26));\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const assignee_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.getAssigneeInitials(assignee_r26), \" \");\n  }\n}\nfunction TasksComponent_div_48_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtemplate(2, TasksComponent_div_48_div_27_div_4_img_2_Template, 1, 2, \"img\", 65);\n    i0.ɵɵtemplate(3, TasksComponent_div_48_div_27_div_4_span_3_Template, 2, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 67);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const assignee_r26 = ctx.$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.hasAssigneeImage(assignee_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.hasAssigneeImage(assignee_r26));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.getAssigneeName(assignee_r26));\n  }\n}\nfunction TasksComponent_div_48_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"span\", 56);\n    i0.ɵɵtext(2, \"Assign\\u00E9 \\u00E0:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61);\n    i0.ɵɵtemplate(4, TasksComponent_div_48_div_27_div_4_Template, 6, 3, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.selectedTask.assignedTo);\n  }\n}\nfunction TasksComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"h3\");\n    i0.ɵɵtext(3, \"D\\u00E9tails de la t\\u00E2che\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_48_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.closeTaskDetails());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"div\", 44)(9, \"h4\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TasksComponent_div_48_p_11_Template, 2, 1, \"p\", 45);\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"div\", 47)(14, \"span\", 48);\n    i0.ɵɵtext(15, \"Statut:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 47)(19, \"span\", 48);\n    i0.ɵɵtext(20, \"Priorit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, TasksComponent_div_48_div_23_Template, 5, 1, \"div\", 49);\n    i0.ɵɵtemplate(24, TasksComponent_div_48_div_24_Template, 6, 4, \"div\", 49);\n    i0.ɵɵtemplate(25, TasksComponent_div_48_div_25_Template, 5, 1, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, TasksComponent_div_48_div_26_Template, 5, 1, \"div\", 50);\n    i0.ɵɵtemplate(27, TasksComponent_div_48_div_27_Template, 5, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 52)(29, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_48_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.openAIAssistant());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Assistant IA \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r7.showTaskDetails);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate1(\"value status-\", ctx_r7.selectedTask.status, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"value priority-\", ctx_r7.selectedTask.priority, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedTask.priority);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.category);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.dueDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.estimatedHours);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.labels && ctx_r7.selectedTask.labels.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedTask.assignedTo && ctx_r7.selectedTask.assignedTo.length > 0);\n  }\n}\nfunction TasksComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 41)(2, \"h3\");\n    i0.ɵɵtext(3, \"Assistant IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TasksComponent_div_49_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.closeAIAssistant());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"app-task-ai-assistant\", 70);\n    i0.ɵɵlistener(\"taskUpdated\", function TasksComponent_div_49_Template_app_task_ai_assistant_taskUpdated_8_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onTaskUpdated($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r8.showAIAssistant);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"task\", ctx_r8.selectedTask);\n  }\n}\nexport let TasksComponent = /*#__PURE__*/(() => {\n  class TasksComponent {\n    constructor(route, router, taskService, equipeService, authService, snackBar, dialog) {\n      this.route = route;\n      this.router = router;\n      this.taskService = taskService;\n      this.equipeService = equipeService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.destroy$ = new Subject();\n      this.team = null;\n      // Vue actuelle\n      this.currentView = 'kanban';\n      // Données Kanban\n      this.kanbanData = null;\n      this.kanbanFilters = {};\n      // Statistiques\n      this.taskStats = null;\n      // États\n      this.loading = false;\n      this.error = null;\n      // Tâche sélectionnée\n      this.selectedTask = null;\n      this.showTaskDetails = false;\n      this.showAIAssistant = false;\n      this.currentUser = this.authService.getCurrentUser();\n    }\n    ngOnInit() {\n      this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.teamId = params['teamId'];\n        if (this.teamId) {\n          this.loadTeamData();\n          this.loadKanbanBoard();\n          this.loadTaskStatistics();\n        } else {\n          this.error = 'ID d\\'équipe manquant';\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    // Charger les données de l'équipe\n    loadTeamData() {\n      this.equipeService.getEquipeById(this.teamId).subscribe({\n        next: team => {\n          this.team = team;\n          // Vérifier les permissions\n          if (!this.hasTeamAccess()) {\n            this.error = 'Vous n\\'avez pas accès aux tâches de cette équipe';\n            return;\n          }\n        },\n        error: error => {\n          console.error('Erreur chargement équipe:', error);\n          this.error = 'Équipe non trouvée';\n        }\n      });\n    }\n    // Charger le tableau Kanban\n    loadKanbanBoard() {\n      this.loading = true;\n      this.error = null;\n      this.taskService.getKanbanBoard(this.teamId, this.kanbanFilters).subscribe({\n        next: data => {\n          this.kanbanData = data;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Erreur chargement Kanban:', error);\n          this.error = 'Erreur lors du chargement des tâches';\n          this.loading = false;\n          this.snackBar.open('Erreur lors du chargement des tâches', 'Fermer', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    // Charger les statistiques\n    loadTaskStatistics() {\n      this.taskService.getTaskStatistics(this.teamId).subscribe({\n        next: stats => {\n          this.taskStats = stats;\n        },\n        error: error => {\n          console.error('Erreur chargement statistiques:', error);\n        }\n      });\n    }\n    // Vérifier l'accès à l'équipe\n    hasTeamAccess() {\n      if (!this.team || !this.currentUser) return false;\n      // Admin système a accès à tout\n      if (this.currentUser.role === 'admin') return true;\n      // Vérifier si l'utilisateur est membre de l'équipe\n      return this.equipeService.isTeamMember(this.team, this.currentUser.id);\n    }\n    // Changer de vue\n    switchView(view) {\n      this.currentView = view;\n      if (view === 'kanban') {\n        this.loadKanbanBoard();\n      }\n      // Ajouter d'autres vues plus tard\n    }\n    // Gérer la sélection d'une tâche\n    onTaskSelected(task) {\n      this.selectedTask = task;\n      this.showTaskDetails = true;\n    }\n    // Gérer la création d'une tâche\n    onTaskCreated(task) {\n      this.snackBar.open('Tâche créée avec succès', 'Fermer', {\n        duration: 2000\n      });\n      this.loadKanbanBoard();\n      this.loadTaskStatistics();\n    }\n    // Gérer la mise à jour d'une tâche\n    onTaskUpdated(task) {\n      this.selectedTask = task;\n      this.loadKanbanBoard();\n      this.loadTaskStatistics();\n    }\n    // Appliquer des filtres\n    applyFilters(filters) {\n      this.kanbanFilters = {\n        ...filters\n      };\n      this.loadKanbanBoard();\n    }\n    // Réinitialiser les filtres\n    resetFilters() {\n      this.kanbanFilters = {};\n      this.loadKanbanBoard();\n    }\n    // Actualiser les données\n    refresh() {\n      this.loadKanbanBoard();\n      this.loadTaskStatistics();\n    }\n    // Ouvrir l'assistant IA\n    openAIAssistant() {\n      if (this.selectedTask) {\n        this.showAIAssistant = true;\n      } else {\n        this.snackBar.open('Veuillez sélectionner une tâche', 'Fermer', {\n          duration: 2000\n        });\n      }\n    }\n    // Fermer les panneaux\n    closeTaskDetails() {\n      this.showTaskDetails = false;\n      this.selectedTask = null;\n    }\n    closeAIAssistant() {\n      this.showAIAssistant = false;\n    }\n    // Naviguer vers l'équipe\n    navigateToTeam() {\n      this.router.navigate(['/equipes/detail', this.teamId]);\n    }\n    // Obtenir le nom de l'équipe\n    getTeamName() {\n      return this.team?.name || 'Équipe';\n    }\n    // Vérifier si l'utilisateur peut créer des tâches\n    canCreateTasks() {\n      if (!this.team || !this.currentUser) return false;\n      // Admin système peut tout faire\n      if (this.currentUser.role === 'admin') return true;\n      // Membres de l'équipe peuvent créer des tâches\n      return this.hasTeamAccess();\n    }\n    // Vérifier si l'utilisateur peut modifier des tâches\n    canEditTasks() {\n      return this.canCreateTasks();\n    }\n    // Obtenir les statistiques rapides\n    getQuickStats() {\n      if (!this.kanbanData) return null;\n      return {\n        total: this.kanbanData.stats.total,\n        todo: this.kanbanData.stats.byStatus['todo'] || 0,\n        inProgress: this.kanbanData.stats.byStatus['in-progress'] || 0,\n        done: this.kanbanData.stats.byStatus['done'] || 0,\n        overdue: this.kanbanData.stats.overdue,\n        blocked: this.kanbanData.stats.blocked\n      };\n    }\n    // Obtenir la couleur du statut de l'équipe\n    getTeamStatusColor() {\n      if (!this.team) return '#6b7280';\n      switch (this.team.status) {\n        case 'active':\n          return '#10b981';\n        case 'inactive':\n          return '#f59e0b';\n        case 'archived':\n          return '#6b7280';\n        default:\n          return '#6b7280';\n      }\n    }\n    // Helper pour obtenir le nom d'un assigné\n    getAssigneeName(assignee) {\n      if (typeof assignee === 'string') {\n        return assignee;\n      }\n      return assignee?.fullName || assignee?.username || assignee?.email || 'Utilisateur';\n    }\n    // Helper pour obtenir l'image de profil d'un assigné\n    getAssigneeImage(assignee) {\n      if (typeof assignee === 'string') {\n        return null;\n      }\n      return assignee?.profileImage || null;\n    }\n    // Helper pour vérifier si un assigné a une image\n    hasAssigneeImage(assignee) {\n      return typeof assignee !== 'string' && !!assignee?.profileImage;\n    }\n    // Helper pour obtenir les initiales d'un assigné\n    getAssigneeInitials(assignee) {\n      const name = this.getAssigneeName(assignee);\n      return name.charAt(0).toUpperCase();\n    }\n    static {\n      this.ɵfac = function TasksComponent_Factory(t) {\n        return new (t || TasksComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TaskService), i0.ɵɵdirectiveInject(i3.EquipeService), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.MatDialog));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TasksComponent,\n        selectors: [[\"app-tasks\"]],\n        decls: 51,\n        vars: 22,\n        consts: [[1, \"tasks-container\"], [1, \"tasks-header\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Retour \\u00E0 l'\\u00E9quipe\", 3, \"click\"], [1, \"team-info\"], [1, \"team-name\"], [1, \"team-status\"], [1, \"header-actions\"], [\"class\", \"quick-stats\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Actualiser\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Assistant IA\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Changer de vue\", 3, \"matMenuTriggerFor\"], [\"viewMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"tasks-content\"], [\"class\", \"kanban-view\", 4, \"ngIf\"], [\"class\", \"list-view\", 4, \"ngIf\"], [\"class\", \"calendar-view\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"task-details-panel\", 3, \"open\", 4, \"ngIf\"], [\"class\", \"ai-assistant-panel\", 3, \"open\", 4, \"ngIf\"], [1, \"panels-overlay\", 3, \"click\"], [1, \"quick-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"stat-value\", \"overdue\"], [1, \"kanban-view\"], [3, \"teamId\", \"filters\", \"taskSelected\", \"taskCreated\"], [1, \"list-view\"], [1, \"coming-soon\"], [1, \"coming-soon-content\"], [1, \"calendar-view\"], [1, \"loading-overlay\"], [\"diameter\", \"40\"], [1, \"error-container\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"task-details-panel\"], [1, \"panel-header\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"panel-content\"], [1, \"task-info\"], [\"class\", \"task-description\", 4, \"ngIf\"], [1, \"task-meta\"], [1, \"meta-item\"], [1, \"label\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"class\", \"task-labels\", 4, \"ngIf\"], [\"class\", \"task-assignees\", 4, \"ngIf\"], [1, \"task-actions\"], [1, \"task-description\"], [1, \"value\"], [1, \"task-labels\"], [1, \"label-title\"], [1, \"labels-list\"], [\"class\", \"task-label\", 3, \"background-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-label\"], [1, \"task-assignees\"], [1, \"assignees-list\"], [\"class\", \"assignee\", 4, \"ngFor\", \"ngForOf\"], [1, \"assignee\"], [1, \"assignee-avatar\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"assignee-name\"], [3, \"src\", \"alt\"], [1, \"ai-assistant-panel\"], [3, \"task\", \"taskUpdated\"]],\n        template: function TasksComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_3_listener() {\n              return ctx.navigateToTeam();\n            });\n            i0.ɵɵelementStart(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"arrow_back\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"h1\", 5);\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 6)(10, \"mat-icon\");\n            i0.ɵɵtext(11, \"circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"span\");\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(14, \"div\", 7);\n            i0.ɵɵtemplate(15, TasksComponent_div_15_Template, 22, 5, \"div\", 8);\n            i0.ɵɵelementStart(16, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_16_listener() {\n              return ctx.refresh();\n            });\n            i0.ɵɵelementStart(17, \"mat-icon\");\n            i0.ɵɵtext(18, \"refresh\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_19_listener() {\n              return ctx.openAIAssistant();\n            });\n            i0.ɵɵelementStart(20, \"mat-icon\");\n            i0.ɵɵtext(21, \"psychology\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"button\", 11)(23, \"mat-icon\");\n            i0.ɵɵtext(24, \"view_module\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"mat-menu\", null, 12)(27, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_27_listener() {\n              return ctx.switchView(\"kanban\");\n            });\n            i0.ɵɵelementStart(28, \"mat-icon\");\n            i0.ɵɵtext(29, \"view_kanban\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"span\");\n            i0.ɵɵtext(31, \"Kanban\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_32_listener() {\n              return ctx.switchView(\"list\");\n            });\n            i0.ɵɵelementStart(33, \"mat-icon\");\n            i0.ɵɵtext(34, \"list\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"span\");\n            i0.ɵɵtext(36, \"Liste\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_button_click_37_listener() {\n              return ctx.switchView(\"calendar\");\n            });\n            i0.ɵɵelementStart(38, \"mat-icon\");\n            i0.ɵɵtext(39, \"calendar_today\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"span\");\n            i0.ɵɵtext(41, \"Calendrier\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(42, \"div\", 14);\n            i0.ɵɵtemplate(43, TasksComponent_div_43_Template, 2, 2, \"div\", 15);\n            i0.ɵɵtemplate(44, TasksComponent_div_44_Template, 10, 0, \"div\", 16);\n            i0.ɵɵtemplate(45, TasksComponent_div_45_Template, 10, 0, \"div\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(46, TasksComponent_div_46_Template, 4, 0, \"div\", 18);\n            i0.ɵɵtemplate(47, TasksComponent_div_47_Template, 9, 1, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(48, TasksComponent_div_48_Template, 33, 17, \"div\", 20);\n            i0.ɵɵtemplate(49, TasksComponent_div_49_Template, 9, 3, \"div\", 21);\n            i0.ɵɵelementStart(50, \"div\", 22);\n            i0.ɵɵlistener(\"click\", function TasksComponent_Template_div_click_50_listener() {\n              ctx.closeTaskDetails();\n              return ctx.closeAIAssistant();\n            });\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const _r1 = i0.ɵɵreference(26);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate(ctx.getTeamName());\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"color\", ctx.getTeamStatusColor());\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate((ctx.team == null ? null : ctx.team.status) || \"active\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.getQuickStats());\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedTask);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"matMenuTriggerFor\", _r1);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"kanban\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"list\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"calendar\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"kanban\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"list\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"calendar\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedTask);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedTask && ctx.showAIAssistant);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.showTaskDetails || ctx.showAIAssistant);\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i8.KanbanBoardComponent, i9.TaskAiAssistantComponent, i10.MatButton, i10.MatIconButton, i11.MatCard, i11.MatCardContent, i12.MatIcon, i13.MatProgressSpinner, i14.MatMenu, i14.MatMenuItem, i14.MatMenuTrigger, i15.MatTooltip, i7.DatePipe],\n        styles: [\".tasks-container[_ngcontent-%COMP%]{height:100vh;display:flex;flex-direction:column;background:#f8fafc;position:relative}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]{background:white;border-bottom:1px solid #e2e8f0;padding:1rem 1.5rem;display:flex;justify-content:space-between;align-items:center;box-shadow:0 1px 3px #0000001a;z-index:10}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-name[_ngcontent-%COMP%]{margin:0;font-size:1.5rem;font-weight:600;color:#1e293b}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.875rem;margin-top:.25rem}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .team-info[_ngcontent-%COMP%]   .team-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:.75rem;width:.75rem;height:.75rem}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]{display:flex;gap:1.5rem;padding:0 1rem;border-left:1px solid #e2e8f0;border-right:1px solid #e2e8f0}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#1e293b}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value.overdue[_ngcontent-%COMP%]{color:#ef4444}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.75rem;color:#64748b;margin-top:.125rem}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]{flex:1;overflow:hidden;position:relative}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .kanban-view[_ngcontent-%COMP%]{height:100%}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]{height:100%;padding:1.5rem}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center;justify-content:center}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]{text-align:center;color:#64748b}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem;opacity:.5}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.5rem}.tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-content[_ngcontent-%COMP%]   .calendar-view[_ngcontent-%COMP%]   .coming-soon[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:1rem}.tasks-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:rgba(255,255,255,.9);display:flex;flex-direction:column;align-items:center;justify-content:center;gap:1rem;z-index:100}.tasks-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#64748b;margin:0}.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;color:#64748b}.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem;margin-bottom:1rem}.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#1e293b}.tasks-container[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1.5rem}.task-details-panel[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:400px;height:100vh;background:white;box-shadow:-2px 0 10px #0000001a;transition:right .3s ease;z-index:1000;display:flex;flex-direction:column}.task-details-panel.open[_ngcontent-%COMP%], .ai-assistant-panel.open[_ngcontent-%COMP%]{right:0}.task-details-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e2e8f0;display:flex;justify-content:space-between;align-items:center;background:#f8fafc}.task-details-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.125rem;font-weight:600;color:#1e293b}.task-details-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:1.25rem;font-weight:600;color:#1e293b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]{margin:0 0 1.5rem;color:#64748b;line-height:1.6}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]{margin-bottom:1.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.5rem 0;border-bottom:1px solid #f1f5f9}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#64748b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:600;color:#1e293b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-todo[_ngcontent-%COMP%]{color:#3b82f6}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-in-progress[_ngcontent-%COMP%]{color:#f59e0b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-review[_ngcontent-%COMP%]{color:#8b5cf6}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-testing[_ngcontent-%COMP%]{color:#06b6d4}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.status-done[_ngcontent-%COMP%]{color:#10b981}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-lowest[_ngcontent-%COMP%]{color:#6b7280}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-low[_ngcontent-%COMP%]{color:#3b82f6}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-medium[_ngcontent-%COMP%]{color:#f59e0b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-high[_ngcontent-%COMP%]{color:#ef4444}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-highest[_ngcontent-%COMP%]{color:#dc2626}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .value.priority-critical[_ngcontent-%COMP%]{color:#991b1b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]{margin-bottom:1.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .label-title[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .label-title[_ngcontent-%COMP%]{display:block;font-weight:500;color:#64748b;margin-bottom:.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .labels-list[_ngcontent-%COMP%]   .task-label[_ngcontent-%COMP%]{padding:.25rem .75rem;border-radius:12px;font-size:.75rem;font-weight:500;color:#fff}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background:#e2e8f0;display:flex;align-items:center;justify-content:center;font-size:.875rem;font-weight:600;color:#64748b}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-labels[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-name[_ngcontent-%COMP%], .task-details-panel[_ngcontent-%COMP%]   .task-info[_ngcontent-%COMP%]   .task-assignees[_ngcontent-%COMP%]   .assignees-list[_ngcontent-%COMP%]   .assignee[_ngcontent-%COMP%]   .assignee-name[_ngcontent-%COMP%]{font-weight:500;color:#1e293b}.task-details-panel[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]{margin-top:2rem;padding-top:1.5rem;border-top:1px solid #e2e8f0}.task-details-panel[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.ai-assistant-panel[_ngcontent-%COMP%]{width:500px;right:-500px}.panels-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.3);z-index:999;opacity:0;visibility:hidden;transition:all .3s ease}.panels-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.mat-menu-item.active[_ngcontent-%COMP%]{background:#f1f5f9;color:#3b82f6}.mat-menu-item.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#3b82f6}@media (max-width: 768px){.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]{padding:.75rem 1rem;flex-direction:column;gap:1rem}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%], .tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{width:100%;justify-content:space-between}.tasks-container[_ngcontent-%COMP%]   .tasks-header[_ngcontent-%COMP%]   .quick-stats[_ngcontent-%COMP%]{gap:1rem;padding:0;border:none}.task-details-panel[_ngcontent-%COMP%], .ai-assistant-panel[_ngcontent-%COMP%]{width:100vw;right:-100vw}}\"]\n      });\n    }\n  }\n  return TasksComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}