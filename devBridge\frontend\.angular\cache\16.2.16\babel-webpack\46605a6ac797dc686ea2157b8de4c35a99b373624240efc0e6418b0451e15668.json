{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * GraphQL Value literals.\n *\n * Returns `undefined` when the value could not be validly coerced according to\n * the provided type.\n *\n * | GraphQL Value        | JSON Value    |\n * | -------------------- | ------------- |\n * | Input Object         | Object        |\n * | List                 | Array         |\n * | Boolean              | Boolean       |\n * | String               | String        |\n * | Int / Float          | Number        |\n * | Enum Value           | Unknown       |\n * | NullValue            | null          |\n *\n */\n\nexport function valueFromAST(valueNode, type, variables) {\n  if (!valueNode) {\n    // When there is no node, then there is also no value.\n    // Importantly, this is different from returning the value null.\n    return;\n  }\n  if (valueNode.kind === Kind.VARIABLE) {\n    const variableName = valueNode.name.value;\n    if (variables == null || variables[variableName] === undefined) {\n      // No valid return value.\n      return;\n    }\n    const variableValue = variables[variableName];\n    if (variableValue === null && isNonNullType(type)) {\n      return; // Invalid: intentionally return no value.\n    } // Note: This does no further checking that this variable is correct.\n    // This assumes that this query has been validated and the variable\n    // usage here is of the correct type.\n\n    return variableValue;\n  }\n  if (isNonNullType(type)) {\n    if (valueNode.kind === Kind.NULL) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return valueFromAST(valueNode, type.ofType, variables);\n  }\n  if (valueNode.kind === Kind.NULL) {\n    // This is explicitly returning the value null.\n    return null;\n  }\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (valueNode.kind === Kind.LIST) {\n      const coercedValues = [];\n      for (const itemNode of valueNode.values) {\n        if (isMissingVariable(itemNode, variables)) {\n          // If an array contains a missing variable, it is either coerced to\n          // null or if the item type is non-null, it considered invalid.\n          if (isNonNullType(itemType)) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(null);\n        } else {\n          const itemValue = valueFromAST(itemNode, itemType, variables);\n          if (itemValue === undefined) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(itemValue);\n        }\n      }\n      return coercedValues;\n    }\n    const coercedValue = valueFromAST(valueNode, itemType, variables);\n    if (coercedValue === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return [coercedValue];\n  }\n  if (isInputObjectType(type)) {\n    if (valueNode.kind !== Kind.OBJECT) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    const coercedObj = Object.create(null);\n    const fieldNodes = keyMap(valueNode.fields, field => field.name.value);\n    for (const field of Object.values(type.getFields())) {\n      const fieldNode = fieldNodes[field.name];\n      if (!fieldNode || isMissingVariable(fieldNode.value, variables)) {\n        if (field.defaultValue !== undefined) {\n          coercedObj[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          return; // Invalid: intentionally return no value.\n        }\n\n        continue;\n      }\n      const fieldValue = valueFromAST(fieldNode.value, field.type, variables);\n      if (fieldValue === undefined) {\n        return; // Invalid: intentionally return no value.\n      }\n\n      coercedObj[field.name] = fieldValue;\n    }\n    return coercedObj;\n  }\n  if (isLeafType(type)) {\n    // Scalars and Enums fulfill parsing a literal value via parseLiteral().\n    // Invalid values represent a failure to parse correctly, in which case\n    // no value is returned.\n    let result;\n    try {\n      result = type.parseLiteral(valueNode, variables);\n    } catch (_error) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    if (result === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return result;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible input types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n} // Returns true if the provided valueNode is a variable which is not defined\n// in the set of variables.\n\nfunction isMissingVariable(valueNode, variables) {\n  return valueNode.kind === Kind.VARIABLE && (variables == null || variables[valueNode.name.value] === undefined);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}