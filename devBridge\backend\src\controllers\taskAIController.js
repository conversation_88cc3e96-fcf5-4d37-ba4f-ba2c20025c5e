const Task = require('../models/Task');
const { Team } = require('../models/Team');
const User = require('../models/User');
const TeamHistory = require('../models/TeamHistory');
const { logger } = require('../utils/logger');

// Service IA simulé (à remplacer par une vraie intégration)
const AIService = {
  async analyzeTask(task) {
    // Simulation d'analyse IA
    const suggestions = [];
    
    // Suggestion d'optimisation basée sur la description
    if (task.description && task.description.length > 500) {
      suggestions.push({
        type: 'optimization',
        content: 'Cette tâche semble complexe. Considérez la diviser en sous-tâches plus petites.',
        confidence: 0.8
      });
    }
    
    // Suggestion d'assignation basée sur les compétences
    if (!task.assignedTo || task.assignedTo.length === 0) {
      suggestions.push({
        type: 'assignment',
        content: 'Cette tâche pourrait bénéficier d\'une assignation à un membre de l\'équipe.',
        confidence: 0.7
      });
    }
    
    // Suggestion de deadline
    if (!task.dueDate && task.priority === 'high') {
      suggestions.push({
        type: 'deadline',
        content: 'Cette tâche haute priorité devrait avoir une date d\'échéance.',
        confidence: 0.9
      });
    }
    
    // Suggestion de priorité
    if (task.category === 'bug' && task.priority === 'low') {
      suggestions.push({
        type: 'priority',
        content: 'Les bugs devraient généralement avoir une priorité plus élevée.',
        confidence: 0.8
      });
    }
    
    return suggestions;
  },
  
  async generateSubtasks(task) {
    // Simulation de génération de sous-tâches
    const subtasks = [];
    
    if (task.category === 'feature') {
      subtasks.push(
        { title: 'Analyser les exigences', description: 'Définir les spécifications détaillées' },
        { title: 'Concevoir l\'interface', description: 'Créer les maquettes et wireframes' },
        { title: 'Implémenter la fonctionnalité', description: 'Développer le code' },
        { title: 'Tester la fonctionnalité', description: 'Tests unitaires et d\'intégration' },
        { title: 'Documenter', description: 'Créer la documentation utilisateur' }
      );
    } else if (task.category === 'bug') {
      subtasks.push(
        { title: 'Reproduire le bug', description: 'Identifier les étapes de reproduction' },
        { title: 'Analyser la cause', description: 'Déboguer et identifier la source' },
        { title: 'Corriger le problème', description: 'Implémenter la correction' },
        { title: 'Tester la correction', description: 'Vérifier que le bug est résolu' }
      );
    }
    
    return subtasks;
  },
  
  async estimateEffort(task) {
    // Simulation d'estimation d'effort
    let hours = 4; // Base
    
    if (task.category === 'epic') hours *= 5;
    else if (task.category === 'feature') hours *= 2;
    else if (task.category === 'bug') hours *= 0.5;
    
    if (task.priority === 'critical') hours *= 1.5;
    else if (task.priority === 'low') hours *= 0.7;
    
    if (task.description && task.description.length > 200) hours *= 1.3;
    
    return Math.round(hours);
  },
  
  async generateDescription(task) {
    // Simulation de génération de description
    const templates = {
      feature: `En tant qu'utilisateur, je veux ${task.title.toLowerCase()} afin d'améliorer mon expérience.
      
Critères d'acceptation:
- [ ] La fonctionnalité est accessible depuis l'interface principale
- [ ] Les données sont sauvegardées correctement
- [ ] L'interface est responsive
- [ ] Les tests sont implémentés`,
      
      bug: `Problème identifié: ${task.title}
      
Étapes de reproduction:
1. [À définir]
2. [À définir]
3. [À définir]

Résultat attendu: [À définir]
Résultat actuel: [À définir]

Impact: ${task.priority === 'critical' ? 'Critique - bloque les utilisateurs' : 'Modéré'}`,
      
      task: `Tâche: ${task.title}
      
Objectif: [À définir]
Livrables: [À définir]
Ressources nécessaires: [À définir]`
    };
    
    return templates[task.category] || templates.task;
  }
};

// ==================== INTÉGRATION IA ====================

// Analyser une tâche avec l'IA
exports.analyzeTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès à cette tâche" 
      });
    }

    // Analyser avec l'IA
    const suggestions = await AIService.analyzeTask(task);

    // Mettre à jour la tâche avec les suggestions
    task.aiSuggestions.suggestions = suggestions;
    task.aiSuggestions.lastAnalysis = new Date();
    await task.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      'task_ai_analyzed',
      `Tâche "${task.title}" analysée par l'IA`,
      {
        affectedUser: userId,
        newData: { suggestionsCount: suggestions.length },
        category: 'task'
      }
    );

    logger.info(`Tâche ${task.title} analysée par l'IA pour ${req.user.fullName}`);

    res.json({
      message: "Analyse IA terminée",
      task,
      suggestions
    });
  } catch (error) {
    logger.error('Erreur analyse IA tâche:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'analyse IA", 
      error: error.message 
    });
  }
};

// Générer des sous-tâches avec l'IA
exports.generateSubtasks = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour modifier cette tâche" 
      });
    }

    // Générer les sous-tâches
    const subtaskTemplates = await AIService.generateSubtasks(task);
    const createdSubtasks = [];

    // Créer les sous-tâches
    for (const [index, subtaskData] of subtaskTemplates.entries()) {
      const subtask = new Task({
        title: subtaskData.title,
        description: subtaskData.description,
        status: 'todo',
        priority: task.priority,
        teamId: task.teamId,
        parentTask: task._id,
        createdBy: userId,
        assignedTo: task.assignedTo,
        position: index,
        category: 'task',
        aiSuggestions: {
          autoGenerated: {
            description: true,
            subtasks: false,
            estimation: false
          }
        }
      });

      await subtask.save();
      createdSubtasks.push(subtask);
    }

    // Mettre à jour la tâche parent
    task.subtasks = createdSubtasks.map(st => st._id);
    task.aiSuggestions.autoGenerated.subtasks = true;
    await task.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      'subtasks_generated',
      `${createdSubtasks.length} sous-tâches générées pour "${task.title}"`,
      {
        affectedUser: userId,
        newData: { subtasksCount: createdSubtasks.length },
        category: 'task'
      }
    );

    await task.populate('subtasks');

    logger.info(`${createdSubtasks.length} sous-tâches générées pour ${task.title} par ${req.user.fullName}`);

    res.status(201).json({
      message: "Sous-tâches générées avec succès",
      task,
      subtasks: createdSubtasks
    });
  } catch (error) {
    logger.error('Erreur génération sous-tâches:', error);
    res.status(500).json({ 
      message: "Erreur lors de la génération des sous-tâches", 
      error: error.message 
    });
  }
};

// Estimer l'effort avec l'IA
exports.estimateEffort = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas accès à cette tâche" 
      });
    }

    // Estimer l'effort
    const estimatedHours = await AIService.estimateEffort(task);

    // Mettre à jour la tâche
    task.estimatedHours = estimatedHours;
    task.aiSuggestions.autoGenerated.estimation = true;
    await task.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      'task_effort_estimated',
      `Effort estimé pour "${task.title}": ${estimatedHours}h`,
      {
        affectedUser: userId,
        newData: { estimatedHours },
        category: 'task'
      }
    );

    logger.info(`Effort estimé pour ${task.title}: ${estimatedHours}h par ${req.user.fullName}`);

    res.json({
      message: "Effort estimé avec succès",
      task,
      estimatedHours
    });
  } catch (error) {
    logger.error('Erreur estimation effort:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'estimation d'effort", 
      error: error.message 
    });
  }
};

// Générer une description avec l'IA
exports.generateDescription = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour modifier cette tâche" 
      });
    }

    // Générer la description
    const generatedDescription = await AIService.generateDescription(task);

    // Mettre à jour la tâche
    task.description = generatedDescription;
    task.aiSuggestions.autoGenerated.description = true;
    await task.save();

    // Enregistrer dans l'historique
    await TeamHistory.logAction(
      task.teamId,
      userId,
      'task_description_generated',
      `Description générée pour "${task.title}"`,
      {
        affectedUser: userId,
        newData: { descriptionLength: generatedDescription.length },
        category: 'task'
      }
    );

    logger.info(`Description générée pour ${task.title} par ${req.user.fullName}`);

    res.json({
      message: "Description générée avec succès",
      task,
      description: generatedDescription
    });
  } catch (error) {
    logger.error('Erreur génération description:', error);
    res.status(500).json({ 
      message: "Erreur lors de la génération de description", 
      error: error.message 
    });
  }
};

// Appliquer une suggestion IA
exports.applySuggestion = async (req, res) => {
  try {
    const { taskId, suggestionIndex } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: "Tâche non trouvée" });
    }

    // Vérifier les permissions
    const team = await Team.findById(task.teamId);
    if (!team.isMember(userId)) {
      return res.status(403).json({ 
        message: "Vous n'avez pas les permissions pour modifier cette tâche" 
      });
    }

    const suggestion = task.aiSuggestions.suggestions[suggestionIndex];
    if (!suggestion) {
      return res.status(404).json({ message: "Suggestion non trouvée" });
    }

    // Appliquer la suggestion selon son type
    let applied = false;
    switch (suggestion.type) {
      case 'priority':
        if (task.category === 'bug' && task.priority === 'low') {
          task.priority = 'medium';
          applied = true;
        }
        break;
      case 'deadline':
        if (!task.dueDate && task.priority === 'high') {
          task.dueDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 jours
          applied = true;
        }
        break;
      // Ajouter d'autres types de suggestions...
    }

    if (applied) {
      suggestion.applied = true;
      await task.save();

      // Enregistrer dans l'historique
      await TeamHistory.logAction(
        task.teamId,
        userId,
        'ai_suggestion_applied',
        `Suggestion IA appliquée pour "${task.title}": ${suggestion.content}`,
        {
          affectedUser: userId,
          newData: { suggestionType: suggestion.type },
          category: 'task'
        }
      );

      logger.info(`Suggestion IA appliquée pour ${task.title} par ${req.user.fullName}`);

      res.json({
        message: "Suggestion appliquée avec succès",
        task,
        suggestion
      });
    } else {
      res.status(400).json({
        message: "Cette suggestion ne peut pas être appliquée automatiquement"
      });
    }
  } catch (error) {
    logger.error('Erreur application suggestion:', error);
    res.status(500).json({ 
      message: "Erreur lors de l'application de la suggestion", 
      error: error.message 
    });
  }
};

module.exports = {
  analyzeTask: exports.analyzeTask,
  generateSubtasks: exports.generateSubtasks,
  estimateEffort: exports.estimateEffort,
  generateDescription: exports.generateDescription,
  applySuggestion: exports.applySuggestion
};
