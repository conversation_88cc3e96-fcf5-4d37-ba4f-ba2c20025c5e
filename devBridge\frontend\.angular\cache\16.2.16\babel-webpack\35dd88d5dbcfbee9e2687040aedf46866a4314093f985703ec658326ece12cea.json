{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { addDays } from \"./addDays.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDay} function options.\n */\n\n/**\n * @name setDay\n * @category Weekday Helpers\n * @summary Set the day of the week to the given date.\n *\n * @description\n * Set the day of the week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the week of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the day of the week set\n *\n * @example\n * // Set week day to Sunday, with the default weekStartsOn of Sunday:\n * const result = setDay(new Date(2014, 8, 1), 0)\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Set week day to Sunday, with a weekStartsOn of Monday:\n * const result = setDay(new Date(2014, 8, 1), 0, { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setDay(date, day, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  const date_ = toDate(date, options?.in);\n  const currentDay = date_.getDay();\n  const remainder = day % 7;\n  const dayIndex = (remainder + 7) % 7;\n  const delta = 7 - weekStartsOn;\n  const diff = day < 0 || day > 6 ? day - (currentDay + delta) % 7 : (dayIndex + delta) % 7 - (currentDay + delta) % 7;\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setDay;", "map": {"version": 3, "names": ["getDefaultOptions", "addDays", "toDate", "setDay", "date", "day", "options", "defaultOptions", "weekStartsOn", "locale", "date_", "in", "currentDay", "getDay", "remainder", "dayIndex", "delta", "diff"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/setDay.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { addDays } from \"./addDays.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDay} function options.\n */\n\n/**\n * @name setDay\n * @category Weekday Helpers\n * @summary Set the day of the week to the given date.\n *\n * @description\n * Set the day of the week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the week of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the day of the week set\n *\n * @example\n * // Set week day to Sunday, with the default weekStartsOn of Sunday:\n * const result = setDay(new Date(2014, 8, 1), 0)\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Set week day to Sunday, with a weekStartsOn of Monday:\n * const result = setDay(new Date(2014, 8, 1), 0, { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setDay(date, day, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const date_ = toDate(date, options?.in);\n  const currentDay = date_.getDay();\n\n  const remainder = day % 7;\n  const dayIndex = (remainder + 7) % 7;\n\n  const delta = 7 - weekStartsOn;\n  const diff =\n    day < 0 || day > 6\n      ? day - ((currentDay + delta) % 7)\n      : ((dayIndex + delta) % 7) - ((currentDay + delta) % 7);\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setDay;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACzC,MAAMC,cAAc,GAAGP,iBAAiB,CAAC,CAAC;EAC1C,MAAMQ,YAAY,GAChBF,OAAO,EAAEE,YAAY,IACrBF,OAAO,EAAEG,MAAM,EAAEH,OAAO,EAAEE,YAAY,IACtCD,cAAc,CAACC,YAAY,IAC3BD,cAAc,CAACE,MAAM,EAAEH,OAAO,EAAEE,YAAY,IAC5C,CAAC;EAEH,MAAME,KAAK,GAAGR,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEK,EAAE,CAAC;EACvC,MAAMC,UAAU,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC;EAEjC,MAAMC,SAAS,GAAGT,GAAG,GAAG,CAAC;EACzB,MAAMU,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAI,CAAC;EAEpC,MAAME,KAAK,GAAG,CAAC,GAAGR,YAAY;EAC9B,MAAMS,IAAI,GACRZ,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,GACdA,GAAG,GAAI,CAACO,UAAU,GAAGI,KAAK,IAAI,CAAE,GAC/B,CAACD,QAAQ,GAAGC,KAAK,IAAI,CAAC,GAAK,CAACJ,UAAU,GAAGI,KAAK,IAAI,CAAE;EAC3D,OAAOf,OAAO,CAACS,KAAK,EAAEO,IAAI,EAAEX,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}