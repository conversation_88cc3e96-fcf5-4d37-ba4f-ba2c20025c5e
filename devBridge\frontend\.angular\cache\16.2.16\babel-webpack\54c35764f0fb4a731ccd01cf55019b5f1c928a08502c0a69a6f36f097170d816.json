{"ast": null, "code": "export default function symbolObservablePonyfill(root) {\n  var result;\n  var Symbol = root.Symbol;\n  if (typeof Symbol === 'function') {\n    if (Symbol.observable) {\n      result = Symbol.observable;\n    } else {\n      if (typeof Symbol.for === 'function') {\n        // This just needs to be something that won't trample other user's Symbol.for use\n        // It also will guide people to the source of their issues, if this is problematic.\n        // META: It's a resource locator!\n        result = Symbol.for('https://github.com/benlesh/symbol-observable');\n      } else {\n        // Symbol.for didn't exist! The best we can do at this point is a totally \n        // unique symbol. Note that the string argument here is a descriptor, not\n        // an identifier. This symbol is unique.\n        result = Symbol('https://github.com/benlesh/symbol-observable');\n      }\n      try {\n        Symbol.observable = result;\n      } catch (err) {\n        // Do nothing. In some environments, users have frozen `Symbol` for security reasons,\n        // if it is frozen assigning to it will throw. In this case, we don't care, because\n        // they will need to use the returned value from the ponyfill.\n      }\n    }\n  } else {\n    result = '@@observable';\n  }\n  return result;\n}\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}