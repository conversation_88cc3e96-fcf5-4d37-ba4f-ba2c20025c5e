{"ast": null, "code": "const {\n  toString,\n  hasOwnProperty\n} = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nexport function equal(a, b) {\n  try {\n    return check(a, b);\n  } finally {\n    previousComparisons.clear();\n  }\n}\n// Allow default imports as well.\nexport default equal;\nfunction check(a, b) {\n  // If the two values are strictly equal, our job is easy.\n  if (a === b) {\n    return true;\n  }\n  // Object.prototype.toString returns a representation of the runtime type of\n  // the given value that is considerably more precise than typeof.\n  const aTag = toString.call(a);\n  const bTag = toString.call(b);\n  // If the runtime types of a and b are different, they could maybe be equal\n  // under some interpretation of equality, but for simplicity and performance\n  // we just return false instead.\n  if (aTag !== bTag) {\n    return false;\n  }\n  switch (aTag) {\n    case '[object Array]':\n      // Arrays are a lot like other objects, but we can cheaply compare their\n      // lengths as a short-cut before comparing their elements.\n      if (a.length !== b.length) return false;\n    // Fall through to object case...\n    case '[object Object]':\n      {\n        if (previouslyCompared(a, b)) return true;\n        const aKeys = definedKeys(a);\n        const bKeys = definedKeys(b);\n        // If `a` and `b` have a different number of enumerable keys, they\n        // must be different.\n        const keyCount = aKeys.length;\n        if (keyCount !== bKeys.length) return false;\n        // Now make sure they have the same keys.\n        for (let k = 0; k < keyCount; ++k) {\n          if (!hasOwnProperty.call(b, aKeys[k])) {\n            return false;\n          }\n        }\n        // Finally, check deep equality of all child properties.\n        for (let k = 0; k < keyCount; ++k) {\n          const key = aKeys[k];\n          if (!check(a[key], b[key])) {\n            return false;\n          }\n        }\n        return true;\n      }\n    case '[object Error]':\n      return a.name === b.name && a.message === b.message;\n    case '[object Number]':\n      // Handle NaN, which is !== itself.\n      if (a !== a) return b !== b;\n    // Fall through to shared +a === +b case...\n    case '[object Boolean]':\n    case '[object Date]':\n      return +a === +b;\n    case '[object RegExp]':\n    case '[object String]':\n      return a == `${b}`;\n    case '[object Map]':\n    case '[object Set]':\n      {\n        if (a.size !== b.size) return false;\n        if (previouslyCompared(a, b)) return true;\n        const aIterator = a.entries();\n        const isMap = aTag === '[object Map]';\n        while (true) {\n          const info = aIterator.next();\n          if (info.done) break;\n          // If a instanceof Set, aValue === aKey.\n          const [aKey, aValue] = info.value;\n          // So this works the same way for both Set and Map.\n          if (!b.has(aKey)) {\n            return false;\n          }\n          // However, we care about deep equality of values only when dealing\n          // with Map structures.\n          if (isMap && !check(aValue, b.get(aKey))) {\n            return false;\n          }\n        }\n        return true;\n      }\n    case '[object Uint16Array]':\n    case '[object Uint8Array]': // Buffer, in Node.js.\n    case '[object Uint32Array]':\n    case '[object Int32Array]':\n    case '[object Int8Array]':\n    case '[object Int16Array]':\n    case '[object ArrayBuffer]':\n      // DataView doesn't need these conversions, but the equality check is\n      // otherwise the same.\n      a = new Uint8Array(a);\n      b = new Uint8Array(b);\n    // Fall through...\n    case '[object DataView]':\n      {\n        let len = a.byteLength;\n        if (len === b.byteLength) {\n          while (len-- && a[len] === b[len]) {\n            // Keep looping as long as the bytes are equal.\n          }\n        }\n        return len === -1;\n      }\n    case '[object AsyncFunction]':\n    case '[object GeneratorFunction]':\n    case '[object AsyncGeneratorFunction]':\n    case '[object Function]':\n      {\n        const aCode = fnToStr.call(a);\n        if (aCode !== fnToStr.call(b)) {\n          return false;\n        }\n        // We consider non-native functions equal if they have the same code\n        // (native functions require === because their code is censored).\n        // Note that this behavior is not entirely sound, since !== function\n        // objects with the same code can behave differently depending on\n        // their closure scope. However, any function can behave differently\n        // depending on the values of its input arguments (including this)\n        // and its calling context (including its closure scope), even\n        // though the function object is === to itself; and it is entirely\n        // possible for functions that are not === to behave exactly the\n        // same under all conceivable circumstances. Because none of these\n        // factors are statically decidable in JavaScript, JS function\n        // equality is not well-defined. This ambiguity allows us to\n        // consider the best possible heuristic among various imperfect\n        // options, and equating non-native functions that have the same\n        // code has enormous practical benefits, such as when comparing\n        // functions that are repeatedly passed as fresh function\n        // expressions within objects that are otherwise deeply equal. Since\n        // any function created from the same syntactic expression (in the\n        // same code location) will always stringify to the same code\n        // according to fnToStr.call, we can reasonably expect these\n        // repeatedly passed function expressions to have the same code, and\n        // thus behave \"the same\" (with all the caveats mentioned above),\n        // even though the runtime function objects are !== to one another.\n        return !endsWith(aCode, nativeCodeSuffix);\n      }\n  }\n  // Otherwise the values are not equal.\n  return false;\n}\nfunction definedKeys(obj) {\n  // Remember that the second argument to Array.prototype.filter will be\n  // used as `this` within the callback function.\n  return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n  return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n  const fromIndex = full.length - suffix.length;\n  return fromIndex >= 0 && full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n  // Though cyclic references can make an object graph appear infinite from the\n  // perspective of a depth-first traversal, the graph still contains a finite\n  // number of distinct object references. We use the previousComparisons cache\n  // to avoid comparing the same pair of object references more than once, which\n  // guarantees termination (even if we end up comparing every object in one\n  // graph to every object in the other graph, which is extremely unlikely),\n  // while still allowing weird isomorphic structures (like rings with different\n  // lengths) a chance to pass the equality test.\n  let bSet = previousComparisons.get(a);\n  if (bSet) {\n    // Return true here because we can be sure false will be returned somewhere\n    // else if the objects are not equivalent.\n    if (bSet.has(b)) return true;\n  } else {\n    previousComparisons.set(a, bSet = new Set());\n  }\n  bSet.add(b);\n  return false;\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}