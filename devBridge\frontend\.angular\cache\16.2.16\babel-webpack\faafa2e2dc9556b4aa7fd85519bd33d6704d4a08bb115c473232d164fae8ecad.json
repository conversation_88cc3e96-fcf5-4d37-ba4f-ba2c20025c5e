{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../../language/ast.mjs';\nimport { DirectiveLocation } from '../../language/directiveLocation.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known directives\n *\n * A GraphQL document is only valid if all `@directives` are known by the\n * schema and legally positioned.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Defined\n */\nexport function KnownDirectivesRule(context) {\n  const locationsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    locationsMap[directive.name] = directive.locations;\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      locationsMap[def.name.value] = def.locations.map(name => name.value);\n    }\n  }\n  return {\n    Directive(node, _key, _parent, _path, ancestors) {\n      const name = node.name.value;\n      const locations = locationsMap[name];\n      if (!locations) {\n        context.reportError(new GraphQLError(`Unknown directive \"@${name}\".`, {\n          nodes: node\n        }));\n        return;\n      }\n      const candidateLocation = getDirectiveLocationForASTPath(ancestors);\n      if (candidateLocation && !locations.includes(candidateLocation)) {\n        context.reportError(new GraphQLError(`Directive \"@${name}\" may not be used on ${candidateLocation}.`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nfunction getDirectiveLocationForASTPath(ancestors) {\n  const appliedTo = ancestors[ancestors.length - 1];\n  'kind' in appliedTo || invariant(false);\n  switch (appliedTo.kind) {\n    case Kind.OPERATION_DEFINITION:\n      return getDirectiveLocationForOperation(appliedTo.operation);\n    case Kind.FIELD:\n      return DirectiveLocation.FIELD;\n    case Kind.FRAGMENT_SPREAD:\n      return DirectiveLocation.FRAGMENT_SPREAD;\n    case Kind.INLINE_FRAGMENT:\n      return DirectiveLocation.INLINE_FRAGMENT;\n    case Kind.FRAGMENT_DEFINITION:\n      return DirectiveLocation.FRAGMENT_DEFINITION;\n    case Kind.VARIABLE_DEFINITION:\n      return DirectiveLocation.VARIABLE_DEFINITION;\n    case Kind.SCHEMA_DEFINITION:\n    case Kind.SCHEMA_EXTENSION:\n      return DirectiveLocation.SCHEMA;\n    case Kind.SCALAR_TYPE_DEFINITION:\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return DirectiveLocation.SCALAR;\n    case Kind.OBJECT_TYPE_DEFINITION:\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.OBJECT;\n    case Kind.FIELD_DEFINITION:\n      return DirectiveLocation.FIELD_DEFINITION;\n    case Kind.INTERFACE_TYPE_DEFINITION:\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return DirectiveLocation.INTERFACE;\n    case Kind.UNION_TYPE_DEFINITION:\n    case Kind.UNION_TYPE_EXTENSION:\n      return DirectiveLocation.UNION;\n    case Kind.ENUM_TYPE_DEFINITION:\n    case Kind.ENUM_TYPE_EXTENSION:\n      return DirectiveLocation.ENUM;\n    case Kind.ENUM_VALUE_DEFINITION:\n      return DirectiveLocation.ENUM_VALUE;\n    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.INPUT_OBJECT;\n    case Kind.INPUT_VALUE_DEFINITION:\n      {\n        const parentNode = ancestors[ancestors.length - 3];\n        'kind' in parentNode || invariant(false);\n        return parentNode.kind === Kind.INPUT_OBJECT_TYPE_DEFINITION ? DirectiveLocation.INPUT_FIELD_DEFINITION : DirectiveLocation.ARGUMENT_DEFINITION;\n      }\n    // Not reachable, all possible types have been considered.\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(appliedTo.kind));\n  }\n}\nfunction getDirectiveLocationForOperation(operation) {\n  switch (operation) {\n    case OperationTypeNode.QUERY:\n      return DirectiveLocation.QUERY;\n    case OperationTypeNode.MUTATION:\n      return DirectiveLocation.MUTATION;\n    case OperationTypeNode.SUBSCRIPTION:\n      return DirectiveLocation.SUBSCRIPTION;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}