{"ast": null, "code": "export { disableWarningsSlot } from \"./utils.js\";\nexport { maskFragment } from \"./maskFragment.js\";\nexport { maskOperation } from \"./maskOperation.js\";", "map": {"version": 3, "names": ["disableWarningsSlot", "maskFragment", "maskOperation"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@apollo/client/masking/index.js"], "sourcesContent": ["export { disableWarningsSlot } from \"./utils.js\";\nexport { maskFragment } from \"./maskFragment.js\";\nexport { maskOperation } from \"./maskOperation.js\";\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,YAAY;AAChD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,aAAa,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}