{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { collectFields } from '../../execution/collectFields.mjs';\n\n/**\n * Subscriptions must only include a non-introspection field.\n *\n * A GraphQL subscription is valid only if it contains a single root field and\n * that root field is not an introspection field.\n *\n * See https://spec.graphql.org/draft/#sec-Single-root-field\n */\nexport function SingleFieldSubscriptionsRule(context) {\n  return {\n    OperationDefinition(node) {\n      if (node.operation === 'subscription') {\n        const schema = context.getSchema();\n        const subscriptionType = schema.getSubscriptionType();\n        if (subscriptionType) {\n          const operationName = node.name ? node.name.value : null;\n          const variableValues = Object.create(null);\n          const document = context.getDocument();\n          const fragments = Object.create(null);\n          for (const definition of document.definitions) {\n            if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n              fragments[definition.name.value] = definition;\n            }\n          }\n          const fields = collectFields(schema, fragments, variableValues, subscriptionType, node.selectionSet);\n          if (fields.size > 1) {\n            const fieldSelectionLists = [...fields.values()];\n            const extraFieldSelectionLists = fieldSelectionLists.slice(1);\n            const extraFieldSelections = extraFieldSelectionLists.flat();\n            context.reportError(new GraphQLError(operationName != null ? `Subscription \"${operationName}\" must select only one top level field.` : 'Anonymous Subscription must select only one top level field.', {\n              nodes: extraFieldSelections\n            }));\n          }\n          for (const fieldNodes of fields.values()) {\n            const field = fieldNodes[0];\n            const fieldName = field.name.value;\n            if (fieldName.startsWith('__')) {\n              context.reportError(new GraphQLError(operationName != null ? `Subscription \"${operationName}\" must not select an introspection top level field.` : 'Anonymous Subscription must not select an introspection top level field.', {\n                nodes: fieldNodes\n              }));\n            }\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "Kind", "collectFields", "SingleFieldSubscriptionsRule", "context", "OperationDefinition", "node", "operation", "schema", "getSchema", "subscriptionType", "getSubscriptionType", "operationName", "name", "value", "variableValues", "Object", "create", "document", "getDocument", "fragments", "definition", "definitions", "kind", "FRAGMENT_DEFINITION", "fields", "selectionSet", "size", "fieldSelectionLists", "values", "extraFieldSelectionLists", "slice", "extraFieldSelections", "flat", "reportError", "nodes", "fieldNodes", "field", "fieldName", "startsWith"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/SingleFieldSubscriptionsRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { collectFields } from '../../execution/collectFields.mjs';\n\n/**\n * Subscriptions must only include a non-introspection field.\n *\n * A GraphQL subscription is valid only if it contains a single root field and\n * that root field is not an introspection field.\n *\n * See https://spec.graphql.org/draft/#sec-Single-root-field\n */\nexport function SingleFieldSubscriptionsRule(context) {\n  return {\n    OperationDefinition(node) {\n      if (node.operation === 'subscription') {\n        const schema = context.getSchema();\n        const subscriptionType = schema.getSubscriptionType();\n\n        if (subscriptionType) {\n          const operationName = node.name ? node.name.value : null;\n          const variableValues = Object.create(null);\n          const document = context.getDocument();\n          const fragments = Object.create(null);\n\n          for (const definition of document.definitions) {\n            if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n              fragments[definition.name.value] = definition;\n            }\n          }\n\n          const fields = collectFields(\n            schema,\n            fragments,\n            variableValues,\n            subscriptionType,\n            node.selectionSet,\n          );\n\n          if (fields.size > 1) {\n            const fieldSelectionLists = [...fields.values()];\n            const extraFieldSelectionLists = fieldSelectionLists.slice(1);\n            const extraFieldSelections = extraFieldSelectionLists.flat();\n            context.reportError(\n              new GraphQLError(\n                operationName != null\n                  ? `Subscription \"${operationName}\" must select only one top level field.`\n                  : 'Anonymous Subscription must select only one top level field.',\n                {\n                  nodes: extraFieldSelections,\n                },\n              ),\n            );\n          }\n\n          for (const fieldNodes of fields.values()) {\n            const field = fieldNodes[0];\n            const fieldName = field.name.value;\n\n            if (fieldName.startsWith('__')) {\n              context.reportError(\n                new GraphQLError(\n                  operationName != null\n                    ? `Subscription \"${operationName}\" must not select an introspection top level field.`\n                    : 'Anonymous Subscription must not select an introspection top level field.',\n                  {\n                    nodes: fieldNodes,\n                  },\n                ),\n              );\n            }\n          }\n        }\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,aAAa,QAAQ,mCAAmC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,OAAO,EAAE;EACpD,OAAO;IACLC,mBAAmBA,CAACC,IAAI,EAAE;MACxB,IAAIA,IAAI,CAACC,SAAS,KAAK,cAAc,EAAE;QACrC,MAAMC,MAAM,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;QAClC,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,mBAAmB,CAAC,CAAC;QAErD,IAAID,gBAAgB,EAAE;UACpB,MAAME,aAAa,GAAGN,IAAI,CAACO,IAAI,GAAGP,IAAI,CAACO,IAAI,CAACC,KAAK,GAAG,IAAI;UACxD,MAAMC,cAAc,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;UAC1C,MAAMC,QAAQ,GAAGd,OAAO,CAACe,WAAW,CAAC,CAAC;UACtC,MAAMC,SAAS,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;UAErC,KAAK,MAAMI,UAAU,IAAIH,QAAQ,CAACI,WAAW,EAAE;YAC7C,IAAID,UAAU,CAACE,IAAI,KAAKtB,IAAI,CAACuB,mBAAmB,EAAE;cAChDJ,SAAS,CAACC,UAAU,CAACR,IAAI,CAACC,KAAK,CAAC,GAAGO,UAAU;YAC/C;UACF;UAEA,MAAMI,MAAM,GAAGvB,aAAa,CAC1BM,MAAM,EACNY,SAAS,EACTL,cAAc,EACdL,gBAAgB,EAChBJ,IAAI,CAACoB,YACP,CAAC;UAED,IAAID,MAAM,CAACE,IAAI,GAAG,CAAC,EAAE;YACnB,MAAMC,mBAAmB,GAAG,CAAC,GAAGH,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC;YAChD,MAAMC,wBAAwB,GAAGF,mBAAmB,CAACG,KAAK,CAAC,CAAC,CAAC;YAC7D,MAAMC,oBAAoB,GAAGF,wBAAwB,CAACG,IAAI,CAAC,CAAC;YAC5D7B,OAAO,CAAC8B,WAAW,CACjB,IAAIlC,YAAY,CACdY,aAAa,IAAI,IAAI,GAChB,iBAAgBA,aAAc,yCAAwC,GACvE,8DAA8D,EAClE;cACEuB,KAAK,EAAEH;YACT,CACF,CACF,CAAC;UACH;UAEA,KAAK,MAAMI,UAAU,IAAIX,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE;YACxC,MAAMQ,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;YAC3B,MAAME,SAAS,GAAGD,KAAK,CAACxB,IAAI,CAACC,KAAK;YAElC,IAAIwB,SAAS,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;cAC9BnC,OAAO,CAAC8B,WAAW,CACjB,IAAIlC,YAAY,CACdY,aAAa,IAAI,IAAI,GAChB,iBAAgBA,aAAc,qDAAoD,GACnF,0EAA0E,EAC9E;gBACEuB,KAAK,EAAEC;cACT,CACF,CACF,CAAC;YACH;UACF;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}