{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique type names\n *\n * A GraphQL document is only valid if all defined types have unique names.\n */\nexport function UniqueTypeNamesRule(context) {\n  const knownTypeNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    ScalarTypeDefinition: checkTypeName,\n    ObjectTypeDefinition: checkTypeName,\n    InterfaceTypeDefinition: checkTypeName,\n    UnionTypeDefinition: checkTypeName,\n    EnumTypeDefinition: checkTypeName,\n    InputObjectTypeDefinition: checkTypeName\n  };\n  function checkTypeName(node) {\n    const typeName = node.name.value;\n    if (schema !== null && schema !== void 0 && schema.getType(typeName)) {\n      context.reportError(new GraphQLError(`Type \"${typeName}\" already exists in the schema. It cannot also be defined in this type definition.`, {\n        nodes: node.name\n      }));\n      return;\n    }\n    if (knownTypeNames[typeName]) {\n      context.reportError(new GraphQLError(`There can be only one type named \"${typeName}\".`, {\n        nodes: [knownTypeNames[typeName], node.name]\n      }));\n    } else {\n      knownTypeNames[typeName] = node.name;\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["GraphQLError", "UniqueTypeNamesRule", "context", "knownTypeNames", "Object", "create", "schema", "getSchema", "ScalarTypeDefinition", "checkTypeName", "ObjectTypeDefinition", "InterfaceTypeDefinition", "UnionTypeDefinition", "EnumTypeDefinition", "InputObjectTypeDefinition", "node", "typeName", "name", "value", "getType", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/graphql/validation/rules/UniqueTypeNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique type names\n *\n * A GraphQL document is only valid if all defined types have unique names.\n */\nexport function UniqueTypeNamesRule(context) {\n  const knownTypeNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    ScalarTypeDefinition: checkTypeName,\n    ObjectTypeDefinition: checkTypeName,\n    InterfaceTypeDefinition: checkTypeName,\n    UnionTypeDefinition: checkTypeName,\n    EnumTypeDefinition: checkTypeName,\n    InputObjectTypeDefinition: checkTypeName,\n  };\n\n  function checkTypeName(node) {\n    const typeName = node.name.value;\n\n    if (schema !== null && schema !== void 0 && schema.getType(typeName)) {\n      context.reportError(\n        new GraphQLError(\n          `Type \"${typeName}\" already exists in the schema. It cannot also be defined in this type definition.`,\n          {\n            nodes: node.name,\n          },\n        ),\n      );\n      return;\n    }\n\n    if (knownTypeNames[typeName]) {\n      context.reportError(\n        new GraphQLError(`There can be only one type named \"${typeName}\".`, {\n          nodes: [knownTypeNames[typeName], node.name],\n        }),\n      );\n    } else {\n      knownTypeNames[typeName] = node.name;\n    }\n\n    return false;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAC3C,MAAMC,cAAc,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMC,MAAM,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;EAClC,OAAO;IACLC,oBAAoB,EAAEC,aAAa;IACnCC,oBAAoB,EAAED,aAAa;IACnCE,uBAAuB,EAAEF,aAAa;IACtCG,mBAAmB,EAAEH,aAAa;IAClCI,kBAAkB,EAAEJ,aAAa;IACjCK,yBAAyB,EAAEL;EAC7B,CAAC;EAED,SAASA,aAAaA,CAACM,IAAI,EAAE;IAC3B,MAAMC,QAAQ,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;IAEhC,IAAIZ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACa,OAAO,CAACH,QAAQ,CAAC,EAAE;MACpEd,OAAO,CAACkB,WAAW,CACjB,IAAIpB,YAAY,CACb,SAAQgB,QAAS,oFAAmF,EACrG;QACEK,KAAK,EAAEN,IAAI,CAACE;MACd,CACF,CACF,CAAC;MACD;IACF;IAEA,IAAId,cAAc,CAACa,QAAQ,CAAC,EAAE;MAC5Bd,OAAO,CAACkB,WAAW,CACjB,IAAIpB,YAAY,CAAE,qCAAoCgB,QAAS,IAAG,EAAE;QAClEK,KAAK,EAAE,CAAClB,cAAc,CAACa,QAAQ,CAAC,EAAED,IAAI,CAACE,IAAI;MAC7C,CAAC,CACH,CAAC;IACH,CAAC,MAAM;MACLd,cAAc,CAACa,QAAQ,CAAC,GAAGD,IAAI,CAACE,IAAI;IACtC;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}