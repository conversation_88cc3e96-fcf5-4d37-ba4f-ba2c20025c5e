{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameMonth} function options.\n */\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return laterDate_.getFullYear() === earlierDate_.getFullYear() && laterDate_.getMonth() === earlierDate_.getMonth();\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}