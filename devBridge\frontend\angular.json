{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["subscriptions-transport-ws", "graphql-ws"], "outputPath": "dist/frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/lightbox2/dist/css/lightbox.min.css", "node_modules/angular-calendar/css/angular-calendar.css", "src/app/styles/conversation-user-lists.css", "src/app/styles/notifications.css", "src/app/styles/common-grid.css", "src/app/shared/styles/task-button-animations.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "node_modules/lightbox2/dist/js/lightbox.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1.5MB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "35KB", "maximumError": "50KB"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "frontend:build:production"}, "development": {"browserTarget": "frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}