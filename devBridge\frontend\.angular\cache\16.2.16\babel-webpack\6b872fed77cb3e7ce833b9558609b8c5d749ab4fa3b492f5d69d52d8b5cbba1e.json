{"ast": null, "code": "function getDef(f, d) {\n  if (typeof f === 'undefined') {\n    return typeof d === 'undefined' ? f : d;\n  }\n  return f;\n}\nfunction boolean(func, def) {\n  func = getDef(func, def);\n  if (typeof func === 'function') {\n    return function f() {\n      var arguments$1 = arguments;\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments$1[_key];\n      }\n      return !!func.apply(this, args);\n    };\n  }\n  return !!func ? function () {\n    return true;\n  } : function () {\n    return false;\n  };\n}\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\nfunction indexOfElement(elements, element) {\n  element = resolveElement(element, true);\n  if (!isElement$1(element)) {\n    return -1;\n  }\n  for (var i = 0; i < elements.length; i++) {\n    if (elements[i] === element) {\n      return i;\n    }\n  }\n  return -1;\n}\nfunction hasElement(elements, element) {\n  return -1 !== indexOfElement(elements, element);\n}\nfunction pushElements(elements, toAdd) {\n  for (var i = 0; i < toAdd.length; i++) {\n    if (!hasElement(elements, toAdd[i])) {\n      elements.push(toAdd[i]);\n    }\n  }\n  return toAdd;\n}\nfunction addElements(elements) {\n  var arguments$1 = arguments;\n  var toAdd = [],\n    len = arguments.length - 1;\n  while (len-- > 0) {\n    toAdd[len] = arguments$1[len + 1];\n  }\n  toAdd = toAdd.map(resolveElement);\n  return pushElements(elements, toAdd);\n}\nfunction removeElements(elements) {\n  var arguments$1 = arguments;\n  var toRemove = [],\n    len = arguments.length - 1;\n  while (len-- > 0) {\n    toRemove[len] = arguments$1[len + 1];\n  }\n  return toRemove.map(resolveElement).reduce(function (last, e) {\n    var index = indexOfElement(elements, e);\n    if (index !== -1) {\n      return last.concat(elements.splice(index, 1));\n    }\n    return last;\n  }, []);\n}\nfunction resolveElement(element, noThrow) {\n  if (typeof element === 'string') {\n    try {\n      return document.querySelector(element);\n    } catch (e) {\n      throw e;\n    }\n  }\n  if (!isElement$1(element) && !noThrow) {\n    throw new TypeError(element + \" is not a DOM element.\");\n  }\n  return element;\n}\nfunction createPointCB(object, options) {\n  // A persistent object (as opposed to returned object) is used to save memory\n  // This is good to prevent layout thrashing, or for games, and such\n\n  // NOTE\n  // This uses IE fixes which should be OK to remove some day. :)\n  // Some speed will be gained by removal of these.\n\n  // pointCB should be saved in a variable on return\n  // This allows the usage of element.removeEventListener\n\n  options = options || {};\n  var allowUpdate = boolean(options.allowUpdate, true);\n\n  /*if(typeof options.allowUpdate === 'function'){\n      allowUpdate = options.allowUpdate;\n  }else{\n      allowUpdate = function(){return true;};\n  }*/\n\n  return function pointCB(event) {\n    event = event || window.event; // IE-ism\n    object.target = event.target || event.srcElement || event.originalTarget;\n    object.element = this;\n    object.type = event.type;\n    if (!allowUpdate(event)) {\n      return;\n    }\n\n    // Support touch\n    // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n    if (event.targetTouches) {\n      object.x = event.targetTouches[0].clientX;\n      object.y = event.targetTouches[0].clientY;\n      object.pageX = event.targetTouches[0].pageX;\n      object.pageY = event.targetTouches[0].pageY;\n      object.screenX = event.targetTouches[0].screenX;\n      object.screenY = event.targetTouches[0].screenY;\n    } else {\n      // If pageX/Y aren't available and clientX/Y are,\n      // calculate pageX/Y - logic taken from jQuery.\n      // (This is to support old IE)\n      // NOTE Hopefully this can be removed soon.\n\n      if (event.pageX === null && event.clientX !== null) {\n        var eventDoc = event.target && event.target.ownerDocument || document;\n        var doc = eventDoc.documentElement;\n        var body = eventDoc.body;\n        object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n        object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n      } else {\n        object.pageX = event.pageX;\n        object.pageY = event.pageY;\n      }\n\n      // pageX, and pageY change with page scroll\n      // so we're not going to use those for x, and y.\n      // NOTE Most browsers also alias clientX/Y with x/y\n      // so that's something to consider down the road.\n\n      object.x = event.clientX;\n      object.y = event.clientY;\n      object.screenX = event.screenX;\n      object.screenY = event.screenY;\n    }\n    object.clientX = object.x;\n    object.clientY = object.y;\n  };\n\n  //NOTE Remember accessibility, Aria roles, and labels.\n}\n\nfunction createWindowRect() {\n  var props = {\n    top: {\n      value: 0,\n      enumerable: true\n    },\n    left: {\n      value: 0,\n      enumerable: true\n    },\n    right: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    bottom: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    width: {\n      value: window.innerWidth,\n      enumerable: true\n    },\n    height: {\n      value: window.innerHeight,\n      enumerable: true\n    },\n    x: {\n      value: 0,\n      enumerable: true\n    },\n    y: {\n      value: 0,\n      enumerable: true\n    }\n  };\n  if (Object.create) {\n    return Object.create({}, props);\n  } else {\n    var rect = {};\n    Object.defineProperties(rect, props);\n    return rect;\n  }\n}\nfunction getClientRect(el) {\n  if (el === window) {\n    return createWindowRect();\n  } else {\n    try {\n      var rect = el.getBoundingClientRect();\n      if (rect.x === undefined) {\n        rect.x = rect.left;\n        rect.y = rect.top;\n      }\n      return rect;\n    } catch (e) {\n      throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n    }\n  }\n}\nfunction pointInside(point, el) {\n  var rect = getClientRect(el);\n  return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined$1) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined$1) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\nvar objectCreate$1 = objectCreate;\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\nfunction createDispatcher(element) {\n  var defaultSettings = {\n    screenX: 0,\n    screenY: 0,\n    clientX: 0,\n    clientY: 0,\n    ctrlKey: false,\n    shiftKey: false,\n    altKey: false,\n    metaKey: false,\n    button: 0,\n    buttons: 1,\n    relatedTarget: null,\n    region: null\n  };\n  if (element !== undefined) {\n    element.addEventListener('mousemove', onMove);\n  }\n  function onMove(e) {\n    for (var i = 0; i < mouseEventProps.length; i++) {\n      defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n    }\n  }\n  var dispatch = function () {\n    if (MouseEvent) {\n      return function m1(element, initMove, data) {\n        var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEvent === 'function') {\n      return function m2(element, initMove, data) {\n        var settings = createMoveInit(defaultSettings, initMove);\n        var evt = document.createEvent('MouseEvents');\n        evt.initMouseEvent(\"mousemove\", true,\n        //can bubble\n        true,\n        //cancelable\n        window,\n        //view\n        0,\n        //detail\n        settings.screenX,\n        //0, //screenX\n        settings.screenY,\n        //0, //screenY\n        settings.clientX,\n        //80, //clientX\n        settings.clientY,\n        //20, //clientY\n        settings.ctrlKey,\n        //false, //ctrlKey\n        settings.altKey,\n        //false, //altKey\n        settings.shiftKey,\n        //false, //shiftKey\n        settings.metaKey,\n        //false, //metaKey\n        settings.button,\n        //0, //button\n        settings.relatedTarget //null //relatedTarget\n        );\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    } else if (typeof document.createEventObject === 'function') {\n      return function m3(element, initMove, data) {\n        var evt = document.createEventObject();\n        var settings = createMoveInit(defaultSettings, initMove);\n        for (var name in settings) {\n          evt[name] = settings[name];\n        }\n\n        //evt.dispatched = 'mousemove';\n        setSpecial(evt, data);\n        return element.dispatchEvent(evt);\n      };\n    }\n  }();\n  function destroy() {\n    if (element) {\n      element.removeEventListener('mousemove', onMove, false);\n    }\n    defaultSettings = null;\n  }\n  return {\n    destroy: destroy,\n    dispatch: dispatch\n  };\n}\nfunction createMoveInit(defaultSettings, initMove) {\n  initMove = initMove || {};\n  var settings = objectCreate$1(defaultSettings);\n  for (var i = 0; i < mouseEventProps.length; i++) {\n    if (initMove[mouseEventProps[i]] !== undefined) {\n      settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n    }\n  }\n  return settings;\n}\nfunction setSpecial(e, data) {\n  console.log('data ', data);\n  e.data = data || {};\n  e.dispatched = 'mousemove';\n}\nvar prefix = ['webkit', 'moz', 'ms', 'o'];\nvar requestFrame = function () {\n  if (typeof window === \"undefined\") {\n    return function () {};\n  }\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n  if (!window.requestAnimationFrame) {\n    var lastTime = 0;\n    window.requestAnimationFrame = function (callback) {\n      var now = new Date().getTime();\n      var ttc = Math.max(0, 16 - now - lastTime);\n      var timer = window.setTimeout(function () {\n        return callback(now + ttc);\n      }, ttc);\n      lastTime = now + ttc;\n      return timer;\n    };\n  }\n  return window.requestAnimationFrame.bind(window);\n}();\nvar cancelFrame = function () {\n  if (typeof window === \"undefined\") {\n    return function () {};\n  }\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n  return window.cancelAnimationFrame.bind(window);\n}();\nfunction AutoScroller(elements, options) {\n  if (options === void 0) options = {};\n  var self = this;\n  var maxSpeed = 4,\n    scrolling = false;\n  if (typeof options.margin !== 'object') {\n    var margin = options.margin || -1;\n    this.margin = {\n      left: margin,\n      right: margin,\n      top: margin,\n      bottom: margin\n    };\n  } else {\n    this.margin = options.margin;\n  }\n\n  //this.scrolling = false;\n  this.scrollWhenOutside = options.scrollWhenOutside || false;\n  var point = {},\n    pointCB = createPointCB(point),\n    dispatcher = createDispatcher(),\n    down = false;\n  window.addEventListener('mousemove', pointCB, false);\n  window.addEventListener('touchmove', pointCB, false);\n  if (!isNaN(options.maxSpeed)) {\n    maxSpeed = options.maxSpeed;\n  }\n  if (typeof maxSpeed !== 'object') {\n    maxSpeed = {\n      left: maxSpeed,\n      right: maxSpeed,\n      top: maxSpeed,\n      bottom: maxSpeed\n    };\n  }\n  this.autoScroll = boolean(options.autoScroll);\n  this.syncMove = boolean(options.syncMove, false);\n  this.destroy = function (forceCleanAnimation) {\n    window.removeEventListener('mousemove', pointCB, false);\n    window.removeEventListener('touchmove', pointCB, false);\n    window.removeEventListener('mousedown', onDown, false);\n    window.removeEventListener('touchstart', onDown, false);\n    window.removeEventListener('mouseup', onUp, false);\n    window.removeEventListener('touchend', onUp, false);\n    window.removeEventListener('pointerup', onUp, false);\n    window.removeEventListener('mouseleave', onMouseOut, false);\n    window.removeEventListener('mousemove', onMove, false);\n    window.removeEventListener('touchmove', onMove, false);\n    window.removeEventListener('scroll', setScroll, true);\n    elements = [];\n    if (forceCleanAnimation) {\n      cleanAnimation();\n    }\n  };\n  this.add = function () {\n    var element = [],\n      len = arguments.length;\n    while (len--) element[len] = arguments[len];\n    addElements.apply(void 0, [elements].concat(element));\n    return this;\n  };\n  this.remove = function () {\n    var element = [],\n      len = arguments.length;\n    while (len--) element[len] = arguments[len];\n    return removeElements.apply(void 0, [elements].concat(element));\n  };\n  var hasWindow = null,\n    windowAnimationFrame;\n  if (Object.prototype.toString.call(elements) !== '[object Array]') {\n    elements = [elements];\n  }\n  (function (temp) {\n    elements = [];\n    temp.forEach(function (element) {\n      if (element === window) {\n        hasWindow = window;\n      } else {\n        self.add(element);\n      }\n    });\n  })(elements);\n  Object.defineProperties(this, {\n    down: {\n      get: function () {\n        return down;\n      }\n    },\n    maxSpeed: {\n      get: function () {\n        return maxSpeed;\n      }\n    },\n    point: {\n      get: function () {\n        return point;\n      }\n    },\n    scrolling: {\n      get: function () {\n        return scrolling;\n      }\n    }\n  });\n  var current = null,\n    animationFrame;\n  window.addEventListener('mousedown', onDown, false);\n  window.addEventListener('touchstart', onDown, false);\n  window.addEventListener('mouseup', onUp, false);\n  window.addEventListener('touchend', onUp, false);\n\n  /*\n  IE does not trigger mouseup event when scrolling.\n  It is a known issue that Microsoft won't fix.\n  https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n  IE supports pointer events instead\n  */\n  window.addEventListener('pointerup', onUp, false);\n  window.addEventListener('mousemove', onMove, false);\n  window.addEventListener('touchmove', onMove, false);\n  window.addEventListener('mouseleave', onMouseOut, false);\n  window.addEventListener('scroll', setScroll, true);\n  function setScroll(e) {\n    for (var i = 0; i < elements.length; i++) {\n      if (elements[i] === e.target) {\n        scrolling = true;\n        break;\n      }\n    }\n    if (scrolling) {\n      requestFrame(function () {\n        return scrolling = false;\n      });\n    }\n  }\n  function onDown() {\n    down = true;\n  }\n  function onUp() {\n    down = false;\n    cleanAnimation();\n  }\n  function cleanAnimation() {\n    cancelFrame(animationFrame);\n    cancelFrame(windowAnimationFrame);\n  }\n  function onMouseOut() {\n    down = false;\n  }\n  function getTarget(target) {\n    if (!target) {\n      return null;\n    }\n    if (current === target) {\n      return target;\n    }\n    if (hasElement(elements, target)) {\n      return target;\n    }\n    while (target = target.parentNode) {\n      if (hasElement(elements, target)) {\n        return target;\n      }\n    }\n    return null;\n  }\n  function getElementUnderPoint() {\n    var underPoint = null;\n    for (var i = 0; i < elements.length; i++) {\n      if (inside(point, elements[i])) {\n        underPoint = elements[i];\n      }\n    }\n    return underPoint;\n  }\n  function onMove(event) {\n    if (!self.autoScroll()) {\n      return;\n    }\n    if (event['dispatched']) {\n      return;\n    }\n    var target = event.target,\n      body = document.body;\n    if (current && !inside(point, current)) {\n      if (!self.scrollWhenOutside) {\n        current = null;\n      }\n    }\n    if (target && target.parentNode === body) {\n      //The special condition to improve speed.\n      target = getElementUnderPoint();\n    } else {\n      target = getTarget(target);\n      if (!target) {\n        target = getElementUnderPoint();\n      }\n    }\n    if (target && target !== current) {\n      current = target;\n    }\n    if (hasWindow) {\n      cancelFrame(windowAnimationFrame);\n      windowAnimationFrame = requestFrame(scrollWindow);\n    }\n    if (!current) {\n      return;\n    }\n    cancelFrame(animationFrame);\n    animationFrame = requestFrame(scrollTick);\n  }\n  function scrollWindow() {\n    autoScroll(hasWindow);\n    cancelFrame(windowAnimationFrame);\n    windowAnimationFrame = requestFrame(scrollWindow);\n  }\n  function scrollTick() {\n    if (!current) {\n      return;\n    }\n    autoScroll(current);\n    cancelFrame(animationFrame);\n    animationFrame = requestFrame(scrollTick);\n  }\n  function autoScroll(el) {\n    var rect = getClientRect(el),\n      scrollx,\n      scrolly;\n    if (point.x < rect.left + self.margin.left) {\n      scrollx = Math.floor(Math.max(-1, (point.x - rect.left) / self.margin.left - 1) * self.maxSpeed.left);\n    } else if (point.x > rect.right - self.margin.right) {\n      scrollx = Math.ceil(Math.min(1, (point.x - rect.right) / self.margin.right + 1) * self.maxSpeed.right);\n    } else {\n      scrollx = 0;\n    }\n    if (point.y < rect.top + self.margin.top) {\n      scrolly = Math.floor(Math.max(-1, (point.y - rect.top) / self.margin.top - 1) * self.maxSpeed.top);\n    } else if (point.y > rect.bottom - self.margin.bottom) {\n      scrolly = Math.ceil(Math.min(1, (point.y - rect.bottom) / self.margin.bottom + 1) * self.maxSpeed.bottom);\n    } else {\n      scrolly = 0;\n    }\n    if (self.syncMove()) {\n      /*\n      Notes about mousemove event dispatch.\n      screen(X/Y) should need to be updated.\n      Some other properties might need to be set.\n      Keep the syncMove option default false until all inconsistencies are taken care of.\n      */\n      dispatcher.dispatch(el, {\n        pageX: point.pageX + scrollx,\n        pageY: point.pageY + scrolly,\n        clientX: point.x + scrollx,\n        clientY: point.y + scrolly\n      });\n    }\n    setTimeout(function () {\n      if (scrolly) {\n        scrollY(el, scrolly);\n      }\n      if (scrollx) {\n        scrollX(el, scrollx);\n      }\n    });\n  }\n  function scrollY(el, amount) {\n    if (el === window) {\n      window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n    } else {\n      el.scrollTop += amount;\n    }\n  }\n  function scrollX(el, amount) {\n    if (el === window) {\n      window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n    } else {\n      el.scrollLeft += amount;\n    }\n  }\n}\nfunction AutoScrollerFactory(element, options) {\n  return new AutoScroller(element, options);\n}\nfunction inside(point, el, rect) {\n  if (!rect) {\n    return pointInside(point, el);\n  } else {\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n  }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n\nexport default AutoScrollerFactory;", "map": {"version": 3, "names": ["getDef", "f", "d", "boolean", "func", "def", "arguments$1", "arguments", "_len", "length", "args", "Array", "_key", "apply", "_typeof", "Symbol", "iterator", "obj", "constructor", "isElement$1", "input", "nodeType", "style", "ownerDocument", "indexOfElement", "elements", "element", "resolveElement", "i", "hasElement", "pushElements", "toAdd", "push", "addElements", "len", "map", "removeElements", "toRemove", "reduce", "last", "e", "index", "concat", "splice", "noThrow", "document", "querySelector", "TypeError", "createPointCB", "object", "options", "allowUpdate", "pointCB", "event", "window", "target", "srcElement", "originalTarget", "type", "targetTouches", "x", "clientX", "y", "clientY", "pageX", "pageY", "screenX", "screenY", "eventDoc", "doc", "documentElement", "body", "scrollLeft", "clientLeft", "scrollTop", "clientTop", "createWindowRect", "props", "top", "value", "enumerable", "left", "right", "innerWidth", "bottom", "innerHeight", "width", "height", "Object", "create", "rect", "defineProperties", "getClientRect", "el", "getBoundingClientRect", "undefined", "pointInside", "point", "objectCreate", "undefined$1", "Temp", "prototype", "propertiesObject", "result", "__proto__", "objectCreate$1", "mouseEventProps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultSettings", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "button", "buttons", "relatedTarget", "region", "addEventListener", "onMove", "dispatch", "MouseEvent", "m1", "initMove", "data", "evt", "createMoveInit", "setSpecial", "dispatchEvent", "createEvent", "m2", "settings", "initMouseEvent", "createEventObject", "m3", "name", "destroy", "removeEventListener", "console", "log", "dispatched", "prefix", "requestFrame", "limit", "requestAnimationFrame", "lastTime", "callback", "now", "Date", "getTime", "ttc", "Math", "max", "timer", "setTimeout", "bind", "cancelFrame", "cancelAnimationFrame", "clearTimeout", "AutoScroller", "self", "maxSpeed", "scrolling", "margin", "scrollWhenOutside", "dispatcher", "down", "isNaN", "autoScroll", "syncMove", "forceCleanAnimation", "onDown", "onUp", "onMouseOut", "setScroll", "cleanAnimation", "add", "remove", "hasW<PERSON>ow", "windowAnimationFrame", "toString", "call", "temp", "for<PERSON>ach", "get", "current", "animationFrame", "get<PERSON><PERSON><PERSON>", "parentNode", "getElementUnderPoint", "underPoint", "inside", "scrollWindow", "scrollTick", "scrollx", "scrolly", "floor", "ceil", "min", "scrollY", "scrollX", "amount", "scrollTo", "pageXOffset", "pageYOffset", "AutoScrollerFactory"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@mattlewis92/dom-autoscroller/dist/bundle.es.js"], "sourcesContent": ["function getDef(f, d) {\n    if (typeof f === 'undefined') {\n        return typeof d === 'undefined' ? f : d;\n    }\n\n    return f;\n}\nfunction boolean(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            var arguments$1 = arguments;\n\n            for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments$1[_key];\n            }\n\n            return !!func.apply(this, args);\n        };\n    }\n\n    return !!func ? function () {\n        return true;\n    } : function () {\n        return false;\n    };\n}\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\n\nfunction indexOfElement(elements, element){\n    element = resolveElement(element, true);\n    if(!isElement$1(element)) { return -1; }\n    for(var i=0; i<elements.length; i++){\n        if(elements[i] === element){\n            return i;\n        }\n    }\n    return -1;\n}\n\nfunction hasElement(elements, element){\n    return -1 !== indexOfElement(elements, element);\n}\n\nfunction pushElements(elements, toAdd){\n\n    for(var i=0; i<toAdd.length; i++){\n        if(!hasElement(elements, toAdd[i]))\n            { elements.push(toAdd[i]); }\n    }\n\n    return toAdd;\n}\n\nfunction addElements(elements){\n    var arguments$1 = arguments;\n\n    var toAdd = [], len = arguments.length - 1;\n    while ( len-- > 0 ) { toAdd[ len ] = arguments$1[ len + 1 ]; }\n\n    toAdd = toAdd.map(resolveElement);\n    return pushElements(elements, toAdd);\n}\n\nfunction removeElements(elements){\n    var arguments$1 = arguments;\n\n    var toRemove = [], len = arguments.length - 1;\n    while ( len-- > 0 ) { toRemove[ len ] = arguments$1[ len + 1 ]; }\n\n    return toRemove.map(resolveElement).reduce(function (last, e){\n\n        var index = indexOfElement(elements, e);\n\n        if(index !== -1)\n            { return last.concat(elements.splice(index, 1)); }\n        return last;\n    }, []);\n}\n\nfunction resolveElement(element, noThrow){\n    if(typeof element === 'string'){\n        try{\n            return document.querySelector(element);\n        }catch(e){\n            throw e;\n        }\n\n    }\n\n    if(!isElement$1(element) && !noThrow){\n        throw new TypeError((element + \" is not a DOM element.\"));\n    }\n    return element;\n}\n\nfunction createPointCB(object, options) {\n\n    // A persistent object (as opposed to returned object) is used to save memory\n    // This is good to prevent layout thrashing, or for games, and such\n\n    // NOTE\n    // This uses IE fixes which should be OK to remove some day. :)\n    // Some speed will be gained by removal of these.\n\n    // pointCB should be saved in a variable on return\n    // This allows the usage of element.removeEventListener\n\n    options = options || {};\n\n    var allowUpdate = boolean(options.allowUpdate, true);\n\n    /*if(typeof options.allowUpdate === 'function'){\n        allowUpdate = options.allowUpdate;\n    }else{\n        allowUpdate = function(){return true;};\n    }*/\n\n    return function pointCB(event) {\n\n        event = event || window.event; // IE-ism\n        object.target = event.target || event.srcElement || event.originalTarget;\n        object.element = this;\n        object.type = event.type;\n\n        if (!allowUpdate(event)) {\n            return;\n        }\n\n        // Support touch\n        // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n        if (event.targetTouches) {\n            object.x = event.targetTouches[0].clientX;\n            object.y = event.targetTouches[0].clientY;\n            object.pageX = event.targetTouches[0].pageX;\n            object.pageY = event.targetTouches[0].pageY;\n            object.screenX = event.targetTouches[0].screenX;\n            object.screenY = event.targetTouches[0].screenY;\n        } else {\n\n            // If pageX/Y aren't available and clientX/Y are,\n            // calculate pageX/Y - logic taken from jQuery.\n            // (This is to support old IE)\n            // NOTE Hopefully this can be removed soon.\n\n            if (event.pageX === null && event.clientX !== null) {\n                var eventDoc = event.target && event.target.ownerDocument || document;\n                var doc = eventDoc.documentElement;\n                var body = eventDoc.body;\n\n                object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n                object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n            } else {\n                object.pageX = event.pageX;\n                object.pageY = event.pageY;\n            }\n\n            // pageX, and pageY change with page scroll\n            // so we're not going to use those for x, and y.\n            // NOTE Most browsers also alias clientX/Y with x/y\n            // so that's something to consider down the road.\n\n            object.x = event.clientX;\n            object.y = event.clientY;\n\n            object.screenX = event.screenX;\n            object.screenY = event.screenY;\n        }\n\n        object.clientX = object.x;\n        object.clientY = object.y;\n    };\n\n    //NOTE Remember accessibility, Aria roles, and labels.\n}\n\nfunction createWindowRect() {\n    var props = {\n        top: { value: 0, enumerable: true },\n        left: { value: 0, enumerable: true },\n        right: { value: window.innerWidth, enumerable: true },\n        bottom: { value: window.innerHeight, enumerable: true },\n        width: { value: window.innerWidth, enumerable: true },\n        height: { value: window.innerHeight, enumerable: true },\n        x: { value: 0, enumerable: true },\n        y: { value: 0, enumerable: true }\n    };\n\n    if (Object.create) {\n        return Object.create({}, props);\n    } else {\n        var rect = {};\n        Object.defineProperties(rect, props);\n        return rect;\n    }\n}\n\nfunction getClientRect(el) {\n    if (el === window) {\n        return createWindowRect();\n    } else {\n        try {\n            var rect = el.getBoundingClientRect();\n            if (rect.x === undefined) {\n                rect.x = rect.left;\n                rect.y = rect.top;\n            }\n            return rect;\n        } catch (e) {\n            throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n        }\n    }\n}\n\nfunction pointInside(point, el) {\n    var rect = getClientRect(el);\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\n\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined$1) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined$1) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\n\nvar objectCreate$1 = objectCreate;\n\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\n\nfunction createDispatcher(element) {\n\n    var defaultSettings = {\n        screenX: 0,\n        screenY: 0,\n        clientX: 0,\n        clientY: 0,\n        ctrlKey: false,\n        shiftKey: false,\n        altKey: false,\n        metaKey: false,\n        button: 0,\n        buttons: 1,\n        relatedTarget: null,\n        region: null\n    };\n\n    if (element !== undefined) {\n        element.addEventListener('mousemove', onMove);\n    }\n\n    function onMove(e) {\n        for (var i = 0; i < mouseEventProps.length; i++) {\n            defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n        }\n    }\n\n    var dispatch = function () {\n        if (MouseEvent) {\n            return function m1(element, initMove, data) {\n                var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEvent === 'function') {\n            return function m2(element, initMove, data) {\n                var settings = createMoveInit(defaultSettings, initMove);\n                var evt = document.createEvent('MouseEvents');\n\n                evt.initMouseEvent(\"mousemove\", true, //can bubble\n                true, //cancelable\n                window, //view\n                0, //detail\n                settings.screenX, //0, //screenX\n                settings.screenY, //0, //screenY\n                settings.clientX, //80, //clientX\n                settings.clientY, //20, //clientY\n                settings.ctrlKey, //false, //ctrlKey\n                settings.altKey, //false, //altKey\n                settings.shiftKey, //false, //shiftKey\n                settings.metaKey, //false, //metaKey\n                settings.button, //0, //button\n                settings.relatedTarget //null //relatedTarget\n                );\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEventObject === 'function') {\n            return function m3(element, initMove, data) {\n                var evt = document.createEventObject();\n                var settings = createMoveInit(defaultSettings, initMove);\n                for (var name in settings) {\n                    evt[name] = settings[name];\n                }\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        }\n    }();\n\n    function destroy() {\n        if (element) { element.removeEventListener('mousemove', onMove, false); }\n        defaultSettings = null;\n    }\n\n    return {\n        destroy: destroy,\n        dispatch: dispatch\n    };\n}\n\nfunction createMoveInit(defaultSettings, initMove) {\n    initMove = initMove || {};\n    var settings = objectCreate$1(defaultSettings);\n    for (var i = 0; i < mouseEventProps.length; i++) {\n        if (initMove[mouseEventProps[i]] !== undefined) { settings[mouseEventProps[i]] = initMove[mouseEventProps[i]]; }\n    }\n\n    return settings;\n}\n\nfunction setSpecial(e, data) {\n    console.log('data ', data);\n    e.data = data || {};\n    e.dispatched = 'mousemove';\n}\n\nvar prefix = [ 'webkit', 'moz', 'ms', 'o' ];\n\nvar requestFrame = (function () {\n\n    if (typeof window === \"undefined\") {\n        return function () {};\n    }\n\n    for ( var i = 0, limit = prefix.length ; i < limit && ! window.requestAnimationFrame ; ++i ) {\n        window.requestAnimationFrame = window[ prefix[ i ] + 'RequestAnimationFrame' ];\n    }\n\n    if ( ! window.requestAnimationFrame ) {\n        var lastTime = 0;\n\n        window.requestAnimationFrame = function (callback) {\n            var now   = new Date().getTime();\n            var ttc   = Math.max( 0, 16 - now - lastTime );\n            var timer = window.setTimeout( function () { return callback( now + ttc ); }, ttc );\n\n            lastTime = now + ttc;\n\n            return timer;\n        };\n    }\n\n    return window.requestAnimationFrame.bind( window );\n})();\n\nvar cancelFrame = (function () {\n\n    if (typeof window === \"undefined\") {\n        return function () {};\n    }\n\n    for ( var i = 0, limit = prefix.length ; i < limit && ! window.cancelAnimationFrame ; ++i ) {\n        window.cancelAnimationFrame = window[ prefix[ i ] + 'CancelAnimationFrame' ] || window[ prefix[ i ] + 'CancelRequestAnimationFrame' ];\n    }\n\n    if ( ! window.cancelAnimationFrame ) {\n        window.cancelAnimationFrame = function (timer) {\n            window.clearTimeout( timer );\n        };\n    }\n\n    return window.cancelAnimationFrame.bind( window );\n})();\n\nfunction AutoScroller(elements, options){\n    if ( options === void 0 ) options = {};\n\n    var self = this;\n    var maxSpeed = 4, scrolling = false;\n\n    if (typeof options.margin !== 'object') {\n        var margin = options.margin || -1;\n\n        this.margin = {\n            left: margin,\n            right: margin,\n            top: margin,\n            bottom: margin\n        };\n    } else {\n        this.margin = options.margin;\n    }\n\n    //this.scrolling = false;\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\n\n    var point = {},\n        pointCB = createPointCB(point),\n        dispatcher = createDispatcher(),\n        down = false;\n\n    window.addEventListener('mousemove', pointCB, false);\n    window.addEventListener('touchmove', pointCB, false);\n\n    if(!isNaN(options.maxSpeed)){\n        maxSpeed = options.maxSpeed;\n    }\n\n    if (typeof maxSpeed !== 'object') {\n        maxSpeed = {\n            left: maxSpeed,\n            right: maxSpeed,\n            top: maxSpeed,\n            bottom: maxSpeed\n        };\n    }\n\n    this.autoScroll = boolean(options.autoScroll);\n    this.syncMove = boolean(options.syncMove, false);\n\n    this.destroy = function(forceCleanAnimation) {\n        window.removeEventListener('mousemove', pointCB, false);\n        window.removeEventListener('touchmove', pointCB, false);\n        window.removeEventListener('mousedown', onDown, false);\n        window.removeEventListener('touchstart', onDown, false);\n        window.removeEventListener('mouseup', onUp, false);\n        window.removeEventListener('touchend', onUp, false);\n        window.removeEventListener('pointerup', onUp, false);\n        window.removeEventListener('mouseleave', onMouseOut, false);\n\n        window.removeEventListener('mousemove', onMove, false);\n        window.removeEventListener('touchmove', onMove, false);\n\n        window.removeEventListener('scroll', setScroll, true);\n        elements = [];\n        if(forceCleanAnimation){\n          cleanAnimation();\n        }\n    };\n\n    this.add = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        addElements.apply(void 0, [ elements ].concat( element ));\n        return this;\n    };\n\n    this.remove = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        return removeElements.apply(void 0, [ elements ].concat( element ));\n    };\n\n    var hasWindow = null, windowAnimationFrame;\n\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\n        elements = [elements];\n    }\n\n    (function(temp){\n        elements = [];\n        temp.forEach(function(element){\n            if(element === window){\n                hasWindow = window;\n            }else {\n                self.add(element);\n            }\n        });\n    }(elements));\n\n    Object.defineProperties(this, {\n        down: {\n            get: function(){ return down; }\n        },\n        maxSpeed: {\n            get: function(){ return maxSpeed; }\n        },\n        point: {\n            get: function(){ return point; }\n        },\n        scrolling: {\n            get: function(){ return scrolling; }\n        }\n    });\n\n    var current = null, animationFrame;\n\n    window.addEventListener('mousedown', onDown, false);\n    window.addEventListener('touchstart', onDown, false);\n    window.addEventListener('mouseup', onUp, false);\n    window.addEventListener('touchend', onUp, false);\n\n    /*\n    IE does not trigger mouseup event when scrolling.\n    It is a known issue that Microsoft won't fix.\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n    IE supports pointer events instead\n    */\n    window.addEventListener('pointerup', onUp, false);\n\n    window.addEventListener('mousemove', onMove, false);\n    window.addEventListener('touchmove', onMove, false);\n\n    window.addEventListener('mouseleave', onMouseOut, false);\n\n    window.addEventListener('scroll', setScroll, true);\n\n    function setScroll(e){\n\n        for(var i=0; i<elements.length; i++){\n            if(elements[i] === e.target){\n                scrolling = true;\n                break;\n            }\n        }\n\n        if(scrolling){\n            requestFrame(function (){ return scrolling = false; });\n        }\n    }\n\n    function onDown(){\n        down = true;\n    }\n\n    function onUp(){\n        down = false;\n        cleanAnimation();\n    }\n    function cleanAnimation(){\n      cancelFrame(animationFrame);\n      cancelFrame(windowAnimationFrame);\n    }\n    function onMouseOut(){\n        down = false;\n    }\n\n    function getTarget(target){\n        if(!target){\n            return null;\n        }\n\n        if(current === target){\n            return target;\n        }\n\n        if(hasElement(elements, target)){\n            return target;\n        }\n\n        while(target = target.parentNode){\n            if(hasElement(elements, target)){\n                return target;\n            }\n        }\n\n        return null;\n    }\n\n    function getElementUnderPoint(){\n        var underPoint = null;\n\n        for(var i=0; i<elements.length; i++){\n            if(inside(point, elements[i])){\n                underPoint = elements[i];\n            }\n        }\n\n        return underPoint;\n    }\n\n\n    function onMove(event){\n\n        if(!self.autoScroll()) { return; }\n\n        if(event['dispatched']){ return; }\n\n        var target = event.target, body = document.body;\n\n        if(current && !inside(point, current)){\n            if(!self.scrollWhenOutside){\n                current = null;\n            }\n        }\n\n        if(target && target.parentNode === body){\n            //The special condition to improve speed.\n            target = getElementUnderPoint();\n        }else {\n            target = getTarget(target);\n\n            if(!target){\n                target = getElementUnderPoint();\n            }\n        }\n\n\n        if(target && target !== current){\n            current = target;\n        }\n\n        if(hasWindow){\n            cancelFrame(windowAnimationFrame);\n            windowAnimationFrame = requestFrame(scrollWindow);\n        }\n\n\n        if(!current){\n            return;\n        }\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n    }\n\n    function scrollWindow(){\n        autoScroll(hasWindow);\n\n        cancelFrame(windowAnimationFrame);\n        windowAnimationFrame = requestFrame(scrollWindow);\n    }\n\n    function scrollTick(){\n\n        if(!current){\n            return;\n        }\n\n        autoScroll(current);\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n\n    }\n\n\n    function autoScroll(el){\n        var rect = getClientRect(el), scrollx, scrolly;\n\n        if(point.x < rect.left + self.margin.left){\n            scrollx = Math.floor(\n                Math.max(-1, (point.x - rect.left) / self.margin.left - 1) * self.maxSpeed.left\n            );\n        }else if(point.x > rect.right - self.margin.right){\n            scrollx = Math.ceil(\n                Math.min(1, (point.x - rect.right) / self.margin.right + 1) * self.maxSpeed.right\n            );\n        }else {\n            scrollx = 0;\n        }\n\n        if(point.y < rect.top + self.margin.top){\n            scrolly = Math.floor(\n                Math.max(-1, (point.y - rect.top) / self.margin.top - 1) * self.maxSpeed.top\n            );\n        }else if(point.y > rect.bottom - self.margin.bottom){\n            scrolly = Math.ceil(\n                Math.min(1, (point.y - rect.bottom) / self.margin.bottom + 1) * self.maxSpeed.bottom\n            );\n        }else {\n            scrolly = 0;\n        }\n\n        if(self.syncMove()){\n            /*\n            Notes about mousemove event dispatch.\n            screen(X/Y) should need to be updated.\n            Some other properties might need to be set.\n            Keep the syncMove option default false until all inconsistencies are taken care of.\n            */\n            dispatcher.dispatch(el, {\n                pageX: point.pageX + scrollx,\n                pageY: point.pageY + scrolly,\n                clientX: point.x + scrollx,\n                clientY: point.y + scrolly\n            });\n        }\n\n        setTimeout(function (){\n\n            if(scrolly){\n                scrollY(el, scrolly);\n            }\n\n            if(scrollx){\n                scrollX(el, scrollx);\n            }\n\n        });\n    }\n\n    function scrollY(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n        }else {\n            el.scrollTop += amount;\n        }\n    }\n\n    function scrollX(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n        }else {\n            el.scrollLeft += amount;\n        }\n    }\n\n}\n\nfunction AutoScrollerFactory(element, options){\n    return new AutoScroller(element, options);\n}\n\nfunction inside(point, el, rect){\n    if(!rect){\n        return pointInside(point, el);\n    }else {\n        return (point.y > rect.top && point.y < rect.bottom &&\n                point.x > rect.left && point.x < rect.right);\n    }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n\nexport default AutoScrollerFactory;\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,IAAI,OAAOD,CAAC,KAAK,WAAW,EAAE;IAC1B,OAAO,OAAOC,CAAC,KAAK,WAAW,GAAGD,CAAC,GAAGC,CAAC;EAC3C;EAEA,OAAOD,CAAC;AACZ;AACA,SAASE,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAExBD,IAAI,GAAGJ,MAAM,CAACI,IAAI,EAAEC,GAAG,CAAC;EAExB,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO,SAASH,CAACA,CAAA,EAAG;MAChB,IAAIK,WAAW,GAAGC,SAAS;MAE3B,KAAK,IAAIC,IAAI,GAAGD,SAAS,CAACE,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACjFF,IAAI,CAACE,IAAI,CAAC,GAAGN,WAAW,CAACM,IAAI,CAAC;MAClC;MAEA,OAAO,CAAC,CAACR,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACnC,CAAC;EACL;EAEA,OAAO,CAAC,CAACN,IAAI,GAAG,YAAY;IACxB,OAAO,IAAI;EACf,CAAC,GAAG,YAAY;IACZ,OAAO,KAAK;EAChB,CAAC;AACL;AAEA,IAAIU,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,GAAG,QAAQ,GAAG,OAAOE,GAAG;AAAE,CAAC;;AAEhP;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACjC,OAAOA,KAAK,IAAI,IAAI,IAAI,CAAC,OAAOA,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGN,OAAO,CAACM,KAAK,CAAC,MAAM,QAAQ,IAAIA,KAAK,CAACC,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC,KAAK,QAAQ,IAAIR,OAAO,CAACM,KAAK,CAACG,aAAa,CAAC,KAAK,QAAQ;AAC9M,CAAC;AAED,SAASC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAC;EACtCA,OAAO,GAAGC,cAAc,CAACD,OAAO,EAAE,IAAI,CAAC;EACvC,IAAG,CAACP,WAAW,CAACO,OAAO,CAAC,EAAE;IAAE,OAAO,CAAC,CAAC;EAAE;EACvC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACH,QAAQ,CAAChB,MAAM,EAAEmB,CAAC,EAAE,EAAC;IAChC,IAAGH,QAAQ,CAACG,CAAC,CAAC,KAAKF,OAAO,EAAC;MACvB,OAAOE,CAAC;IACZ;EACJ;EACA,OAAO,CAAC,CAAC;AACb;AAEA,SAASC,UAAUA,CAACJ,QAAQ,EAAEC,OAAO,EAAC;EAClC,OAAO,CAAC,CAAC,KAAKF,cAAc,CAACC,QAAQ,EAAEC,OAAO,CAAC;AACnD;AAEA,SAASI,YAAYA,CAACL,QAAQ,EAAEM,KAAK,EAAC;EAElC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACG,KAAK,CAACtB,MAAM,EAAEmB,CAAC,EAAE,EAAC;IAC7B,IAAG,CAACC,UAAU,CAACJ,QAAQ,EAAEM,KAAK,CAACH,CAAC,CAAC,CAAC,EAC9B;MAAEH,QAAQ,CAACO,IAAI,CAACD,KAAK,CAACH,CAAC,CAAC,CAAC;IAAE;EACnC;EAEA,OAAOG,KAAK;AAChB;AAEA,SAASE,WAAWA,CAACR,QAAQ,EAAC;EAC1B,IAAInB,WAAW,GAAGC,SAAS;EAE3B,IAAIwB,KAAK,GAAG,EAAE;IAAEG,GAAG,GAAG3B,SAAS,CAACE,MAAM,GAAG,CAAC;EAC1C,OAAQyB,GAAG,EAAE,GAAG,CAAC,EAAG;IAAEH,KAAK,CAAEG,GAAG,CAAE,GAAG5B,WAAW,CAAE4B,GAAG,GAAG,CAAC,CAAE;EAAE;EAE7DH,KAAK,GAAGA,KAAK,CAACI,GAAG,CAACR,cAAc,CAAC;EACjC,OAAOG,YAAY,CAACL,QAAQ,EAAEM,KAAK,CAAC;AACxC;AAEA,SAASK,cAAcA,CAACX,QAAQ,EAAC;EAC7B,IAAInB,WAAW,GAAGC,SAAS;EAE3B,IAAI8B,QAAQ,GAAG,EAAE;IAAEH,GAAG,GAAG3B,SAAS,CAACE,MAAM,GAAG,CAAC;EAC7C,OAAQyB,GAAG,EAAE,GAAG,CAAC,EAAG;IAAEG,QAAQ,CAAEH,GAAG,CAAE,GAAG5B,WAAW,CAAE4B,GAAG,GAAG,CAAC,CAAE;EAAE;EAEhE,OAAOG,QAAQ,CAACF,GAAG,CAACR,cAAc,CAAC,CAACW,MAAM,CAAC,UAAUC,IAAI,EAAEC,CAAC,EAAC;IAEzD,IAAIC,KAAK,GAAGjB,cAAc,CAACC,QAAQ,EAAEe,CAAC,CAAC;IAEvC,IAAGC,KAAK,KAAK,CAAC,CAAC,EACX;MAAE,OAAOF,IAAI,CAACG,MAAM,CAACjB,QAAQ,CAACkB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;IAAE;IACrD,OAAOF,IAAI;EACf,CAAC,EAAE,EAAE,CAAC;AACV;AAEA,SAASZ,cAAcA,CAACD,OAAO,EAAEkB,OAAO,EAAC;EACrC,IAAG,OAAOlB,OAAO,KAAK,QAAQ,EAAC;IAC3B,IAAG;MACC,OAAOmB,QAAQ,CAACC,aAAa,CAACpB,OAAO,CAAC;IAC1C,CAAC,QAAMc,CAAC,EAAC;MACL,MAAMA,CAAC;IACX;EAEJ;EAEA,IAAG,CAACrB,WAAW,CAACO,OAAO,CAAC,IAAI,CAACkB,OAAO,EAAC;IACjC,MAAM,IAAIG,SAAS,CAAErB,OAAO,GAAG,wBAAyB,CAAC;EAC7D;EACA,OAAOA,OAAO;AAClB;AAEA,SAASsB,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAEpC;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,WAAW,GAAGhD,OAAO,CAAC+C,OAAO,CAACC,WAAW,EAAE,IAAI,CAAC;;EAEpD;AACJ;AACA;AACA;AACA;;EAEI,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;IAE3BA,KAAK,GAAGA,KAAK,IAAIC,MAAM,CAACD,KAAK,CAAC,CAAC;IAC/BJ,MAAM,CAACM,MAAM,GAAGF,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,cAAc;IACxER,MAAM,CAACvB,OAAO,GAAG,IAAI;IACrBuB,MAAM,CAACS,IAAI,GAAGL,KAAK,CAACK,IAAI;IAExB,IAAI,CAACP,WAAW,CAACE,KAAK,CAAC,EAAE;MACrB;IACJ;;IAEA;IACA;;IAEA,IAAIA,KAAK,CAACM,aAAa,EAAE;MACrBV,MAAM,CAACW,CAAC,GAAGP,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO;MACzCZ,MAAM,CAACa,CAAC,GAAGT,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACI,OAAO;MACzCd,MAAM,CAACe,KAAK,GAAGX,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACK,KAAK;MAC3Cf,MAAM,CAACgB,KAAK,GAAGZ,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACM,KAAK;MAC3ChB,MAAM,CAACiB,OAAO,GAAGb,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACO,OAAO;MAC/CjB,MAAM,CAACkB,OAAO,GAAGd,KAAK,CAACM,aAAa,CAAC,CAAC,CAAC,CAACQ,OAAO;IACnD,CAAC,MAAM;MAEH;MACA;MACA;MACA;;MAEA,IAAId,KAAK,CAACW,KAAK,KAAK,IAAI,IAAIX,KAAK,CAACQ,OAAO,KAAK,IAAI,EAAE;QAChD,IAAIO,QAAQ,GAAGf,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACE,MAAM,CAAChC,aAAa,IAAIsB,QAAQ;QACrE,IAAIwB,GAAG,GAAGD,QAAQ,CAACE,eAAe;QAClC,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAAI;QAExBtB,MAAM,CAACe,KAAK,GAAGX,KAAK,CAACQ,OAAO,IAAIQ,GAAG,IAAIA,GAAG,CAACG,UAAU,IAAID,IAAI,IAAIA,IAAI,CAACC,UAAU,IAAI,CAAC,CAAC,IAAIH,GAAG,IAAIA,GAAG,CAACI,UAAU,IAAIF,IAAI,IAAIA,IAAI,CAACE,UAAU,IAAI,CAAC,CAAC;QAChJxB,MAAM,CAACgB,KAAK,GAAGZ,KAAK,CAACU,OAAO,IAAIM,GAAG,IAAIA,GAAG,CAACK,SAAS,IAAIH,IAAI,IAAIA,IAAI,CAACG,SAAS,IAAI,CAAC,CAAC,IAAIL,GAAG,IAAIA,GAAG,CAACM,SAAS,IAAIJ,IAAI,IAAIA,IAAI,CAACI,SAAS,IAAI,CAAC,CAAC;MAChJ,CAAC,MAAM;QACH1B,MAAM,CAACe,KAAK,GAAGX,KAAK,CAACW,KAAK;QAC1Bf,MAAM,CAACgB,KAAK,GAAGZ,KAAK,CAACY,KAAK;MAC9B;;MAEA;MACA;MACA;MACA;;MAEAhB,MAAM,CAACW,CAAC,GAAGP,KAAK,CAACQ,OAAO;MACxBZ,MAAM,CAACa,CAAC,GAAGT,KAAK,CAACU,OAAO;MAExBd,MAAM,CAACiB,OAAO,GAAGb,KAAK,CAACa,OAAO;MAC9BjB,MAAM,CAACkB,OAAO,GAAGd,KAAK,CAACc,OAAO;IAClC;IAEAlB,MAAM,CAACY,OAAO,GAAGZ,MAAM,CAACW,CAAC;IACzBX,MAAM,CAACc,OAAO,GAAGd,MAAM,CAACa,CAAC;EAC7B,CAAC;;EAED;AACJ;;AAEA,SAASc,gBAAgBA,CAAA,EAAG;EACxB,IAAIC,KAAK,GAAG;IACRC,GAAG,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAK,CAAC;IACnCC,IAAI,EAAE;MAAEF,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAK,CAAC;IACpCE,KAAK,EAAE;MAAEH,KAAK,EAAEzB,MAAM,CAAC6B,UAAU;MAAEH,UAAU,EAAE;IAAK,CAAC;IACrDI,MAAM,EAAE;MAAEL,KAAK,EAAEzB,MAAM,CAAC+B,WAAW;MAAEL,UAAU,EAAE;IAAK,CAAC;IACvDM,KAAK,EAAE;MAAEP,KAAK,EAAEzB,MAAM,CAAC6B,UAAU;MAAEH,UAAU,EAAE;IAAK,CAAC;IACrDO,MAAM,EAAE;MAAER,KAAK,EAAEzB,MAAM,CAAC+B,WAAW;MAAEL,UAAU,EAAE;IAAK,CAAC;IACvDpB,CAAC,EAAE;MAAEmB,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAK,CAAC;IACjClB,CAAC,EAAE;MAAEiB,KAAK,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAK;EACpC,CAAC;EAED,IAAIQ,MAAM,CAACC,MAAM,EAAE;IACf,OAAOD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,KAAK,CAAC;EACnC,CAAC,MAAM;IACH,IAAIa,IAAI,GAAG,CAAC,CAAC;IACbF,MAAM,CAACG,gBAAgB,CAACD,IAAI,EAAEb,KAAK,CAAC;IACpC,OAAOa,IAAI;EACf;AACJ;AAEA,SAASE,aAAaA,CAACC,EAAE,EAAE;EACvB,IAAIA,EAAE,KAAKvC,MAAM,EAAE;IACf,OAAOsB,gBAAgB,CAAC,CAAC;EAC7B,CAAC,MAAM;IACH,IAAI;MACA,IAAIc,IAAI,GAAGG,EAAE,CAACC,qBAAqB,CAAC,CAAC;MACrC,IAAIJ,IAAI,CAAC9B,CAAC,KAAKmC,SAAS,EAAE;QACtBL,IAAI,CAAC9B,CAAC,GAAG8B,IAAI,CAACT,IAAI;QAClBS,IAAI,CAAC5B,CAAC,GAAG4B,IAAI,CAACZ,GAAG;MACrB;MACA,OAAOY,IAAI;IACf,CAAC,CAAC,OAAOlD,CAAC,EAAE;MACR,MAAM,IAAIO,SAAS,CAAC,sCAAsC,GAAG8C,EAAE,CAAC;IACpE;EACJ;AACJ;AAEA,SAASG,WAAWA,CAACC,KAAK,EAAEJ,EAAE,EAAE;EAC5B,IAAIH,IAAI,GAAGE,aAAa,CAACC,EAAE,CAAC;EAC5B,OAAOI,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACZ,GAAG,IAAImB,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACN,MAAM,IAAIa,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACT,IAAI,IAAIgB,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACR,KAAK;AACrG;AAEA,IAAIgB,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,OAAOV,MAAM,CAACC,MAAM,IAAI,UAAU,EAAE;EACtCS,YAAY,GAAG,UAAUC,WAAW,EAAE;IACpC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG,CAAC,CAAC;IAC7B,OAAO,UAAUC,SAAS,EAAEC,gBAAgB,EAAE;MAC5C,IAAID,SAAS,KAAKb,MAAM,CAACa,SAAS,CAAC,IAAIA,SAAS,KAAK,IAAI,EAAE;QACzD,MAAMtD,SAAS,CAAC,qCAAqC,CAAC;MACxD;MACAqD,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAI,CAAC,CAAC;MAChC,IAAIE,MAAM,GAAG,IAAIH,IAAI,CAAC,CAAC;MACvBA,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAIC,gBAAgB,KAAKH,WAAW,EAAE;QACpCX,MAAM,CAACG,gBAAgB,CAACY,MAAM,EAAED,gBAAgB,CAAC;MACnD;;MAEA;MACA,IAAID,SAAS,KAAK,IAAI,EAAE;QACtBE,MAAM,CAACC,SAAS,GAAG,IAAI;MACzB;MACA,OAAOD,MAAM;IACf,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,MAAM;EACLL,YAAY,GAAGV,MAAM,CAACC,MAAM;AAC9B;AAEA,IAAIgB,cAAc,GAAGP,YAAY;AAEjC,IAAIQ,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC;AAEnP,SAASC,gBAAgBA,CAACjF,OAAO,EAAE;EAE/B,IAAIkF,eAAe,GAAG;IAClB1C,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVN,OAAO,EAAE,CAAC;IACVE,OAAO,EAAE,CAAC;IACV8C,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,aAAa,EAAE,IAAI;IACnBC,MAAM,EAAE;EACZ,CAAC;EAED,IAAI1F,OAAO,KAAKqE,SAAS,EAAE;IACvBrE,OAAO,CAAC2F,gBAAgB,CAAC,WAAW,EAAEC,MAAM,CAAC;EACjD;EAEA,SAASA,MAAMA,CAAC9E,CAAC,EAAE;IACf,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8E,eAAe,CAACjG,MAAM,EAAEmB,CAAC,EAAE,EAAE;MAC7CgF,eAAe,CAACF,eAAe,CAAC9E,CAAC,CAAC,CAAC,GAAGY,CAAC,CAACkE,eAAe,CAAC9E,CAAC,CAAC,CAAC;IAC/D;EACJ;EAEA,IAAI2F,QAAQ,GAAG,YAAY;IACvB,IAAIC,UAAU,EAAE;MACZ,OAAO,SAASC,EAAEA,CAAC/F,OAAO,EAAEgG,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIC,GAAG,GAAG,IAAIJ,UAAU,CAAC,WAAW,EAAEK,cAAc,CAACjB,eAAe,EAAEc,QAAQ,CAAC,CAAC;;QAEhF;QACAI,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOjG,OAAO,CAACqG,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL,CAAC,MAAM,IAAI,OAAO/E,QAAQ,CAACmF,WAAW,KAAK,UAAU,EAAE;MACnD,OAAO,SAASC,EAAEA,CAACvG,OAAO,EAAEgG,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIO,QAAQ,GAAGL,cAAc,CAACjB,eAAe,EAAEc,QAAQ,CAAC;QACxD,IAAIE,GAAG,GAAG/E,QAAQ,CAACmF,WAAW,CAAC,aAAa,CAAC;QAE7CJ,GAAG,CAACO,cAAc,CAAC,WAAW,EAAE,IAAI;QAAE;QACtC,IAAI;QAAE;QACN7E,MAAM;QAAE;QACR,CAAC;QAAE;QACH4E,QAAQ,CAAChE,OAAO;QAAE;QAClBgE,QAAQ,CAAC/D,OAAO;QAAE;QAClB+D,QAAQ,CAACrE,OAAO;QAAE;QAClBqE,QAAQ,CAACnE,OAAO;QAAE;QAClBmE,QAAQ,CAACrB,OAAO;QAAE;QAClBqB,QAAQ,CAACnB,MAAM;QAAE;QACjBmB,QAAQ,CAACpB,QAAQ;QAAE;QACnBoB,QAAQ,CAAClB,OAAO;QAAE;QAClBkB,QAAQ,CAACjB,MAAM;QAAE;QACjBiB,QAAQ,CAACf,aAAa,CAAC;QACvB,CAAC;;QAED;QACAW,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOjG,OAAO,CAACqG,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL,CAAC,MAAM,IAAI,OAAO/E,QAAQ,CAACuF,iBAAiB,KAAK,UAAU,EAAE;MACzD,OAAO,SAASC,EAAEA,CAAC3G,OAAO,EAAEgG,QAAQ,EAAEC,IAAI,EAAE;QACxC,IAAIC,GAAG,GAAG/E,QAAQ,CAACuF,iBAAiB,CAAC,CAAC;QACtC,IAAIF,QAAQ,GAAGL,cAAc,CAACjB,eAAe,EAAEc,QAAQ,CAAC;QACxD,KAAK,IAAIY,IAAI,IAAIJ,QAAQ,EAAE;UACvBN,GAAG,CAACU,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,CAAC;QAC9B;;QAEA;QACAR,UAAU,CAACF,GAAG,EAAED,IAAI,CAAC;QAErB,OAAOjG,OAAO,CAACqG,aAAa,CAACH,GAAG,CAAC;MACrC,CAAC;IACL;EACJ,CAAC,CAAC,CAAC;EAEH,SAASW,OAAOA,CAAA,EAAG;IACf,IAAI7G,OAAO,EAAE;MAAEA,OAAO,CAAC8G,mBAAmB,CAAC,WAAW,EAAElB,MAAM,EAAE,KAAK,CAAC;IAAE;IACxEV,eAAe,GAAG,IAAI;EAC1B;EAEA,OAAO;IACH2B,OAAO,EAAEA,OAAO;IAChBhB,QAAQ,EAAEA;EACd,CAAC;AACL;AAEA,SAASM,cAAcA,CAACjB,eAAe,EAAEc,QAAQ,EAAE;EAC/CA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;EACzB,IAAIQ,QAAQ,GAAGzB,cAAc,CAACG,eAAe,CAAC;EAC9C,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8E,eAAe,CAACjG,MAAM,EAAEmB,CAAC,EAAE,EAAE;IAC7C,IAAI8F,QAAQ,CAAChB,eAAe,CAAC9E,CAAC,CAAC,CAAC,KAAKmE,SAAS,EAAE;MAAEmC,QAAQ,CAACxB,eAAe,CAAC9E,CAAC,CAAC,CAAC,GAAG8F,QAAQ,CAAChB,eAAe,CAAC9E,CAAC,CAAC,CAAC;IAAE;EACnH;EAEA,OAAOsG,QAAQ;AACnB;AAEA,SAASJ,UAAUA,CAACtF,CAAC,EAAEmF,IAAI,EAAE;EACzBc,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEf,IAAI,CAAC;EAC1BnF,CAAC,CAACmF,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACnBnF,CAAC,CAACmG,UAAU,GAAG,WAAW;AAC9B;AAEA,IAAIC,MAAM,GAAG,CAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAE;AAE3C,IAAIC,YAAY,GAAI,YAAY;EAE5B,IAAI,OAAOvF,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,YAAY,CAAC,CAAC;EACzB;EAEA,KAAM,IAAI1B,CAAC,GAAG,CAAC,EAAEkH,KAAK,GAAGF,MAAM,CAACnI,MAAM,EAAGmB,CAAC,GAAGkH,KAAK,IAAI,CAAExF,MAAM,CAACyF,qBAAqB,EAAG,EAAEnH,CAAC,EAAG;IACzF0B,MAAM,CAACyF,qBAAqB,GAAGzF,MAAM,CAAEsF,MAAM,CAAEhH,CAAC,CAAE,GAAG,uBAAuB,CAAE;EAClF;EAEA,IAAK,CAAE0B,MAAM,CAACyF,qBAAqB,EAAG;IAClC,IAAIC,QAAQ,GAAG,CAAC;IAEhB1F,MAAM,CAACyF,qBAAqB,GAAG,UAAUE,QAAQ,EAAE;MAC/C,IAAIC,GAAG,GAAK,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAChC,IAAIC,GAAG,GAAKC,IAAI,CAACC,GAAG,CAAE,CAAC,EAAE,EAAE,GAAGL,GAAG,GAAGF,QAAS,CAAC;MAC9C,IAAIQ,KAAK,GAAGlG,MAAM,CAACmG,UAAU,CAAE,YAAY;QAAE,OAAOR,QAAQ,CAAEC,GAAG,GAAGG,GAAI,CAAC;MAAE,CAAC,EAAEA,GAAI,CAAC;MAEnFL,QAAQ,GAAGE,GAAG,GAAGG,GAAG;MAEpB,OAAOG,KAAK;IAChB,CAAC;EACL;EAEA,OAAOlG,MAAM,CAACyF,qBAAqB,CAACW,IAAI,CAAEpG,MAAO,CAAC;AACtD,CAAC,CAAE,CAAC;AAEJ,IAAIqG,WAAW,GAAI,YAAY;EAE3B,IAAI,OAAOrG,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,YAAY,CAAC,CAAC;EACzB;EAEA,KAAM,IAAI1B,CAAC,GAAG,CAAC,EAAEkH,KAAK,GAAGF,MAAM,CAACnI,MAAM,EAAGmB,CAAC,GAAGkH,KAAK,IAAI,CAAExF,MAAM,CAACsG,oBAAoB,EAAG,EAAEhI,CAAC,EAAG;IACxF0B,MAAM,CAACsG,oBAAoB,GAAGtG,MAAM,CAAEsF,MAAM,CAAEhH,CAAC,CAAE,GAAG,sBAAsB,CAAE,IAAI0B,MAAM,CAAEsF,MAAM,CAAEhH,CAAC,CAAE,GAAG,6BAA6B,CAAE;EACzI;EAEA,IAAK,CAAE0B,MAAM,CAACsG,oBAAoB,EAAG;IACjCtG,MAAM,CAACsG,oBAAoB,GAAG,UAAUJ,KAAK,EAAE;MAC3ClG,MAAM,CAACuG,YAAY,CAAEL,KAAM,CAAC;IAChC,CAAC;EACL;EAEA,OAAOlG,MAAM,CAACsG,oBAAoB,CAACF,IAAI,CAAEpG,MAAO,CAAC;AACrD,CAAC,CAAE,CAAC;AAEJ,SAASwG,YAAYA,CAACrI,QAAQ,EAAEyB,OAAO,EAAC;EACpC,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;EAEtC,IAAI6G,IAAI,GAAG,IAAI;EACf,IAAIC,QAAQ,GAAG,CAAC;IAAEC,SAAS,GAAG,KAAK;EAEnC,IAAI,OAAO/G,OAAO,CAACgH,MAAM,KAAK,QAAQ,EAAE;IACpC,IAAIA,MAAM,GAAGhH,OAAO,CAACgH,MAAM,IAAI,CAAC,CAAC;IAEjC,IAAI,CAACA,MAAM,GAAG;MACVjF,IAAI,EAAEiF,MAAM;MACZhF,KAAK,EAAEgF,MAAM;MACbpF,GAAG,EAAEoF,MAAM;MACX9E,MAAM,EAAE8E;IACZ,CAAC;EACL,CAAC,MAAM;IACH,IAAI,CAACA,MAAM,GAAGhH,OAAO,CAACgH,MAAM;EAChC;;EAEA;EACA,IAAI,CAACC,iBAAiB,GAAGjH,OAAO,CAACiH,iBAAiB,IAAI,KAAK;EAE3D,IAAIlE,KAAK,GAAG,CAAC,CAAC;IACV7C,OAAO,GAAGJ,aAAa,CAACiD,KAAK,CAAC;IAC9BmE,UAAU,GAAGzD,gBAAgB,CAAC,CAAC;IAC/B0D,IAAI,GAAG,KAAK;EAEhB/G,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEjE,OAAO,EAAE,KAAK,CAAC;EACpDE,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEjE,OAAO,EAAE,KAAK,CAAC;EAEpD,IAAG,CAACkH,KAAK,CAACpH,OAAO,CAAC8G,QAAQ,CAAC,EAAC;IACxBA,QAAQ,GAAG9G,OAAO,CAAC8G,QAAQ;EAC/B;EAEA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9BA,QAAQ,GAAG;MACP/E,IAAI,EAAE+E,QAAQ;MACd9E,KAAK,EAAE8E,QAAQ;MACflF,GAAG,EAAEkF,QAAQ;MACb5E,MAAM,EAAE4E;IACZ,CAAC;EACL;EAEA,IAAI,CAACO,UAAU,GAAGpK,OAAO,CAAC+C,OAAO,CAACqH,UAAU,CAAC;EAC7C,IAAI,CAACC,QAAQ,GAAGrK,OAAO,CAAC+C,OAAO,CAACsH,QAAQ,EAAE,KAAK,CAAC;EAEhD,IAAI,CAACjC,OAAO,GAAG,UAASkC,mBAAmB,EAAE;IACzCnH,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAEpF,OAAO,EAAE,KAAK,CAAC;IACvDE,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAEpF,OAAO,EAAE,KAAK,CAAC;IACvDE,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAEkC,MAAM,EAAE,KAAK,CAAC;IACtDpH,MAAM,CAACkF,mBAAmB,CAAC,YAAY,EAAEkC,MAAM,EAAE,KAAK,CAAC;IACvDpH,MAAM,CAACkF,mBAAmB,CAAC,SAAS,EAAEmC,IAAI,EAAE,KAAK,CAAC;IAClDrH,MAAM,CAACkF,mBAAmB,CAAC,UAAU,EAAEmC,IAAI,EAAE,KAAK,CAAC;IACnDrH,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAEmC,IAAI,EAAE,KAAK,CAAC;IACpDrH,MAAM,CAACkF,mBAAmB,CAAC,YAAY,EAAEoC,UAAU,EAAE,KAAK,CAAC;IAE3DtH,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAElB,MAAM,EAAE,KAAK,CAAC;IACtDhE,MAAM,CAACkF,mBAAmB,CAAC,WAAW,EAAElB,MAAM,EAAE,KAAK,CAAC;IAEtDhE,MAAM,CAACkF,mBAAmB,CAAC,QAAQ,EAAEqC,SAAS,EAAE,IAAI,CAAC;IACrDpJ,QAAQ,GAAG,EAAE;IACb,IAAGgJ,mBAAmB,EAAC;MACrBK,cAAc,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,IAAI,CAACC,GAAG,GAAG,YAAU;IACjB,IAAIrJ,OAAO,GAAG,EAAE;MAAEQ,GAAG,GAAG3B,SAAS,CAACE,MAAM;IACxC,OAAQyB,GAAG,EAAE,EAAGR,OAAO,CAAEQ,GAAG,CAAE,GAAG3B,SAAS,CAAE2B,GAAG,CAAE;IAEjDD,WAAW,CAACpB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAEY,QAAQ,CAAE,CAACiB,MAAM,CAAEhB,OAAQ,CAAC,CAAC;IACzD,OAAO,IAAI;EACf,CAAC;EAED,IAAI,CAACsJ,MAAM,GAAG,YAAU;IACpB,IAAItJ,OAAO,GAAG,EAAE;MAAEQ,GAAG,GAAG3B,SAAS,CAACE,MAAM;IACxC,OAAQyB,GAAG,EAAE,EAAGR,OAAO,CAAEQ,GAAG,CAAE,GAAG3B,SAAS,CAAE2B,GAAG,CAAE;IAEjD,OAAOE,cAAc,CAACvB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAEY,QAAQ,CAAE,CAACiB,MAAM,CAAEhB,OAAQ,CAAC,CAAC;EACvE,CAAC;EAED,IAAIuJ,SAAS,GAAG,IAAI;IAAEC,oBAAoB;EAE1C,IAAG1F,MAAM,CAACa,SAAS,CAAC8E,QAAQ,CAACC,IAAI,CAAC3J,QAAQ,CAAC,KAAK,gBAAgB,EAAC;IAC7DA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACzB;EAEC,WAAS4J,IAAI,EAAC;IACX5J,QAAQ,GAAG,EAAE;IACb4J,IAAI,CAACC,OAAO,CAAC,UAAS5J,OAAO,EAAC;MAC1B,IAAGA,OAAO,KAAK4B,MAAM,EAAC;QAClB2H,SAAS,GAAG3H,MAAM;MACtB,CAAC,MAAK;QACFyG,IAAI,CAACgB,GAAG,CAACrJ,OAAO,CAAC;MACrB;IACJ,CAAC,CAAC;EACN,CAAC,EAACD,QAAQ,CAAC;EAEX+D,MAAM,CAACG,gBAAgB,CAAC,IAAI,EAAE;IAC1B0E,IAAI,EAAE;MACFkB,GAAG,EAAE,SAAAA,CAAA,EAAU;QAAE,OAAOlB,IAAI;MAAE;IAClC,CAAC;IACDL,QAAQ,EAAE;MACNuB,GAAG,EAAE,SAAAA,CAAA,EAAU;QAAE,OAAOvB,QAAQ;MAAE;IACtC,CAAC;IACD/D,KAAK,EAAE;MACHsF,GAAG,EAAE,SAAAA,CAAA,EAAU;QAAE,OAAOtF,KAAK;MAAE;IACnC,CAAC;IACDgE,SAAS,EAAE;MACPsB,GAAG,EAAE,SAAAA,CAAA,EAAU;QAAE,OAAOtB,SAAS;MAAE;IACvC;EACJ,CAAC,CAAC;EAEF,IAAIuB,OAAO,GAAG,IAAI;IAAEC,cAAc;EAElCnI,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEqD,MAAM,EAAE,KAAK,CAAC;EACnDpH,MAAM,CAAC+D,gBAAgB,CAAC,YAAY,EAAEqD,MAAM,EAAE,KAAK,CAAC;EACpDpH,MAAM,CAAC+D,gBAAgB,CAAC,SAAS,EAAEsD,IAAI,EAAE,KAAK,CAAC;EAC/CrH,MAAM,CAAC+D,gBAAgB,CAAC,UAAU,EAAEsD,IAAI,EAAE,KAAK,CAAC;;EAEhD;AACJ;AACA;AACA;AACA;AACA;EACIrH,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEsD,IAAI,EAAE,KAAK,CAAC;EAEjDrH,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEC,MAAM,EAAE,KAAK,CAAC;EACnDhE,MAAM,CAAC+D,gBAAgB,CAAC,WAAW,EAAEC,MAAM,EAAE,KAAK,CAAC;EAEnDhE,MAAM,CAAC+D,gBAAgB,CAAC,YAAY,EAAEuD,UAAU,EAAE,KAAK,CAAC;EAExDtH,MAAM,CAAC+D,gBAAgB,CAAC,QAAQ,EAAEwD,SAAS,EAAE,IAAI,CAAC;EAElD,SAASA,SAASA,CAACrI,CAAC,EAAC;IAEjB,KAAI,IAAIZ,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACH,QAAQ,CAAChB,MAAM,EAAEmB,CAAC,EAAE,EAAC;MAChC,IAAGH,QAAQ,CAACG,CAAC,CAAC,KAAKY,CAAC,CAACe,MAAM,EAAC;QACxB0G,SAAS,GAAG,IAAI;QAChB;MACJ;IACJ;IAEA,IAAGA,SAAS,EAAC;MACTpB,YAAY,CAAC,YAAW;QAAE,OAAOoB,SAAS,GAAG,KAAK;MAAE,CAAC,CAAC;IAC1D;EACJ;EAEA,SAASS,MAAMA,CAAA,EAAE;IACbL,IAAI,GAAG,IAAI;EACf;EAEA,SAASM,IAAIA,CAAA,EAAE;IACXN,IAAI,GAAG,KAAK;IACZS,cAAc,CAAC,CAAC;EACpB;EACA,SAASA,cAAcA,CAAA,EAAE;IACvBnB,WAAW,CAAC8B,cAAc,CAAC;IAC3B9B,WAAW,CAACuB,oBAAoB,CAAC;EACnC;EACA,SAASN,UAAUA,CAAA,EAAE;IACjBP,IAAI,GAAG,KAAK;EAChB;EAEA,SAASqB,SAASA,CAACnI,MAAM,EAAC;IACtB,IAAG,CAACA,MAAM,EAAC;MACP,OAAO,IAAI;IACf;IAEA,IAAGiI,OAAO,KAAKjI,MAAM,EAAC;MAClB,OAAOA,MAAM;IACjB;IAEA,IAAG1B,UAAU,CAACJ,QAAQ,EAAE8B,MAAM,CAAC,EAAC;MAC5B,OAAOA,MAAM;IACjB;IAEA,OAAMA,MAAM,GAAGA,MAAM,CAACoI,UAAU,EAAC;MAC7B,IAAG9J,UAAU,CAACJ,QAAQ,EAAE8B,MAAM,CAAC,EAAC;QAC5B,OAAOA,MAAM;MACjB;IACJ;IAEA,OAAO,IAAI;EACf;EAEA,SAASqI,oBAAoBA,CAAA,EAAE;IAC3B,IAAIC,UAAU,GAAG,IAAI;IAErB,KAAI,IAAIjK,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACH,QAAQ,CAAChB,MAAM,EAAEmB,CAAC,EAAE,EAAC;MAChC,IAAGkK,MAAM,CAAC7F,KAAK,EAAExE,QAAQ,CAACG,CAAC,CAAC,CAAC,EAAC;QAC1BiK,UAAU,GAAGpK,QAAQ,CAACG,CAAC,CAAC;MAC5B;IACJ;IAEA,OAAOiK,UAAU;EACrB;EAGA,SAASvE,MAAMA,CAACjE,KAAK,EAAC;IAElB,IAAG,CAAC0G,IAAI,CAACQ,UAAU,CAAC,CAAC,EAAE;MAAE;IAAQ;IAEjC,IAAGlH,KAAK,CAAC,YAAY,CAAC,EAAC;MAAE;IAAQ;IAEjC,IAAIE,MAAM,GAAGF,KAAK,CAACE,MAAM;MAAEgB,IAAI,GAAG1B,QAAQ,CAAC0B,IAAI;IAE/C,IAAGiH,OAAO,IAAI,CAACM,MAAM,CAAC7F,KAAK,EAAEuF,OAAO,CAAC,EAAC;MAClC,IAAG,CAACzB,IAAI,CAACI,iBAAiB,EAAC;QACvBqB,OAAO,GAAG,IAAI;MAClB;IACJ;IAEA,IAAGjI,MAAM,IAAIA,MAAM,CAACoI,UAAU,KAAKpH,IAAI,EAAC;MACpC;MACAhB,MAAM,GAAGqI,oBAAoB,CAAC,CAAC;IACnC,CAAC,MAAK;MACFrI,MAAM,GAAGmI,SAAS,CAACnI,MAAM,CAAC;MAE1B,IAAG,CAACA,MAAM,EAAC;QACPA,MAAM,GAAGqI,oBAAoB,CAAC,CAAC;MACnC;IACJ;IAGA,IAAGrI,MAAM,IAAIA,MAAM,KAAKiI,OAAO,EAAC;MAC5BA,OAAO,GAAGjI,MAAM;IACpB;IAEA,IAAG0H,SAAS,EAAC;MACTtB,WAAW,CAACuB,oBAAoB,CAAC;MACjCA,oBAAoB,GAAGrC,YAAY,CAACkD,YAAY,CAAC;IACrD;IAGA,IAAG,CAACP,OAAO,EAAC;MACR;IACJ;IAEA7B,WAAW,CAAC8B,cAAc,CAAC;IAC3BA,cAAc,GAAG5C,YAAY,CAACmD,UAAU,CAAC;EAC7C;EAEA,SAASD,YAAYA,CAAA,EAAE;IACnBxB,UAAU,CAACU,SAAS,CAAC;IAErBtB,WAAW,CAACuB,oBAAoB,CAAC;IACjCA,oBAAoB,GAAGrC,YAAY,CAACkD,YAAY,CAAC;EACrD;EAEA,SAASC,UAAUA,CAAA,EAAE;IAEjB,IAAG,CAACR,OAAO,EAAC;MACR;IACJ;IAEAjB,UAAU,CAACiB,OAAO,CAAC;IAEnB7B,WAAW,CAAC8B,cAAc,CAAC;IAC3BA,cAAc,GAAG5C,YAAY,CAACmD,UAAU,CAAC;EAE7C;EAGA,SAASzB,UAAUA,CAAC1E,EAAE,EAAC;IACnB,IAAIH,IAAI,GAAGE,aAAa,CAACC,EAAE,CAAC;MAAEoG,OAAO;MAAEC,OAAO;IAE9C,IAAGjG,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACT,IAAI,GAAG8E,IAAI,CAACG,MAAM,CAACjF,IAAI,EAAC;MACtCgH,OAAO,GAAG3C,IAAI,CAAC6C,KAAK,CAChB7C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE,CAACtD,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACT,IAAI,IAAI8E,IAAI,CAACG,MAAM,CAACjF,IAAI,GAAG,CAAC,CAAC,GAAG8E,IAAI,CAACC,QAAQ,CAAC/E,IAC/E,CAAC;IACL,CAAC,MAAK,IAAGgB,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACR,KAAK,GAAG6E,IAAI,CAACG,MAAM,CAAChF,KAAK,EAAC;MAC9C+G,OAAO,GAAG3C,IAAI,CAAC8C,IAAI,CACf9C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAE,CAACpG,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACR,KAAK,IAAI6E,IAAI,CAACG,MAAM,CAAChF,KAAK,GAAG,CAAC,CAAC,GAAG6E,IAAI,CAACC,QAAQ,CAAC9E,KAChF,CAAC;IACL,CAAC,MAAK;MACF+G,OAAO,GAAG,CAAC;IACf;IAEA,IAAGhG,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACZ,GAAG,GAAGiF,IAAI,CAACG,MAAM,CAACpF,GAAG,EAAC;MACpCoH,OAAO,GAAG5C,IAAI,CAAC6C,KAAK,CAChB7C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE,CAACtD,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACZ,GAAG,IAAIiF,IAAI,CAACG,MAAM,CAACpF,GAAG,GAAG,CAAC,CAAC,GAAGiF,IAAI,CAACC,QAAQ,CAAClF,GAC7E,CAAC;IACL,CAAC,MAAK,IAAGmB,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACN,MAAM,GAAG2E,IAAI,CAACG,MAAM,CAAC9E,MAAM,EAAC;MAChD8G,OAAO,GAAG5C,IAAI,CAAC8C,IAAI,CACf9C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAE,CAACpG,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACN,MAAM,IAAI2E,IAAI,CAACG,MAAM,CAAC9E,MAAM,GAAG,CAAC,CAAC,GAAG2E,IAAI,CAACC,QAAQ,CAAC5E,MAClF,CAAC;IACL,CAAC,MAAK;MACF8G,OAAO,GAAG,CAAC;IACf;IAEA,IAAGnC,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAC;MACf;AACZ;AACA;AACA;AACA;AACA;MACYJ,UAAU,CAAC7C,QAAQ,CAAC1B,EAAE,EAAE;QACpB7B,KAAK,EAAEiC,KAAK,CAACjC,KAAK,GAAGiI,OAAO;QAC5BhI,KAAK,EAAEgC,KAAK,CAAChC,KAAK,GAAGiI,OAAO;QAC5BrI,OAAO,EAAEoC,KAAK,CAACrC,CAAC,GAAGqI,OAAO;QAC1BlI,OAAO,EAAEkC,KAAK,CAACnC,CAAC,GAAGoI;MACvB,CAAC,CAAC;IACN;IAEAzC,UAAU,CAAC,YAAW;MAElB,IAAGyC,OAAO,EAAC;QACPI,OAAO,CAACzG,EAAE,EAAEqG,OAAO,CAAC;MACxB;MAEA,IAAGD,OAAO,EAAC;QACPM,OAAO,CAAC1G,EAAE,EAAEoG,OAAO,CAAC;MACxB;IAEJ,CAAC,CAAC;EACN;EAEA,SAASK,OAAOA,CAACzG,EAAE,EAAE2G,MAAM,EAAC;IACxB,IAAG3G,EAAE,KAAKvC,MAAM,EAAC;MACbA,MAAM,CAACmJ,QAAQ,CAAC5G,EAAE,CAAC6G,WAAW,EAAE7G,EAAE,CAAC8G,WAAW,GAAGH,MAAM,CAAC;IAC5D,CAAC,MAAK;MACF3G,EAAE,CAACnB,SAAS,IAAI8H,MAAM;IAC1B;EACJ;EAEA,SAASD,OAAOA,CAAC1G,EAAE,EAAE2G,MAAM,EAAC;IACxB,IAAG3G,EAAE,KAAKvC,MAAM,EAAC;MACbA,MAAM,CAACmJ,QAAQ,CAAC5G,EAAE,CAAC6G,WAAW,GAAGF,MAAM,EAAE3G,EAAE,CAAC8G,WAAW,CAAC;IAC5D,CAAC,MAAK;MACF9G,EAAE,CAACrB,UAAU,IAAIgI,MAAM;IAC3B;EACJ;AAEJ;AAEA,SAASI,mBAAmBA,CAAClL,OAAO,EAAEwB,OAAO,EAAC;EAC1C,OAAO,IAAI4G,YAAY,CAACpI,OAAO,EAAEwB,OAAO,CAAC;AAC7C;AAEA,SAAS4I,MAAMA,CAAC7F,KAAK,EAAEJ,EAAE,EAAEH,IAAI,EAAC;EAC5B,IAAG,CAACA,IAAI,EAAC;IACL,OAAOM,WAAW,CAACC,KAAK,EAAEJ,EAAE,CAAC;EACjC,CAAC,MAAK;IACF,OAAQI,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACZ,GAAG,IAAImB,KAAK,CAACnC,CAAC,GAAG4B,IAAI,CAACN,MAAM,IAC3Ca,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACT,IAAI,IAAIgB,KAAK,CAACrC,CAAC,GAAG8B,IAAI,CAACR,KAAK;EACvD;AACJ;;AAEA;AACA;AACA;AACA;;AAEA,eAAe0H,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}