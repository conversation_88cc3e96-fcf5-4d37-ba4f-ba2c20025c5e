{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport function getMilliseconds(date) {\n  return toDate(date).getMilliseconds();\n}\n\n// Fallback for modularized imports:\nexport default getMilliseconds;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}