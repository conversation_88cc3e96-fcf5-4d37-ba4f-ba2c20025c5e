{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 32)(2, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r9.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 24);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 29);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 30);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 41);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 38);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      ctx_r25.selectedNotifications.clear();\n      ctx_r25.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r25.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 44);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 51);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 90);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      ctx_r42.getNotificationAttachments(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      ctx_r45.joinConversation(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.markAsRead(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64)(2, \"div\", 65)(3, \"label\", 32)(4, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.toggleSelection(notification_r33.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 66);\n    i0.ɵɵelement(7, \"img\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"div\", 69)(10, \"div\", 70)(11, \"div\", 71)(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 73);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 74)(18, \"span\", 75);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 76);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 79);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 80);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 2, 0, \"button\", 81);\n    i0.ɵɵelementStart(26, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      ctx_r53.openNotificationDetails(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 84);\n    i0.ɵɵelementStart(29, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      ctx_r54.deleteNotification(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r33.isRead)(\"futuristic-notification-read\", notification_r33.isRead)(\"futuristic-notification-selected\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r33.senderId == null ? null : notification_r33.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r33.senderId == null ? null : notification_r33.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r33.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r33.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.type === \"NEW_MESSAGE\" || notification_r33.type === \"GROUP_INVITE\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"div\", 97);\n    i0.ɵɵelementStart(2, \"p\", 98);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60, 61);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const _r30 = i0.ɵɵreference(1);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onScroll(_r30));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 62);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"img\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const attachment_r58 = i0.ɵɵnextContext().$implicit;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r58.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r60.getFileIcon(attachment_r58.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r61.formatFileSize(attachment_r58.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 103);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 104);\n    i0.ɵɵelementStart(3, \"div\", 105)(4, \"div\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"span\", 108);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 110)(11, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelement(12, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.downloadAttachment(attachment_r58));\n    });\n    i0.ɵɵelement(14, \"i\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = ctx.$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r58.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r57.getFileTypeLabel(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r58.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nexport let NotificationListComponent = /*#__PURE__*/(() => {\n  class NotificationListComponent {\n    constructor(messageService, themeService, router) {\n      this.messageService = messageService;\n      this.themeService = themeService;\n      this.router = router;\n      this.loading = true;\n      this.loadingMore = false;\n      this.hasMoreNotifications = true;\n      this.error = null;\n      this.showOnlyUnread = false;\n      this.isSoundMuted = false;\n      // Propriétés pour la sélection multiple\n      this.selectedNotifications = new Set();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n      // Propriétés pour le modal des pièces jointes\n      this.showAttachmentsModal = false;\n      this.loadingAttachments = false;\n      this.currentAttachments = [];\n      // Propriétés pour le modal des détails de notification\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n      this.destroy$ = new Subject();\n      this.scrollPosition$ = new BehaviorSubject(0);\n      this.notifications$ = this.messageService.notifications$;\n      this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n      this.unreadCount$ = this.messageService.notificationCount$;\n      this.isDarkMode$ = this.themeService.darkMode$;\n      // Vérifier l'état du son\n      this.isSoundMuted = this.messageService.isMuted();\n    }\n    /**\n     * Rejoint une conversation ou un groupe à partir d'une notification\n     * @param notification Notification contenant les informations de la conversation ou du groupe\n     */\n    joinConversation(notification) {\n      console.log('Rejoindre la conversation:', notification);\n      // Marquer la notification comme lue\n      this.markAsRead(notification.id);\n      // Extraire les informations pertinentes de la notification\n      const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n      const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n      // Déterminer où rediriger l'utilisateur\n      if (conversationId) {\n        // Rediriger vers la conversation existante\n        console.log('Redirection vers la conversation existante:', conversationId);\n        // Utiliser le format exact de l'URL fournie avec l'ID\n        window.location.href = `/messages/conversations/chat/${conversationId}`;\n      } else if (groupId) {\n        // Rediriger vers le groupe\n        console.log('Redirection vers le groupe:', groupId);\n        window.location.href = `/messages/group/${groupId}`;\n      } else if (notification.senderId && notification.senderId.id) {\n        // Si aucun ID de conversation n'est trouvé, mais qu'il y a un expéditeur,\n        // utiliser getOrCreateConversation pour obtenir ou créer une conversation\n        console.log(\"Création/récupération d'une conversation avec l'utilisateur:\", notification.senderId.id);\n        // Afficher un indicateur de chargement si nécessaire\n        // this.loading = true;\n        this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n          next: conversation => {\n            console.log('Conversation obtenue:', conversation);\n            // this.loading = false;\n            if (conversation && conversation.id) {\n              // Rediriger vers la conversation nouvellement créée ou récupérée\n              // Utiliser le format exact de l'URL fournie avec l'ID\n              window.location.href = `/messages/conversations/chat/${conversation.id}`;\n            } else {\n              console.error('Conversation invalide reçue:', conversation);\n              window.location.href = '/messages';\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la création/récupération de la conversation:', error);\n            // this.loading = false;\n            // En cas d'erreur, rediriger vers la liste des messages\n            window.location.href = '/messages';\n          }\n        });\n      } else {\n        // Si aucune information n'est trouvée, rediriger vers la liste des messages\n        console.log('Redirection vers la liste des messages');\n        window.location.href = '/messages';\n      }\n    }\n    onScroll(target) {\n      if (!target) return;\n      const scrollPosition = target.scrollTop;\n      const scrollHeight = target.scrollHeight;\n      const clientHeight = target.clientHeight;\n      // Si on est proche du bas (à 200px du bas)\n      if (scrollHeight - scrollPosition - clientHeight < 200) {\n        this.scrollPosition$.next(scrollPosition);\n      }\n    }\n    ngOnInit() {\n      // Charger la préférence de son depuis le localStorage\n      const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n      if (savedMutePreference !== null) {\n        this.isSoundMuted = savedMutePreference === 'true';\n        console.log(`Préférence de son chargée: ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n        this.messageService.setMuted(this.isSoundMuted);\n      }\n      this.loadNotifications();\n      this.setupSubscriptions();\n      this.setupInfiniteScroll();\n      this.filterDeletedNotifications();\n    }\n    /**\n     * Filtre les notifications supprimées lors du chargement initial\n     */\n    filterDeletedNotifications() {\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      if (deletedNotificationIds.size > 0) {\n        console.log(`Filtrage de ${deletedNotificationIds.size} notifications supprimées`);\n        // Filtrer les notifications pour exclure celles qui ont été supprimées\n        this.notifications$.pipe(take(1)).subscribe(notifications => {\n          const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n          // Mettre à jour l'interface utilisateur\n          this.messageService.notifications.next(filteredNotifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          // Mettre à jour le cache de notifications dans le service\n          this.updateNotificationCache(filteredNotifications);\n        });\n      }\n    }\n    setupInfiniteScroll() {\n      // Configurer le chargement des anciennes notifications lors du défilement\n      this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n      // Attendre 200ms après le dernier événement de défilement\n      distinctUntilChanged(),\n      // Ne déclencher que si la position de défilement a changé\n      filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n      ).subscribe(() => {\n        this.loadMoreNotifications();\n      });\n    }\n    loadNotifications() {\n      console.log('NotificationListComponent: Loading notifications');\n      this.loading = true;\n      this.loadingMore = false;\n      this.error = null;\n      this.hasMoreNotifications = true;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n      this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n        // Filtrer les notifications supprimées\n        if (deletedNotificationIds.size > 0) {\n          return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          console.log('NotificationListComponent: Notifications loaded successfully', notifications);\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\n          this.messageService.notifications.next(notifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = notifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.loading = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          console.error('NotificationListComponent: Error loading notifications', err);\n          this.error = err;\n          this.loading = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    loadMoreNotifications() {\n      if (this.loadingMore || !this.hasMoreNotifications) return;\n      console.log('NotificationListComponent: Loading more notifications');\n      this.loadingMore = true;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n      this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n        // Filtrer les notifications supprimées\n        if (deletedNotificationIds.size > 0) {\n          const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n          console.log(`Filtré ${notifications.length - filteredNotifications.length} notifications supprimées`);\n          return filteredNotifications;\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          console.log('NotificationListComponent: More notifications loaded successfully', notifications);\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\n          this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n            const allNotifications = [...existingNotifications, ...notifications];\n            this.messageService.notifications.next(allNotifications);\n            // Mettre à jour le compteur de notifications non lues\n            const unreadCount = allNotifications.filter(n => !n.isRead).length;\n            this.messageService.notificationCount.next(unreadCount);\n            // Mettre à jour le cache de notifications dans le service\n            this.updateNotificationCache(allNotifications);\n          });\n          this.loadingMore = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          console.error('NotificationListComponent: Error loading more notifications', err);\n          this.loadingMore = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    setupSubscriptions() {\n      this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.error('Notification stream error:', error);\n        return of(null);\n      })).subscribe();\n      this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.error('Notifications read stream error:', error);\n        return of(null);\n      })).subscribe();\n    }\n    markAsRead(notificationId) {\n      console.log('Marking notification as read:', notificationId);\n      if (!notificationId) {\n        console.error('Invalid notification ID:', notificationId);\n        this.error = new Error('Invalid notification ID');\n        return;\n      }\n      // Afficher des informations de débogage sur la notification\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const notification = notifications.find(n => n.id === notificationId);\n        if (notification) {\n          console.log('Found notification to mark as read:', {\n            id: notification.id,\n            type: notification.type,\n            isRead: notification.isRead\n          });\n          // Mettre à jour localement la notification\n          const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n            ...n,\n            isRead: true,\n            readAt: new Date().toISOString()\n          } : n);\n          // Mettre à jour l'interface utilisateur immédiatement\n          this.messageService.notifications.next(updatedNotifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          // Mettre à jour le cache de notifications dans le service\n          this.updateNotificationCache(updatedNotifications);\n          // Appeler le service pour marquer la notification comme lue\n          this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n            next: result => {\n              console.log('Mark as read result:', result);\n              if (result && result.success) {\n                console.log('Notification marked as read successfully');\n                // Si l'erreur était liée à cette opération, la réinitialiser\n                if (this.error && this.error.message.includes('mark')) {\n                  this.error = null;\n                }\n              }\n            },\n            error: err => {\n              console.error('Error marking notification as read:', err);\n              console.error('Error details:', {\n                message: err.message,\n                stack: err.stack,\n                notificationId\n              });\n              // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n              // this.error = err;\n            }\n          });\n        } else {\n          console.warn('Notification not found in local cache:', notificationId);\n          // Forcer le rechargement des notifications\n          this.loadNotifications();\n        }\n      });\n    }\n    // Méthode pour mettre à jour le cache de notifications dans le service\n    updateNotificationCache(notifications) {\n      // Mettre à jour le cache de notifications dans le service\n      const notificationCache = this.messageService.notificationCache;\n      if (notificationCache) {\n        notifications.forEach(notification => {\n          notificationCache.set(notification.id, notification);\n        });\n        // Forcer la mise à jour du compteur\n        const unreadCount = notifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        console.log('Notification cache updated, new unread count:', unreadCount);\n      }\n    }\n    markAllAsRead() {\n      console.log('Marking all notifications as read');\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        console.log('All notifications:', notifications);\n        const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n        console.log('Unread notification IDs to mark as read:', unreadIds);\n        if (unreadIds.length === 0) {\n          console.log('No unread notifications to mark as read');\n          return;\n        }\n        // Vérifier que tous les IDs sont valides\n        const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n        if (validIds.length !== unreadIds.length) {\n          console.error('Some notification IDs are invalid:', unreadIds);\n          this.error = new Error('Invalid notification IDs');\n          return;\n        }\n        console.log('Marking all notifications as read:', validIds);\n        // Mettre à jour localement toutes les notifications\n        const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Appeler le service pour marquer toutes les notifications comme lues\n        this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            console.log('Mark all as read result:', result);\n            if (result && result.success) {\n              console.log('All notifications marked as read successfully');\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            console.error('Error marking all notifications as read:', err);\n            console.error('Error details:', {\n              message: err.message,\n              stack: err.stack\n            });\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n            // this.error = err;\n          }\n        });\n      });\n    }\n\n    hasNotifications() {\n      return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n    }\n    hasUnreadNotifications() {\n      return this.unreadCount$.pipe(map(count => count > 0));\n    }\n    /**\n     * Active/désactive le filtre pour n'afficher que les notifications non lues\n     */\n    toggleUnreadFilter() {\n      this.showOnlyUnread = !this.showOnlyUnread;\n      console.log(`Filtre des notifications non lues ${this.showOnlyUnread ? 'activé' : 'désactivé'}`);\n      if (this.showOnlyUnread) {\n        // Utiliser la méthode du service pour obtenir uniquement les notifications non lues\n        this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n      } else {\n        // Afficher toutes les notifications\n        this.filteredNotifications$ = this.notifications$;\n      }\n    }\n    /**\n     * Active/désactive le son des notifications\n     */\n    toggleSound() {\n      this.isSoundMuted = !this.isSoundMuted;\n      console.log(`Son des notifications ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n      // Utiliser la méthode du service pour activer/désactiver le son\n      this.messageService.setMuted(this.isSoundMuted);\n      // Tester le son si activé\n      if (!this.isSoundMuted) {\n        console.log('Test du son de notification...');\n        // Jouer le son après un court délai pour s'assurer que le navigateur est prêt\n        setTimeout(() => {\n          // Jouer le son deux fois pour s'assurer qu'il est audible\n          this.messageService.playNotificationSound();\n          // Jouer une deuxième fois après 1 seconde\n          setTimeout(() => {\n            this.messageService.playNotificationSound();\n          }, 1000);\n        }, 100);\n      }\n      // Sauvegarder la préférence dans le localStorage\n      localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n    }\n    /**\n     * Récupère les pièces jointes d'une notification et ouvre le modal\n     * @param notificationId ID de la notification\n     */\n    getNotificationAttachments(notificationId) {\n      if (!notificationId) {\n        console.error('ID de notification invalide');\n        return;\n      }\n      console.log(`Récupération des pièces jointes pour la notification ${notificationId}`);\n      // Réinitialiser les pièces jointes et afficher le modal\n      this.currentAttachments = [];\n      this.loadingAttachments = true;\n      this.showAttachmentsModal = true;\n      // Vérifier d'abord si la notification existe dans le cache local\n      let notification;\n      // Utiliser pipe(take(1)) pour obtenir la valeur actuelle de l'Observable\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        notification = notifications.find(n => n.id === notificationId);\n      });\n      if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n        console.log('Pièces jointes trouvées dans le cache local:', notification.message.attachments);\n        this.loadingAttachments = false;\n        // Conversion des pièces jointes au format attendu\n        this.currentAttachments = notification.message.attachments.map(attachment => ({\n          id: '',\n          url: attachment.url || '',\n          type: this.convertAttachmentType(attachment.type),\n          name: attachment.name || '',\n          size: attachment.size || 0,\n          duration: 0 // NotificationAttachment n'a pas de durée\n        }));\n\n        return;\n      }\n      // Si aucune pièce jointe n'est trouvée localement, essayer de les récupérer du serveur\n      this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: attachments => {\n          console.log(`${attachments.length} pièces jointes récupérées du serveur`, attachments);\n          this.loadingAttachments = false;\n          this.currentAttachments = attachments;\n        },\n        error: err => {\n          console.error('Erreur lors de la récupération des pièces jointes', err);\n          this.loadingAttachments = false;\n        }\n      });\n    }\n    /**\n     * Ferme le modal des pièces jointes\n     */\n    closeAttachmentsModal() {\n      this.showAttachmentsModal = false;\n    }\n    /**\n     * Ouvre le modal des détails de notification\n     * @param notification Notification à afficher\n     */\n    openNotificationDetails(notification) {\n      console.log('Ouverture des détails de la notification:', notification);\n      this.currentNotification = notification;\n      this.showNotificationDetailsModal = true;\n      // Marquer la notification comme lue\n      if (!notification.isRead) {\n        this.markAsRead(notification.id);\n      }\n    }\n    /**\n     * Ferme le modal des détails de notification\n     */\n    closeNotificationDetailsModal() {\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n    }\n    /**\n     * Vérifie si le type de fichier est une image\n     * @param type Type MIME du fichier\n     * @returns true si c'est une image, false sinon\n     */\n    isImage(type) {\n      return type?.startsWith('image/') || false;\n    }\n    /**\n     * Obtient l'icône FontAwesome correspondant au type de fichier\n     * @param type Type MIME du fichier\n     * @returns Classe CSS de l'icône\n     */\n    getFileIcon(type) {\n      if (!type) return 'fas fa-file';\n      if (type.startsWith('image/')) return 'fas fa-file-image';\n      if (type.startsWith('video/')) return 'fas fa-file-video';\n      if (type.startsWith('audio/')) return 'fas fa-file-audio';\n      if (type.startsWith('text/')) return 'fas fa-file-alt';\n      if (type.includes('pdf')) return 'fas fa-file-pdf';\n      if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n      if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n      if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n      return 'fas fa-file';\n    }\n    /**\n     * Obtient le libellé du type de fichier\n     * @param type Type MIME du fichier\n     * @returns Libellé du type de fichier\n     */\n    getFileTypeLabel(type) {\n      if (!type) return 'Fichier';\n      if (type.startsWith('image/')) return 'Image';\n      if (type.startsWith('video/')) return 'Vidéo';\n      if (type.startsWith('audio/')) return 'Audio';\n      if (type.startsWith('text/')) return 'Texte';\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('word') || type.includes('document')) return 'Document';\n      if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n      if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n      return 'Fichier';\n    }\n    /**\n     * Formate la taille du fichier en unités lisibles\n     * @param size Taille en octets\n     * @returns Taille formatée (ex: \"1.5 MB\")\n     */\n    formatFileSize(size) {\n      if (!size) return '';\n      const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n      let i = 0;\n      let formattedSize = size;\n      while (formattedSize >= 1024 && i < units.length - 1) {\n        formattedSize /= 1024;\n        i++;\n      }\n      return `${formattedSize.toFixed(1)} ${units[i]}`;\n    }\n    /**\n     * Ouvre une pièce jointe dans un nouvel onglet\n     * @param url URL de la pièce jointe\n     */\n    openAttachment(url) {\n      if (!url) return;\n      window.open(url, '_blank');\n    }\n    /**\n     * Télécharge une pièce jointe\n     * @param attachment Pièce jointe à télécharger\n     */\n    downloadAttachment(attachment) {\n      if (!attachment?.url) return;\n      const link = document.createElement('a');\n      link.href = attachment.url;\n      link.download = attachment.name || 'attachment';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    acceptFriendRequest(notification) {\n      this.markAsRead(notification.id);\n    }\n    /**\n     * Supprime une notification et la stocke dans le localStorage\n     * @param notificationId ID de la notification à supprimer\n     */\n    deleteNotification(notificationId) {\n      console.log('Suppression de la notification:', notificationId);\n      if (!notificationId) {\n        console.error('ID de notification invalide');\n        this.error = new Error('ID de notification invalide');\n        return;\n      }\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      // Ajouter l'ID de la notification à supprimer\n      deletedNotificationIds.add(notificationId);\n      // Sauvegarder les IDs dans le localStorage\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      // Appeler le service pour supprimer la notification\n      this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression:', result);\n          if (result && result.success) {\n            console.log('Notification supprimée avec succès');\n            // Si l'erreur était liée à cette opération, la réinitialiser\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression de la notification:', err);\n          // Même en cas d'erreur, conserver l'ID dans le localStorage\n          this.error = err;\n        }\n      });\n    }\n    /**\n     * Supprime toutes les notifications et les stocke dans le localStorage\n     */\n    deleteAllNotifications() {\n      console.log('Suppression de toutes les notifications');\n      // Récupérer toutes les notifications actuelles\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        // Récupérer les IDs des notifications supprimées du localStorage\n        const deletedNotificationIds = this.getDeletedNotificationIds();\n        // Ajouter tous les IDs des notifications actuelles\n        notifications.forEach(notification => {\n          deletedNotificationIds.add(notification.id);\n        });\n        // Sauvegarder les IDs dans le localStorage\n        this.saveDeletedNotificationIds(deletedNotificationIds);\n        // Appeler le service pour supprimer toutes les notifications\n        this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            console.log('Résultat de la suppression de toutes les notifications:', result);\n            if (result && result.success) {\n              console.log(`${result.count} notifications supprimées avec succès`);\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('suppression')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression de toutes les notifications:', err);\n            // Même en cas d'erreur, conserver les IDs dans le localStorage\n            this.error = err;\n          }\n        });\n      });\n    }\n    getErrorMessage() {\n      return this.error?.message || 'Unknown error occurred';\n    }\n    /**\n     * Récupère les IDs des notifications supprimées du localStorage\n     * @returns Set contenant les IDs des notifications supprimées\n     */\n    getDeletedNotificationIds() {\n      try {\n        const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n        if (deletedIdsJson) {\n          return new Set(JSON.parse(deletedIdsJson));\n        }\n        return new Set();\n      } catch (error) {\n        console.error('Erreur lors de la récupération des IDs de notifications supprimées:', error);\n        return new Set();\n      }\n    }\n    /**\n     * Sauvegarde les IDs des notifications supprimées dans le localStorage\n     * @param deletedIds Set contenant les IDs des notifications supprimées\n     */\n    saveDeletedNotificationIds(deletedIds) {\n      try {\n        localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n        console.log(`${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`);\n      } catch (error) {\n        console.error('Erreur lors de la sauvegarde des IDs de notifications supprimées:', error);\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    /**\n     * Sélectionne ou désélectionne une notification\n     * @param notificationId ID de la notification\n     * @param event Événement de la case à cocher\n     */\n    toggleSelection(notificationId, event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n      // Mettre à jour l'état de sélection globale\n      this.updateSelectionState();\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    }\n    /**\n     * Sélectionne ou désélectionne toutes les notifications\n     * @param event Événement de la case à cocher\n     */\n    toggleSelectAll(event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      this.allSelected = !this.allSelected;\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        if (this.allSelected) {\n          // Sélectionner toutes les notifications\n          notifications.forEach(notification => {\n            this.selectedNotifications.add(notification.id);\n          });\n        } else {\n          // Désélectionner toutes les notifications\n          this.selectedNotifications.clear();\n        }\n        // Afficher ou masquer la barre de sélection\n        this.showSelectionBar = this.selectedNotifications.size > 0;\n      });\n    }\n    /**\n     * Met à jour l'état de sélection globale\n     */\n    updateSelectionState() {\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n      });\n    }\n    /**\n     * Supprime les notifications sélectionnées\n     */\n    deleteSelectedNotifications() {\n      if (this.selectedNotifications.size === 0) {\n        return;\n      }\n      const selectedIds = Array.from(this.selectedNotifications);\n      console.log('Suppression des notifications sélectionnées:', selectedIds);\n      // Supprimer localement les notifications sélectionnées\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Réinitialiser la sélection\n        this.selectedNotifications.clear();\n        this.allSelected = false;\n        this.showSelectionBar = false;\n      });\n      // Appeler le service pour supprimer les notifications sélectionnées\n      this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression multiple:', result);\n          if (result && result.success) {\n            console.log(`${result.count} notifications supprimées avec succès`);\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression multiple des notifications:', err);\n        }\n      });\n    }\n    /**\n     * Marque les notifications sélectionnées comme lues\n     */\n    markSelectedAsRead() {\n      if (this.selectedNotifications.size === 0) {\n        return;\n      }\n      const selectedIds = Array.from(this.selectedNotifications);\n      console.log('Marquage des notifications sélectionnées comme lues:', selectedIds);\n      // Marquer localement les notifications sélectionnées comme lues\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n          ...notification,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : notification);\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Réinitialiser la sélection\n        this.selectedNotifications.clear();\n        this.allSelected = false;\n        this.showSelectionBar = false;\n      });\n      // Appeler le service pour marquer les notifications comme lues\n      this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat du marquage comme lu:', result);\n          if (result && result.success) {\n            console.log('Notifications marquées comme lues avec succès');\n          }\n        },\n        error: err => {\n          console.error('Erreur lors du marquage des notifications comme lues:', err);\n        }\n      });\n    }\n    /**\n     * Vérifie si une notification est sélectionnée\n     * @param notificationId ID de la notification\n     * @returns true si la notification est sélectionnée, false sinon\n     */\n    isSelected(notificationId) {\n      return this.selectedNotifications.has(notificationId);\n    }\n    /**\n     * Convertit un type de pièce jointe de notification en type de message\n     * @param type Type de pièce jointe\n     * @returns Type de message correspondant\n     */\n    convertAttachmentType(type) {\n      switch (type) {\n        case 'IMAGE':\n          return MessageType.IMAGE;\n        case 'FILE':\n          return MessageType.FILE;\n        case 'AUDIO':\n          return MessageType.AUDIO;\n        case 'VIDEO':\n          return MessageType.VIDEO;\n        case 'image':\n          return MessageType.IMAGE_LOWER;\n        case 'file':\n          return MessageType.FILE_LOWER;\n        case 'audio':\n          return MessageType.AUDIO_LOWER;\n        case 'video':\n          return MessageType.VIDEO_LOWER;\n        default:\n          return MessageType.FILE;\n        // Type par défaut\n      }\n    }\n\n    static {\n      this.ɵfac = function NotificationListComponent_Factory(t) {\n        return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationListComponent,\n        selectors: [[\"app-notification-list\"]],\n        viewQuery: function NotificationListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n          }\n        },\n        hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n              return ctx.onScroll($event.target);\n            });\n          }\n        },\n        decls: 28,\n        vars: 19,\n        consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer comme lu\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"click\"], [1, \"fas\", \"fa-comments\"], [\"title\", \"Marquer comme lu\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"]],\n        template: function NotificationListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"div\", 1);\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Notifications \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n            i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n            i0.ɵɵpipe(13, \"async\");\n            i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n            i0.ɵɵpipe(15, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n            i0.ɵɵelement(20, \"i\", 16);\n            i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 19);\n            i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n            i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 13, ctx.isDarkMode$));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 15, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 17, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe],\n        styles: [\"(()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  {// webpackBootstrap \\\"use strict\\\";})()[_ngcontent-%COMP%] ;{%BLOCK%}\"]\n      });\n    }\n  }\n  return NotificationListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}