{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { cacheSizes } from \"./sizes.js\";\nvar globalCaches = {};\nexport function registerGlobalCache(name, getSize) {\n  globalCaches[name] = getSize;\n}\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloClientMemoryInternals = globalThis.__DEV__ !== false ? _getApolloClientMemoryInternals : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getInMemoryCacheMemoryInternals = globalThis.__DEV__ !== false ? _getInMemoryCacheMemoryInternals : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloCacheMemoryInternals = globalThis.__DEV__ !== false ? _getApolloCacheMemoryInternals : undefined;\nfunction getCurrentCacheSizes() {\n  // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n  var defaults = {\n    parser: 1000 /* defaultCacheSizes[\"parser\"] */,\n    canonicalStringify: 1000 /* defaultCacheSizes[\"canonicalStringify\"] */,\n    print: 2000 /* defaultCacheSizes[\"print\"] */,\n    \"documentTransform.cache\": 2000 /* defaultCacheSizes[\"documentTransform.cache\"] */,\n    \"queryManager.getDocumentInfo\": 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */,\n    \"PersistedQueryLink.persistedQueryHashes\": 2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */,\n    \"fragmentRegistry.transform\": 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n    \"fragmentRegistry.lookup\": 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n    \"fragmentRegistry.findFragmentSpreads\": 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n    \"cache.fragmentQueryDocuments\": 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n    \"removeTypenameFromVariables.getVariableDefinitions\": 2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n    \"inMemoryCache.maybeBroadcastWatch\": 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n    \"inMemoryCache.executeSelectionSet\": 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n    \"inMemoryCache.executeSubSelectedArray\": 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */\n  };\n\n  return Object.fromEntries(Object.entries(defaults).map(function (_a) {\n    var k = _a[0],\n      v = _a[1];\n    return [k, cacheSizes[k] || v];\n  }));\n}\nfunction _getApolloClientMemoryInternals() {\n  var _a, _b, _c, _d, _e;\n  if (!(globalThis.__DEV__ !== false)) throw new Error(\"only supported in development mode\");\n  return {\n    limits: getCurrentCacheSizes(),\n    sizes: __assign({\n      print: (_a = globalCaches.print) === null || _a === void 0 ? void 0 : _a.call(globalCaches),\n      parser: (_b = globalCaches.parser) === null || _b === void 0 ? void 0 : _b.call(globalCaches),\n      canonicalStringify: (_c = globalCaches.canonicalStringify) === null || _c === void 0 ? void 0 : _c.call(globalCaches),\n      links: linkInfo(this.link),\n      queryManager: {\n        getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n        documentTransforms: transformInfo(this[\"queryManager\"].documentTransform)\n      }\n    }, (_e = (_d = this.cache).getMemoryInternals) === null || _e === void 0 ? void 0 : _e.call(_d))\n  };\n}\nfunction _getApolloCacheMemoryInternals() {\n  return {\n    cache: {\n      fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"])\n    }\n  };\n}\nfunction _getInMemoryCacheMemoryInternals() {\n  var fragments = this.config.fragments;\n  return __assign(__assign({}, _getApolloCacheMemoryInternals.apply(this)), {\n    addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]),\n    inMemoryCache: {\n      executeSelectionSet: getWrapperInformation(this[\"storeReader\"][\"executeSelectionSet\"]),\n      executeSubSelectedArray: getWrapperInformation(this[\"storeReader\"][\"executeSubSelectedArray\"]),\n      maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"])\n    },\n    fragmentRegistry: {\n      findFragmentSpreads: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.findFragmentSpreads),\n      lookup: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.lookup),\n      transform: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.transform)\n    }\n  });\n}\nfunction isWrapper(f) {\n  return !!f && \"dirtyKey\" in f;\n}\nfunction getWrapperInformation(f) {\n  return isWrapper(f) ? f.size : undefined;\n}\nfunction isDefined(value) {\n  return value != null;\n}\nfunction transformInfo(transform) {\n  return recurseTransformInfo(transform).map(function (cache) {\n    return {\n      cache: cache\n    };\n  });\n}\nfunction recurseTransformInfo(transform) {\n  return transform ? __spreadArray(__spreadArray([getWrapperInformation(transform === null || transform === void 0 ? void 0 : transform[\"performWork\"])], recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"left\"]), true), recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"right\"]), true).filter(isDefined) : [];\n}\nfunction linkInfo(link) {\n  var _a;\n  return link ? __spreadArray(__spreadArray([(_a = link === null || link === void 0 ? void 0 : link.getMemoryInternals) === null || _a === void 0 ? void 0 : _a.call(link)], linkInfo(link === null || link === void 0 ? void 0 : link.left), true), linkInfo(link === null || link === void 0 ? void 0 : link.right), true).filter(isDefined) : [];\n}\n//# sourceMappingURL=getMemoryInternals.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}