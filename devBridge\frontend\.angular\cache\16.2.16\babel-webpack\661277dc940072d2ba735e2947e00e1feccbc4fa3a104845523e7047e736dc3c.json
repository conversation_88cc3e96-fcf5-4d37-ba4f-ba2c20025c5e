{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation names\n *\n * A GraphQL document is only valid if all defined operations have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Operation-Name-Uniqueness\n */\nexport function UniqueOperationNamesRule(context) {\n  const knownOperationNames = Object.create(null);\n  return {\n    OperationDefinition(node) {\n      const operationName = node.name;\n      if (operationName) {\n        if (knownOperationNames[operationName.value]) {\n          context.reportError(new GraphQLError(`There can be only one operation named \"${operationName.value}\".`, {\n            nodes: [knownOperationNames[operationName.value], operationName]\n          }));\n        } else {\n          knownOperationNames[operationName.value] = operationName;\n        }\n      }\n      return false;\n    },\n    FragmentDefinition: () => false\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}