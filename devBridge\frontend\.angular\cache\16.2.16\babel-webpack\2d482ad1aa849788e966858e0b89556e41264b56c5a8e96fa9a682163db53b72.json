{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n// Components\nimport { TasksComponent } from './tasks.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TasksComponent\n}, {\n  path: ':teamId',\n  component: TasksComponent\n}];\nexport class TasksModule {\n  static {\n    this.ɵfac = function TasksModule_Factory(t) {\n      return new (t || TasksModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TasksModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TasksModule, {\n    declarations: [TasksComponent],\n    imports: [CommonModule, i1.RouterModule, SharedModule],\n    exports: [TasksComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "TasksComponent", "routes", "path", "component", "TasksModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\final pi\\devBridge\\frontend\\src\\app\\views\\front\\tasks\\tasks.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Shared Module\nimport { SharedModule } from '../../../shared/shared.module';\n\n// Components\nimport { TasksComponent } from './tasks.component';\n\nconst routes = [\n  {\n    path: '',\n    component: TasksComponent\n  },\n  {\n    path: ':teamId',\n    component: TasksComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TasksComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    SharedModule\n  ],\n  exports: [\n    TasksComponent\n  ]\n})\nexport class TasksModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,+BAA+B;AAE5D;AACA,SAASC,cAAc,QAAQ,mBAAmB;;;AAElD,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEH;CACZ,CACF;AAeD,OAAM,MAAOI,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBARpBP,YAAY,EACZC,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAMHK,WAAW;IAAAE,YAAA,GAXpBN,cAAc;IAAAO,OAAA,GAGdV,YAAY,EAAAW,EAAA,CAAAV,YAAA,EAEZC,YAAY;IAAAU,OAAA,GAGZT,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}