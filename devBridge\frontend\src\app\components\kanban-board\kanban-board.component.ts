import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TaskService } from '../../services/task.service';
import { 
  Task, 
  KanbanBoard, 
  KanbanFilters, 
  MoveTaskRequest,
  CreateTaskRequest 
} from '../../models/task.model';

@Component({
  selector: 'app-kanban-board',
  templateUrl: './kanban-board.component.html',
  styleUrls: ['./kanban-board.component.scss']
})
export class KanbanBoardComponent implements OnInit {
  @Input() teamId!: string;
  @Input() filters: KanbanFilters = {};
  @Output() taskSelected = new EventEmitter<Task>();
  @Output() taskCreated = new EventEmitter<Task>();

  kanbanData: KanbanBoard | null = null;
  loading = false;
  error: string | null = null;

  // Configuration des colonnes Kanban
  columns = [
    { id: 'backlog', title: 'Backlog', color: '#6b7280', icon: 'inventory_2' },
    { id: 'todo', title: 'À faire', color: '#3b82f6', icon: 'assignment' },
    { id: 'in-progress', title: 'En cours', color: '#f59e0b', icon: 'play_circle' },
    { id: 'review', title: 'Révision', color: '#8b5cf6', icon: 'rate_review' },
    { id: 'testing', title: 'Tests', color: '#06b6d4', icon: 'bug_report' },
    { id: 'done', title: 'Terminé', color: '#10b981', icon: 'check_circle' }
  ];

  constructor(
    private taskService: TaskService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadKanbanBoard();
  }

  // Charger le tableau Kanban
  loadKanbanBoard(): void {
    if (!this.teamId) return;

    this.loading = true;
    this.error = null;

    this.taskService.getKanbanBoard(this.teamId, this.filters).subscribe({
      next: (data) => {
        this.kanbanData = data;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement du tableau Kanban';
        this.loading = false;
        console.error('Erreur Kanban:', error);
        this.snackBar.open('Erreur lors du chargement', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Gérer le drag & drop
  onTaskDrop(event: CdkDragDrop<Task[]>): void {
    const task = event.item.data as Task;
    const newStatus = event.container.id;
    const oldStatus = event.previousContainer.id;

    if (event.previousContainer === event.container) {
      // Réorganisation dans la même colonne
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
      this.updateTaskPosition(task, event.currentIndex);
    } else {
      // Déplacement vers une autre colonne
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      this.moveTaskToColumn(task, newStatus, event.currentIndex, oldStatus);
    }
  }

  // Déplacer une tâche vers une nouvelle colonne
  private moveTaskToColumn(task: Task, newStatus: string, newPosition: number, oldStatus: string): void {
    const moveRequest: MoveTaskRequest = {
      newStatus,
      newPosition,
      oldStatus
    };

    this.taskService.moveTask(task._id!, moveRequest).subscribe({
      next: (response) => {
        this.snackBar.open('Tâche déplacée avec succès', 'Fermer', { duration: 2000 });
        // Mettre à jour les statistiques
        this.updateStats();
      },
      error: (error) => {
        console.error('Erreur déplacement tâche:', error);
        this.snackBar.open('Erreur lors du déplacement', 'Fermer', { duration: 3000 });
        // Annuler le déplacement visuel
        this.loadKanbanBoard();
      }
    });
  }

  // Mettre à jour la position d'une tâche
  private updateTaskPosition(task: Task, newPosition: number): void {
    const moveRequest: MoveTaskRequest = {
      newStatus: task.status,
      newPosition,
      oldStatus: task.status
    };

    this.taskService.moveTask(task._id!, moveRequest).subscribe({
      error: (error) => {
        console.error('Erreur mise à jour position:', error);
        this.loadKanbanBoard(); // Recharger en cas d'erreur
      }
    });
  }

  // Créer une nouvelle tâche dans une colonne
  createTaskInColumn(status: string): void {
    const taskData: CreateTaskRequest = {
      title: 'Nouvelle tâche',
      description: '',
      status,
      priority: 'medium',
      category: 'task'
    };

    this.taskService.createTaskInColumn(this.teamId, taskData).subscribe({
      next: (response) => {
        this.taskCreated.emit(response.task);
        this.loadKanbanBoard(); // Recharger pour voir la nouvelle tâche
        this.snackBar.open('Tâche créée avec succès', 'Fermer', { duration: 2000 });
      },
      error: (error) => {
        console.error('Erreur création tâche:', error);
        this.snackBar.open('Erreur lors de la création', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Sélectionner une tâche
  selectTask(task: Task): void {
    this.taskSelected.emit(task);
  }

  // Obtenir les tâches d'une colonne
  getColumnTasks(columnId: string): Task[] {
    if (!this.kanbanData) return [];
    return this.kanbanData.kanbanBoard[columnId as keyof typeof this.kanbanData.kanbanBoard] || [];
  }

  // Obtenir la couleur de priorité
  getPriorityColor(priority: string): string {
    const colors = {
      'lowest': '#6b7280',
      'low': '#3b82f6',
      'medium': '#f59e0b',
      'high': '#ef4444',
      'highest': '#dc2626',
      'critical': '#991b1b'
    };
    return colors[priority as keyof typeof colors] || '#6b7280';
  }

  // Obtenir l'icône de catégorie
  getCategoryIcon(category: string): string {
    const icons = {
      'feature': 'new_releases',
      'bug': 'bug_report',
      'improvement': 'trending_up',
      'task': 'assignment',
      'epic': 'flag',
      'story': 'book'
    };
    return icons[category as keyof typeof icons] || 'assignment';
  }

  // Mettre à jour les statistiques
  private updateStats(): void {
    if (this.kanbanData) {
      // Recalculer les statistiques localement
      const tasks = Object.values(this.kanbanData.kanbanBoard).flat();
      this.kanbanData.stats.total = tasks.length;
      this.kanbanData.stats.overdue = tasks.filter(task => task.isOverdue).length;
      this.kanbanData.stats.blocked = tasks.filter(task => task.isBlocked).length;
      
      // Mettre à jour les statistiques par statut
      Object.keys(this.kanbanData.kanbanBoard).forEach(status => {
        this.kanbanData!.stats.byStatus[status] = this.kanbanData!.kanbanBoard[status as keyof typeof this.kanbanData!.kanbanBoard].length;
      });
    }
  }

  // Appliquer des filtres
  applyFilters(newFilters: KanbanFilters): void {
    this.filters = { ...this.filters, ...newFilters };
    this.loadKanbanBoard();
  }

  // Réinitialiser les filtres
  resetFilters(): void {
    this.filters = {};
    this.loadKanbanBoard();
  }

  // Actualiser le tableau
  refresh(): void {
    this.loadKanbanBoard();
  }

  // Obtenir le nombre de tâches dans une colonne
  getColumnCount(columnId: string): number {
    return this.getColumnTasks(columnId).length;
  }

  // Vérifier si une colonne est vide
  isColumnEmpty(columnId: string): boolean {
    return this.getColumnTasks(columnId).length === 0;
  }

  // Obtenir le texte de placeholder pour une colonne vide
  getEmptyColumnText(columnId: string): string {
    const texts = {
      'backlog': 'Aucune tâche en attente',
      'todo': 'Aucune tâche à faire',
      'in-progress': 'Aucune tâche en cours',
      'review': 'Aucune tâche en révision',
      'testing': 'Aucune tâche en test',
      'done': 'Aucune tâche terminée'
    };
    return texts[columnId as keyof typeof texts] || 'Aucune tâche';
  }
}
