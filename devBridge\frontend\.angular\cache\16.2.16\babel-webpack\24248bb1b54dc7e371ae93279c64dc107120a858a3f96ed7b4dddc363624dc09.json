{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * The {@link formatDuration} function options.\n */\n\nconst defaultFormat = [\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param duration - The duration to format\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport function formatDuration(duration, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const format = options?.format ?? defaultFormat;\n  const zero = options?.zero ?? false;\n  const delimiter = options?.delimiter ?? \" \";\n  if (!locale.formatDistance) {\n    return \"\";\n  }\n  const result = format.reduce((acc, unit) => {\n    const token = `x${unit.replace(/(^.)/, m => m.toUpperCase())}`;\n    const value = duration[unit];\n    if (value !== undefined && (zero || duration[unit])) {\n      return acc.concat(locale.formatDistance(token, value));\n    }\n    return acc;\n  }, []).join(delimiter);\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatDuration;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}