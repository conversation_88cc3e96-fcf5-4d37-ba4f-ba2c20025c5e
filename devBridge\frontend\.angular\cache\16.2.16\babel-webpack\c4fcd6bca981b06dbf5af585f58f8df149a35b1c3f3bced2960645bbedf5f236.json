{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { visit, BREAK, isSelectionNode } from \"graphql\";\nimport { argumentsObjectFromField, buildQueryFromSelectionSet, createFragmentMap, getFragmentDefinitions, getMainDefinition, hasDirectives, isField, isInlineFragment, mergeDeep, mergeDeepArray, removeClientSetsFromDocument, resultKeyNameFromField, shouldInclude } from \"../utilities/index.js\";\nimport { cacheSlot } from \"../cache/index.js\";\nvar LocalState = /** @class */function () {\n  function LocalState(_a) {\n    var cache = _a.cache,\n      client = _a.client,\n      resolvers = _a.resolvers,\n      fragmentMatcher = _a.fragmentMatcher;\n    this.selectionsToResolveCache = new WeakMap();\n    this.cache = cache;\n    if (client) {\n      this.client = client;\n    }\n    if (resolvers) {\n      this.addResolvers(resolvers);\n    }\n    if (fragmentMatcher) {\n      this.setFragmentMatcher(fragmentMatcher);\n    }\n  }\n  LocalState.prototype.addResolvers = function (resolvers) {\n    var _this = this;\n    this.resolvers = this.resolvers || {};\n    if (Array.isArray(resolvers)) {\n      resolvers.forEach(function (resolverGroup) {\n        _this.resolvers = mergeDeep(_this.resolvers, resolverGroup);\n      });\n    } else {\n      this.resolvers = mergeDeep(this.resolvers, resolvers);\n    }\n  };\n  LocalState.prototype.setResolvers = function (resolvers) {\n    this.resolvers = {};\n    this.addResolvers(resolvers);\n  };\n  LocalState.prototype.getResolvers = function () {\n    return this.resolvers || {};\n  };\n  // Run local client resolvers against the incoming query and remote data.\n  // Locally resolved field values are merged with the incoming remote data,\n  // and returned. Note that locally resolved fields will overwrite\n  // remote data using the same field name.\n  LocalState.prototype.runResolvers = function (_a) {\n    return __awaiter(this, arguments, void 0, function (_b) {\n      var document = _b.document,\n        remoteResult = _b.remoteResult,\n        context = _b.context,\n        variables = _b.variables,\n        _c = _b.onlyRunForcedResolvers,\n        onlyRunForcedResolvers = _c === void 0 ? false : _c;\n      return __generator(this, function (_d) {\n        if (document) {\n          return [2 /*return*/, this.resolveDocument(document, remoteResult.data, context, variables, this.fragmentMatcher, onlyRunForcedResolvers).then(function (localResult) {\n            return __assign(__assign({}, remoteResult), {\n              data: localResult.result\n            });\n          })];\n        }\n        return [2 /*return*/, remoteResult];\n      });\n    });\n  };\n  LocalState.prototype.setFragmentMatcher = function (fragmentMatcher) {\n    this.fragmentMatcher = fragmentMatcher;\n  };\n  LocalState.prototype.getFragmentMatcher = function () {\n    return this.fragmentMatcher;\n  };\n  // Client queries contain everything in the incoming document (if a @client\n  // directive is found).\n  LocalState.prototype.clientQuery = function (document) {\n    if (hasDirectives([\"client\"], document)) {\n      if (this.resolvers) {\n        return document;\n      }\n    }\n    return null;\n  };\n  // Server queries are stripped of all @client based selection sets.\n  LocalState.prototype.serverQuery = function (document) {\n    return removeClientSetsFromDocument(document);\n  };\n  LocalState.prototype.prepareContext = function (context) {\n    var cache = this.cache;\n    return __assign(__assign({}, context), {\n      cache: cache,\n      // Getting an entry's cache key is useful for local state resolvers.\n      getCacheKey: function (obj) {\n        return cache.identify(obj);\n      }\n    });\n  };\n  // To support `@client @export(as: \"someVar\")` syntax, we'll first resolve\n  // @client @export fields locally, then pass the resolved values back to be\n  // used alongside the original operation variables.\n  LocalState.prototype.addExportedVariables = function (document_1) {\n    return __awaiter(this, arguments, void 0, function (document, variables, context) {\n      if (variables === void 0) {\n        variables = {};\n      }\n      if (context === void 0) {\n        context = {};\n      }\n      return __generator(this, function (_a) {\n        if (document) {\n          return [2 /*return*/, this.resolveDocument(document, this.buildRootValueFromCache(document, variables) || {}, this.prepareContext(context), variables).then(function (data) {\n            return __assign(__assign({}, variables), data.exportedVariables);\n          })];\n        }\n        return [2 /*return*/, __assign({}, variables)];\n      });\n    });\n  };\n  LocalState.prototype.shouldForceResolvers = function (document) {\n    var forceResolvers = false;\n    visit(document, {\n      Directive: {\n        enter: function (node) {\n          if (node.name.value === \"client\" && node.arguments) {\n            forceResolvers = node.arguments.some(function (arg) {\n              return arg.name.value === \"always\" && arg.value.kind === \"BooleanValue\" && arg.value.value === true;\n            });\n            if (forceResolvers) {\n              return BREAK;\n            }\n          }\n        }\n      }\n    });\n    return forceResolvers;\n  };\n  // Query the cache and return matching data.\n  LocalState.prototype.buildRootValueFromCache = function (document, variables) {\n    return this.cache.diff({\n      query: buildQueryFromSelectionSet(document),\n      variables: variables,\n      returnPartialData: true,\n      optimistic: false\n    }).result;\n  };\n  LocalState.prototype.resolveDocument = function (document_1, rootValue_1) {\n    return __awaiter(this, arguments, void 0, function (document, rootValue, context, variables, fragmentMatcher, onlyRunForcedResolvers) {\n      var mainDefinition, fragments, fragmentMap, selectionsToResolve, definitionOperation, defaultOperationType, _a, cache, client, execContext, isClientFieldDescendant;\n      if (context === void 0) {\n        context = {};\n      }\n      if (variables === void 0) {\n        variables = {};\n      }\n      if (fragmentMatcher === void 0) {\n        fragmentMatcher = function () {\n          return true;\n        };\n      }\n      if (onlyRunForcedResolvers === void 0) {\n        onlyRunForcedResolvers = false;\n      }\n      return __generator(this, function (_b) {\n        mainDefinition = getMainDefinition(document);\n        fragments = getFragmentDefinitions(document);\n        fragmentMap = createFragmentMap(fragments);\n        selectionsToResolve = this.collectSelectionsToResolve(mainDefinition, fragmentMap);\n        definitionOperation = mainDefinition.operation;\n        defaultOperationType = definitionOperation ? definitionOperation.charAt(0).toUpperCase() + definitionOperation.slice(1) : \"Query\";\n        _a = this, cache = _a.cache, client = _a.client;\n        execContext = {\n          fragmentMap: fragmentMap,\n          context: __assign(__assign({}, context), {\n            cache: cache,\n            client: client\n          }),\n          variables: variables,\n          fragmentMatcher: fragmentMatcher,\n          defaultOperationType: defaultOperationType,\n          exportedVariables: {},\n          selectionsToResolve: selectionsToResolve,\n          onlyRunForcedResolvers: onlyRunForcedResolvers\n        };\n        isClientFieldDescendant = false;\n        return [2 /*return*/, this.resolveSelectionSet(mainDefinition.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (result) {\n          return {\n            result: result,\n            exportedVariables: execContext.exportedVariables\n          };\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveSelectionSet = function (selectionSet, isClientFieldDescendant, rootValue, execContext) {\n    return __awaiter(this, void 0, void 0, function () {\n      var fragmentMap, context, variables, resultsToMerge, execute;\n      var _this = this;\n      return __generator(this, function (_a) {\n        fragmentMap = execContext.fragmentMap, context = execContext.context, variables = execContext.variables;\n        resultsToMerge = [rootValue];\n        execute = function (selection) {\n          return __awaiter(_this, void 0, void 0, function () {\n            var fragment, typeCondition;\n            return __generator(this, function (_a) {\n              if (!isClientFieldDescendant && !execContext.selectionsToResolve.has(selection)) {\n                // Skip selections without @client directives\n                // (still processing if one of the ancestors or one of the child fields has @client directive)\n                return [2 /*return*/];\n              }\n\n              if (!shouldInclude(selection, variables)) {\n                // Skip this entirely.\n                return [2 /*return*/];\n              }\n\n              if (isField(selection)) {\n                return [2 /*return*/, this.resolveField(selection, isClientFieldDescendant, rootValue, execContext).then(function (fieldResult) {\n                  var _a;\n                  if (typeof fieldResult !== \"undefined\") {\n                    resultsToMerge.push((_a = {}, _a[resultKeyNameFromField(selection)] = fieldResult, _a));\n                  }\n                })];\n              }\n              if (isInlineFragment(selection)) {\n                fragment = selection;\n              } else {\n                // This is a named fragment.\n                fragment = fragmentMap[selection.name.value];\n                invariant(fragment, 19, selection.name.value);\n              }\n              if (fragment && fragment.typeCondition) {\n                typeCondition = fragment.typeCondition.name.value;\n                if (execContext.fragmentMatcher(rootValue, typeCondition, context)) {\n                  return [2 /*return*/, this.resolveSelectionSet(fragment.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (fragmentResult) {\n                    resultsToMerge.push(fragmentResult);\n                  })];\n                }\n              }\n              return [2 /*return*/];\n            });\n          });\n        };\n\n        return [2 /*return*/, Promise.all(selectionSet.selections.map(execute)).then(function () {\n          return mergeDeepArray(resultsToMerge);\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveField = function (field, isClientFieldDescendant, rootValue, execContext) {\n    return __awaiter(this, void 0, void 0, function () {\n      var variables, fieldName, aliasedFieldName, aliasUsed, defaultResult, resultPromise, resolverType, resolverMap, resolve;\n      var _this = this;\n      return __generator(this, function (_a) {\n        if (!rootValue) {\n          return [2 /*return*/, null];\n        }\n        variables = execContext.variables;\n        fieldName = field.name.value;\n        aliasedFieldName = resultKeyNameFromField(field);\n        aliasUsed = fieldName !== aliasedFieldName;\n        defaultResult = rootValue[aliasedFieldName] || rootValue[fieldName];\n        resultPromise = Promise.resolve(defaultResult);\n        // Usually all local resolvers are run when passing through here, but\n        // if we've specifically identified that we only want to run forced\n        // resolvers (that is, resolvers for fields marked with\n        // `@client(always: true)`), then we'll skip running non-forced resolvers.\n        if (!execContext.onlyRunForcedResolvers || this.shouldForceResolvers(field)) {\n          resolverType = rootValue.__typename || execContext.defaultOperationType;\n          resolverMap = this.resolvers && this.resolvers[resolverType];\n          if (resolverMap) {\n            resolve = resolverMap[aliasUsed ? fieldName : aliasedFieldName];\n            if (resolve) {\n              resultPromise = Promise.resolve(\n              // In case the resolve function accesses reactive variables,\n              // set cacheSlot to the current cache instance.\n              cacheSlot.withValue(this.cache, resolve, [rootValue, argumentsObjectFromField(field, variables), execContext.context, {\n                field: field,\n                fragmentMap: execContext.fragmentMap\n              }]));\n            }\n          }\n        }\n        return [2 /*return*/, resultPromise.then(function (result) {\n          var _a, _b;\n          if (result === void 0) {\n            result = defaultResult;\n          }\n          // If an @export directive is associated with the current field, store\n          // the `as` export variable name and current result for later use.\n          if (field.directives) {\n            field.directives.forEach(function (directive) {\n              if (directive.name.value === \"export\" && directive.arguments) {\n                directive.arguments.forEach(function (arg) {\n                  if (arg.name.value === \"as\" && arg.value.kind === \"StringValue\") {\n                    execContext.exportedVariables[arg.value.value] = result;\n                  }\n                });\n              }\n            });\n          }\n          // Handle all scalar types here.\n          if (!field.selectionSet) {\n            return result;\n          }\n          // From here down, the field has a selection set, which means it's trying\n          // to query a GraphQLObjectType.\n          if (result == null) {\n            // Basically any field in a GraphQL response can be null, or missing\n            return result;\n          }\n          var isClientField = (_b = (_a = field.directives) === null || _a === void 0 ? void 0 : _a.some(function (d) {\n            return d.name.value === \"client\";\n          })) !== null && _b !== void 0 ? _b : false;\n          if (Array.isArray(result)) {\n            return _this.resolveSubSelectedArray(field, isClientFieldDescendant || isClientField, result, execContext);\n          }\n          // Returned value is an object, and the query has a sub-selection. Recurse.\n          if (field.selectionSet) {\n            return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant || isClientField, result, execContext);\n          }\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveSubSelectedArray = function (field, isClientFieldDescendant, result, execContext) {\n    var _this = this;\n    return Promise.all(result.map(function (item) {\n      if (item === null) {\n        return null;\n      }\n      // This is a nested array, recurse.\n      if (Array.isArray(item)) {\n        return _this.resolveSubSelectedArray(field, isClientFieldDescendant, item, execContext);\n      }\n      // This is an object, run the selection set on it.\n      if (field.selectionSet) {\n        return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant, item, execContext);\n      }\n    }));\n  };\n  // Collect selection nodes on paths from document root down to all @client directives.\n  // This function takes into account transitive fragment spreads.\n  // Complexity equals to a single `visit` over the full document.\n  LocalState.prototype.collectSelectionsToResolve = function (mainDefinition, fragmentMap) {\n    var isSingleASTNode = function (node) {\n      return !Array.isArray(node);\n    };\n    var selectionsToResolveCache = this.selectionsToResolveCache;\n    function collectByDefinition(definitionNode) {\n      if (!selectionsToResolveCache.has(definitionNode)) {\n        var matches_1 = new Set();\n        selectionsToResolveCache.set(definitionNode, matches_1);\n        visit(definitionNode, {\n          Directive: function (node, _, __, ___, ancestors) {\n            if (node.name.value === \"client\") {\n              ancestors.forEach(function (node) {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches_1.add(node);\n                }\n              });\n            }\n          },\n          FragmentSpread: function (spread, _, __, ___, ancestors) {\n            var fragment = fragmentMap[spread.name.value];\n            invariant(fragment, 20, spread.name.value);\n            var fragmentSelections = collectByDefinition(fragment);\n            if (fragmentSelections.size > 0) {\n              // Fragment for this spread contains @client directive (either directly or transitively)\n              // Collect selection nodes on paths from the root down to fields with the @client directive\n              ancestors.forEach(function (node) {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches_1.add(node);\n                }\n              });\n              matches_1.add(spread);\n              fragmentSelections.forEach(function (selection) {\n                matches_1.add(selection);\n              });\n            }\n          }\n        });\n      }\n      return selectionsToResolveCache.get(definitionNode);\n    }\n    return collectByDefinition(mainDefinition);\n  };\n  return LocalState;\n}();\nexport { LocalState };\n//# sourceMappingURL=LocalState.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}