{"ast": null, "code": "// Spec Section: \"Executable Definitions\"\nimport { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule.mjs'; // Spec Section: \"Field Selections on Objects, Interfaces, and Unions Types\"\n\nimport { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule.mjs'; // Spec Section: \"Fragments on Composite Types\"\n\nimport { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule.mjs'; // Spec Section: \"Argument Names\"\n\nimport { KnownArgumentNamesOnDirectivesRule, KnownArgumentNamesRule } from './rules/KnownArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Defined\"\n\nimport { KnownDirectivesRule } from './rules/KnownDirectivesRule.mjs'; // Spec Section: \"Fragment spread target defined\"\n\nimport { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule.mjs'; // Spec Section: \"Fragment Spread Type Existence\"\n\nimport { KnownTypeNamesRule } from './rules/KnownTypeNamesRule.mjs'; // Spec Section: \"Lone Anonymous Operation\"\n\nimport { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule.mjs'; // SDL-specific validation rules\n\nimport { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule.mjs'; // Spec Section: \"Fragments must not form cycles\"\n\nimport { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule.mjs'; // Spec Section: \"All Variable Used Defined\"\n\nimport { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule.mjs'; // Spec Section: \"Fragments must be used\"\n\nimport { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule.mjs'; // Spec Section: \"All Variables Used\"\n\nimport { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule.mjs'; // Spec Section: \"Field Selection Merging\"\n\nimport { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule.mjs'; // Spec Section: \"Fragment spread is possible\"\n\nimport { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule.mjs';\nimport { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule.mjs'; // Spec Section: \"Argument Optionality\"\n\nimport { ProvidedRequiredArgumentsOnDirectivesRule, ProvidedRequiredArgumentsRule } from './rules/ProvidedRequiredArgumentsRule.mjs'; // Spec Section: \"Leaf Field Selections\"\n\nimport { ScalarLeafsRule } from './rules/ScalarLeafsRule.mjs'; // Spec Section: \"Subscriptions with Single Root Field\"\n\nimport { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule.mjs';\nimport { UniqueArgumentDefinitionNamesRule } from './rules/UniqueArgumentDefinitionNamesRule.mjs'; // Spec Section: \"Argument Uniqueness\"\n\nimport { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule.mjs';\nimport { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule.mjs'; // Spec Section: \"Directives Are Unique Per Location\"\n\nimport { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule.mjs';\nimport { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule.mjs';\nimport { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule.mjs'; // Spec Section: \"Fragment Name Uniqueness\"\n\nimport { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule.mjs'; // Spec Section: \"Input Object Field Uniqueness\"\n\nimport { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule.mjs'; // Spec Section: \"Operation Name Uniqueness\"\n\nimport { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule.mjs';\nimport { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule.mjs';\nimport { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule.mjs'; // Spec Section: \"Variable Uniqueness\"\n\nimport { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule.mjs'; // Spec Section: \"Value Type Correctness\"\n\nimport { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule.mjs'; // Spec Section: \"Variables are Input Types\"\n\nimport { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule.mjs'; // Spec Section: \"All Variable Usages Are Allowed\"\n\nimport { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule.mjs';\n\n/**\n * This set includes all validation rules defined by the GraphQL spec.\n *\n * The order of the rules in this list has been adjusted to lead to the\n * most clear output when encountering multiple validation errors.\n */\nexport const specifiedRules = Object.freeze([ExecutableDefinitionsRule, UniqueOperationNamesRule, LoneAnonymousOperationRule, SingleFieldSubscriptionsRule, KnownTypeNamesRule, FragmentsOnCompositeTypesRule, VariablesAreInputTypesRule, ScalarLeafsRule, FieldsOnCorrectTypeRule, UniqueFragmentNamesRule, KnownFragmentNamesRule, NoUnusedFragmentsRule, PossibleFragmentSpreadsRule, NoFragmentCyclesRule, UniqueVariableNamesRule, NoUndefinedVariablesRule, NoUnusedVariablesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, KnownArgumentNamesRule, UniqueArgumentNamesRule, ValuesOfCorrectTypeRule, ProvidedRequiredArgumentsRule, VariablesInAllowedPositionRule, OverlappingFieldsCanBeMergedRule, UniqueInputFieldNamesRule]);\n/**\n * @internal\n */\n\nexport const specifiedSDLRules = Object.freeze([LoneSchemaDefinitionRule, UniqueOperationTypesRule, UniqueTypeNamesRule, UniqueEnumValueNamesRule, UniqueFieldDefinitionNamesRule, UniqueArgumentDefinitionNamesRule, UniqueDirectiveNamesRule, KnownTypeNamesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, PossibleTypeExtensionsRule, KnownArgumentNamesOnDirectivesRule, UniqueArgumentNamesRule, UniqueInputFieldNamesRule, ProvidedRequiredArgumentsOnDirectivesRule]);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}