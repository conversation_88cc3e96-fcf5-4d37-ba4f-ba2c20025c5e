{"ast": null, "code": "import { invariant } from '../jsutils/invariant.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { executeSync } from '../execution/execute.mjs';\nimport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n/**\n * Build an IntrospectionQuery from a GraphQLSchema\n *\n * IntrospectionQuery is useful for utilities that care about type and field\n * relationships, but do not need to traverse through those relationships.\n *\n * This is the inverse of buildClientSchema. The primary use case is outside\n * of the server context, for instance when doing schema comparisons.\n */\n\nexport function introspectionFromSchema(schema, options) {\n  const optionsWithDefaults = {\n    specifiedByUrl: true,\n    directiveIsRepeatable: true,\n    schemaDescription: true,\n    inputValueDeprecation: true,\n    ...options\n  };\n  const document = parse(getIntrospectionQuery(optionsWithDefaults));\n  const result = executeSync({\n    schema,\n    document\n  });\n  !result.errors && result.data || invariant(false);\n  return result.data;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}