{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../ai-chat/ai-chat.component\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-person-fill\": a2\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-success bg-opacity-10 text-success\": a0,\n    \"bg-primary bg-opacity-10 text-primary\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-person-fill-gear\": a0,\n    \"bi-person\": a1\n  };\n};\nfunction EquipeDetailComponent_div_0_div_122_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 88)(3, \"div\", 89);\n    i0.ɵɵelement(4, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 91);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 92)(9, \"span\", 93);\n    i0.ɵɵelement(10, \"i\", 90);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"small\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_122_div_4_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const membre_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.removeMembreFromEquipe(membre_r8._id));\n    });\n    i0.ɵɵelement(15, \"i\", 96);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membre_r8 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getUserName(membre_r8.user), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c2, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c3, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", membre_r8.role === \"admin\" ? \"Administrateur\" : \"Membre\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\" ? \"\\u00C9tudiant\" : ctx_r5.getUserProfession(membre_r8.user) === \"professeur\" ? \"Professeur\" : \"Utilisateur\");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 98);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_10_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r15._id || user_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r15.firstName || \"\", \" \", user_r15.lastName || user_r15.name || user_r15.id, \" \", user_r15.email ? \"- \" + user_r15.email : \"\", \" \", user_r15.profession ? \"(\" + (user_r15.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"label\", 101);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 102, 103)(6, \"option\", 104);\n    i0.ɵɵtext(7, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_div_122_div_10_option_8_Template, 2, 5, \"option\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 100)(10, \"label\", 106);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 107)(13, \"div\", 108);\n    i0.ɵɵelement(14, \"input\", 109, 110);\n    i0.ɵɵelementStart(16, \"label\", 111);\n    i0.ɵɵelement(17, \"i\", 112);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 108);\n    i0.ɵɵelement(20, \"input\", 113, 114);\n    i0.ɵɵelementStart(22, \"label\", 115);\n    i0.ɵɵelement(23, \"i\", 116);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 117)(26, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_122_div_10_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const _r11 = i0.ɵɵreference(5);\n      const _r13 = i0.ɵɵreference(15);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      ctx_r16.addMembre(_r11.value, _r13.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r11.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 119);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(5);\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r11.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 43)(2, \"div\", 78)(3, \"div\", 79);\n    i0.ɵɵtemplate(4, EquipeDetailComponent_div_0_div_122_div_4_Template, 16, 21, \"div\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 81)(6, \"h5\", 82);\n    i0.ɵɵelement(7, \"i\", 83);\n    i0.ɵɵtext(8, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, EquipeDetailComponent_div_0_div_122_div_9_Template, 4, 0, \"div\", 84);\n    i0.ɵɵtemplate(10, EquipeDetailComponent_div_0_div_122_div_10_Template, 29, 2, \"div\", 85);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.teamMembers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 98);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r24._id || user_r24.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r24.firstName || \"\", \" \", user_r24.lastName || user_r24.name || user_r24.id, \" \", user_r24.email ? \"- \" + user_r24.email : \"\", \" \", user_r24.profession ? \"(\" + (user_r24.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"label\", 124);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 125, 126)(6, \"option\", 104);\n    i0.ɵɵtext(7, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template, 2, 5, \"option\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 100)(10, \"label\", 127);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 107)(13, \"div\", 108);\n    i0.ɵɵelement(14, \"input\", 128, 129);\n    i0.ɵɵelementStart(16, \"label\", 130);\n    i0.ɵɵelement(17, \"i\", 112);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 108);\n    i0.ɵɵelement(20, \"input\", 131, 132);\n    i0.ɵɵelementStart(22, \"label\", 133);\n    i0.ɵɵelement(23, \"i\", 116);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 117)(26, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_ng_template_123_div_14_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const _r20 = i0.ɵɵreference(5);\n      const _r22 = i0.ɵɵreference(15);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      ctx_r25.addMembre(_r20.value, _r22.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r20.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 119);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(5);\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r20.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 78)(2, \"div\", 121)(3, \"div\", 122);\n    i0.ɵɵelement(4, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 94);\n    i0.ɵɵtext(6, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 94);\n    i0.ɵɵtext(8, \" Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-contre. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 81)(10, \"h5\", 82);\n    i0.ɵɵelement(11, \"i\", 83);\n    i0.ɵɵtext(12, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, EquipeDetailComponent_div_0_ng_template_123_div_13_Template, 4, 0, \"div\", 84);\n    i0.ɵɵtemplate(14, EquipeDetailComponent_div_0_ng_template_123_div_14_Template, 29, 2, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"div\", 4)(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelement(6, \"div\", 8)(7, \"div\", 8)(8, \"div\", 8)(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10);\n    i0.ɵɵelement(19, \"div\", 11)(20, \"div\", 12);\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15)(24, \"h1\", 16);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\", 17);\n    i0.ɵɵtext(27, \" Gestion et collaboration d'\\u00E9quipe - Administration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19);\n    i0.ɵɵelement(30, \"i\", 20);\n    i0.ɵɵelementStart(31, \"span\", 21);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"small\", 22);\n    i0.ɵɵtext(34, \"Membres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 19);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementStart(37, \"span\", 21);\n    i0.ɵɵtext(38, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"small\", 22);\n    i0.ɵɵtext(40, \"T\\u00E2ches\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 19);\n    i0.ɵɵelement(42, \"i\", 24);\n    i0.ɵɵelementStart(43, \"span\", 25);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"small\", 22);\n    i0.ɵɵtext(46, \"Cr\\u00E9\\u00E9e le\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 26)(48, \"h4\", 27);\n    i0.ɵɵelement(49, \"i\", 28);\n    i0.ɵɵtext(50, \"Actions rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 29)(52, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.navigateToTasks());\n    });\n    i0.ɵɵelement(53, \"i\", 31);\n    i0.ɵɵtext(54, \" G\\u00E9rer les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.navigateToEditEquipe());\n    });\n    i0.ɵɵelement(56, \"i\", 33);\n    i0.ɵɵtext(57, \" Modifier l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 34)(59, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.navigateToEquipeList());\n    });\n    i0.ɵɵelement(60, \"i\", 36);\n    i0.ɵɵtext(61, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.deleteEquipe());\n    });\n    i0.ɵɵelement(63, \"i\", 38);\n    i0.ɵɵtext(64, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(65, \"div\", 39)(66, \"div\", 40)(67, \"div\", 41)(68, \"div\", 42)(69, \"div\", 43)(70, \"div\", 44)(71, \"div\", 45);\n    i0.ɵɵelement(72, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"h3\", 47);\n    i0.ɵɵtext(74, \"\\u00C0 propos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"p\", 48);\n    i0.ɵɵtext(76, \" D\\u00E9tails et informations sur l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 49)(78, \"div\", 50)(79, \"h4\", 51);\n    i0.ɵɵtext(80, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 52);\n    i0.ɵɵelement(82, \"i\", 53);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"div\", 54)(85, \"p\", 55);\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 56)(88, \"span\", 57);\n    i0.ɵɵelement(89, \"i\", 58);\n    i0.ɵɵtext(90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"span\", 59);\n    i0.ɵɵelement(92, \"i\", 60);\n    i0.ɵɵtext(93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\", 61);\n    i0.ɵɵelement(95, \"i\", 62);\n    i0.ɵɵtext(96, \" Gestion de projet \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(97, \"div\", 39)(98, \"div\", 40)(99, \"div\", 41)(100, \"div\", 63)(101, \"div\", 64)(102, \"h3\", 65)(103, \"div\", 66);\n    i0.ɵɵelement(104, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \" Assistant IA Gemini \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"span\", 68);\n    i0.ɵɵelement(107, \"i\", 69);\n    i0.ɵɵtext(108, \" G\\u00E9n\\u00E9ration de t\\u00E2ches intelligente \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 42);\n    i0.ɵɵelement(110, \"app-ai-chat\", 70);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(111, \"div\", 39)(112, \"div\", 40)(113, \"div\", 41)(114, \"div\", 71)(115, \"h3\", 65)(116, \"div\", 72);\n    i0.ɵɵelement(117, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(118, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(119, \"span\", 74);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"div\", 42);\n    i0.ɵɵtemplate(122, EquipeDetailComponent_div_0_div_122_Template, 11, 3, \"div\", 75);\n    i0.ɵɵtemplate(123, EquipeDetailComponent_div_0_ng_template_123_Template, 15, 2, \"ng-template\", null, 76, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(124);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.name, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(ctx_r0.equipe.createdAt));\n    i0.ɵɵadvance(39);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.getAdminId(ctx_r0.equipe.admin)) || ctx_r0.getAdminId(ctx_r0.equipe.admin) : \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.description || \"Aucune description disponible pour cette \\u00E9quipe.\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Cr\\u00E9\\u00E9e le \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"team\", ctx_r0.equipe);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teamMembers.length || 0, \" membres \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teamMembers && ctx_r0.teamMembers.length > 0)(\"ngIfElse\", _r3);\n  }\n}\nfunction EquipeDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 135)(2, \"div\", 136)(3, \"div\", 137)(4, \"div\", 138);\n    i0.ɵɵelement(5, \"i\", 139);\n    i0.ɵɵelementStart(6, \"div\", 140);\n    i0.ɵɵtext(7, \" \\u00C9quipe non trouv\\u00E9e ou en cours de chargement... \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.navigateToEquipeList());\n    });\n    i0.ɵɵelement(9, \"i\", 142);\n    i0.ɵɵtext(10, \" Retour \\u00E0 la liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport let EquipeDetailComponent = /*#__PURE__*/(() => {\n  class EquipeDetailComponent {\n    constructor(equipeService, userService,\n    // TODO: Will be used when implementing real user API calls\n    route, router) {\n      this.equipeService = equipeService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.equipe = null;\n      this.loading = false;\n      this.error = null;\n      this.equipeId = null;\n      this.newMembre = {\n        id: '',\n        role: 'membre'\n      };\n      this.availableUsers = [];\n      this.memberNames = {}; // Map pour stocker les noms des membres\n      this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n    }\n\n    ngOnInit() {\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      // Charger tous les utilisateurs disponibles\n      this.loadUsers();\n      if (this.equipeId) {\n        this.loadEquipe(this.equipeId);\n      } else {\n        this.error = \"ID d'équipe non spécifié\";\n      }\n    }\n    // Méthode pour charger tous les utilisateurs\n    loadUsers() {\n      // TODO: Implémenter l'API pour récupérer les utilisateurs\n      // Pour l'instant, utiliser des données mockées\n      const mockUsers = [{\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true\n      }, {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true\n      }, {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true\n      }];\n      // Simuler un délai d'API\n      setTimeout(() => {\n        // Stocker tous les utilisateurs pour la recherche de noms\n        const allUsers = [...mockUsers];\n        console.log('Tous les utilisateurs chargés (mock):', allUsers);\n        // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n        if (this.teamMembers && this.teamMembers.length > 0) {\n          const memberUserIds = this.teamMembers.map(m => m.user);\n          this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n        } else {\n          this.availableUsers = mockUsers;\n        }\n        console.log('Utilisateurs disponibles:', this.availableUsers);\n        // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members) {\n          this.updateMemberNames();\n        }\n      }, 500);\n    }\n    // Méthode pour mettre à jour les noms des membres\n    updateMemberNames() {\n      if (!this.equipe || !this.equipe.members) return;\n      this.equipe.members.forEach(membre => {\n        const membreId = typeof membre === 'string' ? membre : membre._id || membre.id;\n        if (membreId) {\n          const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n          if (user && user.name) {\n            this.memberNames[membreId] = user.name;\n          } else {\n            // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n            // TODO: Implémenter getUser dans AuthuserService\n            // Pour l'instant, utiliser l'ID comme nom par défaut\n            this.memberNames[membreId] = membreId;\n          }\n        }\n      });\n    }\n    // Méthode pour obtenir le nom d'un membre\n    getMembreName(membreId) {\n      return this.memberNames[membreId] || membreId;\n    }\n    // Méthode utilitaire pour extraire l'ID d'un admin\n    getAdminId(admin) {\n      if (typeof admin === 'string') {\n        return admin;\n      }\n      return admin?._id || admin?.id || '';\n    }\n    // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n    getUserName(userId) {\n      if (!userId) {\n        return 'Non défini';\n      }\n      const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n      if (user) {\n        if (user.firstName && user.lastName) {\n          return `${user.firstName} ${user.lastName}`;\n        } else if (user.name) {\n          return user.name;\n        }\n      }\n      return userId;\n    }\n    // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n    getUserProfession(userId) {\n      if (!userId) {\n        return '';\n      }\n      const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n      if (user) {\n        return user.profession || user.role || '';\n      }\n      return '';\n    }\n    loadEquipe(id) {\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipe(id).subscribe({\n        next: data => {\n          console.log(\"Détails de l'équipe chargés:\", data);\n          this.equipe = data;\n          // Charger les détails des membres de l'équipe\n          this.loadTeamMembers(id);\n          // Mettre à jour les noms des membres\n          if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n            this.updateMemberNames();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour charger les détails des membres de l'équipe\n    loadTeamMembers(teamId) {\n      this.equipeService.getTeamMembers(teamId).subscribe({\n        next: members => {\n          console.log('Détails des membres chargés:', members);\n          this.teamMembers = members;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des détails des membres:', error);\n        }\n      });\n    }\n    navigateToEditEquipe() {\n      if (this.equipeId) {\n        this.router.navigate(['/equipes/modifier', this.equipeId]);\n      }\n    }\n    navigateToEquipeList() {\n      this.router.navigate(['/equipes/liste']);\n    }\n    navigateToTasks() {\n      if (this.equipeId) {\n        this.router.navigate(['/admin/tasks', this.equipeId]);\n      }\n    }\n    // Méthode pour formater les dates\n    formatDate(date) {\n      if (!date) {\n        return 'N/A';\n      }\n      try {\n        let dateObj;\n        if (typeof date === 'string') {\n          dateObj = new Date(date);\n        } else {\n          dateObj = date;\n        }\n        if (isNaN(dateObj.getTime())) {\n          return 'Date invalide';\n        }\n        // Format: JJ/MM/AAAA\n        return dateObj.toLocaleDateString('fr-FR', {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        });\n      } catch (error) {\n        console.error('Erreur lors du formatage de la date:', error);\n        return 'Erreur de date';\n      }\n    }\n    // Méthode pour ajouter un membre à l'équipe\n    addMembre(userId, role) {\n      console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n      if (!this.equipeId || !userId) {\n        console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n        this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n        return;\n      }\n      // Vérifier si l'utilisateur est déjà membre de l'équipe\n      const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n      if (isAlreadyMember) {\n        this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n        alert(\"Cet utilisateur est déjà membre de l'équipe\");\n        return;\n      }\n      // Créer l'objet membre avec le rôle spécifié\n      const membre = {\n        id: userId,\n        role: role || 'membre'\n      };\n      // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n      const userName = this.getUserName(userId);\n      const roleName = role === 'admin' ? 'administrateur' : 'membre';\n      this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n          this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n          alert(this.error);\n        }\n      });\n    }\n    // Méthode pour mettre à jour la liste des utilisateurs disponibles\n    updateAvailableUsers() {\n      // TODO: Implémenter l'API pour récupérer les utilisateurs\n      // Pour l'instant, utiliser les données mockées de loadUsers()\n      this.loadUsers();\n    }\n    // Ancienne méthode maintenue pour compatibilité\n    addMembreToEquipe() {\n      if (!this.equipeId || !this.newMembre.id) {\n        console.error(\"ID d'équipe ou ID de membre manquant\");\n        return;\n      }\n      this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n    }\n    removeMembreFromEquipe(membreId) {\n      console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n      if (!this.equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n        return;\n      }\n      // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n      const userId = membreId;\n      // Récupérer le nom de l'utilisateur pour un message plus informatif\n      const userName = this.getUserName(userId);\n      console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n      if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n          next: response => {\n            console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n            this.loading = false;\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId);\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId);\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: error => {\n            console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n          }\n        });\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    }\n    deleteEquipe() {\n      console.log('Méthode deleteEquipe appelée');\n      if (!this.equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n        return;\n      }\n      console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        this.equipeService.deleteEquipe(this.equipeId).subscribe({\n          next: () => {\n            console.log('Équipe supprimée avec succès');\n            this.loading = false;\n            alert('Équipe supprimée avec succès');\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n            this.loading = false;\n            this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n            alert(`Erreur lors de la suppression: ${this.error}`);\n          }\n        });\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    }\n    static {\n      this.ɵfac = function EquipeDetailComponent_Factory(t) {\n        return new (t || EquipeDetailComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeDetailComponent,\n        selectors: [[\"app-equipe-detail\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\", 4, \"ngIf\"], [\"class\", \"container-fluid py-5 bg-light\", 4, \"ngIf\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-0\"], [1, \"lg:col-span-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"p-6\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\", \"tracking-wide\"], [1, \"text-white/80\", \"text-sm\", \"mb-6\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"bg-white/20\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-4\", \"text-white\", \"text-center\"], [1, \"fas\", \"fa-users\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-xl\", \"font-bold\", \"block\"], [1, \"text-white/80\"], [1, \"fas\", \"fa-tasks\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"fas\", \"fa-calendar-check\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-sm\", \"font-bold\", \"block\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"p-6\", \"flex\", \"flex-col\", \"justify-center\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-bolt\", \"mr-2\"], [1, \"space-y-3\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", 3, \"click\"], [1, \"fas\", \"fa-tasks\", \"mr-2\"], [1, \"w-full\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"grid\", \"grid-cols-2\", \"gap-2\"], [1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-4\", \"py-2\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-1\"], [1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-4\", \"py-2\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-1\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"overflow-hidden\", \"hover-card\"], [1, \"card-body\", \"p-0\"], [1, \"row\", \"g-0\"], [1, \"col-md-3\", \"bg-primary\", \"text-white\", \"p-4\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"text-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"mb-3\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-1\"], [1, \"mb-2\"], [1, \"mb-0\", \"text-white-50\"], [1, \"col-md-9\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-primary\", \"mb-0\"], [1, \"badge\", \"bg-light\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-person-fill-gear\", \"me-1\"], [1, \"description-box\", \"p-3\", \"bg-light\", \"rounded-4\", \"mb-4\"], [1, \"lead\", \"mb-0\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\", \"mt-4\"], [1, \"badge\", \"bg-primary\", \"bg-opacity-10\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-people-fill\", \"me-1\"], [1, \"badge\", \"bg-success\", \"bg-opacity-10\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-calendar-check\", \"me-1\"], [1, \"badge\", \"bg-info\", \"bg-opacity-10\", \"text-info\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-kanban\", \"me-1\"], [1, \"card-header\", \"border-0\", \"py-4\", 2, \"background\", \"linear-gradient(45deg, #8e2de2, #4a00e0)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\", \"text-white\", \"d-flex\", \"align-items-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"me-3\"], [1, \"bi\", \"bi-robot\"], [1, \"badge\", \"bg-white\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-magic\", \"me-1\"], [3, \"team\"], [1, \"card-header\", \"border-0\", \"py-4\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background\", \"linear-gradient(45deg, #11998e, #38ef7d)\"], [1, \"icon-circle\", \"bg-white\", \"text-success\", \"me-3\"], [1, \"bi\", \"bi-people-fill\"], [1, \"badge\", \"bg-white\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [\"class\", \"p-0\", 4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"p-0\"], [1, \"col-md-8\"], [1, \"member-grid\", \"p-4\"], [\"class\", \"member-card mb-3 p-3 rounded-4 shadow-sm transition\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"bg-light\", \"p-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-4\", \"text-success\"], [1, \"bi\", \"bi-person-plus-fill\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"add-member-form\", 4, \"ngIf\"], [1, \"member-card\", \"mb-3\", \"p-3\", \"rounded-4\", \"shadow-sm\", \"transition\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\"], [1, \"d-flex\", \"align-items-center\"], [1, \"member-avatar\", \"rounded-circle\", \"text-white\", \"me-3\", 3, \"ngClass\"], [1, \"bi\", 3, \"ngClass\"], [1, \"mb-0\", \"fw-bold\"], [1, \"d-flex\", \"align-items-center\", \"mt-1\"], [1, \"badge\", \"rounded-pill\", \"me-2\", 3, \"ngClass\"], [1, \"text-muted\"], [\"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-4\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [1, \"add-member-form\"], [1, \"mb-3\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [1, \"d-flex\", \"gap-2\"], [1, \"form-check\", \"flex-grow-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleMembre\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre\", \"\"], [\"for\", \"roleMembre\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person\", \"d-block\", \"fs-4\", \"mb-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleAdmin\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin\", \"\"], [\"for\", \"roleAdmin\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person-fill-gear\", \"d-block\", \"fs-4\", \"mb-1\"], [1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"rounded-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [3, \"value\"], [1, \"text-center\", \"py-5\"], [1, \"empty-state-icon\", \"mb-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\"], [\"for\", \"userSelect2\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect2\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect2\", \"\"], [\"for\", \"roleSelect2\", 1, \"form-label\", \"fw-medium\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleMembre2\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre2\", \"\"], [\"for\", \"roleMembre2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleAdmin2\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin2\", \"\"], [\"for\", \"roleAdmin2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"text-center\"], [1, \"alert\", \"alert-warning\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\", \"p-4\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-1\", \"me-4\", \"text-warning\"], [1, \"fs-5\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-pill\", \"mt-4\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"]],\n        template: function EquipeDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, EquipeDetailComponent_div_0_Template, 125, 11, \"div\", 0);\n            i0.ɵɵtemplate(1, EquipeDetailComponent_div_1_Template, 11, 0, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.equipe);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.equipe);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i6.AiChatComponent],\n        styles: [\".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}summary[_ngcontent-%COMP%]:hover{text-decoration:underline}\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  .bg-gradient-light[_ngcontent-%COMP%] {\\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  \\n\\n  .hover-card[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .hover-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-card[_ngcontent-%COMP%] {\\n    background-color: white;\\n    transition: all 0.3s ease;\\n    border-left: 4px solid transparent;\\n  }\\n\\n  .member-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-3px);\\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .icon-circle[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .description-box[_ngcontent-%COMP%] {\\n    border-left: 4px solid #007bff;\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\\n\\n  \\n\\n  .badge[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n  }\\n\\n  \\n\\n  .form-select[_ngcontent-%COMP%], .form-control[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .empty-state-icon[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n    margin: 0 auto;\\n    background-color: #f8f9fa;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 2rem;\\n    color: #adb5bd;\\n  }\\n\\n  \\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-check-input[_ngcontent-%COMP%]:checked    + .form-check-label[_ngcontent-%COMP%] {\\n    background-color: rgba(13, 110, 253, 0.1);\\n    border-color: #007bff;\\n  }\\n\\n  \\n\\n  .rounded-4[_ngcontent-%COMP%] {\\n    border-radius: 0.75rem !important;\\n  }\\n\\n  \\n\\n  .member-grid[_ngcontent-%COMP%] {\\n    max-height: 500px;\\n    overflow-y: auto;\\n  }\"]\n      });\n    }\n  }\n  return EquipeDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}