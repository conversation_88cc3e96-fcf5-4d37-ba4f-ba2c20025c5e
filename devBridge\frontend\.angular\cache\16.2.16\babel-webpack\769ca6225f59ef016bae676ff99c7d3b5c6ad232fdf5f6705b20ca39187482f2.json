{"ast": null, "code": "/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind = /*#__PURE__*/function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n  return TokenKind;\n}(TokenKind || {});\nexport { TokenKind };\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}