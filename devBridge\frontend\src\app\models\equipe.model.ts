import { User } from './user.model';

export interface Equipe {
  _id?: string;
  id?: string;
  name: string;
  description?: string;
  admin?: string | User; // Peut être un ID ou un objet User populé

  // Système de rôles avancé
  coAdmins?: (string | User)[];
  moderators?: (string | User)[];
  members?: (string | User)[]; // Peut être des IDs ou des objets User populés

  // Nouveaux champs
  status?: 'active' | 'inactive' | 'archived';
  maxMembers?: number;
  project?: string; // ID du projet associé
  tags?: string[];
  isPublic?: boolean;

  // Nouvelles fonctionnalités
  allowJoinRequests?: boolean;
  requireApproval?: boolean;
  inviteCode?: string;
  inviteCodeExpiry?: Date | string;

  // Statistiques
  stats?: {
    totalProjects?: number;
    completedTasks?: number;
    totalTasks?: number;
    lastActivity?: Date | string;
  };

  // Paramètres avancés
  settings?: {
    allowMemberInvite?: boolean;
    allowMemberKick?: boolean;
    autoArchiveInactive?: boolean;
    inactivityDays?: number;
  };

  // Template et duplication
  isTemplate?: boolean;
  templateCategory?: 'development' | 'research' | 'design' | 'marketing' | 'general';
  originalTeam?: string;

  // Propriétés virtuelles (calculées côté backend)
  memberCount?: number;
  isFullTeam?: boolean;
  availableSlots?: number;

  // Rôle de l'utilisateur actuel dans l'équipe
  userRole?: 'admin' | 'co-admin' | 'moderator' | 'member' | 'none';

  // Timestamps
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface TeamMember {
  _id?: string;
  user: string | User;
  role: 'admin' | 'membre';
  userType: 'etudiant' | 'enseignant';
  team: string | Equipe;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface CreateTeamRequest {
  name: string;
  description?: string;
  maxMembers?: number;
  tags?: string[];
  isPublic?: boolean;
}

export interface UpdateTeamRequest {
  name?: string;
  description?: string;
  maxMembers?: number;
  tags?: string[];
  isPublic?: boolean;
  status?: 'active' | 'inactive' | 'archived';
}

export interface AddMemberRequest {
  userId: string;
}

export interface RemoveMemberRequest {
  userId: string;
}

export interface TeamSearchFilters {
  status?: string;
  isPublic?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface TeamListResponse {
  teams: Equipe[];
  pagination: {
    current: number;
    total: number;
    count: number;
    totalItems: number;
  };
}

// ==================== INVITATIONS ====================

export interface TeamInvitation {
  _id?: string;
  team?: string | Equipe;
  inviter?: string | User;
  invitee?: string | User;
  email?: string;
  type?: 'direct' | 'email' | 'link' | 'request';
  status?: 'pending' | 'accepted' | 'declined' | 'expired' | 'cancelled';
  role?: 'member' | 'moderator' | 'co-admin';
  message?: string;
  token?: string;
  expiresAt?: Date | string;
  acceptedAt?: Date | string;
  declinedAt?: Date | string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface SendInvitationRequest {
  email?: string;
  userId?: string;
  role?: 'member' | 'moderator' | 'co-admin';
  message?: string;
}

// ==================== DEMANDES D'ADHÉSION ====================

export interface JoinRequest {
  _id?: string;
  team?: string | Equipe;
  user?: string | User;
  status?: 'pending' | 'approved' | 'rejected' | 'cancelled';
  message?: string;
  requestedRole?: 'member' | 'moderator';
  adminResponse?: {
    admin?: string | User;
    message?: string;
    respondedAt?: Date | string;
  };
  approvedAt?: Date | string;
  rejectedAt?: Date | string;
  cancelledAt?: Date | string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface SendJoinRequestRequest {
  message?: string;
  requestedRole?: 'member' | 'moderator';
}

export interface ApproveJoinRequestRequest {
  message?: string;
  assignedRole?: 'member' | 'moderator' | 'co-admin';
}

export interface RejectJoinRequestRequest {
  message?: string;
}

// ==================== FAVORIS ====================

export interface TeamFavorite {
  _id?: string;
  user?: string | User;
  team?: string | Equipe;
  category?: 'work' | 'personal' | 'study' | 'project' | 'other';
  notes?: string;
  tags?: string[];
  priority?: number;
  notifications?: {
    enabled?: boolean;
    types?: string[];
  };
  stats?: {
    viewCount?: number;
    lastViewed?: Date | string;
    interactionCount?: number;
  };
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface AddToFavoritesRequest {
  category?: 'work' | 'personal' | 'study' | 'project' | 'other';
  notes?: string;
  tags?: string[];
  priority?: number;
}

// ==================== STATISTIQUES ====================

export interface TeamStats {
  teamId?: string;
  teamName?: string;
  basicStats?: {
    totalMembers?: number;
    maxMembers?: number;
    availableSlots?: number;
    completionRate?: number;
    isFullTeam?: boolean;
    status?: string;
    createdAt?: Date | string;
    lastActivity?: Date | string;
  };
  roleDistribution?: {
    admin?: number;
    coAdmins?: number;
    moderators?: number;
    members?: number;
  };
  projectStats?: {
    totalProjects?: number;
    completedTasks?: number;
    totalTasks?: number;
    pendingTasks?: number;
  };
  activityStats?: any[];
  generatedAt?: Date | string;
}

export interface TeamAnalytics {
  teamId?: string;
  teamName?: string;
  period?: number;
  analytics?: {
    activity?: any[];
    members?: any[];
    topContributors?: any[];
  };
  generatedAt?: Date | string;
}

// ==================== HISTORIQUE ====================

export interface TeamHistory {
  _id?: string;
  team?: string | Equipe;
  user?: string | User;
  action?: string;
  description?: string;
  previousData?: any;
  newData?: any;
  affectedUser?: string | User;
  category?: 'team' | 'member' | 'settings' | 'invitation' | 'request' | 'admin';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  tags?: string[];
  createdAt?: Date | string;
}

// ==================== TEMPLATES ====================

export interface TeamTemplate {
  _id?: string;
  name?: string;
  description?: string;
  templateCategory?: 'development' | 'research' | 'design' | 'marketing' | 'general';
  tags?: string[];
  maxMembers?: number;
  settings?: any;
  admin?: string | User;
  createdAt?: Date | string;
}

export interface UseTemplateRequest {
  name: string;
  description?: string;
}

export interface DuplicateTeamRequest {
  name: string;
  description?: string;
  includeMembers?: boolean;
}

export interface CreateTemplateRequest {
  templateName: string;
  templateCategory?: 'development' | 'research' | 'design' | 'marketing' | 'general';
  description?: string;
}

// ==================== RÔLES ====================

export interface RoleChangeRequest {
  userId: string;
}

export interface TransferAdminRequest {
  userId: string;
}
