{"ast": null, "code": "import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted to milliseconds\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\nexport function hoursToMilliseconds(hours) {\n  return Math.trunc(hours * millisecondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMilliseconds;", "map": {"version": 3, "names": ["millisecondsInHour", "hoursToMilliseconds", "hours", "Math", "trunc"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/hoursToMilliseconds.js"], "sourcesContent": ["import { millisecondsInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted to milliseconds\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\nexport function hoursToMilliseconds(hours) {\n  return Math.trunc(hours * millisecondsInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMilliseconds;\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gBAAgB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,kBAAkB,CAAC;AAC/C;;AAEA;AACA,eAAeC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}