{"ast": null, "code": "function defaultDispose() {}\nexport class StrongCache {\n  constructor(max = Infinity, dispose = defaultDispose) {\n    this.max = max;\n    this.dispose = dispose;\n    this.map = new Map();\n    this.newest = null;\n    this.oldest = null;\n  }\n  has(key) {\n    return this.map.has(key);\n  }\n  get(key) {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n  get size() {\n    return this.map.size;\n  }\n  getNode(key) {\n    const node = this.map.get(key);\n    if (node && node !== this.newest) {\n      const {\n        older,\n        newer\n      } = node;\n      if (newer) {\n        newer.older = older;\n      }\n      if (older) {\n        older.newer = newer;\n      }\n      node.older = this.newest;\n      node.older.newer = node;\n      node.newer = null;\n      this.newest = node;\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n    return node;\n  }\n  set(key, value) {\n    let node = this.getNode(key);\n    if (node) {\n      return node.value = value;\n    }\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest\n    };\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n    this.newest = node;\n    this.oldest = this.oldest || node;\n    this.map.set(key, node);\n    return node.value;\n  }\n  clean() {\n    while (this.oldest && this.map.size > this.max) {\n      this.delete(this.oldest.key);\n    }\n  }\n  delete(key) {\n    const node = this.map.get(key);\n    if (node) {\n      if (node === this.newest) {\n        this.newest = node.older;\n      }\n      if (node === this.oldest) {\n        this.oldest = node.newer;\n      }\n      if (node.newer) {\n        node.newer.older = node.older;\n      }\n      if (node.older) {\n        node.older.newer = node.newer;\n      }\n      this.map.delete(key);\n      this.dispose(node.value, key);\n      return true;\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["defaultDispose", "StrongCache", "constructor", "max", "Infinity", "dispose", "map", "Map", "newest", "oldest", "has", "key", "get", "node", "getNode", "value", "size", "older", "newer", "set", "clean", "delete"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/@wry/caches/lib/strong.js"], "sourcesContent": ["function defaultDispose() { }\nexport class StrongCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new Map();\n        this.newest = null;\n        this.oldest = null;\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    get size() {\n        return this.map.size;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return node.value = value;\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.map.set(key, node);\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.map.size > this.max) {\n            this.delete(this.oldest.key);\n        }\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            if (node === this.newest) {\n                this.newest = node.older;\n            }\n            if (node === this.oldest) {\n                this.oldest = node.newer;\n            }\n            if (node.newer) {\n                node.newer.older = node.older;\n            }\n            if (node.older) {\n                node.older.newer = node.newer;\n            }\n            this.map.delete(key);\n            this.dispose(node.value, key);\n            return true;\n        }\n        return false;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAAA,EAAG,CAAE;AAC5B,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,GAAG,GAAGC,QAAQ,EAAEC,OAAO,GAAGL,cAAc,EAAE;IAClD,IAAI,CAACG,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB;EACAC,GAAGA,CAACC,GAAG,EAAE;IACL,OAAO,IAAI,CAACL,GAAG,CAACI,GAAG,CAACC,GAAG,CAAC;EAC5B;EACAC,GAAGA,CAACD,GAAG,EAAE;IACL,MAAME,IAAI,GAAG,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC;IAC9B,OAAOE,IAAI,IAAIA,IAAI,CAACE,KAAK;EAC7B;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACV,GAAG,CAACU,IAAI;EACxB;EACAF,OAAOA,CAACH,GAAG,EAAE;IACT,MAAME,IAAI,GAAG,IAAI,CAACP,GAAG,CAACM,GAAG,CAACD,GAAG,CAAC;IAC9B,IAAIE,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACL,MAAM,EAAE;MAC9B,MAAM;QAAES,KAAK;QAAEC;MAAM,CAAC,GAAGL,IAAI;MAC7B,IAAIK,KAAK,EAAE;QACPA,KAAK,CAACD,KAAK,GAAGA,KAAK;MACvB;MACA,IAAIA,KAAK,EAAE;QACPA,KAAK,CAACC,KAAK,GAAGA,KAAK;MACvB;MACAL,IAAI,CAACI,KAAK,GAAG,IAAI,CAACT,MAAM;MACxBK,IAAI,CAACI,KAAK,CAACC,KAAK,GAAGL,IAAI;MACvBA,IAAI,CAACK,KAAK,GAAG,IAAI;MACjB,IAAI,CAACV,MAAM,GAAGK,IAAI;MAClB,IAAIA,IAAI,KAAK,IAAI,CAACJ,MAAM,EAAE;QACtB,IAAI,CAACA,MAAM,GAAGS,KAAK;MACvB;IACJ;IACA,OAAOL,IAAI;EACf;EACAM,GAAGA,CAACR,GAAG,EAAEI,KAAK,EAAE;IACZ,IAAIF,IAAI,GAAG,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC;IAC5B,IAAIE,IAAI,EAAE;MACN,OAAOA,IAAI,CAACE,KAAK,GAAGA,KAAK;IAC7B;IACAF,IAAI,GAAG;MACHF,GAAG;MACHI,KAAK;MACLG,KAAK,EAAE,IAAI;MACXD,KAAK,EAAE,IAAI,CAACT;IAChB,CAAC;IACD,IAAI,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACU,KAAK,GAAGL,IAAI;IAC5B;IACA,IAAI,CAACL,MAAM,GAAGK,IAAI;IAClB,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACA,MAAM,IAAII,IAAI;IACjC,IAAI,CAACP,GAAG,CAACa,GAAG,CAACR,GAAG,EAAEE,IAAI,CAAC;IACvB,OAAOA,IAAI,CAACE,KAAK;EACrB;EACAK,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACX,MAAM,IAAI,IAAI,CAACH,GAAG,CAACU,IAAI,GAAG,IAAI,CAACb,GAAG,EAAE;MAC5C,IAAI,CAACkB,MAAM,CAAC,IAAI,CAACZ,MAAM,CAACE,GAAG,CAAC;IAChC;EACJ;EACAU,MAAMA,CAACV,GAAG,EAAE;IACR,MAAME,IAAI,GAAG,IAAI,CAACP,GAAG,CAACM,GAAG,CAACD,GAAG,CAAC;IAC9B,IAAIE,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACL,MAAM,EAAE;QACtB,IAAI,CAACA,MAAM,GAAGK,IAAI,CAACI,KAAK;MAC5B;MACA,IAAIJ,IAAI,KAAK,IAAI,CAACJ,MAAM,EAAE;QACtB,IAAI,CAACA,MAAM,GAAGI,IAAI,CAACK,KAAK;MAC5B;MACA,IAAIL,IAAI,CAACK,KAAK,EAAE;QACZL,IAAI,CAACK,KAAK,CAACD,KAAK,GAAGJ,IAAI,CAACI,KAAK;MACjC;MACA,IAAIJ,IAAI,CAACI,KAAK,EAAE;QACZJ,IAAI,CAACI,KAAK,CAACC,KAAK,GAAGL,IAAI,CAACK,KAAK;MACjC;MACA,IAAI,CAACZ,GAAG,CAACe,MAAM,CAACV,GAAG,CAAC;MACpB,IAAI,CAACN,OAAO,CAACQ,IAAI,CAACE,KAAK,EAAEJ,GAAG,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}