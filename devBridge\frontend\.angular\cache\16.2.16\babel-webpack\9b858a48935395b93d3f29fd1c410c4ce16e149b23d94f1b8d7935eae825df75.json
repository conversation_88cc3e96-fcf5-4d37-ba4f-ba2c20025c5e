{"ast": null, "code": "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}", "map": {"version": 3, "names": ["dayOfYearTokenRE", "weekYearTokenRE", "throwTokens", "isProtectedDayOfYearToken", "token", "test", "isProtectedWeekYearToken", "warnOrThrowProtectedError", "format", "input", "_message", "message", "console", "warn", "includes", "RangeError", "subject", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/final pi/devBridge/frontend/node_modules/date-fns/_lib/protectedTokens.js"], "sourcesContent": ["const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\n\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\n\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\n\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\n\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\n\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,MAAM;AAC/B,MAAMC,eAAe,GAAG,MAAM;AAE9B,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;AAE7C,OAAO,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,OAAOJ,gBAAgB,CAACK,IAAI,CAACD,KAAK,CAAC;AACrC;AAEA,OAAO,SAASE,wBAAwBA,CAACF,KAAK,EAAE;EAC9C,OAAOH,eAAe,CAACI,IAAI,CAACD,KAAK,CAAC;AACpC;AAEA,OAAO,SAASG,yBAAyBA,CAACH,KAAK,EAAEI,MAAM,EAAEC,KAAK,EAAE;EAC9D,MAAMC,QAAQ,GAAGC,OAAO,CAACP,KAAK,EAAEI,MAAM,EAAEC,KAAK,CAAC;EAC9CG,OAAO,CAACC,IAAI,CAACH,QAAQ,CAAC;EACtB,IAAIR,WAAW,CAACY,QAAQ,CAACV,KAAK,CAAC,EAAE,MAAM,IAAIW,UAAU,CAACL,QAAQ,CAAC;AACjE;AAEA,SAASC,OAAOA,CAACP,KAAK,EAAEI,MAAM,EAAEC,KAAK,EAAE;EACrC,MAAMO,OAAO,GAAGZ,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,OAAO,GAAG,mBAAmB;EAChE,OAAQ,SAAQA,KAAK,CAACa,WAAW,CAAC,CAAE,mBAAkBb,KAAM,YAAWI,MAAO,sBAAqBQ,OAAQ,mBAAkBP,KAAM,iFAAgF;AACrN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}